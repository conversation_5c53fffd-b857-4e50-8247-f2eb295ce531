// Core types for the interior design AI application

export interface Room {
  id: string;
  name: string;
  type: RoomType;
  dimensions?: {
    width: number;
    height: number;
    length?: number;
  };
  budget?: number;
  style?: DesignStyle;
  imageUrl?: string;
  analysisResult?: RoomAnalysis;
  recommendations?: FurnitureRecommendation[];
}

export type RoomType = 
  | 'living_room'
  | 'bedroom'
  | 'kitchen'
  | 'bathroom'
  | 'dining_room'
  | 'office'
  | 'nursery'
  | 'guest_room';

export type DesignStyle = 
  | 'modern'
  | 'minimalist'
  | 'boho'
  | 'industrial'
  | 'scandinavian'
  | 'traditional'
  | 'contemporary'
  | 'rustic';

export interface RoomAnalysis {
  layout: {
    doors: DetectedObject[];
    windows: DetectedObject[];
    walls: Wall[];
    floorArea: number;
  };
  existingFurniture: DetectedFurniture[];
  lightingSources: LightingSource[];
  colorPalette: string[];
  spatialFlow: {
    entryPoints: Point[];
    trafficPaths: Path[];
    functionalZones: Zone[];
  };
}

export interface DetectedObject {
  id: string;
  type: string;
  boundingBox: BoundingBox;
  confidence: number;
}

export interface DetectedFurniture extends DetectedObject {
  category: FurnitureCategory;
  estimatedSize: Dimensions;
  condition?: 'good' | 'fair' | 'poor';
}

export interface Wall {
  id: string;
  start: Point;
  end: Point;
  length: number;
  hasOpenings: boolean;
  openings: DetectedObject[];
}

export interface LightingSource {
  type: 'natural' | 'artificial';
  position: Point;
  intensity: number;
  direction?: string;
}

export interface Point {
  x: number;
  y: number;
}

export interface Path {
  points: Point[];
  width: number;
}

export interface Zone {
  id: string;
  type: string;
  area: Point[];
  function: string;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface Dimensions {
  width: number;
  height: number;
  depth?: number;
}

export type FurnitureCategory = 
  | 'seating'
  | 'tables'
  | 'storage'
  | 'lighting'
  | 'decor'
  | 'textiles'
  | 'plants';

export interface FurnitureRecommendation {
  id: string;
  name: string;
  category: FurnitureCategory;
  description: string;
  price: number;
  currency: string;
  imageUrl: string;
  productUrl: string;
  affiliateUrl?: string;
  retailer: 'amazon' | 'ikea' | 'other';
  dimensions: Dimensions;
  style: DesignStyle[];
  placement: {
    suggestedPosition: Point;
    orientation?: number;
    zone: string;
  };
  compatibility: {
    roomType: RoomType[];
    existingFurniture: string[];
    styleMatch: number; // 0-1 score
  };
}

export interface User {
  id: string;
  email: string;
  name: string;
  preferences?: UserPreferences;
  savedRooms: string[];
  wishlist: string[];
}

export interface UserPreferences {
  preferredStyles: DesignStyle[];
  budgetRange: {
    min: number;
    max: number;
  };
  favoriteRetailers: string[];
  colorPreferences: string[];
}

export interface AIModelConfig {
  modelName: string;
  modelUrl: string;
  inputSize: {
    width: number;
    height: number;
  };
  confidence: number;
  labels: string[];
}

export interface ImageUpload {
  file: File;
  preview: string;
  roomType?: RoomType;
  budget?: number;
  style?: DesignStyle;
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
