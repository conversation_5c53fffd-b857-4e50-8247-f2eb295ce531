import { RecommendationEngine } from '@/lib/recommendation-engine';
import { RoomAnalysis, RoomType, DesignStyle } from '@/types';

describe('RecommendationEngine', () => {
  const mockRoomAnalysis: RoomAnalysis = {
    layout: {
      doors: [],
      windows: [],
      walls: [],
      floorArea: 2000
    },
    existingFurniture: [],
    lightingSources: [],
    colorPalette: ['#FFFFFF', '#F5F5F5'],
    spatialFlow: {
      entryPoints: [],
      trafficPaths: [],
      functionalZones: []
    }
  };

  const recommendationEngine = new RecommendationEngine();

  it('generates recommendations for a living room', async () => {
    const recommendations = await recommendationEngine.generateRecommendations(
      mockRoomAnalysis,
      'living_room' as RoomType,
      2000,
      'modern' as DesignStyle
    );

    expect(Array.isArray(recommendations)).toBe(true);
    expect(recommendations.length).toBeGreaterThan(0);
  });

  it('respects budget constraints', async () => {
    const budget = 500;
    const recommendations = await recommendationEngine.generateRecommendations(
      mockRoomAnalysis,
      'living_room' as RoomType,
      budget,
      'modern' as DesignStyle
    );

    const totalCost = recommendations.reduce((sum, item) => sum + item.price, 0);
    expect(totalCost).toBeLessThanOrEqual(budget);
  });

  it('prioritizes style matching', async () => {
    const recommendations = await recommendationEngine.generateRecommendations(
      mockRoomAnalysis,
      'living_room' as RoomType,
      2000,
      'modern' as DesignStyle
    );

    // At least some recommendations should match the requested style
    const modernItems = recommendations.filter(item => 
      item.style.includes('modern')
    );
    expect(modernItems.length).toBeGreaterThan(0);
  });
});
