import { render, screen } from '@testing-library/react';
import { Header } from '@/components/Header';

describe('Header Component', () => {
  it('renders the logo and navigation', () => {
    render(<Header />);
    
    // Check if logo is present
    expect(screen.getByText('Interior AI')).toBeInTheDocument();
    
    // Check if navigation items are present
    expect(screen.getByText('How it Works')).toBeInTheDocument();
    expect(screen.getByText('Examples')).toBeInTheDocument();
    expect(screen.getByText('Pricing')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
  });

  it('has responsive mobile menu', () => {
    render(<Header />);

    // Mobile menu button should be present (look for the menu icon)
    const menuButtons = screen.getAllByRole('button');
    expect(menuButtons.length).toBeGreaterThan(0);
  });
});
