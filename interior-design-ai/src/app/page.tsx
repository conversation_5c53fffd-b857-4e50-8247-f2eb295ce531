'use client';

import { useState } from 'react';
import { ImageUpload } from '@/components/ImageUpload';
import { RoomConfiguration } from '@/components/RoomConfiguration';
import { RoomAnalysis } from '@/components/RoomAnalysis';
import { RecommendationDisplay } from '@/components/RecommendationDisplay';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Room, RoomType, DesignStyle } from '@/types';

export default function Home() {
  const [currentStep, setCurrentStep] = useState<'upload' | 'configure' | 'analyze' | 'recommendations'>('upload');
  const [room, setRoom] = useState<Partial<Room>>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleImageUpload = (uploadData: any) => {
    setRoom(prev => ({
      ...prev,
      id: uploadData.id,
      imageUrl: uploadData.url,
      name: `Room ${uploadData.id}`
    }));
    setCurrentStep('configure');
  };

  const handleConfiguration = (config: {
    roomType: RoomType;
    budget: number;
    style: DesignStyle;
  }) => {
    setRoom(prev => ({
      ...prev,
      type: config.roomType,
      budget: config.budget,
      style: config.style
    }));
    setCurrentStep('analyze');
  };

  const handleAnalysisComplete = (analysisResult: any) => {
    setRoom(prev => ({
      ...prev,
      analysisResult: analysisResult.analysis
    }));
    setCurrentStep('recommendations');
  };

  const handleStartOver = () => {
    setRoom({});
    setCurrentStep('upload');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[
              { step: 'upload', label: 'Upload Photo', icon: '📸' },
              { step: 'configure', label: 'Configure Room', icon: '⚙️' },
              { step: 'analyze', label: 'AI Analysis', icon: '🤖' },
              { step: 'recommendations', label: 'Recommendations', icon: '✨' }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-12 h-12 rounded-full text-lg
                  ${currentStep === item.step
                    ? 'bg-blue-600 text-white'
                    : index < ['upload', 'configure', 'analyze', 'recommendations'].indexOf(currentStep)
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                  }
                `}>
                  {item.icon}
                </div>
                <span className={`
                  ml-2 text-sm font-medium
                  ${currentStep === item.step ? 'text-blue-600' : 'text-gray-500'}
                `}>
                  {item.label}
                </span>
                {index < 3 && (
                  <div className={`
                    w-8 h-0.5 mx-4
                    ${index < ['upload', 'configure', 'analyze', 'recommendations'].indexOf(currentStep)
                      ? 'bg-green-500'
                      : 'bg-gray-200'
                    }
                  `} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {currentStep === 'upload' && (
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Transform Your Space with AI
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Upload a photo of your room and get personalized furniture recommendations
              </p>
              <ImageUpload onUpload={handleImageUpload} isLoading={isLoading} />
            </div>
          )}

          {currentStep === 'configure' && room.imageUrl && (
            <RoomConfiguration
              imageUrl={room.imageUrl}
              onConfiguration={handleConfiguration}
              onBack={() => setCurrentStep('upload')}
            />
          )}

          {currentStep === 'analyze' && room.type && room.budget && room.style && (
            <RoomAnalysis
              room={room as Room}
              onAnalysisComplete={handleAnalysisComplete}
              onBack={() => setCurrentStep('configure')}
            />
          )}

          {currentStep === 'recommendations' && room.analysisResult && (
            <RecommendationDisplay
              room={room as Room}
              onStartOver={handleStartOver}
              onBack={() => setCurrentStep('analyze')}
            />
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
