// API endpoint for shopping list management and affiliate tracking

import { NextRequest, NextResponse } from 'next/server';
import { shoppingService } from '@/lib/shopping-service';
import { createSuccessResponse, createErrorResponse, validateRequired } from '@/utils/api-helpers';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'create-list':
        return handleCreateShoppingList(body);
      case 'track-click':
        return handleTrackAffiliateClick(body);
      case 'price-comparison':
        return handlePriceComparison(body);
      case 'export-csv':
        return handleExportCSV(body);
      case 'generate-share-link':
        return handleGenerateShareLink(body);
      default:
        return createErrorResponse('Invalid action', 400);
    }
  } catch (error) {
    console.error('Shopping API error:', error);
    return createErrorResponse(error as Error);
  }
}

async function handleCreateShoppingList(body: any) {
  const { recommendations, budget, roomId, name } = body;

  validateRequired(body, ['recommendations', 'budget']);

  if (!Array.isArray(recommendations)) {
    return createErrorResponse('Recommendations must be an array', 400);
  }

  const shoppingList = shoppingService.createShoppingList(
    recommendations,
    budget,
    roomId
  );

  if (name) {
    shoppingList.name = name;
  }

  // Generate affiliate URLs for all items
  shoppingList.items.forEach(item => {
    item.product.affiliateUrl = shoppingService.generateAffiliateUrl(item.product);
  });

  // Calculate statistics
  const stats = shoppingService.calculateShoppingStats(shoppingList);

  return createSuccessResponse({
    shoppingList,
    stats,
    shareableLink: shoppingService.generateShareableLink(shoppingList)
  });
}

async function handleTrackAffiliateClick(body: any) {
  const { productId, retailer, userId } = body;

  validateRequired(body, ['productId', 'retailer']);

  await shoppingService.trackAffiliateClick(productId, retailer, userId);

  return createSuccessResponse({
    message: 'Click tracked successfully',
    productId,
    retailer,
    timestamp: new Date().toISOString()
  });
}

async function handlePriceComparison(body: any) {
  const { productName, productId } = body;

  if (!productName && !productId) {
    return createErrorResponse('Product name or ID is required', 400);
  }

  const searchTerm = productName || productId;
  const priceComparison = await shoppingService.getPriceComparison(searchTerm);

  return createSuccessResponse({
    searchTerm,
    priceComparison,
    timestamp: new Date().toISOString()
  });
}

async function handleExportCSV(body: any) {
  const { shoppingList } = body;

  validateRequired(body, ['shoppingList']);

  const csvContent = shoppingService.exportShoppingListCSV(shoppingList);
  
  // Return CSV as downloadable response
  return new NextResponse(csvContent, {
    status: 200,
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="shopping-list-${shoppingList.id}.csv"`
    }
  });
}

async function handleGenerateShareLink(body: any) {
  const { shoppingList } = body;

  validateRequired(body, ['shoppingList']);

  const shareableLink = shoppingService.generateShareableLink(shoppingList);

  return createSuccessResponse({
    shareableLink,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
  });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'affiliate-config':
        return handleGetAffiliateConfig();
      case 'price-alerts':
        return handleGetPriceAlerts(searchParams);
      case 'stats':
        return handleGetShoppingStats(searchParams);
      default:
        return createErrorResponse('Invalid action parameter', 400);
    }
  } catch (error) {
    console.error('Shopping GET API error:', error);
    return createErrorResponse(error as Error);
  }
}

async function handleGetAffiliateConfig() {
  // Return public affiliate configuration (without sensitive keys)
  const config = {
    amazon: {
      tag: process.env.AMAZON_AFFILIATE_TAG || 'interiorai-20',
      hasApiAccess: !!(process.env.AMAZON_ACCESS_KEY && process.env.AMAZON_SECRET_KEY)
    },
    ikea: {
      partnerId: process.env.IKEA_PARTNER_ID || 'interior-ai',
      hasApiAccess: !!process.env.IKEA_API_KEY
    },
    supportedRetailers: ['amazon', 'ikea'],
    trackingEnabled: true
  };

  return createSuccessResponse(config);
}

async function handleGetPriceAlerts(searchParams: URLSearchParams) {
  // Mock implementation - in production, fetch from database
  const mockAlerts = [
    {
      productId: 'ikea-kivik-sofa',
      targetPrice: 500,
      currentPrice: 599,
      isActive: true,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
    },
    {
      productId: 'amazon-rivet-sofa',
      targetPrice: 800,
      currentPrice: 750,
      isActive: true,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
    }
  ];

  // Check for price updates
  const updatedAlerts = await shoppingService.checkPriceAlerts(mockAlerts);

  return createSuccessResponse({
    alerts: updatedAlerts,
    total: updatedAlerts.length,
    activeAlerts: updatedAlerts.filter(alert => alert.isActive).length
  });
}

async function handleGetShoppingStats(searchParams: URLSearchParams) {
  const period = searchParams.get('period') || '30d';
  
  // Mock shopping statistics - in production, fetch from analytics
  const stats = {
    period,
    totalClicks: Math.floor(Math.random() * 1000) + 100,
    totalConversions: Math.floor(Math.random() * 50) + 10,
    conversionRate: 0,
    totalCommission: Math.floor(Math.random() * 500) + 50,
    topProducts: [
      { productId: 'ikea-kivik-sofa', clicks: 45, conversions: 3 },
      { productId: 'amazon-rivet-sofa', clicks: 38, conversions: 2 },
      { productId: 'ikea-lack-coffee-table', clicks: 32, conversions: 4 }
    ],
    retailerBreakdown: {
      amazon: { clicks: 234, conversions: 12, commission: 156 },
      ikea: { clicks: 189, conversions: 8, commission: 89 }
    }
  };

  stats.conversionRate = (stats.totalConversions / stats.totalClicks) * 100;

  return createSuccessResponse(stats);
}

// Handle CORS for affiliate tracking
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
