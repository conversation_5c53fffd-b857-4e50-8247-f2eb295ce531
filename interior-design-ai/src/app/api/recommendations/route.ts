// API endpoint for generating furniture and decor recommendations

import { NextRequest, NextResponse } from 'next/server';
import { recommendationEngine } from '@/lib/recommendation-engine';
import { RoomAnalysis, RoomType, DesignStyle, FurnitureRecommendation } from '@/types';

interface RecommendationRequest {
  roomAnalysis: RoomAnalysis;
  roomType: RoomType;
  budget: number;
  style: DesignStyle;
  existingFurnitureIds?: string[];
  preferences?: {
    maxItems?: number;
    priorityCategories?: string[];
    excludeCategories?: string[];
    priceRange?: {
      min: number;
      max: number;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: RecommendationRequest = await request.json();
    
    const {
      roomAnalysis,
      roomType,
      budget,
      style,
      existingFurnitureIds = [],
      preferences = {}
    } = body;

    // Validate required fields
    if (!roomAnalysis || !roomType || !budget || !style) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: roomAnalysis, roomType, budget, style' 
        },
        { status: 400 }
      );
    }

    // Validate budget
    if (budget <= 0 || budget > 100000) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Budget must be between $1 and $100,000' 
        },
        { status: 400 }
      );
    }

    // Generate recommendations
    const recommendations = await recommendationEngine.generateRecommendations(
      roomAnalysis,
      roomType,
      budget,
      style,
      existingFurnitureIds
    );

    // Apply user preferences
    let filteredRecommendations = recommendations;

    // Filter by price range if specified
    if (preferences.priceRange) {
      filteredRecommendations = filteredRecommendations.filter(item => 
        item.price >= preferences.priceRange!.min && 
        item.price <= preferences.priceRange!.max
      );
    }

    // Exclude categories if specified
    if (preferences.excludeCategories?.length) {
      filteredRecommendations = filteredRecommendations.filter(item =>
        !preferences.excludeCategories!.includes(item.category)
      );
    }

    // Prioritize categories if specified
    if (preferences.priorityCategories?.length) {
      filteredRecommendations.sort((a, b) => {
        const aPriority = preferences.priorityCategories!.indexOf(a.category);
        const bPriority = preferences.priorityCategories!.indexOf(b.category);
        
        if (aPriority !== -1 && bPriority !== -1) {
          return aPriority - bPriority;
        }
        if (aPriority !== -1) return -1;
        if (bPriority !== -1) return 1;
        return 0;
      });
    }

    // Limit number of items if specified
    if (preferences.maxItems) {
      filteredRecommendations = filteredRecommendations.slice(0, preferences.maxItems);
    }

    // Calculate total cost and budget utilization
    const totalCost = filteredRecommendations.reduce((sum, item) => sum + item.price, 0);
    const budgetUtilization = (totalCost / budget) * 100;

    // Group recommendations by category
    const recommendationsByCategory = filteredRecommendations.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    }, {} as Record<string, FurnitureRecommendation[]>);

    // Generate shopping list with priorities
    const shoppingList = filteredRecommendations.map((item, index) => ({
      ...item,
      priority: index < 3 ? 'high' : index < 6 ? 'medium' : 'low',
      budgetImpact: (item.price / budget) * 100
    }));

    // Calculate style coherence score
    const styleCoherenceScore = calculateStyleCoherence(filteredRecommendations, style);

    // Generate alternative suggestions for different budgets
    const alternativeBudgets = [budget * 0.7, budget * 1.3];
    const alternatives = await Promise.all(
      alternativeBudgets.map(async (altBudget) => {
        const altRecommendations = await recommendationEngine.generateRecommendations(
          roomAnalysis,
          roomType,
          altBudget,
          style,
          existingFurnitureIds
        );
        return {
          budget: altBudget,
          itemCount: altRecommendations.length,
          totalCost: altRecommendations.reduce((sum, item) => sum + item.price, 0)
        };
      })
    );

    const response = {
      success: true,
      data: {
        recommendations: filteredRecommendations,
        summary: {
          totalItems: filteredRecommendations.length,
          totalCost,
          budget,
          budgetUtilization: Math.round(budgetUtilization * 100) / 100,
          remainingBudget: budget - totalCost,
          styleCoherenceScore: Math.round(styleCoherenceScore * 100) / 100
        },
        categorizedRecommendations: recommendationsByCategory,
        shoppingList,
        alternatives,
        metadata: {
          roomType,
          style,
          generatedAt: new Date().toISOString(),
          processingTime: Math.random() * 1000 + 500 // Mock processing time
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Recommendations error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate recommendations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Calculate how well the recommendations match the selected style
function calculateStyleCoherence(
  recommendations: FurnitureRecommendation[], 
  targetStyle: DesignStyle
): number {
  if (recommendations.length === 0) return 0;

  const styleMatches = recommendations.reduce((sum, item) => {
    if (item.style.includes(targetStyle)) {
      return sum + 1;
    }
    // Partial credit for compatible styles
    const compatibleStyles = ['modern', 'contemporary', 'minimalist'];
    if (compatibleStyles.includes(targetStyle) && 
        item.style.some(s => compatibleStyles.includes(s))) {
      return sum + 0.7;
    }
    return sum + 0.3; // Base score for any style
  }, 0);

  return (styleMatches / recommendations.length) * 100;
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const roomType = searchParams.get('roomType') as RoomType;
  const style = searchParams.get('style') as DesignStyle;
  const budget = searchParams.get('budget');

  if (!roomType || !style || !budget) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Missing required query parameters: roomType, style, budget' 
      },
      { status: 400 }
    );
  }

  try {
    // Return quick recommendations without room analysis
    const mockAnalysis: Partial<RoomAnalysis> = {
      layout: {
        doors: [],
        windows: [],
        walls: [],
        floorArea: 2000
      },
      existingFurniture: [],
      lightingSources: [],
      colorPalette: ['#FFFFFF', '#F5F5F5'],
      spatialFlow: {
        entryPoints: [],
        trafficPaths: [],
        functionalZones: []
      }
    };

    const recommendations = await recommendationEngine.generateRecommendations(
      mockAnalysis as RoomAnalysis,
      roomType,
      parseFloat(budget),
      style
    );

    return NextResponse.json({
      success: true,
      data: {
        recommendations: recommendations.slice(0, 6), // Limit for quick preview
        summary: {
          totalItems: recommendations.length,
          estimatedCost: recommendations.reduce((sum, item) => sum + item.price, 0)
        }
      }
    });

  } catch (error) {
    console.error('Quick recommendations error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate quick recommendations' 
      },
      { status: 500 }
    );
  }
}
