// API endpoint for product search and alternatives

import { NextRequest, NextResponse } from 'next/server';
import { ProductDatabase } from '@/lib/product-database';
import { StyleMatcher } from '@/lib/style-matcher';
import { FurnitureCategory, DesignStyle } from '@/types';
import { createSuccessResponse, createErrorResponse, validateRequired } from '@/utils/api-helpers';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'search':
        return handleProductSearch(searchParams);
      case 'similar':
        return handleSimilarProducts(searchParams);
      case 'alternatives':
        return handleStyleAlternatives(searchParams);
      case 'random':
        return handleRandomProducts(searchParams);
      default:
        return createErrorResponse('Invalid action parameter', 400);
    }
  } catch (error) {
    console.error('Products API error:', error);
    return createErrorResponse(error as Error);
  }
}

async function handleProductSearch(searchParams: URLSearchParams) {
  const category = searchParams.get('category') as FurnitureCategory | null;
  const style = searchParams.get('style') as DesignStyle | null;
  const minPrice = searchParams.get('minPrice');
  const maxPrice = searchParams.get('maxPrice');
  const retailer = searchParams.get('retailer') as 'amazon' | 'ikea' | null;
  const limit = parseInt(searchParams.get('limit') || '20');

  const priceRange = minPrice && maxPrice ? {
    min: parseFloat(minPrice),
    max: parseFloat(maxPrice)
  } : undefined;

  const products = ProductDatabase.searchProducts(
    category || undefined,
    style || undefined,
    priceRange,
    retailer || undefined
  ).slice(0, limit);

  return createSuccessResponse({
    products,
    total: products.length,
    filters: {
      category,
      style,
      priceRange,
      retailer
    }
  });
}

async function handleSimilarProducts(searchParams: URLSearchParams) {
  const productId = searchParams.get('productId');
  const limit = parseInt(searchParams.get('limit') || '5');

  if (!productId) {
    return createErrorResponse('Product ID is required', 400);
  }

  const product = ProductDatabase.getProductById(productId);
  if (!product) {
    return createErrorResponse('Product not found', 404);
  }

  const similarProducts = ProductDatabase.getSimilarProducts(productId, limit);

  return createSuccessResponse({
    originalProduct: product,
    similarProducts,
    total: similarProducts.length
  });
}

async function handleStyleAlternatives(searchParams: URLSearchParams) {
  const productId = searchParams.get('productId');
  const targetStyle = searchParams.get('targetStyle') as DesignStyle;
  const limit = parseInt(searchParams.get('limit') || '3');

  if (!productId || !targetStyle) {
    return createErrorResponse('Product ID and target style are required', 400);
  }

  const product = ProductDatabase.getProductById(productId);
  if (!product) {
    return createErrorResponse('Product not found', 404);
  }

  // Get all products for alternatives
  const allProducts = ProductDatabase.searchProducts();
  
  // Create a mock FurnitureRecommendation for the style matcher
  const mockRecommendation = {
    ...product,
    placement: { suggestedPosition: { x: 0, y: 0 }, zone: 'main' },
    compatibility: { roomType: [], existingFurniture: [], styleMatch: 0 }
  };

  const alternatives = StyleMatcher.generateStyleAlternatives(
    mockRecommendation,
    targetStyle,
    allProducts
  ).slice(0, limit);

  return createSuccessResponse({
    originalProduct: product,
    targetStyle,
    alternatives,
    total: alternatives.length
  });
}

async function handleRandomProducts(searchParams: URLSearchParams) {
  const count = parseInt(searchParams.get('count') || '10');
  const category = searchParams.get('category') as FurnitureCategory | null;

  let products = ProductDatabase.getRandomProducts(count * 2); // Get more to filter

  if (category) {
    products = products.filter(p => p.category === category);
  }

  return createSuccessResponse({
    products: products.slice(0, count),
    total: products.length
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'bulk-search':
        return handleBulkSearch(body);
      case 'style-analysis':
        return handleStyleAnalysis(body);
      default:
        return createErrorResponse('Invalid action', 400);
    }
  } catch (error) {
    console.error('Products POST API error:', error);
    return createErrorResponse(error as Error);
  }
}

async function handleBulkSearch(body: any) {
  const { searches } = body;

  if (!Array.isArray(searches)) {
    return createErrorResponse('Searches must be an array', 400);
  }

  const results = searches.map((search: any) => {
    const { category, style, priceRange, retailer, limit = 10 } = search;
    
    const products = ProductDatabase.searchProducts(
      category,
      style,
      priceRange,
      retailer
    ).slice(0, limit);

    return {
      query: search,
      products,
      total: products.length
    };
  });

  return createSuccessResponse({
    results,
    totalQueries: searches.length
  });
}

async function handleStyleAnalysis(body: any) {
  const { productIds, targetStyle } = body;

  validateRequired(body, ['productIds', 'targetStyle']);

  if (!Array.isArray(productIds)) {
    return createErrorResponse('Product IDs must be an array', 400);
  }

  const products = productIds
    .map((id: string) => ProductDatabase.getProductById(id))
    .filter(Boolean);

  if (products.length === 0) {
    return createErrorResponse('No valid products found', 404);
  }

  // Create mock recommendations for style analysis
  const mockRecommendations = products.map(product => ({
    ...product!,
    placement: { suggestedPosition: { x: 0, y: 0 }, zone: 'main' },
    compatibility: { roomType: [], existingFurniture: [], styleMatch: 0 }
  }));

  const styleAnalysis = StyleMatcher.scoreStyleCoherence(
    mockRecommendations,
    targetStyle
  );

  // Get style guide for the target style
  const styleGuide = StyleMatcher.getStyleGuide(targetStyle);

  return createSuccessResponse({
    products,
    targetStyle,
    styleAnalysis,
    styleGuide,
    recommendations: styleAnalysis.suggestions
  });
}

// Helper endpoint to get all available categories and styles
export async function OPTIONS() {
  const categories: FurnitureCategory[] = [
    'seating', 'tables', 'storage', 'lighting', 'decor', 'textiles', 'plants'
  ];

  const styles: DesignStyle[] = [
    'modern', 'minimalist', 'boho', 'industrial', 'scandinavian', 
    'traditional', 'contemporary', 'rustic'
  ];

  const retailers = ['amazon', 'ikea'];

  return createSuccessResponse({
    categories,
    styles,
    retailers,
    endpoints: {
      search: '/api/products?action=search&category=seating&style=modern',
      similar: '/api/products?action=similar&productId=ikea-kivik-sofa',
      alternatives: '/api/products?action=alternatives&productId=ikea-kivik-sofa&targetStyle=modern',
      random: '/api/products?action=random&count=10'
    }
  });
}
