// Health check endpoint for monitoring API status

import { NextResponse } from 'next/server';
import { existsSync } from 'fs';
import { join } from 'path';

export async function GET() {
  try {
    const startTime = Date.now();
    
    // Check upload directory
    const uploadDir = join(process.cwd(), 'public', 'uploads');
    const uploadDirExists = existsSync(uploadDir);
    
    // Check system resources (mock)
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    // Check AI models status (mock)
    const aiModelsStatus = {
      objectDetection: 'ready',
      roomSegmentation: 'ready',
      recommendationEngine: 'ready'
    };
    
    const responseTime = Date.now() - startTime;
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: Math.floor(uptime),
      responseTime,
      services: {
        api: 'operational',
        imageProcessing: 'operational',
        aiModels: aiModelsStatus,
        fileSystem: uploadDirExists ? 'operational' : 'warning'
      },
      system: {
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024)
        },
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      endpoints: {
        upload: '/api/upload',
        analyze: '/api/analyze',
        recommendations: '/api/recommendations',
        health: '/api/health'
      }
    };
    
    return NextResponse.json(healthData);
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        services: {
          api: 'error'
        }
      },
      { status: 500 }
    );
  }
}
