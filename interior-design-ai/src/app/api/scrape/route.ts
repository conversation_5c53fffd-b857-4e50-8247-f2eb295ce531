import { NextRequest, NextResponse } from 'next/server';
import { ServerScraper } from '@/lib/server-scraper';
import { DatabaseService } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, retailer, category, query, limit } = body;

    const serverScraper = new ServerScraper();

    switch (action) {
      case 'scrape_category':
        if (!retailer || !category) {
          return NextResponse.json(
            { error: 'Retailer and category are required for scraping' },
            { status: 400 }
          );
        }

        const result = await serverScraper.scrapeProducts(retailer, category, limit || 10);
        return NextResponse.json({
          success: result.success,
          productsScraped: result.productsScraped,
          message: result.message
        });

      case 'scrape_all':
        const categories = ['seating', 'tables', 'storage', 'lighting', 'decor'];
        const retailers = ['ikea', 'amazon', 'daraz'];
        const results = [];

        for (const ret of retailers) {
          for (const cat of categories) {
            const result = await serverScraper.scrapeProducts(ret, cat, 5);
            results.push({ retailer: ret, category: cat, ...result });
          }
        }

        return NextResponse.json({
          success: true,
          results,
          message: `Completed scraping all retailers for ${categories.length} categories`
        });

      case 'get_products':
        const database = new DatabaseService();
        const products = await database.searchProducts({
          category: category || undefined,
          retailer: retailer || undefined,
          limit: limit || 100,
          isAvailable: true
        });

        return NextResponse.json({
          success: true,
          products,
          count: products.length
        });

      case 'stats':
        const stats = await serverScraper.getScrapingStats();
        return NextResponse.json({
          success: true,
          stats
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: scrape_category, scrape_all, get_products, stats' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Scraping API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const category = searchParams.get('category');
    const retailer = searchParams.get('retailer');
    const limit = parseInt(searchParams.get('limit') || '20');

    const serverScraper = new ServerScraper();
    const database = new DatabaseService();

    switch (action) {
      case 'products':
        const products = await database.searchProducts({
          category: category || undefined,
          retailer: retailer || undefined,
          limit,
          isAvailable: true
        });

        return NextResponse.json({
          success: true,
          products,
          count: products.length
        });

      case 'stats':
        const stats = await serverScraper.getScrapingStats();
        return NextResponse.json({
          success: true,
          stats
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'Scraping API is running',
          endpoints: {
            'GET /api/scrape?action=products&category=seating': 'Get scraped products',
            'GET /api/scrape?action=stats': 'Get scraping statistics',
            'POST /api/scrape': 'Start scraping operations'
          }
        });
    }

  } catch (error) {
    console.error('Scraping API GET error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
