import { NextRequest, NextResponse } from 'next/server';
import { ScraperManager } from '@/lib/scrapers/scraper-manager';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, retailer, category, query, limit } = body;

    const scraperManager = new ScraperManager();

    switch (action) {
      case 'scrape_category':
        if (!retailer || !category) {
          return NextResponse.json(
            { error: 'Retailer and category are required for scraping' },
            { status: 400 }
          );
        }

        const job = await scraperManager.startScrapingJob(retailer, category, limit || 20);
        return NextResponse.json({
          success: true,
          job,
          message: `Started scraping ${retailer} - ${category}`
        });

      case 'scrape_all':
        const categories = ['seating', 'tables', 'storage', 'lighting', 'decor'];
        const jobs = await scraperManager.scrapeAllRetailers(categories);
        return NextResponse.json({
          success: true,
          jobs,
          message: `Started scraping all retailers for ${categories.length} categories`
        });

      case 'search':
        if (!query) {
          return NextResponse.json(
            { error: 'Query is required for search' },
            { status: 400 }
          );
        }

        const retailers = retailer ? [retailer] : ['ikea', 'amazon', 'daraz'];
        const searchResults = await scraperManager.searchProducts(query, retailers);
        
        return NextResponse.json({
          success: true,
          products: searchResults,
          count: searchResults.length,
          message: `Found ${searchResults.length} products for "${query}"`
        });

      case 'get_jobs':
        const allJobs = await scraperManager.getAllJobs();
        return NextResponse.json({
          success: true,
          jobs: allJobs
        });

      case 'get_products':
        const products = await scraperManager.getProductsFromDatabase(category, limit || 100);
        return NextResponse.json({
          success: true,
          products,
          count: products.length
        });

      case 'update_prices':
        await scraperManager.updateProductPrices(retailer);
        return NextResponse.json({
          success: true,
          message: `Started price update for ${retailer || 'all retailers'}`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: scrape_category, scrape_all, search, get_jobs, get_products, update_prices' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Scraping API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const category = searchParams.get('category');
    const retailer = searchParams.get('retailer');
    const limit = parseInt(searchParams.get('limit') || '20');

    const scraperManager = new ScraperManager();

    switch (action) {
      case 'products':
        const products = await scraperManager.getProductsFromDatabase(category || undefined, limit);
        return NextResponse.json({
          success: true,
          products,
          count: products.length
        });

      case 'jobs':
        const jobs = await scraperManager.getAllJobs();
        return NextResponse.json({
          success: true,
          jobs
        });

      case 'status':
        const jobId = searchParams.get('jobId');
        if (!jobId) {
          return NextResponse.json(
            { error: 'Job ID is required' },
            { status: 400 }
          );
        }

        const job = await scraperManager.getJobStatus(jobId);
        if (!job) {
          return NextResponse.json(
            { error: 'Job not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          job
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'Scraping API is running',
          endpoints: {
            'GET /api/scrape?action=products&category=seating': 'Get scraped products',
            'GET /api/scrape?action=jobs': 'Get all scraping jobs',
            'GET /api/scrape?action=status&jobId=xxx': 'Get job status',
            'POST /api/scrape': 'Start scraping operations'
          }
        });
    }

  } catch (error) {
    console.error('Scraping API GET error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
