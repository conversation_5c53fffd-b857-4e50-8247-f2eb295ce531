// API endpoint for image upload and initial processing

import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

const UPLOAD_DIR = join(process.cwd(), 'public', 'uploads');
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

// Ensure upload directory exists
async function ensureUploadDir() {
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true });
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const roomType = formData.get('roomType') as string;
    const budget = formData.get('budget') as string;
    const style = formData.get('style') as string;

    // Validate file
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: 'File too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Ensure upload directory exists
    await ensureUploadDir();

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `room_${timestamp}.jpg`;
    const filepath = join(UPLOAD_DIR, filename);

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Process image with Sharp
    const processedBuffer = await sharp(buffer)
      .resize(1024, 768, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ 
        quality: 85,
        progressive: true 
      })
      .toBuffer();

    // Save processed image
    await writeFile(filepath, processedBuffer);

    // Create thumbnail
    const thumbnailFilename = `thumb_${timestamp}.jpg`;
    const thumbnailPath = join(UPLOAD_DIR, thumbnailFilename);
    
    await sharp(buffer)
      .resize(300, 300, { 
        fit: 'cover',
        position: 'center' 
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    // Extract image metadata
    const metadata = await sharp(buffer).metadata();

    const response = {
      success: true,
      data: {
        id: timestamp.toString(),
        filename,
        thumbnailFilename,
        originalName: file.name,
        size: file.size,
        processedSize: processedBuffer.length,
        dimensions: {
          width: metadata.width,
          height: metadata.height
        },
        format: metadata.format,
        url: `/uploads/${filename}`,
        thumbnailUrl: `/uploads/${thumbnailFilename}`,
        roomType: roomType || null,
        budget: budget ? parseFloat(budget) : null,
        style: style || null,
        uploadedAt: new Date().toISOString()
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process upload',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false, 
      error: 'Method not allowed. Use POST to upload images.' 
    },
    { status: 405 }
  );
}
