'use client';

import React, { useState, useEffect } from 'react';
import { Play, RefreshCw, Database, Search, Download, Clock, CheckCircle, XCircle } from 'lucide-react';

interface ScrapingJob {
  id: string;
  retailer: string;
  category: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  productsScraped: number;
  error?: string;
}

interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  currency: string;
  retailer: string;
  imageUrl: string;
  scrapedAt?: string;
}

export default function ScrapingAdminPage() {
  const [jobs, setJobs] = useState<ScrapingJob[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRetailer, setSelectedRetailer] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  const retailers = ['ikea', 'amazon', 'daraz'];
  const categories = ['seating', 'tables', 'storage', 'lighting', 'decor', 'bedroom', 'electronics'];

  useEffect(() => {
    fetchJobs();
    fetchProducts();
  }, []);

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/scrape?action=jobs');
      const data = await response.json();
      if (data.success) {
        setJobs(data.jobs);
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/scrape?action=products&limit=50');
      const data = await response.json();
      if (data.success) {
        setProducts(data.products);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    }
  };

  const startScraping = async (retailer: string, category: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'scrape_category',
          retailer,
          category,
          limit: 20
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Started scraping ${retailer} - ${category}`);
        fetchJobs();
      } else {
        alert(`Failed to start scraping: ${data.error}`);
      }
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const scrapeAll = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'scrape_all'
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Started scraping all retailers`);
        fetchJobs();
      } else {
        alert(`Failed to start scraping: ${data.error}`);
      }
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const searchProducts = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'search',
          query: searchQuery,
          retailer: selectedRetailer || undefined
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Found ${data.count} products for "${searchQuery}"`);
        fetchProducts();
      } else {
        alert(`Search failed: ${data.error}`);
      }
    } catch (error) {
      alert(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Scraping Administration</h1>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Scraping Controls</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Retailer</label>
              <select
                value={selectedRetailer}
                onChange={(e) => setSelectedRetailer(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">All Retailers</option>
                {retailers.map(retailer => (
                  <option key={retailer} value={retailer}>{retailer.toUpperCase()}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search Query</label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="e.g., sofa, table, lamp"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => selectedRetailer && selectedCategory && startScraping(selectedRetailer, selectedCategory)}
              disabled={loading || !selectedRetailer || !selectedCategory}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <Play className="w-4 h-4" />
              <span>Scrape Category</span>
            </button>

            <button
              onClick={scrapeAll}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              <Database className="w-4 h-4" />
              <span>Scrape All</span>
            </button>

            <button
              onClick={searchProducts}
              disabled={loading || !searchQuery.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              <Search className="w-4 h-4" />
              <span>Search & Scrape</span>
            </button>

            <button
              onClick={() => { fetchJobs(); fetchProducts(); }}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Jobs Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Scraping Jobs</h2>
          
          {jobs.length === 0 ? (
            <p className="text-gray-500">No scraping jobs found</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Status</th>
                    <th className="text-left py-2">Retailer</th>
                    <th className="text-left py-2">Category</th>
                    <th className="text-left py-2">Products</th>
                    <th className="text-left py-2">Started</th>
                    <th className="text-left py-2">Completed</th>
                  </tr>
                </thead>
                <tbody>
                  {jobs.map(job => (
                    <tr key={job.id} className="border-b">
                      <td className="py-2">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(job.status)}
                          <span className="capitalize">{job.status}</span>
                        </div>
                      </td>
                      <td className="py-2 uppercase">{job.retailer}</td>
                      <td className="py-2 capitalize">{job.category}</td>
                      <td className="py-2">{job.productsScraped}</td>
                      <td className="py-2">
                        {job.startedAt ? new Date(job.startedAt).toLocaleString() : '-'}
                      </td>
                      <td className="py-2">
                        {job.completedAt ? new Date(job.completedAt).toLocaleString() : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Products */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Scraped Products ({products.length})</h2>
          
          {products.length === 0 ? (
            <p className="text-gray-500">No products found</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {products.slice(0, 20).map(product => (
                <div key={product.id} className="border border-gray-200 rounded-lg p-4">
                  <img
                    src={product.imageUrl}
                    alt={product.name}
                    className="w-full h-32 object-cover rounded-md mb-2"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop';
                    }}
                  />
                  <h3 className="font-medium text-sm mb-1 line-clamp-2">{product.name}</h3>
                  <p className="text-green-600 font-semibold">{product.currency} {product.price}</p>
                  <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                    <span className="uppercase">{product.retailer}</span>
                    <span className="capitalize">{product.category}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
