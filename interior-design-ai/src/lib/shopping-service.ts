// Shopping integration service with affiliate links and price tracking

import { FurnitureRecommendation } from '@/types';

export interface ShoppingListItem {
  id: string;
  product: FurnitureRecommendation;
  quantity: number;
  priority: 'high' | 'medium' | 'low';
  addedAt: Date;
  notes?: string;
  alternatives?: FurnitureRecommendation[];
}

export interface ShoppingList {
  id: string;
  name: string;
  items: ShoppingListItem[];
  totalCost: number;
  budget: number;
  createdAt: Date;
  updatedAt: Date;
  roomId?: string;
}

export interface PriceAlert {
  productId: string;
  targetPrice: number;
  currentPrice: number;
  isActive: boolean;
  createdAt: Date;
}

export interface AffiliateConfig {
  amazon: {
    tag: string;
    accessKey?: string;
    secretKey?: string;
  };
  ikea: {
    partnerId: string;
    apiKey?: string;
  };
}

export class ShoppingService {
  private affiliateConfig: AffiliateConfig;

  constructor() {
    this.affiliateConfig = {
      amazon: {
        tag: process.env.AMAZON_AFFILIATE_TAG || 'interiorai-20',
        accessKey: process.env.AMAZON_ACCESS_KEY,
        secretKey: process.env.AMAZON_SECRET_KEY
      },
      ikea: {
        partnerId: process.env.IKEA_PARTNER_ID || 'interior-ai',
        apiKey: process.env.IKEA_API_KEY
      }
    };
  }

  /**
   * Create a shopping list from recommendations
   */
  createShoppingList(
    recommendations: FurnitureRecommendation[],
    budget: number,
    roomId?: string
  ): ShoppingList {
    const items: ShoppingListItem[] = recommendations.map((product, index) => ({
      id: `item_${Date.now()}_${index}`,
      product,
      quantity: 1,
      priority: index < 3 ? 'high' : index < 6 ? 'medium' : 'low',
      addedAt: new Date(),
      alternatives: []
    }));

    const totalCost = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);

    return {
      id: `list_${Date.now()}`,
      name: `Room Design - ${new Date().toLocaleDateString()}`,
      items,
      totalCost,
      budget,
      createdAt: new Date(),
      updatedAt: new Date(),
      roomId
    };
  }

  /**
   * Generate affiliate URL for a product
   */
  generateAffiliateUrl(product: FurnitureRecommendation): string {
    try {
      if (product.retailer === 'amazon') {
        return this.generateAmazonAffiliateUrl(product.productUrl);
      } else if (product.retailer === 'ikea') {
        return this.generateIkeaAffiliateUrl(product.productUrl);
      }
      return product.productUrl;
    } catch (error) {
      console.error('Error generating affiliate URL:', error);
      return product.productUrl;
    }
  }

  /**
   * Generate Amazon affiliate URL
   */
  private generateAmazonAffiliateUrl(productUrl: string): string {
    try {
      const url = new URL(productUrl);
      
      // Add affiliate tag
      url.searchParams.set('tag', this.affiliateConfig.amazon.tag);
      
      // Add additional tracking parameters
      url.searchParams.set('linkCode', 'as2');
      url.searchParams.set('camp', '1789');
      url.searchParams.set('creative', '9325');
      
      return url.toString();
    } catch (error) {
      console.error('Error generating Amazon affiliate URL:', error);
      return productUrl;
    }
  }

  /**
   * Generate IKEA affiliate URL
   */
  private generateIkeaAffiliateUrl(productUrl: string): string {
    try {
      const url = new URL(productUrl);
      
      // IKEA uses different affiliate structure
      url.searchParams.set('partner', this.affiliateConfig.ikea.partnerId);
      url.searchParams.set('source', 'interior-ai');
      
      return url.toString();
    } catch (error) {
      console.error('Error generating IKEA affiliate URL:', error);
      return productUrl;
    }
  }

  /**
   * Track affiliate click
   */
  async trackAffiliateClick(
    productId: string,
    retailer: string,
    userId?: string
  ): Promise<void> {
    try {
      const clickData = {
        productId,
        retailer,
        userId: userId || 'anonymous',
        timestamp: new Date().toISOString(),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        referrer: typeof window !== 'undefined' ? window.document.referrer : ''
      };

      // In production, send to analytics service
      console.log('Affiliate click tracked:', clickData);
      
      // Could send to Google Analytics, Mixpanel, etc.
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'affiliate_click', {
          product_id: productId,
          retailer: retailer,
          value: 1
        });
      }
    } catch (error) {
      console.error('Error tracking affiliate click:', error);
    }
  }

  /**
   * Get price comparison across retailers
   */
  async getPriceComparison(productName: string): Promise<{
    amazon?: { price: number; url: string };
    ikea?: { price: number; url: string };
    others?: Array<{ retailer: string; price: number; url: string }>;
  }> {
    // Mock implementation - in production, integrate with price comparison APIs
    return {
      amazon: {
        price: Math.floor(Math.random() * 500) + 100,
        url: `https://amazon.com/search?k=${encodeURIComponent(productName)}`
      },
      ikea: {
        price: Math.floor(Math.random() * 400) + 80,
        url: `https://ikea.com/search?q=${encodeURIComponent(productName)}`
      }
    };
  }

  /**
   * Check for price drops and deals
   */
  async checkPriceAlerts(alerts: PriceAlert[]): Promise<PriceAlert[]> {
    const updatedAlerts: PriceAlert[] = [];

    for (const alert of alerts) {
      try {
        // Mock price check - in production, integrate with retailer APIs
        const currentPrice = await this.getCurrentPrice(alert.productId);
        
        const updatedAlert = {
          ...alert,
          currentPrice
        };

        if (currentPrice <= alert.targetPrice && alert.isActive) {
          // Price target reached - could send notification
          console.log(`Price alert triggered for product ${alert.productId}: $${currentPrice}`);
        }

        updatedAlerts.push(updatedAlert);
      } catch (error) {
        console.error(`Error checking price for product ${alert.productId}:`, error);
        updatedAlerts.push(alert);
      }
    }

    return updatedAlerts;
  }

  /**
   * Get current price for a product (mock implementation)
   */
  private async getCurrentPrice(productId: string): Promise<number> {
    // Mock implementation - in production, integrate with retailer APIs
    return Math.floor(Math.random() * 1000) + 50;
  }

  /**
   * Generate shopping list export (CSV format)
   */
  exportShoppingListCSV(shoppingList: ShoppingList): string {
    const headers = ['Item Name', 'Category', 'Price', 'Quantity', 'Total', 'Retailer', 'Priority', 'Product URL'];
    const rows = shoppingList.items.map(item => [
      item.product.name,
      item.product.category,
      `$${item.product.price}`,
      item.quantity.toString(),
      `$${item.product.price * item.quantity}`,
      item.product.retailer,
      item.priority,
      this.generateAffiliateUrl(item.product)
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  }

  /**
   * Calculate shopping list statistics
   */
  calculateShoppingStats(shoppingList: ShoppingList): {
    totalItems: number;
    totalCost: number;
    budgetUtilization: number;
    remainingBudget: number;
    categoryBreakdown: Record<string, { count: number; cost: number }>;
    retailerBreakdown: Record<string, { count: number; cost: number }>;
    priorityBreakdown: Record<string, { count: number; cost: number }>;
  } {
    const stats = {
      totalItems: shoppingList.items.length,
      totalCost: shoppingList.totalCost,
      budgetUtilization: (shoppingList.totalCost / shoppingList.budget) * 100,
      remainingBudget: shoppingList.budget - shoppingList.totalCost,
      categoryBreakdown: {} as Record<string, { count: number; cost: number }>,
      retailerBreakdown: {} as Record<string, { count: number; cost: number }>,
      priorityBreakdown: {} as Record<string, { count: number; cost: number }>
    };

    shoppingList.items.forEach(item => {
      const cost = item.product.price * item.quantity;

      // Category breakdown
      if (!stats.categoryBreakdown[item.product.category]) {
        stats.categoryBreakdown[item.product.category] = { count: 0, cost: 0 };
      }
      stats.categoryBreakdown[item.product.category].count += item.quantity;
      stats.categoryBreakdown[item.product.category].cost += cost;

      // Retailer breakdown
      if (!stats.retailerBreakdown[item.product.retailer]) {
        stats.retailerBreakdown[item.product.retailer] = { count: 0, cost: 0 };
      }
      stats.retailerBreakdown[item.product.retailer].count += item.quantity;
      stats.retailerBreakdown[item.product.retailer].cost += cost;

      // Priority breakdown
      if (!stats.priorityBreakdown[item.priority]) {
        stats.priorityBreakdown[item.priority] = { count: 0, cost: 0 };
      }
      stats.priorityBreakdown[item.priority].count += item.quantity;
      stats.priorityBreakdown[item.priority].cost += cost;
    });

    return stats;
  }

  /**
   * Generate shareable shopping list link
   */
  generateShareableLink(shoppingList: ShoppingList): string {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';
    const encodedList = btoa(JSON.stringify({
      id: shoppingList.id,
      name: shoppingList.name,
      items: shoppingList.items.map(item => ({
        productId: item.product.id,
        quantity: item.quantity,
        priority: item.priority
      }))
    }));
    
    return `${baseUrl}/shopping-list/shared?data=${encodedList}`;
  }
}

export const shoppingService = new ShoppingService();
