import { DatabaseService } from './database';

// Sample scraped data to demonstrate the functionality
const SAMPLE_SCRAPED_PRODUCTS = [
  // IKEA Products
  {
    externalId: 'ikea-kivik-sofa-001',
    name: 'KIVIK 3-seat sofa, Hillared beige',
    category: 'seating',
    subcategory: 'sofas',
    description: 'A generous seating series with a soft, deep seat and comfortable support for your back.',
    price: 599,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/kivik-3-seat-sofa-hillared-beige__0818588_pe774490_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',
    retailer: 'ikea',
    brand: 'IKEA',
    rating: 4.3,
    reviewCount: 1247,
    width: 228,
    height: 83,
    depth: 95,
    styles: ['modern', 'scandinavian', 'contemporary'],
    colors: ['beige', 'neutral'],
    materials: ['fabric', 'wood'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'ikea-ektorp-sofa-002',
    name: 'EKTORP 3-seat sofa, Totebo light beige',
    category: 'seating',
    subcategory: 'sofas',
    description: 'A timeless sofa with soft, generous proportions, deep seats and pocket springs that follow your body.',
    price: 449,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/ektorp-3-seat-sofa-totebo-light-beige__0818582_pe774484_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/ektorp-sofa-totebo-light-beige-s79318702/',
    retailer: 'ikea',
    brand: 'IKEA',
    rating: 4.1,
    reviewCount: 892,
    width: 218,
    height: 88,
    depth: 88,
    styles: ['traditional', 'classic', 'scandinavian'],
    colors: ['beige', 'light'],
    materials: ['fabric', 'wood'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'ikea-hemnes-table-003',
    name: 'HEMNES Coffee table, white stain',
    category: 'tables',
    subcategory: 'coffee_tables',
    description: 'Solid wood has a natural feel. A coffee table with drawers gives you extra storage space.',
    price: 179,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/hemnes-coffee-table-white-stain__0318329_pe515964_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-coffee-table-white-stain-80104294/',
    retailer: 'ikea',
    brand: 'IKEA',
    rating: 4.5,
    reviewCount: 634,
    width: 118,
    height: 46,
    depth: 75,
    styles: ['traditional', 'scandinavian', 'classic'],
    colors: ['white', 'light'],
    materials: ['wood', 'pine'],
    isAvailable: true,
    scrapedAt: new Date()
  },

  // Amazon Products
  {
    externalId: 'amazon-rivet-sofa-004',
    name: 'Rivet Revolve Modern Upholstered Sofa, 80"W, Storm',
    category: 'seating',
    subcategory: 'sofas',
    description: 'Mid-century modern style with clean lines and button tufting. Perfect for contemporary living spaces.',
    price: 899,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ?tag=youraffid-20',
    retailer: 'amazon',
    brand: 'Rivet',
    rating: 4.2,
    reviewCount: 1856,
    width: 203,
    height: 86,
    depth: 91,
    styles: ['modern', 'mid-century', 'contemporary'],
    colors: ['storm', 'gray'],
    materials: ['fabric', 'wood'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'amazon-samsung-tv-005',
    name: 'Samsung 55" Class 4K UHD Smart TV',
    category: 'electronics',
    subcategory: 'televisions',
    description: 'Crystal clear 4K display with smart TV features and HDR support for stunning picture quality.',
    price: 599,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=600&h=400&fit=crop',
    productUrl: 'https://www.amazon.com/dp/B08N5WRWNW?tag=youraffid-20',
    retailer: 'amazon',
    brand: 'Samsung',
    rating: 4.4,
    reviewCount: 3247,
    width: 123,
    height: 71,
    depth: 6,
    styles: ['modern', 'minimalist', 'contemporary'],
    colors: ['black'],
    materials: ['plastic', 'metal'],
    isAvailable: true,
    scrapedAt: new Date()
  },

  // Daraz Products
  {
    externalId: 'daraz-wooden-chair-006',
    name: 'Premium Wooden Dining Chair Set of 4',
    category: 'seating',
    subcategory: 'dining_chairs',
    description: 'Elegant wooden dining chairs with comfortable cushioned seats. Perfect for dining rooms.',
    price: 299,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=600&h=400&fit=crop',
    productUrl: 'https://www.daraz.pk/products/wooden-dining-chair-set-i123456789.html',
    retailer: 'daraz',
    brand: 'HomeStyle',
    rating: 4.0,
    reviewCount: 156,
    width: 45,
    height: 85,
    depth: 50,
    styles: ['traditional', 'classic', 'rustic'],
    colors: ['brown', 'wood'],
    materials: ['wood', 'fabric'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'daraz-storage-cabinet-007',
    name: 'Modern Storage Cabinet with Shelves',
    category: 'storage',
    subcategory: 'cabinets',
    description: 'Spacious storage cabinet with multiple shelves for organizing your belongings.',
    price: 199,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop',
    productUrl: 'https://www.daraz.pk/products/storage-cabinet-shelves-i987654321.html',
    retailer: 'daraz',
    brand: 'ModernHome',
    rating: 4.2,
    reviewCount: 89,
    width: 80,
    height: 120,
    depth: 40,
    styles: ['modern', 'minimalist', 'contemporary'],
    colors: ['white', 'light'],
    materials: ['wood', 'metal'],
    isAvailable: true,
    scrapedAt: new Date()
  },

  // More variety
  {
    externalId: 'ikea-lamp-008',
    name: 'FOTO Pendant lamp, white',
    category: 'lighting',
    subcategory: 'pendant_lights',
    description: 'Gives a soft mood light. The lamp shade in handmade paper gives a warm, cozy light.',
    price: 49,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=600&h=400&fit=crop',
    productUrl: 'https://www.ikea.com/us/en/p/foto-pendant-lamp-white-00394264/',
    retailer: 'ikea',
    brand: 'IKEA',
    rating: 4.6,
    reviewCount: 234,
    width: 25,
    height: 35,
    depth: 25,
    styles: ['scandinavian', 'minimalist', 'modern'],
    colors: ['white'],
    materials: ['paper', 'metal'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'amazon-bookshelf-009',
    name: 'VASAGLE 5-Tier Bookshelf, Industrial Style',
    category: 'storage',
    subcategory: 'bookshelves',
    description: 'Industrial-style bookshelf with 5 tiers for books, decorations, and storage.',
    price: 129,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop',
    productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ?tag=youraffid-20',
    retailer: 'amazon',
    brand: 'VASAGLE',
    rating: 4.3,
    reviewCount: 567,
    width: 70,
    height: 170,
    depth: 30,
    styles: ['industrial', 'modern', 'rustic'],
    colors: ['brown', 'black'],
    materials: ['wood', 'metal'],
    isAvailable: true,
    scrapedAt: new Date()
  },
  {
    externalId: 'daraz-mirror-010',
    name: 'Decorative Wall Mirror, Round 24"',
    category: 'decor',
    subcategory: 'mirrors',
    description: 'Beautiful round wall mirror with decorative frame. Perfect for living rooms and bedrooms.',
    price: 79,
    currency: 'USD',
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
    productUrl: 'https://www.daraz.pk/products/decorative-wall-mirror-i456789123.html',
    retailer: 'daraz',
    brand: 'DecorPlus',
    rating: 4.1,
    reviewCount: 78,
    width: 61,
    height: 61,
    depth: 3,
    styles: ['modern', 'decorative', 'contemporary'],
    colors: ['gold', 'metallic'],
    materials: ['glass', 'metal'],
    isAvailable: true,
    scrapedAt: new Date()
  }
];

export class ScrapedDataSeeder {
  private static database = new DatabaseService();

  static async seedDatabase(): Promise<void> {
    console.log('🌱 Starting to seed database with scraped product data...');

    try {
      let seededCount = 0;
      let skippedCount = 0;

      for (const productData of SAMPLE_SCRAPED_PRODUCTS) {
        try {
          // Check if product already exists
          const existingProduct = await this.database.getProductByExternalId(productData.externalId);
          
          if (existingProduct) {
            console.log(`⏭️  Skipping existing product: ${productData.name}`);
            skippedCount++;
            continue;
          }

          // Create new product
          await this.database.createProduct(productData);
          console.log(`✅ Seeded product: ${productData.name} (${productData.retailer})`);
          seededCount++;

        } catch (error) {
          console.error(`❌ Failed to seed product ${productData.name}:`, error);
        }
      }

      console.log(`\n🎉 Database seeding completed!`);
      console.log(`   ✅ Seeded: ${seededCount} products`);
      console.log(`   ⏭️  Skipped: ${skippedCount} existing products`);
      console.log(`   📊 Total: ${SAMPLE_SCRAPED_PRODUCTS.length} products processed`);

    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      throw error;
    }
  }

  static async getSeededStats(): Promise<{
    totalProducts: number;
    productsByRetailer: Record<string, number>;
    productsByCategory: Record<string, number>;
  }> {
    const stats = {
      totalProducts: SAMPLE_SCRAPED_PRODUCTS.length,
      productsByRetailer: {} as Record<string, number>,
      productsByCategory: {} as Record<string, number>
    };

    SAMPLE_SCRAPED_PRODUCTS.forEach(product => {
      // Count by retailer
      stats.productsByRetailer[product.retailer] = 
        (stats.productsByRetailer[product.retailer] || 0) + 1;

      // Count by category
      stats.productsByCategory[product.category] = 
        (stats.productsByCategory[product.category] || 0) + 1;
    });

    return stats;
  }
}
