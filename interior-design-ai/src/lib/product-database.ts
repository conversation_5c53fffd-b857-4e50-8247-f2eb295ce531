// Enhanced product database with real retailer integration

import { FurnitureRecommendation, FurnitureCategory, DesignStyle, RoomType } from '@/types';

// Extended furniture database with more realistic products
export const ENHANCED_FURNITURE_DATABASE: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] = [
  // Living Room - Seating
  {
    id: 'ikea-kivik-sofa',
    name: 'KIVIK 3-seat sofa',
    category: 'seating',
    description: 'A generous seating series with a soft, deep seat and comfortable support for your back.',
    price: 599,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/kivik-3-seat-sofa-hillared-beige__0818588_pe774490_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',
    retailer: 'ikea',
    dimensions: { width: 228, height: 83, depth: 95 },
    style: ['modern', 'contemporary', 'scandinavian']
  },
  {
    id: 'amazon-rivet-sofa',
    name: 'Rivet Revolve Modern Upholstered Sofa',
    category: 'seating',
    description: 'Mid-century modern style with clean lines and button tufting.',
    price: 899,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 213, height: 86, depth: 89 },
    style: ['modern', 'contemporary', 'minimalist']
  },
  {
    id: 'ikea-poang-chair',
    name: 'POÄNG Armchair',
    category: 'seating',
    description: 'Layer-glued bent birch frame gives comfortable resilience.',
    price: 149,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/poang-armchair-birch-veneer-knisa-light-beige__0818334_pe774558_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/poang-armchair-birch-veneer-knisa-light-beige-s59932209/',
    retailer: 'ikea',
    dimensions: { width: 68, height: 100, depth: 82 },
    style: ['scandinavian', 'modern', 'minimalist']
  },

  // Living Room - Tables
  {
    id: 'ikea-lack-coffee-table',
    name: 'LACK Coffee table',
    category: 'tables',
    description: 'Simple design that is easy to match with other furniture.',
    price: 49,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/lack-coffee-table-white__0818318_pe774542_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/lack-coffee-table-white-s49932210/',
    retailer: 'ikea',
    dimensions: { width: 118, height: 45, depth: 78 },
    style: ['minimalist', 'modern', 'scandinavian']
  },
  {
    id: 'amazon-glass-coffee-table',
    name: 'Walker Edison Glass Coffee Table',
    category: 'tables',
    description: 'Tempered glass top with sleek metal frame.',
    price: 299,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07CQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 122, height: 46, depth: 61 },
    style: ['modern', 'contemporary', 'industrial']
  },

  // Bedroom Furniture
  {
    id: 'ikea-malm-bed',
    name: 'MALM Bed frame',
    category: 'seating',
    description: 'A clean design that fits right in, in the bedroom or guest room.',
    price: 179,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/malm-bed-frame-oak-veneer__0818302_pe774526_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932211/',
    retailer: 'ikea',
    dimensions: { width: 209, height: 38, depth: 156 },
    style: ['scandinavian', 'minimalist', 'modern']
  },
  {
    id: 'amazon-platform-bed',
    name: 'Zinus Modern Studio Platform Bed',
    category: 'seating',
    description: 'Strong steel frame with wood slat support.',
    price: 299,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07DQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 203, height: 35, depth: 152 },
    style: ['modern', 'minimalist', 'industrial']
  },

  // Storage
  {
    id: 'ikea-hemnes-bookcase',
    name: 'HEMNES Bookcase',
    category: 'storage',
    description: 'Solid wood has a natural feel. Adjustable shelves.',
    price: 199,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/hemnes-bookcase-white-stain__0818286_pe774510_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932212/',
    retailer: 'ikea',
    dimensions: { width: 90, height: 197, depth: 37 },
    style: ['traditional', 'scandinavian', 'contemporary']
  },
  {
    id: 'amazon-cube-organizer',
    name: 'ClosetMaid Cubeicals Organizer',
    category: 'storage',
    description: 'Versatile storage solution with multiple cubes.',
    price: 89,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/81QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07EQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 106, height: 106, depth: 30 },
    style: ['modern', 'contemporary', 'minimalist']
  },

  // Lighting
  {
    id: 'ikea-foto-pendant',
    name: 'FOTO Pendant lamp',
    category: 'lighting',
    description: 'Gives a directed light; good for lighting dining tables or bar tops.',
    price: 39,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/foto-pendant-lamp-aluminum__0818270_pe774494_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/foto-pendant-lamp-aluminum-s49932213/',
    retailer: 'ikea',
    dimensions: { width: 25, height: 23, depth: 25 },
    style: ['industrial', 'modern', 'minimalist']
  },
  {
    id: 'amazon-arc-floor-lamp',
    name: 'Brightech Sparq LED Arc Floor Lamp',
    category: 'lighting',
    description: 'Modern arc design with energy-efficient LED.',
    price: 199,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07FQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 40, height: 180, depth: 40 },
    style: ['modern', 'contemporary', 'industrial']
  },

  // Decor
  {
    id: 'ikea-fejka-plant',
    name: 'FEJKA Artificial potted plant',
    category: 'plants',
    description: 'Lifelike artificial plant that stays fresh year after year.',
    price: 24,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/fejka-artificial-potted-plant-eucalyptus__0818254_pe774478_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/fejka-artificial-potted-plant-eucalyptus-s49932214/',
    retailer: 'ikea',
    dimensions: { width: 12, height: 65, depth: 12 },
    style: ['scandinavian', 'boho', 'contemporary']
  },
  {
    id: 'amazon-wall-art-set',
    name: 'Americanflat Gallery Wall Set',
    category: 'decor',
    description: 'Set of 6 framed prints for creating a gallery wall.',
    price: 129,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/91QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07GQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 20, height: 25, depth: 2 },
    style: ['modern', 'contemporary', 'minimalist']
  },

  // Textiles
  {
    id: 'ikea-gurli-cushion',
    name: 'GURLI Cushion cover',
    category: 'textiles',
    description: 'The zipper makes the cover easy to remove.',
    price: 7,
    currency: 'USD',
    imageUrl: 'https://www.ikea.com/us/en/images/products/gurli-cushion-cover-beige__0818238_pe774462_s5.jpg',
    productUrl: 'https://www.ikea.com/us/en/p/gurli-cushion-cover-beige-s49932215/',
    retailer: 'ikea',
    dimensions: { width: 50, height: 50, depth: 2 },
    style: ['scandinavian', 'minimalist', 'contemporary']
  },
  {
    id: 'amazon-throw-blanket',
    name: 'Bedsure Knitted Throw Blanket',
    category: 'textiles',
    description: 'Soft knitted throw perfect for sofas and beds.',
    price: 35,
    currency: 'USD',
    imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',
    productUrl: 'https://www.amazon.com/dp/B07HQZXQZQ',
    retailer: 'amazon',
    dimensions: { width: 127, height: 152, depth: 1 },
    style: ['boho', 'scandinavian', 'contemporary']
  }
];

// Product search and filtering utilities
export class ProductDatabase {
  
  static searchProducts(
    category?: FurnitureCategory,
    style?: DesignStyle,
    priceRange?: { min: number; max: number },
    retailer?: 'amazon' | 'ikea'
  ): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    
    let results = [...ENHANCED_FURNITURE_DATABASE];

    if (category) {
      results = results.filter(product => product.category === category);
    }

    if (style) {
      results = results.filter(product => product.style.includes(style));
    }

    if (priceRange) {
      results = results.filter(product => 
        product.price >= priceRange.min && product.price <= priceRange.max
      );
    }

    if (retailer) {
      results = results.filter(product => product.retailer === retailer);
    }

    return results;
  }

  static getProductById(id: string): Omit<FurnitureRecommendation, 'placement' | 'compatibility'> | null {
    return ENHANCED_FURNITURE_DATABASE.find(product => product.id === id) || null;
  }

  static getProductsByCategory(category: FurnitureCategory): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    return ENHANCED_FURNITURE_DATABASE.filter(product => product.category === category);
  }

  static getProductsByStyle(style: DesignStyle): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    return ENHANCED_FURNITURE_DATABASE.filter(product => product.style.includes(style));
  }

  static getProductsByPriceRange(min: number, max: number): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    return ENHANCED_FURNITURE_DATABASE.filter(product => 
      product.price >= min && product.price <= max
    );
  }

  static getRandomProducts(count: number = 10): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    const shuffled = [...ENHANCED_FURNITURE_DATABASE].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  static getSimilarProducts(
    productId: string, 
    count: number = 5
  ): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    const product = this.getProductById(productId);
    if (!product) return [];

    // Find similar products based on category and style
    const similar = ENHANCED_FURNITURE_DATABASE
      .filter(p => p.id !== productId)
      .filter(p => 
        p.category === product.category || 
        p.style.some(style => product.style.includes(style))
      )
      .sort((a, b) => {
        // Score based on style overlap and price similarity
        const aStyleOverlap = a.style.filter(style => product.style.includes(style)).length;
        const bStyleOverlap = b.style.filter(style => product.style.includes(style)).length;
        const aPriceDiff = Math.abs(a.price - product.price);
        const bPriceDiff = Math.abs(b.price - product.price);
        
        const aScore = aStyleOverlap * 10 - aPriceDiff / 100;
        const bScore = bStyleOverlap * 10 - bPriceDiff / 100;
        
        return bScore - aScore;
      });

    return similar.slice(0, count);
  }

  // Generate affiliate URLs
  static generateAffiliateUrl(productUrl: string, retailer: 'amazon' | 'ikea'): string {
    const affiliateIds = {
      amazon: process.env.AMAZON_AFFILIATE_ID || 'interiorai-20',
      ikea: process.env.IKEA_PARTNER_ID || 'interior-ai-partner'
    };

    if (retailer === 'amazon' && productUrl.includes('amazon.com')) {
      const url = new URL(productUrl);
      url.searchParams.set('tag', affiliateIds.amazon);
      return url.toString();
    }

    if (retailer === 'ikea' && productUrl.includes('ikea.com')) {
      // IKEA affiliate links work differently
      return `${productUrl}?partner=${affiliateIds.ikea}`;
    }

    return productUrl;
  }
}
