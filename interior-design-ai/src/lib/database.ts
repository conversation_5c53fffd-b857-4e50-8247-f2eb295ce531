// Database service using Prisma

import { PrismaClient } from '@prisma/client';
import { Room, FurnitureRecommendation, RoomType, DesignStyle } from '@/types';

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

export class DatabaseService {
  
  // Room Management
  static async createRoom(data: {
    name: string;
    type: RoomType;
    budget?: number;
    style?: DesignStyle;
    imageUrl?: string;
    thumbnailUrl?: string;
    userId?: string;
  }) {
    return await prisma.room.create({
      data: {
        ...data,
        analysisResult: null,
      },
    });
  }

  static async getRoomById(id: string) {
    return await prisma.room.findUnique({
      where: { id },
      include: {
        recommendations: {
          include: {
            product: true,
          },
        },
        shoppingLists: {
          include: {
            items: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });
  }

  static async updateRoomAnalysis(roomId: string, analysisResult: any) {
    return await prisma.room.update({
      where: { id: roomId },
      data: {
        analysisResult,
        updatedAt: new Date(),
      },
    });
  }

  static async getUserRooms(userId: string) {
    return await prisma.room.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
      include: {
        recommendations: {
          take: 3, // Preview of top recommendations
          include: {
            product: true,
          },
        },
      },
    });
  }

  // Product Management
  static async createProduct(data: {
    name: string;
    description?: string;
    category: string;
    price: number;
    currency?: string;
    imageUrl: string;
    productUrl: string;
    retailer: string;
    sku?: string;
    brand?: string;
    width?: number;
    height?: number;
    depth?: number;
    styles?: string[];
    colors?: string[];
    materials?: string[];
    tags?: string[];
  }) {
    return await prisma.product.create({
      data: {
        ...data,
        styles: data.styles ? JSON.stringify(data.styles) : null,
        colors: data.colors ? JSON.stringify(data.colors) : null,
        materials: data.materials ? JSON.stringify(data.materials) : null,
        tags: data.tags ? JSON.stringify(data.tags) : null,
      },
    });
  }

  static async getProductById(id: string) {
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        priceHistory: {
          orderBy: { recordedAt: 'desc' },
          take: 10,
        },
      },
    });

    if (product) {
      return {
        ...product,
        styles: product.styles ? JSON.parse(product.styles as string) : [],
        colors: product.colors ? JSON.parse(product.colors as string) : [],
        materials: product.materials ? JSON.parse(product.materials as string) : [],
        tags: product.tags ? JSON.parse(product.tags as string) : [],
      };
    }

    return null;
  }

  static async searchProducts(filters: {
    category?: string;
    retailer?: string;
    minPrice?: number;
    maxPrice?: number;
    styles?: string[];
    isAvailable?: boolean;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    if (filters.category) where.category = filters.category;
    if (filters.retailer) where.retailer = filters.retailer;
    if (filters.isAvailable !== undefined) where.isAvailable = filters.isAvailable;
    
    if (filters.minPrice || filters.maxPrice) {
      where.price = {};
      if (filters.minPrice) where.price.gte = filters.minPrice;
      if (filters.maxPrice) where.price.lte = filters.maxPrice;
    }

    const products = await prisma.product.findMany({
      where,
      take: filters.limit || 20,
      skip: filters.offset || 0,
      orderBy: { createdAt: 'desc' },
    });

    return products.map(product => ({
      ...product,
      styles: product.styles ? JSON.parse(product.styles as string) : [],
      colors: product.colors ? JSON.parse(product.colors as string) : [],
      materials: product.materials ? JSON.parse(product.materials as string) : [],
      tags: product.tags ? JSON.parse(product.tags as string) : [],
    }));
  }

  // Recommendations
  static async saveRecommendations(
    roomId: string,
    recommendations: Array<{
      productId: string;
      score: number;
      styleMatch: number;
      budgetMatch: number;
      spaceMatch: number;
      placement?: any;
      priority: string;
      rank: number;
    }>
  ) {
    // Delete existing recommendations for this room
    await prisma.recommendation.deleteMany({
      where: { roomId },
    });

    // Create new recommendations
    return await prisma.recommendation.createMany({
      data: recommendations.map(rec => ({
        ...rec,
        roomId,
        placement: rec.placement ? JSON.stringify(rec.placement) : null,
      })),
    });
  }

  static async getRoomRecommendations(roomId: string) {
    const recommendations = await prisma.recommendation.findMany({
      where: { roomId },
      include: {
        product: true,
      },
      orderBy: { rank: 'asc' },
    });

    return recommendations.map(rec => ({
      ...rec,
      placement: rec.placement ? JSON.parse(rec.placement as string) : null,
      product: {
        ...rec.product,
        styles: rec.product.styles ? JSON.parse(rec.product.styles as string) : [],
        colors: rec.product.colors ? JSON.parse(rec.product.colors as string) : [],
        materials: rec.product.materials ? JSON.parse(rec.product.materials as string) : [],
        tags: rec.product.tags ? JSON.parse(rec.product.tags as string) : [],
      },
    }));
  }

  // Shopping Lists
  static async createShoppingList(data: {
    name: string;
    budget?: number;
    userId?: string;
    roomId?: string;
    items: Array<{
      productId: string;
      quantity: number;
      priority: string;
      priceAtAdd: number;
      notes?: string;
    }>;
  }) {
    const totalCost = data.items.reduce((sum, item) => sum + (item.priceAtAdd * item.quantity), 0);

    return await prisma.shoppingList.create({
      data: {
        name: data.name,
        budget: data.budget,
        totalCost,
        userId: data.userId,
        roomId: data.roomId,
        items: {
          create: data.items,
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  }

  static async getShoppingList(id: string) {
    const shoppingList = await prisma.shoppingList.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        user: true,
        room: true,
      },
    });

    if (shoppingList) {
      return {
        ...shoppingList,
        items: shoppingList.items.map(item => ({
          ...item,
          product: {
            ...item.product,
            styles: item.product.styles ? JSON.parse(item.product.styles as string) : [],
            colors: item.product.colors ? JSON.parse(item.product.colors as string) : [],
            materials: item.product.materials ? JSON.parse(item.product.materials as string) : [],
            tags: item.product.tags ? JSON.parse(item.product.tags as string) : [],
          },
        })),
      };
    }

    return null;
  }

  // User Management
  static async createUser(data: {
    email: string;
    name?: string;
    avatar?: string;
  }) {
    return await prisma.user.create({
      data,
    });
  }

  static async getUserByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        preferences: true,
      },
    });
  }

  static async updateUserPreferences(userId: string, preferences: {
    preferredStyles?: string[];
    budgetMin?: number;
    budgetMax?: number;
    favoriteRetailers?: string[];
    colorPreferences?: string[];
    priceAlerts?: boolean;
    newProducts?: boolean;
    styleUpdates?: boolean;
  }) {
    return await prisma.userPreferences.upsert({
      where: { userId },
      update: {
        ...preferences,
        preferredStyles: preferences.preferredStyles ? JSON.stringify(preferences.preferredStyles) : undefined,
        favoriteRetailers: preferences.favoriteRetailers ? JSON.stringify(preferences.favoriteRetailers) : undefined,
        colorPreferences: preferences.colorPreferences ? JSON.stringify(preferences.colorPreferences) : undefined,
        updatedAt: new Date(),
      },
      create: {
        userId,
        ...preferences,
        preferredStyles: preferences.preferredStyles ? JSON.stringify(preferences.preferredStyles) : null,
        favoriteRetailers: preferences.favoriteRetailers ? JSON.stringify(preferences.favoriteRetailers) : null,
        colorPreferences: preferences.colorPreferences ? JSON.stringify(preferences.colorPreferences) : null,
      },
    });
  }

  // Analytics
  static async trackEvent(data: {
    eventType: string;
    eventData?: any;
    userId?: string;
    sessionId?: string;
    userAgent?: string;
    ipAddress?: string;
    productId?: string;
    roomId?: string;
  }) {
    return await prisma.analyticsEvent.create({
      data: {
        ...data,
        eventData: data.eventData ? JSON.stringify(data.eventData) : null,
      },
    });
  }

  // Price Tracking
  static async addPriceHistory(productId: string, price: number, source: string) {
    return await prisma.priceHistory.create({
      data: {
        productId,
        price,
        source,
      },
    });
  }

  static async createPriceAlert(data: {
    userId: string;
    productId: string;
    targetPrice: number;
    emailNotify?: boolean;
  }) {
    return await prisma.priceAlert.create({
      data,
    });
  }

  // Utility methods
  static async cleanup() {
    await prisma.$disconnect();
  }
}

export default DatabaseService;
