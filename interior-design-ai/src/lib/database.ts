// Database service using Prisma

import { PrismaClient } from '@prisma/client';
import { Room, FurnitureRecommendation, RoomType, DesignStyle } from '@/types';

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

export class DatabaseService {
  
  // Room Management
  static async createRoom(data: {
    name: string;
    type: RoomType;
    budget?: number;
    style?: DesignStyle;
    imageUrl?: string;
    thumbnailUrl?: string;
    userId?: string;
  }) {
    return await prisma.room.create({
      data: {
        ...data,
      },
    });
  }

  static async getRoomById(id: string) {
    return await prisma.room.findUnique({
      where: { id },
      include: {
        recommendations: {
          include: {
            product: true,
          },
        },
        shoppingLists: {
          include: {
            items: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });
  }

  static async updateRoomAnalysis(roomId: string, analysisResult: any) {
    return await prisma.room.update({
      where: { id: roomId },
      data: {
        analysisResult,
        updatedAt: new Date(),
      },
    });
  }

  static async getUserRooms(userId: string) {
    return await prisma.room.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
      include: {
        recommendations: {
          take: 3, // Preview of top recommendations
          include: {
            product: true,
          },
        },
      },
    });
  }

  // Product Management
  static async createProduct(data: {
    name: string;
    description?: string;
    category: string;
    price: number;
    currency?: string;
    imageUrl: string;
    productUrl: string;
    retailer: string;
    sku?: string;
    brand?: string;
    width?: number;
    height?: number;
    depth?: number;
    styles?: string[];
    colors?: string[];
    materials?: string[];
    tags?: string[];
  }) {
    return await prisma.product.create({
      data: {
        ...data,
        styles: data.styles || [],
        colors: data.colors || [],
        materials: data.materials || [],
        tags: data.tags || [],
      },
    });
  }

  static async getProductById(id: string) {
    return await prisma.product.findUnique({
      where: { id },
      include: {
        priceHistory: {
          orderBy: { recordedAt: 'desc' },
          take: 10,
        },
      },
    });
  }

  static async searchProducts(filters: {
    category?: string;
    retailer?: string;
    minPrice?: number;
    maxPrice?: number;
    styles?: string[];
    isAvailable?: boolean;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    if (filters.category) where.category = filters.category;
    if (filters.retailer) where.retailer = filters.retailer;
    if (filters.isAvailable !== undefined) where.isAvailable = filters.isAvailable;
    
    if (filters.minPrice || filters.maxPrice) {
      where.price = {};
      if (filters.minPrice) where.price.gte = filters.minPrice;
      if (filters.maxPrice) where.price.lte = filters.maxPrice;
    }

    if (filters.styles && filters.styles.length > 0) {
      where.styles = {
        hasSome: filters.styles,
      };
    }

    return await prisma.product.findMany({
      where,
      take: filters.limit || 20,
      skip: filters.offset || 0,
      orderBy: { createdAt: 'desc' },
    });
  }

  // Recommendations
  static async saveRecommendations(
    roomId: string,
    recommendations: Array<{
      productId: string;
      score: number;
      styleMatch: number;
      budgetMatch: number;
      spaceMatch: number;
      placement?: any;
      priority: string;
      rank: number;
    }>
  ) {
    // Delete existing recommendations for this room
    await prisma.recommendation.deleteMany({
      where: { roomId },
    });

    // Create new recommendations
    return await prisma.recommendation.createMany({
      data: recommendations.map(rec => ({
        ...rec,
        roomId,
        placement: rec.placement || undefined,
      })),
    });
  }

  static async getRoomRecommendations(roomId: string) {
    const recommendations = await prisma.recommendation.findMany({
      where: { roomId },
      include: {
        product: true,
      },
      orderBy: { rank: 'asc' },
    });

    return recommendations;
  }

  // Shopping Lists
  static async createShoppingList(data: {
    name: string;
    budget?: number;
    userId?: string;
    roomId?: string;
    items: Array<{
      productId: string;
      quantity: number;
      priority: string;
      priceAtAdd: number;
      notes?: string;
    }>;
  }) {
    const totalCost = data.items.reduce((sum, item) => sum + (item.priceAtAdd * item.quantity), 0);

    return await prisma.shoppingList.create({
      data: {
        name: data.name,
        budget: data.budget,
        totalCost,
        userId: data.userId,
        roomId: data.roomId,
        items: {
          create: data.items,
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  }

  static async getShoppingList(id: string) {
    const shoppingList = await prisma.shoppingList.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        user: true,
        room: true,
      },
    });

    if (shoppingList) {
      return {
        ...shoppingList,
        items: shoppingList.items,
      };
    }

    return null;
  }

  // User Management
  static async createUser(data: {
    email: string;
    name?: string;
    avatar?: string;
  }) {
    return await prisma.user.create({
      data,
    });
  }

  static async getUserByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        preferences: true,
      },
    });
  }

  static async updateUserPreferences(userId: string, preferences: {
    preferredStyles?: string[];
    budgetMin?: number;
    budgetMax?: number;
    favoriteRetailers?: string[];
    colorPreferences?: string[];
    priceAlerts?: boolean;
    newProducts?: boolean;
    styleUpdates?: boolean;
  }) {
    return await prisma.userPreferences.upsert({
      where: { userId },
      update: {
        ...preferences,
        updatedAt: new Date(),
      },
      create: {
        userId,
        ...preferences,
        preferredStyles: preferences.preferredStyles || [],
        favoriteRetailers: preferences.favoriteRetailers || [],
        colorPreferences: preferences.colorPreferences || [],
      },
    });
  }

  // Analytics
  static async trackEvent(data: {
    eventType: string;
    eventData?: any;
    userId?: string;
    sessionId?: string;
    userAgent?: string;
    ipAddress?: string;
    productId?: string;
    roomId?: string;
  }) {
    return await prisma.analyticsEvent.create({
      data: {
        ...data,
        eventData: data.eventData || undefined,
      },
    });
  }

  // Price Tracking
  static async addPriceHistory(productId: string, price: number, source: string) {
    return await prisma.priceHistory.create({
      data: {
        productId,
        price,
        source,
      },
    });
  }

  static async createPriceAlert(data: {
    userId: string;
    productId: string;
    targetPrice: number;
    emailNotify?: boolean;
  }) {
    return await prisma.priceAlert.create({
      data,
    });
  }

  // Utility methods
  static async cleanup() {
    await prisma.$disconnect();
  }
}

export default DatabaseService;
