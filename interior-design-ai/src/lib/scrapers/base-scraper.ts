import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import UserAgent from 'user-agents';

export interface ScrapedProduct {
  id: string;
  name: string;
  price: number;
  currency: string;
  imageUrl: string;
  productUrl: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  dimensions?: {
    width?: number;
    height?: number;
    depth?: number;
  };
  colors?: string[];
  materials?: string[];
  styles?: string[];
  rating?: number;
  reviewCount?: number;
  inStock: boolean;
  retailer: string;
  scrapedAt: Date;
}

export interface ScraperConfig {
  headless?: boolean;
  timeout?: number;
  retries?: number;
  delay?: number;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
}

export abstract class BaseScraper {
  protected browser: Browser | null = null;
  protected config: ScraperConfig;

  constructor(config: ScraperConfig = {}) {
    this.config = {
      headless: true,
      timeout: 30000,
      retries: 3,
      delay: 1000,
      userAgent: new UserAgent().toString(),
      viewport: { width: 1920, height: 1080 },
      ...config
    };
  }

  async initialize(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
    } catch (error) {
      console.error('Failed to initialize browser:', error);
      throw error;
    }
  }

  async createPage(): Promise<Page> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();
    
    // Set user agent and viewport
    await page.setUserAgent(this.config.userAgent!);
    await page.setViewport(this.config.viewport!);
    
    // Set timeout
    page.setDefaultTimeout(this.config.timeout!);
    
    // Block unnecessary resources to speed up scraping
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    return page;
  }

  async delay(ms: number = this.config.delay!): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async retryOperation<T>(
    operation: () => Promise<T>,
    retries: number = this.config.retries!
  ): Promise<T> {
    for (let i = 0; i < retries; i++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`Operation failed (attempt ${i + 1}/${retries}):`, error);
        if (i === retries - 1) throw error;
        await this.delay(1000 * (i + 1)); // Exponential backoff
      }
    }
    throw new Error('All retry attempts failed');
  }

  async safeGetText(page: Page, selector: string): Promise<string> {
    try {
      const element = await page.$(selector);
      if (!element) return '';
      return await page.evaluate(el => el.textContent?.trim() || '', element);
    } catch {
      return '';
    }
  }

  async safeGetAttribute(page: Page, selector: string, attribute: string): Promise<string> {
    try {
      const element = await page.$(selector);
      if (!element) return '';
      return await page.evaluate((el, attr) => el.getAttribute(attr) || '', element, attribute);
    } catch {
      return '';
    }
  }

  parsePrice(priceText: string): { price: number; currency: string } {
    // Remove common price formatting
    const cleanPrice = priceText.replace(/[^\d.,₹$€£¥]/g, '');
    
    // Extract currency
    let currency = 'USD';
    if (priceText.includes('₹')) currency = 'INR';
    else if (priceText.includes('€')) currency = 'EUR';
    else if (priceText.includes('£')) currency = 'GBP';
    else if (priceText.includes('¥')) currency = 'JPY';
    else if (priceText.includes('$')) currency = 'USD';

    // Parse price
    const price = parseFloat(cleanPrice.replace(/,/g, '')) || 0;
    
    return { price, currency };
  }

  generateProductId(name: string, retailer: string): string {
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const timestamp = Date.now().toString(36);
    return `${retailer}-${cleanName}-${timestamp}`;
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Abstract methods to be implemented by specific scrapers
  abstract scrapeProducts(category: string, limit?: number): Promise<ScrapedProduct[]>;
  abstract scrapeProduct(url: string): Promise<ScrapedProduct | null>;
  abstract searchProducts(query: string, limit?: number): Promise<ScrapedProduct[]>;
}

export class ScraperError extends Error {
  constructor(message: string, public retailer: string, public url?: string) {
    super(message);
    this.name = 'ScraperError';
  }
}
