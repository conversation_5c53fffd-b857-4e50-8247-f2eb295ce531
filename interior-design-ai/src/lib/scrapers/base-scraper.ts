import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import AdblockerPlugin from 'puppeteer-extra-plugin-adblocker';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import UserAgent from 'user-agents';

// Add stealth plugin to avoid detection
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

export interface ScrapedProduct {
  id: string;
  name: string;
  price: number;
  currency: string;
  imageUrl: string;
  productUrl: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  dimensions?: {
    width?: number;
    height?: number;
    depth?: number;
  };
  colors?: string[];
  materials?: string[];
  styles?: string[];
  rating?: number;
  reviewCount?: number;
  inStock: boolean;
  retailer: string;
  scrapedAt: Date;
}

export interface ScraperConfig {
  headless?: boolean;
  timeout?: number;
  retries?: number;
  delay?: number;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
}

export abstract class BaseScraper {
  protected browser: Browser | null = null;
  protected config: ScraperConfig;

  constructor(config: ScraperConfig = {}) {
    this.config = {
      headless: true,
      timeout: 30000,
      retries: 3,
      delay: 1000,
      userAgent: new UserAgent().toString(),
      viewport: { width: 1920, height: 1080 },
      ...config
    };
  }

  async initialize(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless ? 'new' : false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--exclude-switches=enable-automation',
          '--disable-extensions-except',
          '--disable-plugins-discovery',
          '--start-maximized',
          '--disable-default-apps',
          '--disable-sync',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection'
        ],
        ignoreDefaultArgs: ['--enable-automation', '--enable-blink-features=AutomationControlled'],
        ignoreHTTPSErrors: true,
        defaultViewport: null
      });
    } catch (error) {
      console.error('Failed to initialize browser:', error);
      throw error;
    }
  }

  async createPage(): Promise<Page> {
    if (!this.browser) {
      await this.initialize();
    }

    const page = await this.browser!.newPage();

    // Advanced anti-detection measures
    await this.setupAntiDetection(page);

    // Set user agent and viewport
    await page.setUserAgent(this.config.userAgent!);
    await page.setViewport(this.config.viewport!);

    // Set timeout
    page.setDefaultTimeout(this.config.timeout!);

    // Block unnecessary resources but allow images for product scraping
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    return page;
  }

  private async setupAntiDetection(page: Page): Promise<void> {
    // Remove webdriver property
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
    });

    // Mock plugins
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });
    });

    // Mock languages
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });
    });

    // Mock permissions
    await page.evaluateOnNewDocument(() => {
      const originalQuery = window.navigator.permissions.query;
      return window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );
    });

    // Add random mouse movements
    await page.evaluateOnNewDocument(() => {
      let mouseX = 0;
      let mouseY = 0;

      const moveRandomly = () => {
        mouseX += (Math.random() - 0.5) * 10;
        mouseY += (Math.random() - 0.5) * 10;

        const event = new MouseEvent('mousemove', {
          clientX: mouseX,
          clientY: mouseY
        });
        document.dispatchEvent(event);
      };

      setInterval(moveRandomly, 1000 + Math.random() * 2000);
    });
  }

  async delay(ms: number = this.config.delay!): Promise<void> {
    // Add random variation to make delays more human-like
    const variation = ms * 0.3; // 30% variation
    const randomDelay = ms + (Math.random() - 0.5) * variation;
    return new Promise(resolve => setTimeout(resolve, Math.max(500, randomDelay)));
  }

  async humanLikeDelay(): Promise<void> {
    // Random delay between 1-3 seconds
    const delay = 1000 + Math.random() * 2000;
    await this.delay(delay);
  }

  async scrollRandomly(page: Page): Promise<void> {
    // Simulate human scrolling behavior
    const scrollSteps = 3 + Math.floor(Math.random() * 5);
    for (let i = 0; i < scrollSteps; i++) {
      await page.evaluate(() => {
        window.scrollBy(0, 200 + Math.random() * 300);
      });
      await this.delay(500 + Math.random() * 1000);
    }
  }

  async retryOperation<T>(
    operation: () => Promise<T>,
    retries: number = this.config.retries!
  ): Promise<T> {
    for (let i = 0; i < retries; i++) {
      try {
        return await operation();
      } catch (error) {
        console.warn(`Operation failed (attempt ${i + 1}/${retries}):`, error);
        if (i === retries - 1) throw error;
        await this.delay(1000 * (i + 1)); // Exponential backoff
      }
    }
    throw new Error('All retry attempts failed');
  }

  async safeGetText(page: Page, selector: string): Promise<string> {
    try {
      const element = await page.$(selector);
      if (!element) return '';
      return await page.evaluate(el => el.textContent?.trim() || '', element);
    } catch {
      return '';
    }
  }

  async safeGetAttribute(page: Page, selector: string, attribute: string): Promise<string> {
    try {
      const element = await page.$(selector);
      if (!element) return '';
      return await page.evaluate((el, attr) => el.getAttribute(attr) || '', element, attribute);
    } catch {
      return '';
    }
  }

  parsePrice(priceText: string): { price: number; currency: string } {
    // Remove common price formatting
    const cleanPrice = priceText.replace(/[^\d.,₹$€£¥]/g, '');
    
    // Extract currency
    let currency = 'USD';
    if (priceText.includes('₹')) currency = 'INR';
    else if (priceText.includes('€')) currency = 'EUR';
    else if (priceText.includes('£')) currency = 'GBP';
    else if (priceText.includes('¥')) currency = 'JPY';
    else if (priceText.includes('$')) currency = 'USD';

    // Parse price
    const price = parseFloat(cleanPrice.replace(/,/g, '')) || 0;
    
    return { price, currency };
  }

  generateProductId(name: string, retailer: string): string {
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const timestamp = Date.now().toString(36);
    return `${retailer}-${cleanName}-${timestamp}`;
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Abstract methods to be implemented by specific scrapers
  abstract scrapeProducts(category: string, limit?: number): Promise<ScrapedProduct[]>;
  abstract scrapeProduct(url: string): Promise<ScrapedProduct | null>;
  abstract searchProducts(query: string, limit?: number): Promise<ScrapedProduct[]>;
}

export class ScraperError extends Error {
  constructor(message: string, public retailer: string, public url?: string) {
    super(message);
    this.name = 'ScraperError';
  }
}
