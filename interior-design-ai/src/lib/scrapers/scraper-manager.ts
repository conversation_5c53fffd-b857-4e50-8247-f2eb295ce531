import { IkeaScraper } from './ikea-scraper';
import { AmazonScraper } from './amazon-scraper';
import { DarazScraper } from './daraz-scraper';
import { ScrapedProduct, BaseScraper } from './base-scraper';
import { DatabaseService } from '../database';

export interface ScrapingJob {
  id: string;
  retailer: string;
  category: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  productsScraped: number;
  error?: string;
}

export class ScraperManager {
  private scrapers: Map<string, BaseScraper>;
  private database: DatabaseService;
  private activeJobs: Map<string, ScrapingJob>;

  constructor() {
    this.scrapers = new Map([
      ['ikea', new IkeaScraper()],
      ['amazon', new AmazonScraper()],
      ['daraz', new DarazScraper()]
    ]);
    this.database = new DatabaseService();
    this.activeJobs = new Map();
  }

  async scrapeAllRetailers(categories: string[] = ['seating', 'tables', 'storage', 'lighting', 'decor']): Promise<ScrapingJob[]> {
    const jobs: ScrapingJob[] = [];

    for (const retailer of this.scrapers.keys()) {
      for (const category of categories) {
        const job = await this.startScrapingJob(retailer, category);
        jobs.push(job);
      }
    }

    // Wait for all jobs to complete
    await Promise.allSettled(
      jobs.map(job => this.waitForJob(job.id))
    );

    return jobs;
  }

  async startScrapingJob(retailer: string, category: string, limit: number = 20): Promise<ScrapingJob> {
    const jobId = `${retailer}-${category}-${Date.now()}`;
    
    const job: ScrapingJob = {
      id: jobId,
      retailer,
      category,
      status: 'pending',
      productsScraped: 0
    };

    this.activeJobs.set(jobId, job);

    // Start scraping in background
    this.runScrapingJob(jobId, limit).catch(error => {
      console.error(`Scraping job ${jobId} failed:`, error);
      const failedJob = this.activeJobs.get(jobId);
      if (failedJob) {
        failedJob.status = 'failed';
        failedJob.error = error.message;
        failedJob.completedAt = new Date();
      }
    });

    return job;
  }

  private async runScrapingJob(jobId: string, limit: number): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) throw new Error(`Job ${jobId} not found`);

    const scraper = this.scrapers.get(job.retailer);
    if (!scraper) throw new Error(`Scraper for ${job.retailer} not found`);

    try {
      job.status = 'running';
      job.startedAt = new Date();

      console.log(`Starting scraping job: ${job.retailer} - ${job.category}`);

      // Scrape products
      const products = await scraper.scrapeProducts(job.category, limit);
      
      console.log(`Scraped ${products.length} products from ${job.retailer} - ${job.category}`);

      // Save to database
      for (const product of products) {
        try {
          await this.saveProductToDatabase(product);
          job.productsScraped++;
        } catch (error) {
          console.warn(`Failed to save product ${product.id}:`, error);
        }
      }

      job.status = 'completed';
      job.completedAt = new Date();

      console.log(`Completed scraping job: ${job.retailer} - ${job.category} (${job.productsScraped} products)`);

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();
      throw error;
    } finally {
      // Cleanup scraper resources
      await scraper.cleanup();
    }
  }

  private async saveProductToDatabase(product: ScrapedProduct): Promise<void> {
    try {
      // Check if product already exists
      const existingProduct = await this.database.getProductByExternalId(product.id);
      
      if (existingProduct) {
        // Update existing product
        await this.database.updateProduct(existingProduct.id, {
          name: product.name,
          price: product.price,
          currency: product.currency,
          imageUrl: product.imageUrl,
          productUrl: product.productUrl,
          description: product.description,
          inStock: product.inStock,
          updatedAt: new Date()
        });
      } else {
        // Create new product
        await this.database.createProduct({
          externalId: product.id,
          name: product.name,
          category: product.category,
          subcategory: product.subcategory,
          price: product.price,
          currency: product.currency,
          imageUrl: product.imageUrl,
          productUrl: product.productUrl,
          description: product.description,
          brand: product.brand,
          dimensions: product.dimensions ? JSON.stringify(product.dimensions) : null,
          colors: product.colors || [],
          materials: product.materials || [],
          styles: product.styles || [],
          rating: product.rating,
          reviewCount: product.reviewCount,
          inStock: product.inStock,
          retailer: product.retailer,
          scrapedAt: product.scrapedAt
        });
      }
    } catch (error) {
      console.error(`Failed to save product ${product.id} to database:`, error);
      throw error;
    }
  }

  async getJobStatus(jobId: string): Promise<ScrapingJob | null> {
    return this.activeJobs.get(jobId) || null;
  }

  async getAllJobs(): Promise<ScrapingJob[]> {
    return Array.from(this.activeJobs.values());
  }

  private async waitForJob(jobId: string, timeout: number = 300000): Promise<ScrapingJob> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const job = this.activeJobs.get(jobId);
      if (!job) throw new Error(`Job ${jobId} not found`);
      
      if (job.status === 'completed' || job.status === 'failed') {
        return job;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Job ${jobId} timed out`);
  }

  async searchProducts(query: string, retailers: string[] = ['ikea', 'amazon', 'daraz']): Promise<ScrapedProduct[]> {
    const allProducts: ScrapedProduct[] = [];

    for (const retailerName of retailers) {
      const scraper = this.scrapers.get(retailerName);
      if (!scraper) continue;

      try {
        console.log(`Searching ${retailerName} for: ${query}`);
        const products = await scraper.searchProducts(query, 10);
        allProducts.push(...products);
        
        // Save products to database
        for (const product of products) {
          try {
            await this.saveProductToDatabase(product);
          } catch (error) {
            console.warn(`Failed to save search result ${product.id}:`, error);
          }
        }
      } catch (error) {
        console.warn(`Search failed for ${retailerName}:`, error);
      } finally {
        await scraper.cleanup();
      }
    }

    return allProducts;
  }

  async getProductsFromDatabase(category?: string, limit: number = 100): Promise<any[]> {
    try {
      return await this.database.getProducts({
        category,
        limit,
        orderBy: 'createdAt',
        orderDirection: 'desc'
      });
    } catch (error) {
      console.error('Failed to get products from database:', error);
      return [];
    }
  }

  async updateProductPrices(retailer?: string): Promise<void> {
    try {
      const products = await this.database.getProducts({
        retailer,
        inStock: true,
        limit: 100
      });

      for (const product of products) {
        const scraper = this.scrapers.get(product.retailer);
        if (!scraper) continue;

        try {
          const updatedProduct = await scraper.scrapeProduct(product.productUrl);
          if (updatedProduct && updatedProduct.price !== product.price) {
            await this.database.updateProduct(product.id, {
              price: updatedProduct.price,
              inStock: updatedProduct.inStock,
              updatedAt: new Date()
            });
            
            console.log(`Updated price for ${product.name}: ${product.price} -> ${updatedProduct.price}`);
          }
        } catch (error) {
          console.warn(`Failed to update price for product ${product.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to update product prices:', error);
    }
  }

  async cleanup(): Promise<void> {
    for (const scraper of this.scrapers.values()) {
      await scraper.cleanup();
    }
  }
}
