import { BaseScraper, ScrapedProduct, ScraperError } from './base-scraper';
import { Page } from 'puppeteer';

export class AmazonScraper extends BaseScraper {
  private baseUrl = 'https://www.amazon.com';
  private affiliateTag = process.env.AMAZON_AFFILIATE_TAG || 'youraffid-20';

  async scrapeProducts(category: string, limit: number = 50): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      // Map categories to Amazon search terms
      const categoryQueries: Record<string, string> = {
        'seating': 'furniture chairs sofas',
        'tables': 'furniture tables coffee dining',
        'storage': 'furniture storage cabinets shelves',
        'lighting': 'home lighting lamps fixtures',
        'decor': 'home decor decorative accessories',
        'bedroom': 'bedroom furniture bed nightstand',
        'electronics': 'tv television smart 4k'
      };

      const query = categoryQueries[category.toLowerCase()] || category;
      const searchUrl = `${this.baseUrl}/s?k=${encodeURIComponent(query)}&rh=n%3A1055398`;

      await page.goto(searchUrl, { waitUntil: 'networkidle2' });
      
      // Handle potential captcha or bot detection
      await this.handleBotDetection(page);

      // Wait for search results
      await page.waitForSelector('[data-component-type="s-search-result"]', { timeout: 15000 });

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-component-type="s-search-result"] h2 a',
        (links) => links.map(link => 'https://www.amazon.com' + (link as HTMLAnchorElement).getAttribute('href')).slice(0, 50)
      );

      console.log(`Found ${productLinks.length} Amazon products for: ${query}`);

      // Scrape each product
      for (let i = 0; i < Math.min(productLinks.length, limit); i++) {
        try {
          const product = await this.scrapeProduct(productLinks[i]);
          if (product) {
            products.push(product);
            console.log(`Scraped Amazon product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);
          }
          await this.delay(1000); // Be more conservative with Amazon
        } catch (error) {
          console.warn(`Failed to scrape Amazon product: ${productLinks[i]}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to scrape Amazon category: ${category}`, 'amazon');
    } finally {
      await page.close();
    }

    return products;
  }

  async scrapeProduct(url: string): Promise<ScrapedProduct | null> {
    const page = await this.createPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      // Handle potential captcha or bot detection
      await this.handleBotDetection(page);

      // Wait for product details to load
      await page.waitForSelector('#productTitle', { timeout: 15000 });

      // Extract product information
      const name = await this.safeGetText(page, '#productTitle');
      const priceText = await this.safeGetText(page, '.a-price-current .a-offscreen, .a-price .a-offscreen');
      
      // Try multiple selectors for description
      let description = await this.safeGetText(page, '#feature-bullets ul');
      if (!description) {
        description = await this.safeGetText(page, '#productDescription');
      }
      if (!description) {
        description = await this.safeGetText(page, '[data-feature-name="productDescription"]');
      }

      // Get main product image
      let imageUrl = await this.safeGetAttribute(page, '#landingImage', 'src');
      if (!imageUrl) {
        imageUrl = await this.safeGetAttribute(page, '#imgBlkFront', 'src');
      }

      // Parse price
      const { price, currency } = this.parsePrice(priceText);

      // Extract brand
      const brand = await this.safeGetText(page, '#bylineInfo, .a-row .a-size-base');

      // Extract rating and review count
      const rating = await this.extractRating(page);
      const reviewCount = await this.extractReviewCount(page);

      // Check availability
      const inStock = await this.checkAvailability(page);

      // Extract dimensions from product details
      const dimensions = await this.extractDimensions(page);

      // Determine category
      const category = await this.extractCategory(page);

      // Add affiliate tag to URL
      const affiliateUrl = this.addAffiliateTag(url);

      if (!name || !price) {
        console.warn(`Incomplete Amazon product data for: ${url}`);
        return null;
      }

      const product: ScrapedProduct = {
        id: this.generateProductId(name, 'amazon'),
        name: name.trim(),
        price,
        currency,
        imageUrl: imageUrl || '',
        productUrl: affiliateUrl,
        description: description.trim().substring(0, 500), // Limit description length
        category,
        brand: brand.replace(/^by\s+/i, '').trim(),
        dimensions,
        rating,
        reviewCount,
        inStock,
        retailer: 'amazon',
        styles: ['modern', 'contemporary'], // Default styles for Amazon
        scrapedAt: new Date()
      };

      return product;

    } catch (error) {
      console.error(`Failed to scrape Amazon product: ${url}`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      const searchUrl = `${this.baseUrl}/s?k=${encodeURIComponent(query)}&rh=n%3A1055398`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      await this.handleBotDetection(page);
      await page.waitForSelector('[data-component-type="s-search-result"]', { timeout: 15000 });

      const productLinks = await page.$$eval(
        '[data-component-type="s-search-result"] h2 a',
        (links) => links.map(link => 'https://www.amazon.com' + (link as HTMLAnchorElement).getAttribute('href')).slice(0, limit)
      );

      for (const link of productLinks) {
        try {
          const product = await this.scrapeProduct(link);
          if (product) {
            products.push(product);
          }
          await this.delay(1000);
        } catch (error) {
          console.warn(`Failed to scrape Amazon search result: ${link}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to search Amazon products: ${query}`, 'amazon');
    } finally {
      await page.close();
    }

    return products;
  }

  private async handleBotDetection(page: Page): Promise<void> {
    try {
      // Check for captcha or bot detection
      const captcha = await page.$('form[action*="validateCaptcha"]');
      if (captcha) {
        console.warn('Amazon captcha detected, waiting...');
        await this.delay(5000);
      }

      // Check for "Sorry, we just need to make sure you're not a robot"
      const robotCheck = await page.$('input[id="captchacharacters"]');
      if (robotCheck) {
        console.warn('Amazon robot check detected');
        throw new ScraperError('Amazon robot check detected', 'amazon');
      }
    } catch (error) {
      // Continue if we can't handle bot detection
      console.warn('Bot detection handling failed:', error);
    }
  }

  private async extractRating(page: Page): Promise<number | undefined> {
    try {
      const ratingText = await this.safeGetText(page, '[data-hook="average-star-rating"] .a-icon-alt, .a-icon-alt');
      const match = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
      return match ? parseFloat(match[1]) : undefined;
    } catch {
      return undefined;
    }
  }

  private async extractReviewCount(page: Page): Promise<number | undefined> {
    try {
      const reviewText = await this.safeGetText(page, '[data-hook="total-review-count"], #acrCustomerReviewText');
      const match = reviewText.match(/(\d+(?:,\d+)*)/);
      return match ? parseInt(match[1].replace(/,/g, '')) : undefined;
    } catch {
      return undefined;
    }
  }

  private async checkAvailability(page: Page): Promise<boolean> {
    try {
      const availabilityText = await this.safeGetText(page, '#availability span, #outOfStock');
      return !availabilityText.toLowerCase().includes('out of stock') && 
             !availabilityText.toLowerCase().includes('unavailable');
    } catch {
      return true;
    }
  }

  private async extractDimensions(page: Page): Promise<{ width?: number; height?: number; depth?: number } | undefined> {
    try {
      // Look for dimensions in product details table
      const detailsText = await page.$$eval(
        '#productDetails_detailBullets_sections1 tr, #productDetails_techSpec_section_1 tr',
        (rows) => rows.map(row => row.textContent || '').join(' ')
      );

      const dimensions: any = {};
      
      // Parse dimension patterns
      const dimensionMatch = detailsText.match(/(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)/i);
      if (dimensionMatch) {
        dimensions.width = parseFloat(dimensionMatch[1]);
        dimensions.height = parseFloat(dimensionMatch[2]);
        dimensions.depth = parseFloat(dimensionMatch[3]);
      }

      return Object.keys(dimensions).length > 0 ? dimensions : undefined;
    } catch {
      return undefined;
    }
  }

  private async extractCategory(page: Page): Promise<string> {
    try {
      const breadcrumbs = await page.$$eval(
        '#wayfinding-breadcrumbs_feature_div a',
        (links) => links.map(link => link.textContent?.trim() || '')
      );

      // Map Amazon categories to our categories
      const categoryMap: Record<string, string> = {
        'furniture': 'furniture',
        'home': 'decor',
        'kitchen': 'kitchen',
        'lighting': 'lighting',
        'electronics': 'electronics',
        'tv': 'electronics'
      };

      for (const breadcrumb of breadcrumbs) {
        const lower = breadcrumb.toLowerCase();
        for (const [key, value] of Object.entries(categoryMap)) {
          if (lower.includes(key)) {
            return value;
          }
        }
      }

      return 'furniture'; // Default
    } catch {
      return 'furniture';
    }
  }

  private addAffiliateTag(url: string): string {
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.set('tag', this.affiliateTag);
      return urlObj.toString();
    } catch {
      return url;
    }
  }
}
