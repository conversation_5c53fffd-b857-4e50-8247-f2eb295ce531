import { BaseScraper, ScrapedProduct, ScraperError } from './base-scraper';
import { Page } from 'puppeteer';

export class DarazScraper extends BaseScraper {
  private baseUrl = 'https://www.daraz.pk';

  async scrapeProducts(category: string, limit: number = 50): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      // Map categories to Daraz category URLs
      const categoryUrls: Record<string, string> = {
        'seating': '/furniture-decor/living-room/sofas-chairs/',
        'tables': '/furniture-decor/living-room/tables/',
        'storage': '/furniture-decor/storage-organization/',
        'lighting': '/furniture-decor/lighting/',
        'decor': '/furniture-decor/home-decor/',
        'bedroom': '/furniture-decor/bedroom/',
        'kitchen': '/furniture-decor/kitchen-dining/'
      };

      const categoryUrl = categoryUrls[category.toLowerCase()];
      if (!categoryUrl) {
        throw new ScraperError(`Unknown category: ${category}`, 'daraz');
      }

      await page.goto(this.baseUrl + categoryUrl, { waitUntil: 'networkidle2' });
      
      // Wait for products to load
      await page.waitForSelector('[data-qa-locator="product-item"]', { timeout: 15000 });

      // Scroll to load more products
      await this.scrollToLoadProducts(page, limit);

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-qa-locator="product-item"] a',
        (links) => links.map(link => {
          const href = (link as HTMLAnchorElement).href;
          return href.startsWith('http') ? href : 'https://www.daraz.pk' + href;
        }).slice(0, 50)
      );

      console.log(`Found ${productLinks.length} Daraz products in category: ${category}`);

      // Scrape each product
      for (let i = 0; i < Math.min(productLinks.length, limit); i++) {
        try {
          const product = await this.scrapeProduct(productLinks[i]);
          if (product) {
            products.push(product);
            console.log(`Scraped Daraz product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);
          }
          await this.delay(800); // Be respectful to Daraz's servers
        } catch (error) {
          console.warn(`Failed to scrape Daraz product: ${productLinks[i]}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to scrape Daraz category: ${category}`, 'daraz', categoryUrls[category.toLowerCase()]);
    } finally {
      await page.close();
    }

    return products;
  }

  async scrapeProduct(url: string): Promise<ScrapedProduct | null> {
    const page = await this.createPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });

      // Wait for product details to load
      await page.waitForSelector('[data-qa-locator="product-detail-name"]', { timeout: 15000 });

      // Extract product information
      const name = await this.safeGetText(page, '[data-qa-locator="product-detail-name"], .pdp-product-name');
      const priceText = await this.safeGetText(page, '[data-qa-locator="product-price"], .pdp-price');
      
      // Try multiple selectors for description
      let description = await this.safeGetText(page, '[data-qa-locator="product-detail-desc"], .html-content');
      if (!description) {
        description = await this.safeGetText(page, '.product-description, .pdp-product-desc');
      }

      // Get main product image
      let imageUrl = await this.safeGetAttribute(page, '.gallery-preview-panel img, .pdp-gallery img', 'src');
      if (!imageUrl) {
        imageUrl = await this.safeGetAttribute(page, '.item-gallery img', 'src');
      }

      // Parse price (Daraz uses PKR)
      const { price, currency } = this.parsePrice(priceText);

      // Extract brand
      const brand = await this.safeGetText(page, '.pdp-product-brand, [data-qa-locator="product-brand"]');

      // Extract rating and review count
      const rating = await this.extractRating(page);
      const reviewCount = await this.extractReviewCount(page);

      // Check availability
      const inStock = await this.checkAvailability(page);

      // Extract specifications for dimensions and materials
      const specifications = await this.extractSpecifications(page);

      // Determine category from breadcrumbs
      const category = await this.extractCategory(page);

      if (!name || !price) {
        console.warn(`Incomplete Daraz product data for: ${url}`);
        return null;
      }

      const product: ScrapedProduct = {
        id: this.generateProductId(name, 'daraz'),
        name: name.trim(),
        price,
        currency: currency || 'PKR', // Default to PKR for Daraz
        imageUrl: imageUrl.startsWith('//') ? 'https:' + imageUrl : imageUrl,
        productUrl: url,
        description: description.trim().substring(0, 500),
        category,
        brand: brand.trim(),
        dimensions: specifications.dimensions,
        colors: specifications.colors,
        materials: specifications.materials,
        rating,
        reviewCount,
        inStock,
        retailer: 'daraz',
        styles: ['modern', 'contemporary'], // Default styles
        scrapedAt: new Date()
      };

      return product;

    } catch (error) {
      console.error(`Failed to scrape Daraz product: ${url}`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      const searchUrl = `${this.baseUrl}/catalog/?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Wait for search results
      await page.waitForSelector('[data-qa-locator="product-item"]', { timeout: 15000 });

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-qa-locator="product-item"] a',
        (links) => links.map(link => {
          const href = (link as HTMLAnchorElement).href;
          return href.startsWith('http') ? href : 'https://www.daraz.pk' + href;
        }).slice(0, limit)
      );

      // Scrape each product
      for (const link of productLinks) {
        try {
          const product = await this.scrapeProduct(link);
          if (product) {
            products.push(product);
          }
          await this.delay(800);
        } catch (error) {
          console.warn(`Failed to scrape Daraz search result: ${link}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to search Daraz products: ${query}`, 'daraz');
    } finally {
      await page.close();
    }

    return products;
  }

  private async scrollToLoadProducts(page: Page, targetCount: number): Promise<void> {
    let previousCount = 0;
    let currentCount = 0;
    let attempts = 0;
    const maxAttempts = 8;

    while (currentCount < targetCount && attempts < maxAttempts) {
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await this.delay(2000);

      currentCount = await page.$$eval(
        '[data-qa-locator="product-item"]',
        (items) => items.length
      );

      if (currentCount === previousCount) {
        attempts++;
      } else {
        attempts = 0;
      }

      previousCount = currentCount;
    }
  }

  private async extractRating(page: Page): Promise<number | undefined> {
    try {
      const ratingText = await this.safeGetText(page, '.score-average, .pdp-review-summary .score');
      const rating = parseFloat(ratingText);
      return isNaN(rating) ? undefined : rating;
    } catch {
      return undefined;
    }
  }

  private async extractReviewCount(page: Page): Promise<number | undefined> {
    try {
      const reviewText = await this.safeGetText(page, '.review-count, .pdp-review-summary .count');
      const match = reviewText.match(/(\d+)/);
      return match ? parseInt(match[1]) : undefined;
    } catch {
      return undefined;
    }
  }

  private async checkAvailability(page: Page): Promise<boolean> {
    try {
      const stockText = await this.safeGetText(page, '.inventory-status, .pdp-stock');
      return !stockText.toLowerCase().includes('out of stock') && 
             !stockText.toLowerCase().includes('unavailable');
    } catch {
      return true;
    }
  }

  private async extractSpecifications(page: Page): Promise<{
    dimensions?: { width?: number; height?: number; depth?: number };
    colors?: string[];
    materials?: string[];
  }> {
    try {
      const specs = await page.$$eval(
        '.specification-keys, .pdp-product-detail .key-title',
        (elements) => elements.map(el => ({
          key: el.textContent?.trim() || '',
          value: el.nextElementSibling?.textContent?.trim() || ''
        }))
      );

      const result: any = {};

      // Extract dimensions
      const dimensionSpec = specs.find(spec => 
        spec.key.toLowerCase().includes('dimension') || 
        spec.key.toLowerCase().includes('size')
      );
      
      if (dimensionSpec) {
        const dimensionMatch = dimensionSpec.value.match(/(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)\s*x\s*(\d+(?:\.\d+)?)/);
        if (dimensionMatch) {
          result.dimensions = {
            width: parseFloat(dimensionMatch[1]),
            height: parseFloat(dimensionMatch[2]),
            depth: parseFloat(dimensionMatch[3])
          };
        }
      }

      // Extract colors
      const colorSpec = specs.find(spec => spec.key.toLowerCase().includes('color'));
      if (colorSpec) {
        result.colors = [colorSpec.value];
      }

      // Extract materials
      const materialSpec = specs.find(spec => 
        spec.key.toLowerCase().includes('material') || 
        spec.key.toLowerCase().includes('fabric')
      );
      if (materialSpec) {
        result.materials = [materialSpec.value];
      }

      return result;
    } catch {
      return {};
    }
  }

  private async extractCategory(page: Page): Promise<string> {
    try {
      const breadcrumbs = await page.$$eval(
        '.breadcrumb a, .breadcrumb-item',
        (links) => links.map(link => link.textContent?.trim() || '')
      );

      // Map Daraz categories to our categories
      const categoryMap: Record<string, string> = {
        'furniture': 'furniture',
        'living room': 'seating',
        'bedroom': 'bedroom',
        'kitchen': 'kitchen',
        'lighting': 'lighting',
        'decor': 'decor',
        'storage': 'storage'
      };

      for (const breadcrumb of breadcrumbs) {
        const lower = breadcrumb.toLowerCase();
        for (const [key, value] of Object.entries(categoryMap)) {
          if (lower.includes(key)) {
            return value;
          }
        }
      }

      return 'furniture'; // Default
    } catch {
      return 'furniture';
    }
  }
}
