import { BaseScraper, ScrapedProduct, ScraperError } from './base-scraper';
import { Page } from 'puppeteer';

export class IkeaScraper extends BaseScraper {
  private baseUrl = 'https://www.ikea.com';
  private searchUrl = 'https://www.ikea.com/us/en/search/products/';

  constructor() {
    super({
      headless: true,
      timeout: 45000,
      retries: 5,
      delay: 2000,
      viewport: { width: 1366, height: 768 }
    });
  }

  async scrapeProducts(category: string, limit: number = 50): Promise<ScrapedProduct[]> {
    return this.retryOperation(async () => {
      const page = await this.createPage();
      const products: ScrapedProduct[] = [];

      try {
        // Map categories to IKEA category URLs
        const categoryUrls: Record<string, string> = {
          'seating': '/us/en/cat/chairs-fu003/',
          'sofas': '/us/en/cat/sofas-armchairs-fu003/',
          'tables': '/us/en/cat/tables-desks-fu004/',
          'storage': '/us/en/cat/storage-furniture-st002/',
          'lighting': '/us/en/cat/lighting-li001/',
          'decor': '/us/en/cat/decoration-de001/',
          'bedroom': '/us/en/cat/bedroom-furniture-bm003/',
          'kitchen': '/us/en/cat/kitchen-cabinets-fronts-ka001/'
        };

        const categoryUrl = categoryUrls[category.toLowerCase()];
        if (!categoryUrl) {
          throw new ScraperError(`Unknown category: ${category}`, 'ikea');
        }

        console.log(`🔍 Navigating to IKEA ${category} category...`);

        // Navigate with better error handling
        await page.goto(this.baseUrl + categoryUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // Add human-like delay
        await this.humanLikeDelay();

        // Handle potential cookie banners or popups
        await this.handlePopups(page);

        // Wait for products to load with multiple selectors
        const productListLoaded = await this.waitForProductList(page);
        if (!productListLoaded) {
          throw new ScraperError('Product list failed to load', 'ikea');
        }

        // Simulate human scrolling
        await this.scrollRandomly(page);
        await this.humanLikeDelay();

        // Extract product links with better error handling
        const productLinks = await this.extractProductLinks(page, limit);

        if (productLinks.length === 0) {
          console.warn('No product links found, trying alternative selectors...');
          return products;
        }

        console.log(`Found ${productLinks.length} IKEA products in category: ${category}`);

        // Scrape each product with better error handling
        for (let i = 0; i < Math.min(productLinks.length, limit); i++) {
          try {
            const product = await this.scrapeProduct(productLinks[i]);
            if (product) {
              products.push(product);
              console.log(`✅ Scraped IKEA product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);
            }

            // Human-like delay between requests
            await this.humanLikeDelay();

          } catch (error) {
            console.warn(`❌ Failed to scrape IKEA product: ${productLinks[i]}`, error);
            // Continue with next product instead of failing completely
          }
        }

      } catch (error) {
        console.error(`Failed to scrape IKEA category: ${category}`, error);
        throw error;
      } finally {
        await page.close();
      }

      return products;
    });
  }

  private async handlePopups(page: Page): Promise<void> {
    try {
      // Handle cookie consent
      const cookieButton = await page.$('[data-testid="cookie-consent-accept"]');
      if (cookieButton) {
        await cookieButton.click();
        await this.delay(1000);
      }

      // Handle location popup
      const locationButton = await page.$('[data-testid="location-selector-close"]');
      if (locationButton) {
        await locationButton.click();
        await this.delay(1000);
      }

      // Handle any other modal dialogs
      const modalClose = await page.$('[aria-label="Close"]');
      if (modalClose) {
        await modalClose.click();
        await this.delay(1000);
      }
    } catch (error) {
      // Ignore popup handling errors
      console.log('Popup handling completed (some may have failed)');
    }
  }

  private async waitForProductList(page: Page): Promise<boolean> {
    const selectors = [
      '[data-testid="plp-product-list"]',
      '.plp-fragment-wrapper',
      '.product-compact',
      '.serp-grid__item'
    ];

    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 15000 });
        console.log(`✅ Product list loaded with selector: ${selector}`);
        return true;
      } catch (error) {
        console.log(`⏭️  Selector ${selector} not found, trying next...`);
      }
    }

    return false;
  }

  private async extractProductLinks(page: Page, limit: number): Promise<string[]> {
    const linkSelectors = [
      '[data-testid="plp-product-list"] a[href*="/products/"]',
      '.plp-fragment-wrapper a[href*="/products/"]',
      '.product-compact a[href*="/products/"]',
      '.serp-grid__item a[href*="/products/"]'
    ];

    for (const selector of linkSelectors) {
      try {
        const links = await page.$$eval(
          selector,
          (elements) => elements.map(link => (link as HTMLAnchorElement).href)
        );

        if (links.length > 0) {
          console.log(`✅ Found ${links.length} product links with selector: ${selector}`);
          return links.slice(0, limit);
        }
      } catch (error) {
        console.log(`⏭️  Selector ${selector} failed, trying next...`);
      }
    }

    return [];
  }

  async scrapeProduct(url: string): Promise<ScrapedProduct | null> {
    return this.retryOperation(async () => {
      const page = await this.createPage();

      try {
        console.log(`🔍 Scraping IKEA product: ${url}`);

        await page.goto(url, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // Human-like delay
        await this.humanLikeDelay();

        // Handle popups
        await this.handlePopups(page);

        // Wait for product details with multiple selectors
        const productLoaded = await this.waitForProductDetails(page);
        if (!productLoaded) {
          console.warn(`Product details failed to load for: ${url}`);
          return null;
        }

        // Extract product information with fallbacks
        const name = await this.extractProductName(page);
        const priceText = await this.extractProductPrice(page);
        const description = await this.extractProductDescription(page);
        const imageUrl = await this.extractProductImage(page);

        // Parse price
        const { price, currency } = this.parsePrice(priceText);

        // Extract additional details
        const dimensions = await this.extractDimensions(page);
        const colors = await this.extractColors(page);
        const materials = await this.extractMaterials(page);
        const inStock = await this.checkStock(page);

        // Determine category
        const category = await this.extractCategory(page);

        if (!name || !price) {
          console.warn(`❌ Incomplete product data for: ${url} (name: ${!!name}, price: ${!!price})`);
          return null;
        }

        const product: ScrapedProduct = {
          id: this.generateProductId(name, 'ikea'),
          name: name.trim(),
          price,
          currency,
          imageUrl: imageUrl.startsWith('//') ? 'https:' + imageUrl : imageUrl,
          productUrl: url,
          description: description.trim(),
          category,
          brand: 'IKEA',
          dimensions,
          colors,
          materials,
          styles: ['scandinavian', 'modern', 'minimalist'],
          inStock,
          retailer: 'ikea',
          scrapedAt: new Date()
        };

        console.log(`✅ Successfully scraped: ${product.name} - $${product.price}`);
        return product;

      } catch (error) {
        console.error(`❌ Failed to scrape IKEA product: ${url}`, error);
        throw error;
      } finally {
        await page.close();
      }
    });
  }

  private async waitForProductDetails(page: Page): Promise<boolean> {
    const selectors = [
      '[data-testid="pip-product-name"]',
      '.pip-header-section h1',
      '.product-name',
      'h1'
    ];

    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 10000 });
        return true;
      } catch (error) {
        console.log(`⏭️  Product detail selector ${selector} not found, trying next...`);
      }
    }

    return false;
  }

  private async extractProductName(page: Page): Promise<string> {
    const selectors = [
      '[data-testid="pip-product-name"]',
      '.pip-header-section h1',
      '.product-name',
      'h1'
    ];

    for (const selector of selectors) {
      const name = await this.safeGetText(page, selector);
      if (name) return name;
    }

    return '';
  }

  private async extractProductPrice(page: Page): Promise<string> {
    const selectors = [
      '[data-testid="pip-price-current-price"]',
      '.pip-price__current',
      '.price-current',
      '.price'
    ];

    for (const selector of selectors) {
      const price = await this.safeGetText(page, selector);
      if (price) return price;
    }

    return '';
  }

  private async extractProductDescription(page: Page): Promise<string> {
    const selectors = [
      '[data-testid="pip-product-description"]',
      '.pip-product-summary',
      '.product-description',
      '.product-summary'
    ];

    for (const selector of selectors) {
      const description = await this.safeGetText(page, selector);
      if (description) return description;
    }

    return '';
  }

  private async extractProductImage(page: Page): Promise<string> {
    const selectors = [
      '[data-testid="pip-product-image"] img',
      '.pip-media img',
      '.product-image img',
      'img[alt*="product"]'
    ];

    for (const selector of selectors) {
      const imageUrl = await this.safeGetAttribute(page, selector, 'src');
      if (imageUrl) return imageUrl;
    }

    return '';
  }

  private async extractCategory(page: Page): Promise<string> {
    try {
      const breadcrumbs = await page.$$eval(
        '[data-testid="pip-breadcrumb"] a, .breadcrumb a',
        (links) => links.map(link => link.textContent?.trim() || '')
      );

      return this.mapIkeaCategory(breadcrumbs);
    } catch (error) {
      return 'furniture';
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      const searchUrl = `${this.searchUrl}?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Wait for search results
      await page.waitForSelector('[data-testid="plp-product-list"]', { timeout: 10000 });

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-testid="plp-product-list"] a[href*="/products/"]',
        (links) => links.map(link => (link as HTMLAnchorElement).href).slice(0, limit)
      );

      // Scrape each product
      for (const link of productLinks) {
        try {
          const product = await this.scrapeProduct(link);
          if (product) {
            products.push(product);
          }
          await this.delay(500);
        } catch (error) {
          console.warn(`Failed to scrape IKEA search result: ${link}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to search IKEA products: ${query}`, 'ikea', searchUrl);
    } finally {
      await page.close();
    }

    return products;
  }

  private async scrollToLoadProducts(page: Page, targetCount: number): Promise<void> {
    let previousCount = 0;
    let currentCount = 0;
    let attempts = 0;
    const maxAttempts = 10;

    while (currentCount < targetCount && attempts < maxAttempts) {
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await this.delay(2000);

      currentCount = await page.$$eval(
        '[data-testid="plp-product-list"] a[href*="/products/"]',
        (links) => links.length
      );

      if (currentCount === previousCount) {
        attempts++;
      } else {
        attempts = 0;
      }

      previousCount = currentCount;
    }
  }

  private async extractDimensions(page: Page): Promise<{ width?: number; height?: number; depth?: number } | undefined> {
    try {
      const dimensionText = await this.safeGetText(page, '[data-testid="pip-product-dimensions"]');
      if (!dimensionText) return undefined;

      const dimensions: any = {};
      
      // Parse common dimension formats
      const widthMatch = dimensionText.match(/width[:\s]*(\d+(?:\.\d+)?)/i);
      const heightMatch = dimensionText.match(/height[:\s]*(\d+(?:\.\d+)?)/i);
      const depthMatch = dimensionText.match(/depth[:\s]*(\d+(?:\.\d+)?)/i);

      if (widthMatch) dimensions.width = parseFloat(widthMatch[1]);
      if (heightMatch) dimensions.height = parseFloat(heightMatch[1]);
      if (depthMatch) dimensions.depth = parseFloat(depthMatch[1]);

      return Object.keys(dimensions).length > 0 ? dimensions : undefined;
    } catch {
      return undefined;
    }
  }

  private async extractColors(page: Page): Promise<string[]> {
    try {
      const colors = await page.$$eval(
        '[data-testid="pip-product-color-options"] button',
        (buttons) => buttons.map(btn => btn.getAttribute('aria-label') || '').filter(Boolean)
      );
      return colors;
    } catch {
      return [];
    }
  }

  private async extractMaterials(page: Page): Promise<string[]> {
    try {
      const materialsText = await this.safeGetText(page, '[data-testid="pip-product-materials"]');
      if (!materialsText) return [];

      // Extract common materials
      const materials = ['wood', 'metal', 'plastic', 'fabric', 'glass', 'leather'];
      return materials.filter(material => 
        materialsText.toLowerCase().includes(material)
      );
    } catch {
      return [];
    }
  }

  private async checkStock(page: Page): Promise<boolean> {
    try {
      const stockText = await this.safeGetText(page, '[data-testid="pip-product-availability"]');
      return !stockText.toLowerCase().includes('out of stock');
    } catch {
      return true; // Assume in stock if we can't determine
    }
  }

  private mapIkeaCategory(breadcrumbs: string[]): string {
    const categoryMap: Record<string, string> = {
      'chairs': 'seating',
      'sofas': 'seating',
      'armchairs': 'seating',
      'tables': 'tables',
      'desks': 'tables',
      'storage': 'storage',
      'lighting': 'lighting',
      'decoration': 'decor',
      'bedroom': 'bedroom',
      'kitchen': 'kitchen'
    };

    for (const breadcrumb of breadcrumbs) {
      const lower = breadcrumb.toLowerCase();
      for (const [key, value] of Object.entries(categoryMap)) {
        if (lower.includes(key)) {
          return value;
        }
      }
    }

    return 'furniture'; // Default category
  }
}
