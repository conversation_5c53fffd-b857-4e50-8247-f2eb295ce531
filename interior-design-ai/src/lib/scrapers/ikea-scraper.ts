import { BaseScraper, ScrapedProduct, ScraperError } from './base-scraper';
import { Page } from 'puppeteer';

export class IkeaScraper extends BaseScraper {
  private baseUrl = 'https://www.ikea.com';
  private searchUrl = 'https://www.ikea.com/us/en/search/products/';

  async scrapeProducts(category: string, limit: number = 50): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      // Map categories to IKEA category URLs
      const categoryUrls: Record<string, string> = {
        'seating': '/us/en/cat/chairs-fu003/',
        'sofas': '/us/en/cat/sofas-armchairs-fu003/',
        'tables': '/us/en/cat/tables-desks-fu004/',
        'storage': '/us/en/cat/storage-furniture-st002/',
        'lighting': '/us/en/cat/lighting-li001/',
        'decor': '/us/en/cat/decoration-de001/',
        'bedroom': '/us/en/cat/bedroom-furniture-bm003/',
        'kitchen': '/us/en/cat/kitchen-cabinets-fronts-ka001/'
      };

      const categoryUrl = categoryUrls[category.toLowerCase()];
      if (!categoryUrl) {
        throw new ScraperError(`Unknown category: ${category}`, 'ikea');
      }

      await page.goto(this.baseUrl + categoryUrl, { waitUntil: 'networkidle2' });
      
      // Wait for products to load
      await page.waitForSelector('[data-testid="plp-product-list"]', { timeout: 10000 });

      // Scroll to load more products
      await this.scrollToLoadProducts(page, limit);

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-testid="plp-product-list"] a[href*="/products/"]',
        (links) => links.map(link => (link as HTMLAnchorElement).href).slice(0, 50)
      );

      console.log(`Found ${productLinks.length} IKEA products in category: ${category}`);

      // Scrape each product
      for (let i = 0; i < Math.min(productLinks.length, limit); i++) {
        try {
          const product = await this.scrapeProduct(productLinks[i]);
          if (product) {
            products.push(product);
            console.log(`Scraped IKEA product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);
          }
          await this.delay(500); // Be respectful to IKEA's servers
        } catch (error) {
          console.warn(`Failed to scrape IKEA product: ${productLinks[i]}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to scrape IKEA category: ${category}`, 'ikea', categoryUrls[category.toLowerCase()]);
    } finally {
      await page.close();
    }

    return products;
  }

  async scrapeProduct(url: string): Promise<ScrapedProduct | null> {
    const page = await this.createPage();

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });

      // Wait for product details to load
      await page.waitForSelector('[data-testid="pip-product-name"]', { timeout: 10000 });

      // Extract product information
      const name = await this.safeGetText(page, '[data-testid="pip-product-name"]');
      const priceText = await this.safeGetText(page, '[data-testid="pip-price-current-price"]');
      const description = await this.safeGetText(page, '[data-testid="pip-product-description"]');
      
      // Get main product image
      const imageUrl = await this.safeGetAttribute(page, '[data-testid="pip-product-image"] img', 'src');
      
      // Parse price
      const { price, currency } = this.parsePrice(priceText);

      // Extract dimensions if available
      const dimensions = await this.extractDimensions(page);

      // Extract colors and materials
      const colors = await this.extractColors(page);
      const materials = await this.extractMaterials(page);

      // Check stock status
      const inStock = await this.checkStock(page);

      // Determine category and subcategory
      const breadcrumbs = await page.$$eval(
        '[data-testid="pip-breadcrumb"] a',
        (links) => links.map(link => link.textContent?.trim() || '')
      );

      const category = this.mapIkeaCategory(breadcrumbs);

      if (!name || !price) {
        console.warn(`Incomplete product data for: ${url}`);
        return null;
      }

      const product: ScrapedProduct = {
        id: this.generateProductId(name, 'ikea'),
        name: name.trim(),
        price,
        currency,
        imageUrl: imageUrl.startsWith('//') ? 'https:' + imageUrl : imageUrl,
        productUrl: url,
        description: description.trim(),
        category,
        brand: 'IKEA',
        dimensions,
        colors,
        materials,
        styles: ['scandinavian', 'modern', 'minimalist'],
        inStock,
        retailer: 'ikea',
        scrapedAt: new Date()
      };

      return product;

    } catch (error) {
      console.error(`Failed to scrape IKEA product: ${url}`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ScrapedProduct[]> {
    const page = await this.createPage();
    const products: ScrapedProduct[] = [];

    try {
      const searchUrl = `${this.searchUrl}?q=${encodeURIComponent(query)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Wait for search results
      await page.waitForSelector('[data-testid="plp-product-list"]', { timeout: 10000 });

      // Extract product links
      const productLinks = await page.$$eval(
        '[data-testid="plp-product-list"] a[href*="/products/"]',
        (links) => links.map(link => (link as HTMLAnchorElement).href).slice(0, limit)
      );

      // Scrape each product
      for (const link of productLinks) {
        try {
          const product = await this.scrapeProduct(link);
          if (product) {
            products.push(product);
          }
          await this.delay(500);
        } catch (error) {
          console.warn(`Failed to scrape IKEA search result: ${link}`, error);
        }
      }

    } catch (error) {
      throw new ScraperError(`Failed to search IKEA products: ${query}`, 'ikea', searchUrl);
    } finally {
      await page.close();
    }

    return products;
  }

  private async scrollToLoadProducts(page: Page, targetCount: number): Promise<void> {
    let previousCount = 0;
    let currentCount = 0;
    let attempts = 0;
    const maxAttempts = 10;

    while (currentCount < targetCount && attempts < maxAttempts) {
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await this.delay(2000);

      currentCount = await page.$$eval(
        '[data-testid="plp-product-list"] a[href*="/products/"]',
        (links) => links.length
      );

      if (currentCount === previousCount) {
        attempts++;
      } else {
        attempts = 0;
      }

      previousCount = currentCount;
    }
  }

  private async extractDimensions(page: Page): Promise<{ width?: number; height?: number; depth?: number } | undefined> {
    try {
      const dimensionText = await this.safeGetText(page, '[data-testid="pip-product-dimensions"]');
      if (!dimensionText) return undefined;

      const dimensions: any = {};
      
      // Parse common dimension formats
      const widthMatch = dimensionText.match(/width[:\s]*(\d+(?:\.\d+)?)/i);
      const heightMatch = dimensionText.match(/height[:\s]*(\d+(?:\.\d+)?)/i);
      const depthMatch = dimensionText.match(/depth[:\s]*(\d+(?:\.\d+)?)/i);

      if (widthMatch) dimensions.width = parseFloat(widthMatch[1]);
      if (heightMatch) dimensions.height = parseFloat(heightMatch[1]);
      if (depthMatch) dimensions.depth = parseFloat(depthMatch[1]);

      return Object.keys(dimensions).length > 0 ? dimensions : undefined;
    } catch {
      return undefined;
    }
  }

  private async extractColors(page: Page): Promise<string[]> {
    try {
      const colors = await page.$$eval(
        '[data-testid="pip-product-color-options"] button',
        (buttons) => buttons.map(btn => btn.getAttribute('aria-label') || '').filter(Boolean)
      );
      return colors;
    } catch {
      return [];
    }
  }

  private async extractMaterials(page: Page): Promise<string[]> {
    try {
      const materialsText = await this.safeGetText(page, '[data-testid="pip-product-materials"]');
      if (!materialsText) return [];

      // Extract common materials
      const materials = ['wood', 'metal', 'plastic', 'fabric', 'glass', 'leather'];
      return materials.filter(material => 
        materialsText.toLowerCase().includes(material)
      );
    } catch {
      return [];
    }
  }

  private async checkStock(page: Page): Promise<boolean> {
    try {
      const stockText = await this.safeGetText(page, '[data-testid="pip-product-availability"]');
      return !stockText.toLowerCase().includes('out of stock');
    } catch {
      return true; // Assume in stock if we can't determine
    }
  }

  private mapIkeaCategory(breadcrumbs: string[]): string {
    const categoryMap: Record<string, string> = {
      'chairs': 'seating',
      'sofas': 'seating',
      'armchairs': 'seating',
      'tables': 'tables',
      'desks': 'tables',
      'storage': 'storage',
      'lighting': 'lighting',
      'decoration': 'decor',
      'bedroom': 'bedroom',
      'kitchen': 'kitchen'
    };

    for (const breadcrumb of breadcrumbs) {
      const lower = breadcrumb.toLowerCase();
      for (const [key, value] of Object.entries(categoryMap)) {
        if (lower.includes(key)) {
          return value;
        }
      }
    }

    return 'furniture'; // Default category
  }
}
