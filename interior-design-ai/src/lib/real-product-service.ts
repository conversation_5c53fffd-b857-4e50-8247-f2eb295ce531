import { DatabaseService } from './database';
import { FurnitureRecommendation } from '@/types';
import { ENHANCED_FURNITURE_DATABASE } from './product-database';

export class RealProductService {
  private static database = new DatabaseService();

  static async getRecommendations(filters: {
    category?: string;
    roomType?: string;
    budget?: number;
    style?: string;
    limit?: number;
  }): Promise<FurnitureRecommendation[]> {
    try {
      // First try to get real scraped products
      const scrapedProducts = await this.getScrapedProducts(filters);
      
      // If we have enough scraped products, use them
      if (scrapedProducts.length >= (filters.limit || 10)) {
        return scrapedProducts.slice(0, filters.limit || 10);
      }

      // Otherwise, supplement with mock data
      const mockProducts = this.getMockProducts(filters);
      const combined = [...scrapedProducts, ...mockProducts];
      
      return combined.slice(0, filters.limit || 10);
    } catch (error) {
      console.error('Failed to get recommendations:', error);
      // Fallback to mock data only
      return this.getMockProducts(filters);
    }
  }

  private static async getScrapedProducts(filters: {
    category?: string;
    roomType?: string;
    budget?: number;
    style?: string;
    limit?: number;
  }): Promise<FurnitureRecommendation[]> {
    const searchFilters: any = {
      isAvailable: true,
      limit: filters.limit || 50
    };

    if (filters.category) {
      searchFilters.category = filters.category;
    }

    if (filters.budget) {
      searchFilters.maxPrice = filters.budget;
    }

    const dbProducts = await this.database.searchProducts(searchFilters);

    return dbProducts.map(this.convertDbProductToRecommendation);
  }

  private static getMockProducts(filters: {
    category?: string;
    roomType?: string;
    budget?: number;
    style?: string;
    limit?: number;
  }): FurnitureRecommendation[] {
    let products = [...ENHANCED_FURNITURE_DATABASE];

    // Apply filters
    if (filters.category) {
      products = products.filter(p => p.category === filters.category);
    }

    if (filters.budget) {
      products = products.filter(p => p.price <= filters.budget);
    }

    if (filters.style) {
      products = products.filter(p => 
        p.style.some(s => s.toLowerCase().includes(filters.style!.toLowerCase()))
      );
    }

    // Add mock placement and compatibility data
    return products.map(product => ({
      ...product,
      placement: {
        x: Math.random() * 400 + 100,
        y: Math.random() * 300 + 100,
        rotation: 0,
        scale: 1
      },
      compatibility: {
        styleMatch: 0.8 + Math.random() * 0.2,
        sizeMatch: 0.7 + Math.random() * 0.3,
        budgetMatch: filters.budget ? Math.min(1, filters.budget / product.price) : 1,
        overallScore: 0.75 + Math.random() * 0.25
      }
    })).slice(0, filters.limit || 10);
  }

  private static convertDbProductToRecommendation(dbProduct: any): FurnitureRecommendation {
    return {
      id: dbProduct.id,
      name: dbProduct.name,
      category: dbProduct.category,
      description: dbProduct.description || '',
      price: dbProduct.price,
      currency: dbProduct.currency,
      imageUrl: dbProduct.imageUrl,
      productUrl: dbProduct.productUrl,
      retailer: dbProduct.retailer,
      style: dbProduct.styles || ['modern'],
      dimensions: {
        width: dbProduct.width || 100,
        height: dbProduct.height || 100,
        depth: dbProduct.depth || 50
      },
      placement: {
        x: Math.random() * 400 + 100,
        y: Math.random() * 300 + 100,
        rotation: 0,
        scale: 1
      },
      compatibility: {
        styleMatch: 0.8 + Math.random() * 0.2,
        sizeMatch: 0.7 + Math.random() * 0.3,
        budgetMatch: 1,
        overallScore: 0.75 + Math.random() * 0.25
      }
    };
  }

  static async searchProducts(query: string, filters?: {
    category?: string;
    retailer?: string;
    maxPrice?: number;
    limit?: number;
  }): Promise<FurnitureRecommendation[]> {
    try {
      // Search in scraped products first
      const searchFilters: any = {
        isAvailable: true,
        limit: filters?.limit || 20
      };

      if (filters?.category) {
        searchFilters.category = filters.category;
      }

      if (filters?.retailer) {
        searchFilters.retailer = filters.retailer;
      }

      if (filters?.maxPrice) {
        searchFilters.maxPrice = filters.maxPrice;
      }

      const dbProducts = await this.database.searchProducts(searchFilters);

      // Filter by search query
      const filteredProducts = dbProducts.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description?.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase())
      );

      const scrapedResults = filteredProducts.map(this.convertDbProductToRecommendation);

      // If we don't have enough results, supplement with mock data
      if (scrapedResults.length < (filters?.limit || 20)) {
        const mockResults = ENHANCED_FURNITURE_DATABASE.filter(product =>
          product.name.toLowerCase().includes(query.toLowerCase()) ||
          product.description.toLowerCase().includes(query.toLowerCase())
        ).map(product => ({
          ...product,
          placement: {
            x: Math.random() * 400 + 100,
            y: Math.random() * 300 + 100,
            rotation: 0,
            scale: 1
          },
          compatibility: {
            styleMatch: 0.8 + Math.random() * 0.2,
            sizeMatch: 0.7 + Math.random() * 0.3,
            budgetMatch: 1,
            overallScore: 0.75 + Math.random() * 0.25
          }
        }));

        return [...scrapedResults, ...mockResults].slice(0, filters?.limit || 20);
      }

      return scrapedResults;
    } catch (error) {
      console.error('Failed to search products:', error);
      // Fallback to mock data search
      return ENHANCED_FURNITURE_DATABASE.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase())
      ).map(product => ({
        ...product,
        placement: {
          x: Math.random() * 400 + 100,
          y: Math.random() * 300 + 100,
          rotation: 0,
          scale: 1
        },
        compatibility: {
          styleMatch: 0.8 + Math.random() * 0.2,
          sizeMatch: 0.7 + Math.random() * 0.3,
          budgetMatch: 1,
          overallScore: 0.75 + Math.random() * 0.25
        }
      })).slice(0, filters?.limit || 20);
    }
  }

  static async getProductsByCategory(category: string, limit: number = 20): Promise<FurnitureRecommendation[]> {
    return this.getRecommendations({ category, limit });
  }

  static async getProductsByRetailer(retailer: string, limit: number = 20): Promise<FurnitureRecommendation[]> {
    try {
      const dbProducts = await this.database.searchProducts({
        retailer,
        isAvailable: true,
        limit
      });

      return dbProducts.map(this.convertDbProductToRecommendation);
    } catch (error) {
      console.error('Failed to get products by retailer:', error);
      return ENHANCED_FURNITURE_DATABASE
        .filter(p => p.retailer === retailer)
        .map(product => ({
          ...product,
          placement: {
            x: Math.random() * 400 + 100,
            y: Math.random() * 300 + 100,
            rotation: 0,
            scale: 1
          },
          compatibility: {
            styleMatch: 0.8 + Math.random() * 0.2,
            sizeMatch: 0.7 + Math.random() * 0.3,
            budgetMatch: 1,
            overallScore: 0.75 + Math.random() * 0.25
          }
        }))
        .slice(0, limit);
    }
  }

  static async getScrapingStats(): Promise<{
    totalProducts: number;
    productsByRetailer: Record<string, number>;
    productsByCategory: Record<string, number>;
    lastScrapedAt?: Date;
  }> {
    try {
      const allProducts = await this.database.searchProducts({
        isAvailable: true,
        limit: 1000
      });

      const stats = {
        totalProducts: allProducts.length,
        productsByRetailer: {} as Record<string, number>,
        productsByCategory: {} as Record<string, number>,
        lastScrapedAt: undefined as Date | undefined
      };

      // Calculate stats
      allProducts.forEach(product => {
        // Count by retailer
        stats.productsByRetailer[product.retailer] = 
          (stats.productsByRetailer[product.retailer] || 0) + 1;

        // Count by category
        stats.productsByCategory[product.category] = 
          (stats.productsByCategory[product.category] || 0) + 1;

        // Track latest scrape date
        if (product.scrapedAt) {
          const scrapedDate = new Date(product.scrapedAt);
          if (!stats.lastScrapedAt || scrapedDate > stats.lastScrapedAt) {
            stats.lastScrapedAt = scrapedDate;
          }
        }
      });

      return stats;
    } catch (error) {
      console.error('Failed to get scraping stats:', error);
      return {
        totalProducts: 0,
        productsByRetailer: {},
        productsByCategory: {},
        lastScrapedAt: undefined
      };
    }
  }
}
