// Advanced AI-powered style matching and compatibility analysis

import { DesignStyle, RoomAnalysis, FurnitureRecommendation } from '@/types';

// Style characteristics and compatibility matrix
const STYLE_CHARACTERISTICS: Record<DesignStyle, {
  colors: string[];
  materials: string[];
  patterns: string[];
  keywords: string[];
  compatibility: DesignStyle[];
  opposites: DesignStyle[];
}> = {
  modern: {
    colors: ['#FFFFFF', '#000000', '#808080', '#C0C0C0'],
    materials: ['glass', 'steel', 'chrome', 'leather'],
    patterns: ['geometric', 'solid', 'minimal'],
    keywords: ['clean', 'sleek', 'minimal', 'geometric', 'functional'],
    compatibility: ['contemporary', 'minimalist', 'industrial'],
    opposites: ['traditional', 'rustic', 'boho']
  },
  minimalist: {
    colors: ['#FFFFFF', '#F5F5F5', '#E8E8E8', '#D3D3D3'],
    materials: ['wood', 'concrete', 'linen', 'cotton'],
    patterns: ['solid', 'subtle texture'],
    keywords: ['simple', 'clean', 'uncluttered', 'functional', 'serene'],
    compatibility: ['modern', 'scandinavian', 'contemporary'],
    opposites: ['boho', 'traditional', 'rustic']
  },
  boho: {
    colors: ['#8B4513', '#DAA520', '#CD853F', '#DEB887', '#F4A460'],
    materials: ['rattan', 'macrame', 'velvet', 'brass', 'natural fiber'],
    patterns: ['paisley', 'tribal', 'floral', 'geometric'],
    keywords: ['eclectic', 'colorful', 'textured', 'artistic', 'layered'],
    compatibility: ['rustic', 'traditional'],
    opposites: ['modern', 'minimalist', 'industrial']
  },
  industrial: {
    colors: ['#2F4F4F', '#696969', '#A9A9A9', '#8B4513'],
    materials: ['metal', 'concrete', 'brick', 'leather', 'reclaimed wood'],
    patterns: ['exposed', 'raw', 'weathered'],
    keywords: ['raw', 'exposed', 'urban', 'functional', 'edgy'],
    compatibility: ['modern', 'contemporary'],
    opposites: ['traditional', 'boho', 'scandinavian']
  },
  scandinavian: {
    colors: ['#FFFFFF', '#F0F8FF', '#E6E6FA', '#FFFAF0'],
    materials: ['light wood', 'wool', 'linen', 'cotton'],
    patterns: ['natural', 'simple', 'cozy'],
    keywords: ['cozy', 'functional', 'light', 'natural', 'hygge'],
    compatibility: ['minimalist', 'contemporary', 'modern'],
    opposites: ['industrial', 'boho']
  },
  traditional: {
    colors: ['#8B0000', '#006400', '#191970', '#8B4513'],
    materials: ['mahogany', 'oak', 'velvet', 'silk', 'brass'],
    patterns: ['floral', 'damask', 'stripes', 'plaid'],
    keywords: ['classic', 'elegant', 'formal', 'timeless', 'refined'],
    compatibility: ['contemporary', 'rustic'],
    opposites: ['modern', 'minimalist', 'industrial']
  },
  contemporary: {
    colors: ['#FFFFFF', '#000000', '#808080', '#4169E1'],
    materials: ['mixed', 'glass', 'metal', 'fabric'],
    patterns: ['mixed', 'current', 'sophisticated'],
    keywords: ['current', 'sophisticated', 'mixed', 'balanced', 'updated'],
    compatibility: ['modern', 'traditional', 'scandinavian'],
    opposites: []
  },
  rustic: {
    colors: ['#8B4513', '#A0522D', '#CD853F', '#DEB887'],
    materials: ['reclaimed wood', 'stone', 'iron', 'natural fiber'],
    patterns: ['natural', 'weathered', 'textured'],
    keywords: ['natural', 'warm', 'cozy', 'weathered', 'handcrafted'],
    compatibility: ['traditional', 'boho'],
    opposites: ['modern', 'minimalist', 'industrial']
  }
};

export class StyleMatcher {
  
  /**
   * Analyze room colors and suggest compatible styles
   */
  static analyzeRoomStyle(roomAnalysis: RoomAnalysis): {
    suggestedStyles: DesignStyle[];
    confidence: Record<DesignStyle, number>;
    reasoning: string[];
  } {
    const colorPalette = roomAnalysis.colorPalette || [];
    const styleScores: Record<DesignStyle, number> = {} as any;
    const reasoning: string[] = [];

    // Initialize scores
    Object.keys(STYLE_CHARACTERISTICS).forEach(style => {
      styleScores[style as DesignStyle] = 0;
    });

    // Analyze color compatibility
    colorPalette.forEach(color => {
      Object.entries(STYLE_CHARACTERISTICS).forEach(([style, characteristics]) => {
        const colorMatch = this.calculateColorSimilarity(color, characteristics.colors);
        styleScores[style as DesignStyle] += colorMatch * 10;
      });
    });

    // Analyze furniture styles if available
    if (roomAnalysis.existingFurniture) {
      roomAnalysis.existingFurniture.forEach(furniture => {
        // Simple heuristic based on furniture type
        if (furniture.type.includes('modern') || furniture.type.includes('sleek')) {
          styleScores.modern += 5;
          styleScores.contemporary += 3;
        }
        if (furniture.type.includes('wood') || furniture.type.includes('natural')) {
          styleScores.scandinavian += 5;
          styleScores.rustic += 3;
        }
      });
    }

    // Analyze lighting for style hints
    if (roomAnalysis.lightingSources) {
      const naturalLight = roomAnalysis.lightingSources.filter(l => l.type === 'natural').length;
      if (naturalLight > 0) {
        styleScores.scandinavian += naturalLight * 2;
        styleScores.minimalist += naturalLight * 1.5;
        reasoning.push(`Good natural lighting suggests Scandinavian or minimalist styles`);
      }
    }

    // Normalize scores
    const maxScore = Math.max(...Object.values(styleScores));
    if (maxScore > 0) {
      Object.keys(styleScores).forEach(style => {
        styleScores[style as DesignStyle] = styleScores[style as DesignStyle] / maxScore;
      });
    }

    // Get top 3 suggested styles
    const sortedStyles = Object.entries(styleScores)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([style]) => style as DesignStyle);

    return {
      suggestedStyles: sortedStyles,
      confidence: styleScores,
      reasoning
    };
  }

  /**
   * Calculate style compatibility between furniture items
   */
  static calculateStyleCompatibility(
    item1Style: DesignStyle[],
    item2Style: DesignStyle[]
  ): number {
    let compatibility = 0;
    let totalComparisons = 0;

    item1Style.forEach(style1 => {
      item2Style.forEach(style2 => {
        totalComparisons++;
        
        if (style1 === style2) {
          compatibility += 1.0; // Perfect match
        } else if (STYLE_CHARACTERISTICS[style1].compatibility.includes(style2)) {
          compatibility += 0.7; // Good compatibility
        } else if (STYLE_CHARACTERISTICS[style1].opposites.includes(style2)) {
          compatibility += 0.1; // Poor compatibility
        } else {
          compatibility += 0.4; // Neutral
        }
      });
    });

    return totalComparisons > 0 ? compatibility / totalComparisons : 0;
  }

  /**
   * Score furniture recommendations based on style coherence
   */
  static scoreStyleCoherence(
    recommendations: FurnitureRecommendation[],
    targetStyle: DesignStyle
  ): {
    overallScore: number;
    itemScores: Record<string, number>;
    suggestions: string[];
  } {
    const itemScores: Record<string, number> = {};
    const suggestions: string[] = [];
    let totalScore = 0;

    recommendations.forEach(item => {
      let itemScore = 0;

      // Direct style match
      if (item.style.includes(targetStyle)) {
        itemScore += 1.0;
      }

      // Compatible style match
      const compatibleStyles = STYLE_CHARACTERISTICS[targetStyle].compatibility;
      const compatibilityBonus = item.style
        .filter(style => compatibleStyles.includes(style))
        .length * 0.7;
      itemScore += compatibilityBonus;

      // Opposite style penalty
      const oppositeStyles = STYLE_CHARACTERISTICS[targetStyle].opposites;
      const oppositesPenalty = item.style
        .filter(style => oppositeStyles.includes(style))
        .length * 0.5;
      itemScore -= oppositesPenalty;

      // Normalize score
      itemScore = Math.max(0, Math.min(1, itemScore));
      itemScores[item.id] = itemScore;
      totalScore += itemScore;

      // Generate suggestions
      if (itemScore < 0.5) {
        suggestions.push(`Consider replacing ${item.name} with a more ${targetStyle}-style alternative`);
      }
    });

    const overallScore = recommendations.length > 0 ? totalScore / recommendations.length : 0;

    return {
      overallScore,
      itemScores,
      suggestions
    };
  }

  /**
   * Generate style-based product alternatives
   */
  static generateStyleAlternatives(
    currentItem: FurnitureRecommendation,
    targetStyle: DesignStyle,
    availableProducts: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[]
  ): Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] {
    
    return availableProducts
      .filter(product => 
        product.category === currentItem.category &&
        product.id !== currentItem.id &&
        product.style.includes(targetStyle)
      )
      .sort((a, b) => {
        // Score based on style match and price similarity
        const aStyleScore = a.style.includes(targetStyle) ? 1 : 0.5;
        const bStyleScore = b.style.includes(targetStyle) ? 1 : 0.5;
        const aPriceDiff = Math.abs(a.price - currentItem.price) / currentItem.price;
        const bPriceDiff = Math.abs(b.price - currentItem.price) / currentItem.price;
        
        const aScore = aStyleScore - aPriceDiff * 0.3;
        const bScore = bStyleScore - bPriceDiff * 0.3;
        
        return bScore - aScore;
      })
      .slice(0, 3);
  }

  /**
   * Calculate color similarity using simple RGB distance
   */
  private static calculateColorSimilarity(color1: string, colorPalette: string[]): number {
    const rgb1 = this.hexToRgb(color1);
    if (!rgb1) return 0;

    let maxSimilarity = 0;
    
    colorPalette.forEach(color2 => {
      const rgb2 = this.hexToRgb(color2);
      if (!rgb2) return;

      const distance = Math.sqrt(
        Math.pow(rgb1.r - rgb2.r, 2) +
        Math.pow(rgb1.g - rgb2.g, 2) +
        Math.pow(rgb1.b - rgb2.b, 2)
      );

      // Normalize distance (max distance is ~441 for RGB)
      const similarity = 1 - (distance / 441);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    });

    return maxSimilarity;
  }

  /**
   * Convert hex color to RGB
   */
  private static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Get style description and tips
   */
  static getStyleGuide(style: DesignStyle): {
    description: string;
    tips: string[];
    doAndDonts: { dos: string[]; donts: string[] };
  } {
    const guides = {
      modern: {
        description: 'Clean lines, minimal clutter, and a focus on function over form.',
        tips: [
          'Use neutral colors with bold accent pieces',
          'Choose furniture with geometric shapes',
          'Minimize decorative elements',
          'Focus on quality over quantity'
        ],
        doAndDonts: {
          dos: ['Use glass and metal materials', 'Keep surfaces clutter-free', 'Choose statement lighting'],
          donts: ['Mix too many patterns', 'Use ornate decorations', 'Overcrowd the space']
        }
      },
      minimalist: {
        description: 'Less is more philosophy with emphasis on simplicity and functionality.',
        tips: [
          'Stick to a neutral color palette',
          'Choose multi-functional furniture',
          'Keep decorations to a minimum',
          'Focus on natural light'
        ],
        doAndDonts: {
          dos: ['Use hidden storage', 'Choose quality basics', 'Embrace negative space'],
          donts: ['Display too many items', 'Use bold patterns', 'Add unnecessary furniture']
        }
      },
      // Add more style guides as needed...
    };

    return guides[style as keyof typeof guides] || {
      description: 'A unique style with its own characteristics.',
      tips: ['Follow your personal taste', 'Mix and match thoughtfully'],
      doAndDonts: { dos: ['Be creative'], donts: ['Overthink it'] }
    };
  }
}
