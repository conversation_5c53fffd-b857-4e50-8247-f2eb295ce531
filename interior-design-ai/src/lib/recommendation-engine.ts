// Recommendation engine for furniture and decor suggestions

import { 
  FurnitureRecommendation, 
  RoomAnalysis, 
  RoomType, 
  DesignStyle, 
  FurnitureCategory,
  Point 
} from '@/types';

// Mock furniture database - in production, this would come from APIs
const FURNITURE_DATABASE: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] = [
  // Living Room Furniture
  {
    id: 'sofa-modern-1',
    name: 'Modern 3-Seat Sofa',
    category: 'seating',
    description: 'Comfortable modern sofa with clean lines and neutral upholstery',
    price: 899,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',
    retailer: 'ikea',
    dimensions: { width: 228, height: 83, depth: 95 },
    style: ['modern', 'minimalist', 'contemporary']
  },
  {
    id: 'coffee-table-1',
    name: 'Glass Coffee Table',
    category: 'tables',
    description: 'Sleek glass coffee table with metal legs',
    price: 299,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08XYZ123',
    retailer: 'amazon',
    dimensions: { width: 120, height: 45, depth: 60 },
    style: ['modern', 'contemporary', 'minimalist']
  },
  {
    id: 'accent-chair-1',
    name: 'Velvet Accent Chair',
    category: 'seating',
    description: 'Luxurious velvet accent chair in emerald green',
    price: 449,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08ABC456',
    retailer: 'amazon',
    dimensions: { width: 76, height: 86, depth: 81 },
    style: ['boho', 'contemporary', 'traditional']
  },
  
  // Bedroom Furniture
  {
    id: 'bed-frame-1',
    name: 'Platform Bed Frame',
    category: 'seating',
    description: 'Minimalist platform bed frame in natural wood',
    price: 599,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932209/',
    retailer: 'ikea',
    dimensions: { width: 209, height: 38, depth: 156 },
    style: ['scandinavian', 'minimalist', 'modern']
  },
  {
    id: 'nightstand-1',
    name: 'Two-Drawer Nightstand',
    category: 'storage',
    description: 'Compact nightstand with two drawers',
    price: 149,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-nightstand-white-stain-s49932210/',
    retailer: 'ikea',
    dimensions: { width: 46, height: 61, depth: 35 },
    style: ['scandinavian', 'traditional', 'contemporary']
  },

  // Lighting
  {
    id: 'floor-lamp-1',
    name: 'Arc Floor Lamp',
    category: 'lighting',
    description: 'Modern arc floor lamp with adjustable height',
    price: 199,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08DEF789',
    retailer: 'amazon',
    dimensions: { width: 40, height: 180, depth: 40 },
    style: ['modern', 'contemporary', 'industrial']
  },

  // Decor
  {
    id: 'wall-art-1',
    name: 'Abstract Canvas Print Set',
    category: 'decor',
    description: 'Set of 3 abstract canvas prints in neutral tones',
    price: 89,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08GHI012',
    retailer: 'amazon',
    dimensions: { width: 40, height: 60, depth: 2 },
    style: ['modern', 'contemporary', 'minimalist']
  },
  {
    id: 'plant-1',
    name: 'Fiddle Leaf Fig',
    category: 'plants',
    description: 'Large fiddle leaf fig plant in decorative pot',
    price: 79,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08JKL345',
    retailer: 'amazon',
    dimensions: { width: 30, height: 150, depth: 30 },
    style: ['boho', 'scandinavian', 'contemporary']
  },

  // Storage
  {
    id: 'bookshelf-1',
    name: '5-Tier Bookshelf',
    category: 'storage',
    description: 'Tall bookshelf with 5 adjustable shelves',
    price: 179,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932211/',
    retailer: 'ikea',
    dimensions: { width: 90, height: 197, depth: 37 },
    style: ['scandinavian', 'traditional', 'contemporary']
  }
];

// Style compatibility matrix
const STYLE_COMPATIBILITY: Record<DesignStyle, DesignStyle[]> = {
  modern: ['contemporary', 'minimalist', 'industrial'],
  minimalist: ['modern', 'scandinavian', 'contemporary'],
  boho: ['rustic', 'traditional'],
  industrial: ['modern', 'contemporary'],
  scandinavian: ['minimalist', 'contemporary', 'modern'],
  traditional: ['boho', 'rustic'],
  contemporary: ['modern', 'minimalist', 'scandinavian', 'industrial'],
  rustic: ['boho', 'traditional']
};

// Room-specific furniture priorities
const ROOM_FURNITURE_PRIORITIES: Record<RoomType, FurnitureCategory[]> = {
  living_room: ['seating', 'tables', 'lighting', 'storage', 'decor', 'plants'],
  bedroom: ['seating', 'storage', 'lighting', 'decor', 'textiles'],
  kitchen: ['storage', 'lighting', 'decor'],
  bathroom: ['storage', 'lighting', 'textiles'],
  dining_room: ['seating', 'tables', 'lighting', 'storage', 'decor'],
  office: ['seating', 'tables', 'storage', 'lighting', 'decor'],
  nursery: ['seating', 'storage', 'lighting', 'textiles', 'decor'],
  guest_room: ['seating', 'storage', 'lighting', 'decor', 'textiles']
};

export class RecommendationEngine {
  
  async generateRecommendations(
    roomAnalysis: RoomAnalysis,
    roomType: RoomType,
    budget: number,
    preferredStyle: DesignStyle,
    existingFurnitureIds: string[] = []
  ): Promise<FurnitureRecommendation[]> {
    
    // Filter furniture based on room type and style
    const compatibleFurniture = this.filterByRoomAndStyle(roomType, preferredStyle);
    
    // Remove existing furniture to avoid duplicates
    const availableFurniture = compatibleFurniture.filter(
      item => !existingFurnitureIds.includes(item.id)
    );
    
    // Score and rank furniture based on multiple factors
    const scoredFurniture = availableFurniture.map(item => ({
      ...item,
      score: this.calculateFurnitureScore(item, roomAnalysis, roomType, preferredStyle, budget)
    }));
    
    // Sort by score and apply budget constraints
    const sortedFurniture = scoredFurniture
      .sort((a, b) => b.score - a.score)
      .filter(item => item.price <= budget * 0.3); // No single item should exceed 30% of budget
    
    // Select diverse recommendations
    const recommendations = this.selectDiverseRecommendations(sortedFurniture, budget);
    
    // Add placement and compatibility information
    return recommendations.map(item => this.enhanceRecommendation(item, roomAnalysis, roomType));
  }

  private filterByRoomAndStyle(roomType: RoomType, style: DesignStyle) {
    const compatibleStyles = [style, ...STYLE_COMPATIBILITY[style]];
    const priorityCategories = ROOM_FURNITURE_PRIORITIES[roomType];
    
    return FURNITURE_DATABASE.filter(item => {
      const hasCompatibleStyle = item.style.some(itemStyle => 
        compatibleStyles.includes(itemStyle)
      );
      const isRelevantCategory = priorityCategories.includes(item.category);
      
      return hasCompatibleStyle && isRelevantCategory;
    });
  }

  private calculateFurnitureScore(
    item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>,
    roomAnalysis: RoomAnalysis,
    roomType: RoomType,
    preferredStyle: DesignStyle,
    budget: number
  ): number {
    let score = 0;
    
    // Style match score (0-40 points)
    const styleMatch = item.style.includes(preferredStyle) ? 40 : 
      item.style.some(s => STYLE_COMPATIBILITY[preferredStyle].includes(s)) ? 25 : 10;
    score += styleMatch;
    
    // Category priority score (0-30 points)
    const categoryPriority = ROOM_FURNITURE_PRIORITIES[roomType].indexOf(item.category);
    const categoryScore = categoryPriority >= 0 ? (30 - categoryPriority * 5) : 0;
    score += Math.max(categoryScore, 0);
    
    // Budget efficiency score (0-20 points)
    const budgetRatio = item.price / budget;
    const budgetScore = budgetRatio <= 0.1 ? 20 : 
      budgetRatio <= 0.2 ? 15 : 
      budgetRatio <= 0.3 ? 10 : 0;
    score += budgetScore;
    
    // Space compatibility score (0-10 points)
    const spaceScore = this.calculateSpaceCompatibility(item, roomAnalysis);
    score += spaceScore;
    
    return score;
  }

  private calculateSpaceCompatibility(
    item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>,
    roomAnalysis: RoomAnalysis
  ): number {
    // Simple space check - in production, this would be more sophisticated
    const itemArea = (item.dimensions.width / 100) * (item.dimensions.depth || item.dimensions.width) / 100;
    const roomArea = roomAnalysis.layout.floorArea / 10000; // Convert to square meters
    
    if (itemArea / roomArea < 0.1) return 10; // Good fit
    if (itemArea / roomArea < 0.2) return 5;  // Acceptable
    return 0; // Too large
  }

  private selectDiverseRecommendations(
    scoredFurniture: any[],
    budget: number
  ): any[] {
    const selected: any[] = [];
    const usedCategories = new Set<FurnitureCategory>();
    let remainingBudget = budget;
    
    for (const item of scoredFurniture) {
      if (selected.length >= 8) break; // Limit to 8 recommendations
      if (item.price > remainingBudget) continue;
      
      // Prefer diverse categories, but allow duplicates if highly scored
      if (!usedCategories.has(item.category) || item.score > 80) {
        selected.push(item);
        usedCategories.add(item.category);
        remainingBudget -= item.price;
      }
    }
    
    return selected;
  }

  private enhanceRecommendation(
    item: any,
    roomAnalysis: RoomAnalysis,
    roomType: RoomType
  ): FurnitureRecommendation {
    // Calculate suggested placement
    const placement = this.calculatePlacement(item, roomAnalysis);
    
    // Calculate compatibility scores
    const compatibility = {
      roomType: [roomType],
      existingFurniture: roomAnalysis.existingFurniture.map(f => f.type),
      styleMatch: item.score / 100
    };

    // Generate affiliate URL (mock)
    const affiliateUrl = this.generateAffiliateUrl(item.productUrl, item.retailer);

    return {
      ...item,
      placement,
      compatibility,
      affiliateUrl
    };
  }

  private calculatePlacement(item: any, roomAnalysis: RoomAnalysis): any {
    // Mock placement calculation - in production, use spatial analysis
    const zones = roomAnalysis.spatialFlow?.functionalZones || [];
    const suggestedZone = zones.find(zone => 
      this.isCategoryCompatibleWithZone(item.category, zone.function)
    );

    return {
      suggestedPosition: suggestedZone ? 
        { x: suggestedZone.area[0].x + 50, y: suggestedZone.area[0].y + 50 } :
        { x: 100, y: 100 },
      orientation: 0,
      zone: suggestedZone?.id || 'main-area'
    };
  }

  private isCategoryCompatibleWithZone(category: FurnitureCategory, zoneFunction: string): boolean {
    const compatibility: Record<string, FurnitureCategory[]> = {
      'seating and entertainment': ['seating', 'tables', 'lighting', 'decor'],
      'sleeping': ['seating', 'storage', 'lighting'],
      'work': ['seating', 'tables', 'storage', 'lighting'],
      'dining': ['seating', 'tables', 'lighting', 'decor']
    };
    
    return compatibility[zoneFunction]?.includes(category) || false;
  }

  private generateAffiliateUrl(productUrl: string, retailer: string): string {
    // Mock affiliate URL generation - in production, use actual affiliate programs
    const affiliateIds = {
      amazon: 'your-amazon-tag',
      ikea: 'your-ikea-partner-id'
    };
    
    if (retailer === 'amazon' && affiliateIds.amazon) {
      return `${productUrl}?tag=${affiliateIds.amazon}`;
    }
    
    return productUrl;
  }
}

export const recommendationEngine = new RecommendationEngine();
