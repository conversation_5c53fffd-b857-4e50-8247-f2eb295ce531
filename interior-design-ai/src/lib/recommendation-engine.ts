// Enhanced recommendation engine for furniture and decor suggestions

import {
  FurnitureRecommendation,
  RoomAnalysis,
  RoomType,
  DesignStyle,
  FurnitureCategory,
  Point
} from '@/types';
import { RealProductService } from './real-product-service';
import { StyleMatcher } from './style-matcher';
import { DatabaseService } from './database';

// Enhanced furniture database with more realistic data
const FURNITURE_DATABASE: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>[] = [
  // Living Room Furniture
  {
    id: 'sofa-modern-1',
    name: 'Modern 3-Seat Sofa',
    category: 'seating',
    description: 'Comfortable modern sofa with clean lines and neutral upholstery',
    price: 899,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',
    retailer: 'ikea',
    dimensions: { width: 228, height: 83, depth: 95 },
    style: ['modern', 'minimalist', 'contemporary']
  },
  {
    id: 'coffee-table-1',
    name: 'Glass Coffee Table',
    category: 'tables',
    description: 'Sleek glass coffee table with metal legs',
    price: 299,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08XYZ123',
    retailer: 'amazon',
    dimensions: { width: 120, height: 45, depth: 60 },
    style: ['modern', 'contemporary', 'minimalist']
  },
  {
    id: 'accent-chair-1',
    name: 'Velvet Accent Chair',
    category: 'seating',
    description: 'Luxurious velvet accent chair in emerald green',
    price: 449,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08ABC456',
    retailer: 'amazon',
    dimensions: { width: 76, height: 86, depth: 81 },
    style: ['boho', 'contemporary', 'traditional']
  },
  
  // Bedroom Furniture
  {
    id: 'bed-frame-1',
    name: 'Platform Bed Frame',
    category: 'seating',
    description: 'Minimalist platform bed frame in natural wood',
    price: 599,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932209/',
    retailer: 'ikea',
    dimensions: { width: 209, height: 38, depth: 156 },
    style: ['scandinavian', 'minimalist', 'modern']
  },
  {
    id: 'nightstand-1',
    name: 'Two-Drawer Nightstand',
    category: 'storage',
    description: 'Compact nightstand with two drawers',
    price: 149,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-nightstand-white-stain-s49932210/',
    retailer: 'ikea',
    dimensions: { width: 46, height: 61, depth: 35 },
    style: ['scandinavian', 'traditional', 'contemporary']
  },

  // Lighting
  {
    id: 'floor-lamp-1',
    name: 'Arc Floor Lamp',
    category: 'lighting',
    description: 'Modern arc floor lamp with adjustable height',
    price: 199,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08DEF789',
    retailer: 'amazon',
    dimensions: { width: 40, height: 180, depth: 40 },
    style: ['modern', 'contemporary', 'industrial']
  },

  // Decor
  {
    id: 'wall-art-1',
    name: 'Abstract Canvas Print Set',
    category: 'decor',
    description: 'Set of 3 abstract canvas prints in neutral tones',
    price: 89,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08GHI012',
    retailer: 'amazon',
    dimensions: { width: 40, height: 60, depth: 2 },
    style: ['modern', 'contemporary', 'minimalist']
  },
  {
    id: 'plant-1',
    name: 'Fiddle Leaf Fig',
    category: 'plants',
    description: 'Large fiddle leaf fig plant in decorative pot',
    price: 79,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.amazon.com/dp/B08JKL345',
    retailer: 'amazon',
    dimensions: { width: 30, height: 150, depth: 30 },
    style: ['boho', 'scandinavian', 'contemporary']
  },

  // Storage
  {
    id: 'bookshelf-1',
    name: '5-Tier Bookshelf',
    category: 'storage',
    description: 'Tall bookshelf with 5 adjustable shelves',
    price: 179,
    currency: 'USD',
    imageUrl: '/api/placeholder/400/300',
    productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932211/',
    retailer: 'ikea',
    dimensions: { width: 90, height: 197, depth: 37 },
    style: ['scandinavian', 'traditional', 'contemporary']
  }
];

// Style compatibility matrix
const STYLE_COMPATIBILITY: Record<DesignStyle, DesignStyle[]> = {
  modern: ['contemporary', 'minimalist', 'industrial'],
  minimalist: ['modern', 'scandinavian', 'contemporary'],
  boho: ['rustic', 'traditional'],
  industrial: ['modern', 'contemporary'],
  scandinavian: ['minimalist', 'contemporary', 'modern'],
  traditional: ['boho', 'rustic'],
  contemporary: ['modern', 'minimalist', 'scandinavian', 'industrial'],
  rustic: ['boho', 'traditional']
};

// Room-specific furniture priorities
const ROOM_FURNITURE_PRIORITIES: Record<RoomType, FurnitureCategory[]> = {
  living_room: ['seating', 'tables', 'lighting', 'storage', 'decor', 'plants'],
  bedroom: ['seating', 'storage', 'lighting', 'decor', 'textiles'],
  kitchen: ['storage', 'lighting', 'decor'],
  bathroom: ['storage', 'lighting', 'textiles'],
  dining_room: ['seating', 'tables', 'lighting', 'storage', 'decor'],
  office: ['seating', 'tables', 'storage', 'lighting', 'decor'],
  nursery: ['seating', 'storage', 'lighting', 'textiles', 'decor'],
  guest_room: ['seating', 'storage', 'lighting', 'decor', 'textiles']
};

export class RecommendationEngine {

  async generateRecommendations(
    roomAnalysis: RoomAnalysis,
    roomType: RoomType,
    budget: number,
    preferredStyle: DesignStyle,
    existingFurnitureIds: string[] = []
  ): Promise<FurnitureRecommendation[]> {

    // Analyze room style compatibility
    const styleAnalysis = StyleMatcher.analyzeRoomStyle(roomAnalysis);
    const effectiveStyle = styleAnalysis.confidence[preferredStyle] > 0.5
      ? preferredStyle
      : styleAnalysis.suggestedStyles[0];

    // Get products from real scraped data and enhanced database
    const availableProducts = await RealProductService.getRecommendations({
      roomType,
      budget: budget * 0.4, // Max 40% of budget per item
      style: effectiveStyle,
      limit: 100
    });

    // Remove existing furniture to avoid duplicates
    const filteredProducts = availableProducts.filter(
      item => !existingFurnitureIds.includes(item.id)
    );

    // Score and rank furniture based on multiple factors
    const scoredFurniture = filteredProducts.map(item => ({
      ...item,
      score: this.calculateAdvancedFurnitureScore(
        item, roomAnalysis, roomType, effectiveStyle, budget, styleAnalysis
      )
    }));

    // Sort by score and apply budget constraints
    const sortedFurniture = scoredFurniture
      .sort((a, b) => b.score - a.score);

    // Select diverse recommendations using advanced algorithm
    const recommendations = this.selectOptimalRecommendations(
      sortedFurniture, budget, roomType, effectiveStyle
    );

    // Add placement and compatibility information
    return recommendations.map(item => this.enhanceRecommendation(item, roomAnalysis, roomType));
  }

  private filterByRoomAndStyle(roomType: RoomType, style: DesignStyle) {
    const compatibleStyles = [style, ...STYLE_COMPATIBILITY[style]];
    const priorityCategories = ROOM_FURNITURE_PRIORITIES[roomType];
    
    return FURNITURE_DATABASE.filter(item => {
      const hasCompatibleStyle = item.style.some(itemStyle => 
        compatibleStyles.includes(itemStyle)
      );
      const isRelevantCategory = priorityCategories.includes(item.category);
      
      return hasCompatibleStyle && isRelevantCategory;
    });
  }

  private calculateAdvancedFurnitureScore(
    item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>,
    roomAnalysis: RoomAnalysis,
    roomType: RoomType,
    preferredStyle: DesignStyle,
    budget: number,
    styleAnalysis: any
  ): number {
    let score = 0;

    // Enhanced style match score (0-40 points)
    const directStyleMatch = item.style.includes(preferredStyle);
    const compatibleStyles = STYLE_COMPATIBILITY[preferredStyle] || [];
    const hasCompatibleStyle = item.style.some(s => compatibleStyles.includes(s));

    if (directStyleMatch) {
      score += 40;
    } else if (hasCompatibleStyle) {
      score += 25;
    } else {
      // Check against room's suggested styles
      const roomStyleMatch = styleAnalysis.suggestedStyles.some((s: DesignStyle) =>
        item.style.includes(s)
      );
      score += roomStyleMatch ? 20 : 10;
    }

    // Category priority score with room-specific weighting (0-30 points)
    const categoryPriority = ROOM_FURNITURE_PRIORITIES[roomType].indexOf(item.category);
    const categoryScore = categoryPriority >= 0 ? (30 - categoryPriority * 4) : 5;
    score += Math.max(categoryScore, 0);

    // Advanced budget efficiency score (0-20 points)
    const budgetRatio = item.price / budget;
    let budgetScore = 0;
    if (budgetRatio <= 0.05) budgetScore = 20;      // Very affordable
    else if (budgetRatio <= 0.1) budgetScore = 18;  // Affordable
    else if (budgetRatio <= 0.15) budgetScore = 15; // Reasonable
    else if (budgetRatio <= 0.25) budgetScore = 10; // Moderate
    else if (budgetRatio <= 0.35) budgetScore = 5;  // Expensive
    else budgetScore = 0;                            // Too expensive
    score += budgetScore;

    // Enhanced space compatibility score (0-15 points)
    const spaceScore = this.calculateAdvancedSpaceCompatibility(item, roomAnalysis, roomType);
    score += spaceScore;

    // Quality and retailer bonus (0-10 points)
    const qualityScore = this.calculateQualityScore(item);
    score += qualityScore;

    // Color harmony score (0-10 points)
    const colorScore = this.calculateColorHarmony(item, roomAnalysis.colorPalette || []);
    score += colorScore;

    return score;
  }

  private calculateQualityScore(item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>): number {
    let score = 5; // Base score

    // Retailer reputation bonus
    if (item.retailer === 'ikea') score += 2; // Known for good value
    if (item.retailer === 'amazon') score += 1; // Wide selection

    // Price-quality correlation (higher price often means better quality)
    if (item.price > 500) score += 2;
    if (item.price > 1000) score += 1;

    return Math.min(score, 10);
  }

  private calculateColorHarmony(
    item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>,
    roomColors: string[]
  ): number {
    if (roomColors.length === 0) return 5; // Neutral score if no color data

    // Simple heuristic: items with neutral colors score higher
    const neutralColors = ['#FFFFFF', '#000000', '#808080', '#F5F5F5', '#E8E8E8'];
    const hasNeutralStyle = item.style.some(style =>
      ['modern', 'minimalist', 'contemporary', 'scandinavian'].includes(style)
    );

    if (hasNeutralStyle) return 8; // Neutral styles typically work well

    // For colorful styles, assume they work if they match the style
    const colorfulStyles = ['boho', 'traditional', 'rustic'];
    const isColorfulStyle = item.style.some(style => colorfulStyles.includes(style));

    return isColorfulStyle ? 6 : 7;
  }

  private calculateAdvancedSpaceCompatibility(
    item: Omit<FurnitureRecommendation, 'placement' | 'compatibility'>,
    roomAnalysis: RoomAnalysis,
    roomType: RoomType
  ): number {
    const itemArea = (item.dimensions.width / 100) * (item.dimensions.depth || item.dimensions.width) / 100;
    const roomArea = roomAnalysis.layout.floorArea / 10000; // Convert to square meters
    const areaRatio = itemArea / roomArea;

    let spaceScore = 0;

    // Base space compatibility
    if (areaRatio < 0.05) spaceScore += 15;      // Perfect fit
    else if (areaRatio < 0.1) spaceScore += 12;  // Good fit
    else if (areaRatio < 0.15) spaceScore += 8;  // Acceptable
    else if (areaRatio < 0.25) spaceScore += 4;  // Tight fit
    else spaceScore += 0;                        // Too large

    // Room type specific adjustments
    if (roomType === 'living_room' && item.category === 'seating') {
      spaceScore += 2; // Living rooms need seating
    }
    if (roomType === 'bedroom' && item.category === 'storage') {
      spaceScore += 2; // Bedrooms need storage
    }

    // Existing furniture consideration
    const existingFurnitureArea = roomAnalysis.existingFurniture?.reduce((total, furniture) => {
      const furnitureArea = (furniture.boundingBox.width * furniture.boundingBox.height) / 10000;
      return total + furnitureArea;
    }, 0) || 0;

    const remainingSpace = roomArea - existingFurnitureArea;
    if (itemArea / remainingSpace > 0.5) {
      spaceScore -= 5; // Penalty for overcrowding
    }

    return Math.max(0, Math.min(15, spaceScore));
  }

  private selectOptimalRecommendations(
    scoredFurniture: any[],
    budget: number,
    roomType: RoomType,
    style: DesignStyle
  ): any[] {
    const selected: any[] = [];
    const categoryCount = new Map<FurnitureCategory, number>();
    let remainingBudget = budget;

    // Priority categories for this room type
    const priorities = ROOM_FURNITURE_PRIORITIES[roomType];

    // Phase 1: Select high-priority, high-score items
    for (const category of priorities.slice(0, 3)) { // Top 3 priorities
      const categoryItems = scoredFurniture
        .filter(item => item.category === category && item.price <= remainingBudget)
        .slice(0, 2); // Max 2 items per priority category

      for (const item of categoryItems) {
        if (selected.length >= 10) break;
        if (item.price <= remainingBudget) {
          selected.push(item);
          categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
          remainingBudget -= item.price;
        }
      }
    }

    // Phase 2: Fill remaining budget with diverse, high-scoring items
    const remainingItems = scoredFurniture.filter(item =>
      !selected.some(s => s.id === item.id) &&
      item.price <= remainingBudget
    );

    for (const item of remainingItems) {
      if (selected.length >= 12) break; // Max 12 total recommendations
      if (item.price > remainingBudget) continue;

      const currentCategoryCount = categoryCount.get(item.category) || 0;

      // Prefer categories we haven't used much
      if (currentCategoryCount < 2 || item.score > 90) {
        selected.push(item);
        categoryCount.set(item.category, currentCategoryCount + 1);
        remainingBudget -= item.price;
      }
    }

    // Phase 3: Style coherence check and optimization
    const styleCoherence = StyleMatcher.scoreStyleCoherence(
      selected.map(item => ({ ...item, style: item.style })),
      style
    );

    // If style coherence is low, try to replace some items
    if (styleCoherence.overallScore < 0.7) {
      this.optimizeStyleCoherence(selected, scoredFurniture, style, remainingBudget);
    }

    return selected;
  }

  private optimizeStyleCoherence(
    selected: any[],
    allItems: any[],
    targetStyle: DesignStyle,
    remainingBudget: number
  ): void {
    // Find items with low style scores and try to replace them
    const lowStyleItems = selected.filter(item =>
      !item.style.includes(targetStyle) &&
      !item.style.some((s: DesignStyle) => STYLE_COMPATIBILITY[targetStyle]?.includes(s))
    );

    for (const lowItem of lowStyleItems.slice(0, 2)) { // Only replace up to 2 items
      const alternatives = allItems.filter(alt =>
        alt.category === lowItem.category &&
        alt.style.includes(targetStyle) &&
        alt.price <= lowItem.price + remainingBudget &&
        !selected.some(s => s.id === alt.id)
      );

      if (alternatives.length > 0) {
        const bestAlternative = alternatives[0]; // Already sorted by score
        const index = selected.findIndex(item => item.id === lowItem.id);
        if (index !== -1) {
          selected[index] = bestAlternative;
          remainingBudget += lowItem.price - bestAlternative.price;
        }
      }
    }
  }

  private enhanceRecommendation(
    item: any,
    roomAnalysis: RoomAnalysis,
    roomType: RoomType
  ): FurnitureRecommendation {
    // Calculate suggested placement
    const placement = this.calculatePlacement(item, roomAnalysis);
    
    // Calculate compatibility scores
    const compatibility = {
      roomType: [roomType],
      existingFurniture: roomAnalysis.existingFurniture.map(f => f.type),
      styleMatch: item.score / 100
    };

    // Generate affiliate URL (mock)
    const affiliateUrl = this.generateAffiliateUrl(item.productUrl, item.retailer);

    return {
      ...item,
      placement,
      compatibility,
      affiliateUrl
    };
  }

  private calculatePlacement(item: any, roomAnalysis: RoomAnalysis): any {
    // Mock placement calculation - in production, use spatial analysis
    const zones = roomAnalysis.spatialFlow?.functionalZones || [];
    const suggestedZone = zones.find(zone => 
      this.isCategoryCompatibleWithZone(item.category, zone.function)
    );

    return {
      suggestedPosition: suggestedZone ? 
        { x: suggestedZone.area[0].x + 50, y: suggestedZone.area[0].y + 50 } :
        { x: 100, y: 100 },
      orientation: 0,
      zone: suggestedZone?.id || 'main-area'
    };
  }

  private isCategoryCompatibleWithZone(category: FurnitureCategory, zoneFunction: string): boolean {
    const compatibility: Record<string, FurnitureCategory[]> = {
      'seating and entertainment': ['seating', 'tables', 'lighting', 'decor'],
      'sleeping': ['seating', 'storage', 'lighting'],
      'work': ['seating', 'tables', 'storage', 'lighting'],
      'dining': ['seating', 'tables', 'lighting', 'decor']
    };
    
    return compatibility[zoneFunction]?.includes(category) || false;
  }

  private generateAffiliateUrl(productUrl: string, retailer: 'amazon' | 'ikea'): string {
    return ProductDatabase.generateAffiliateUrl(productUrl, retailer);
  }
}

export const recommendationEngine = new RecommendationEngine();
