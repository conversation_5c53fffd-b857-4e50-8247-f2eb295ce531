// API utility functions and error handling

import { NextResponse } from 'next/server';
import { APIResponse } from '@/types';

export class APIError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export function createErrorResponse(
  error: string | Error | APIError,
  statusCode: number = 500
): NextResponse<APIResponse<never>> {
  let message: string;
  let code: number;
  let errorCode: string | undefined;

  if (error instanceof APIError) {
    message = error.message;
    code = error.statusCode;
    errorCode = error.code;
  } else if (error instanceof Error) {
    message = error.message;
    code = statusCode;
  } else {
    message = error;
    code = statusCode;
  }

  const response: APIResponse<never> = {
    success: false,
    error: message,
    ...(errorCode && { code: errorCode })
  };

  return NextResponse.json(response, { status: code });
}

export function createSuccessResponse<T>(
  data: T,
  message?: string
): NextResponse<APIResponse<T>> {
  const response: APIResponse<T> = {
    success: true,
    data,
    ...(message && { message })
  };

  return NextResponse.json(response);
}

export async function withErrorHandling<T>(
  handler: () => Promise<T>
): Promise<NextResponse<APIResponse<T>> | NextResponse<APIResponse<never>>> {
  try {
    const result = await handler();
    return createSuccessResponse(result);
  } catch (error) {
    console.error('API Error:', error);
    return createErrorResponse(error as Error);
  }
}

export function validateRequired(
  data: Record<string, any>,
  requiredFields: string[]
): void {
  const missingFields = requiredFields.filter(field => 
    data[field] === undefined || data[field] === null || data[field] === ''
  );

  if (missingFields.length > 0) {
    throw new APIError(
      `Missing required fields: ${missingFields.join(', ')}`,
      400,
      'MISSING_FIELDS'
    );
  }
}

export function validateFileUpload(file: File): void {
  const MAX_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

  if (!file) {
    throw new APIError('No file provided', 400, 'NO_FILE');
  }

  if (!ALLOWED_TYPES.includes(file.type)) {
    throw new APIError(
      'Invalid file type. Only JPEG, PNG, and WebP are allowed.',
      400,
      'INVALID_FILE_TYPE'
    );
  }

  if (file.size > MAX_SIZE) {
    throw new APIError(
      'File too large. Maximum size is 10MB.',
      400,
      'FILE_TOO_LARGE'
    );
  }
}

export function validateBudget(budget: number): void {
  if (typeof budget !== 'number' || isNaN(budget)) {
    throw new APIError('Budget must be a valid number', 400, 'INVALID_BUDGET');
  }

  if (budget <= 0) {
    throw new APIError('Budget must be greater than 0', 400, 'BUDGET_TOO_LOW');
  }

  if (budget > 100000) {
    throw new APIError('Budget cannot exceed $100,000', 400, 'BUDGET_TOO_HIGH');
  }
}

export function sanitizeFilename(filename: string): string {
  // Remove or replace unsafe characters
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase();
}

export function generateUniqueId(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

export function isValidRoomType(roomType: string): boolean {
  const validTypes = [
    'living_room',
    'bedroom', 
    'kitchen',
    'bathroom',
    'dining_room',
    'office',
    'nursery',
    'guest_room'
  ];
  return validTypes.includes(roomType);
}

export function isValidDesignStyle(style: string): boolean {
  const validStyles = [
    'modern',
    'minimalist',
    'boho',
    'industrial',
    'scandinavian',
    'traditional',
    'contemporary',
    'rustic'
  ];
  return validStyles.includes(style);
}

export function logAPICall(
  method: string,
  endpoint: string,
  duration: number,
  success: boolean,
  userId?: string
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    method,
    endpoint,
    duration,
    success,
    userId: userId || 'anonymous'
  };

  // In production, you would send this to a logging service
  console.log('API Call:', logData);
}

export function rateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  // Simple in-memory rate limiting
  // In production, use Redis or a proper rate limiting service
  
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // This is a simplified implementation
  // In production, implement proper rate limiting
  return true; // Allow all requests for now
}

export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}
