// Image helper utilities for better fallback images

export const getFallbackImage = (category: string, name: string): string => {
  const fallbackImages: Record<string, string[]> = {
    seating: [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&auto=format', // Modern sofa
      'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=400&h=400&fit=crop&auto=format', // Armchair
      'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=400&h=400&fit=crop&auto=format', // Chair
    ],
    tables: [
      'https://images.unsplash.com/photo-1549497538-303791108f95?w=400&h=400&fit=crop&auto=format', // Coffee table
      'https://images.unsplash.com/photo-1581539250439-c96689b516dd?w=400&h=400&fit=crop&auto=format', // Dining table
      'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=400&h=400&fit=crop&auto=format', // Side table
    ],
    storage: [
      'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&auto=format', // Bookshelf
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&auto=format', // Cabinet
      'https://images.unsplash.com/photo-1549497538-303791108f95?w=400&h=400&fit=crop&auto=format', // Dresser
    ],
    electronics: [
      'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop&auto=format', // TV
      'https://images.unsplash.com/photo-1567690187548-f07b1d7bf5a9?w=400&h=400&fit=crop&auto=format', // Large TV
      'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=400&h=400&fit=crop&auto=format', // Smart TV
    ],
    lighting: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&auto=format', // Table lamp
      'https://images.unsplash.com/photo-1513506003901-1e6a229e2d15?w=400&h=400&fit=crop&auto=format', // Floor lamp
      'https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=400&h=400&fit=crop&auto=format', // Pendant light
    ],
    decor: [
      'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop&auto=format', // Plant
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&auto=format', // Artwork
      'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=400&h=400&fit=crop&auto=format', // Decorative item
    ]
  };

  const categoryImages = fallbackImages[category] || fallbackImages.decor;
  
  // Use a simple hash of the name to consistently pick the same image for the same item
  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  const index = Math.abs(hash) % categoryImages.length;
  return categoryImages[index];
};

export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export const isImageValid = async (src: string): Promise<boolean> => {
  try {
    await preloadImage(src);
    return true;
  } catch {
    return false;
  }
};
