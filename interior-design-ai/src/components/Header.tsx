'use client';

import { useState } from 'react';
import { Menu, X, Home, Info, Mail } from 'lucide-react';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Home className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">
              Interior AI
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a 
              href="#" 
              className="text-gray-600 hover:text-blue-600 transition-colors font-medium"
            >
              How it Works
            </a>
            <a 
              href="#" 
              className="text-gray-600 hover:text-blue-600 transition-colors font-medium"
            >
              Examples
            </a>
            <a 
              href="#" 
              className="text-gray-600 hover:text-blue-600 transition-colors font-medium"
            >
              Pricing
            </a>
            <a 
              href="#" 
              className="text-gray-600 hover:text-blue-600 transition-colors font-medium"
            >
              About
            </a>
          </nav>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="text-gray-600 hover:text-blue-600 transition-colors font-medium">
              Sign In
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
              Get Started
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="w-6 h-6 text-gray-600" />
            ) : (
              <Menu className="w-6 h-6 text-gray-600" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <a 
                href="#" 
                className="text-gray-600 hover:text-blue-600 transition-colors font-medium py-2"
              >
                How it Works
              </a>
              <a 
                href="#" 
                className="text-gray-600 hover:text-blue-600 transition-colors font-medium py-2"
              >
                Examples
              </a>
              <a 
                href="#" 
                className="text-gray-600 hover:text-blue-600 transition-colors font-medium py-2"
              >
                Pricing
              </a>
              <a 
                href="#" 
                className="text-gray-600 hover:text-blue-600 transition-colors font-medium py-2"
              >
                About
              </a>
              <div className="pt-4 border-t border-gray-200">
                <button className="w-full text-left text-gray-600 hover:text-blue-600 transition-colors font-medium py-2">
                  Sign In
                </button>
                <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium mt-2">
                  Get Started
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
