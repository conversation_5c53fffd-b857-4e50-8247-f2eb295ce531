'use client';

import { useState } from 'react';
import { <PERSON><PERSON>ef<PERSON>, ArrowRight, DollarSign } from 'lucide-react';
import { RoomType, DesignStyle } from '@/types';

interface RoomConfigurationProps {
  imageUrl: string;
  onConfiguration: (config: {
    roomType: RoomType;
    budget: number;
    style: DesignStyle;
  }) => void;
  onBack: () => void;
}

const ROOM_TYPES: { value: RoomType; label: string; icon: string }[] = [
  { value: 'living_room', label: 'Living Room', icon: '🛋️' },
  { value: 'bedroom', label: 'Bedroom', icon: '🛏️' },
  { value: 'kitchen', label: 'Kitchen', icon: '🍳' },
  { value: 'bathroom', label: 'Bathroom', icon: '🚿' },
  { value: 'dining_room', label: 'Dining Room', icon: '🍽️' },
  { value: 'office', label: 'Office', icon: '💼' },
  { value: 'nursery', label: 'Nursery', icon: '👶' },
  { value: 'guest_room', label: 'Guest Room', icon: '🏠' },
];

const DESIGN_STYLES: { value: DesignStyle; label: string; description: string }[] = [
  { value: 'modern', label: 'Modern', description: 'Clean lines, minimal clutter, neutral colors' },
  { value: 'minimalist', label: 'Minimalist', description: 'Less is more, functional, simple' },
  { value: 'boho', label: 'Bohemian', description: 'Eclectic, colorful, artistic' },
  { value: 'industrial', label: 'Industrial', description: 'Raw materials, exposed elements' },
  { value: 'scandinavian', label: 'Scandinavian', description: 'Light woods, cozy, functional' },
  { value: 'traditional', label: 'Traditional', description: 'Classic, elegant, timeless' },
  { value: 'contemporary', label: 'Contemporary', description: 'Current trends, sophisticated' },
  { value: 'rustic', label: 'Rustic', description: 'Natural materials, warm, cozy' },
];

const BUDGET_RANGES = [
  { min: 500, max: 1000, label: '$500 - $1,000' },
  { min: 1000, max: 2500, label: '$1,000 - $2,500' },
  { min: 2500, max: 5000, label: '$2,500 - $5,000' },
  { min: 5000, max: 10000, label: '$5,000 - $10,000' },
  { min: 10000, max: 25000, label: '$10,000+' },
];

export function RoomConfiguration({ imageUrl, onConfiguration, onBack }: RoomConfigurationProps) {
  const [roomType, setRoomType] = useState<RoomType | null>(null);
  const [style, setStyle] = useState<DesignStyle | null>(null);
  const [budget, setBudget] = useState<number | null>(null);
  const [customBudget, setCustomBudget] = useState('');
  const [useCustomBudget, setUseCustomBudget] = useState(false);

  const handleSubmit = () => {
    if (!roomType || !style || !budget) return;

    onConfiguration({
      roomType,
      budget,
      style,
    });
  };

  const isValid = roomType && style && budget;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Configure Your Room
        </h2>
        <p className="text-gray-600">
          Help us understand your space and preferences for better recommendations
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Preview */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Your Room</h3>
          <img
            src={imageUrl}
            alt="Room preview"
            className="w-full h-64 object-cover rounded-lg border border-gray-200"
          />
        </div>

        {/* Configuration Form */}
        <div className="space-y-6">
          {/* Room Type Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              What type of room is this?
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {ROOM_TYPES.map((type) => (
                <button
                  key={type.value}
                  onClick={() => setRoomType(type.value)}
                  className={`
                    p-3 rounded-lg border-2 text-left transition-colors
                    ${roomType === type.value
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{type.icon}</span>
                    <span className="font-medium">{type.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Design Style Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              What's your preferred style?
            </h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {DESIGN_STYLES.map((styleOption) => (
                <button
                  key={styleOption.value}
                  onClick={() => setStyle(styleOption.value)}
                  className={`
                    w-full p-3 rounded-lg border-2 text-left transition-colors
                    ${style === styleOption.value
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="font-medium">{styleOption.label}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {styleOption.description}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Budget Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              What's your budget?
            </h3>
            
            {!useCustomBudget ? (
              <div className="space-y-2">
                {BUDGET_RANGES.map((range) => (
                  <button
                    key={range.label}
                    onClick={() => setBudget(range.max)}
                    className={`
                      w-full p-3 rounded-lg border-2 text-left transition-colors
                      ${budget === range.max
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4" />
                      <span className="font-medium">{range.label}</span>
                    </div>
                  </button>
                ))}
                
                <button
                  onClick={() => setUseCustomBudget(true)}
                  className="w-full p-3 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 text-gray-600 transition-colors"
                >
                  Enter custom amount
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="number"
                    value={customBudget}
                    onChange={(e) => {
                      setCustomBudget(e.target.value);
                      setBudget(parseFloat(e.target.value) || 0);
                    }}
                    placeholder="Enter your budget"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={() => {
                    setUseCustomBudget(false);
                    setCustomBudget('');
                    setBudget(null);
                  }}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Choose from preset ranges
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>

        <button
          onClick={handleSubmit}
          disabled={!isValid}
          className={`
            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors
            ${isValid
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          <span>Analyze Room</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
