'use client';

import { useState } from 'react';
import { 
  ShoppingCart, 
  Download, 
  Share2, 
  ExternalLink, 
  Trash2, 
  Plus, 
  Minus,
  DollarSign,
  TrendingDown,
  AlertCircle
} from 'lucide-react';
import { FurnitureRecommendation } from '@/types';

interface ShoppingListProps {
  recommendations: FurnitureRecommendation[];
  budget: number;
  onItemClick?: (item: FurnitureRecommendation) => void;
}

interface ShoppingListItem {
  product: FurnitureRecommendation;
  quantity: number;
  priority: 'high' | 'medium' | 'low';
}

export function ShoppingList({ recommendations, budget, onItemClick }: ShoppingListProps) {
  const [items, setItems] = useState<ShoppingListItem[]>(
    recommendations.map((product, index) => ({
      product,
      quantity: 1,
      priority: index < 3 ? 'high' : index < 6 ? 'medium' : 'low'
    }))
  );
  const [isExporting, setIsExporting] = useState(false);
  const [shareLink, setShareLink] = useState<string | null>(null);

  const totalCost = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const budgetUtilization = (totalCost / budget) * 100;
  const remainingBudget = budget - totalCost;

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setItems(prev => prev.map(item => 
      item.product.id === productId 
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const removeItem = (productId: string) => {
    setItems(prev => prev.filter(item => item.product.id !== productId));
  };

  const updatePriority = (productId: string, priority: 'high' | 'medium' | 'low') => {
    setItems(prev => prev.map(item => 
      item.product.id === productId 
        ? { ...item, priority }
        : item
    ));
  };

  const handleAffiliateClick = async (product: FurnitureRecommendation) => {
    try {
      // Track the click
      await fetch('/api/shopping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'track-click',
          productId: product.id,
          retailer: product.retailer
        })
      });

      // Open affiliate link
      window.open(product.affiliateUrl || product.productUrl, '_blank');
      
      if (onItemClick) {
        onItemClick(product);
      }
    } catch (error) {
      console.error('Error tracking affiliate click:', error);
      // Still open the link even if tracking fails
      window.open(product.affiliateUrl || product.productUrl, '_blank');
    }
  };

  const exportToCSV = async () => {
    setIsExporting(true);
    try {
      const response = await fetch('/api/shopping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export-csv',
          shoppingList: {
            id: `list_${Date.now()}`,
            name: `Shopping List - ${new Date().toLocaleDateString()}`,
            items: items.map(item => ({
              id: `item_${item.product.id}`,
              product: item.product,
              quantity: item.quantity,
              priority: item.priority,
              addedAt: new Date()
            })),
            totalCost,
            budget,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `shopping-list-${Date.now()}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const generateShareLink = async () => {
    try {
      const response = await fetch('/api/shopping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate-share-link',
          shoppingList: {
            id: `list_${Date.now()}`,
            name: `Shopping List - ${new Date().toLocaleDateString()}`,
            items: items.map(item => ({
              id: `item_${item.product.id}`,
              product: item.product,
              quantity: item.quantity,
              priority: item.priority,
              addedAt: new Date()
            })),
            totalCost,
            budget,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        setShareLink(result.data.shareableLink);
        
        // Copy to clipboard
        await navigator.clipboard.writeText(result.data.shareableLink);
        alert('Share link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error generating share link:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <ShoppingCart className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">Shopping List</h2>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={exportToCSV}
            disabled={isExporting}
            className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
          >
            <Download className="w-4 h-4" />
            <span>{isExporting ? 'Exporting...' : 'Export CSV'}</span>
          </button>
          
          <button
            onClick={generateShareLink}
            className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </button>
        </div>
      </div>

      {/* Budget Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{items.length}</div>
            <div className="text-sm text-gray-600">Items</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900">${totalCost.toFixed(2)}</div>
            <div className="text-sm text-gray-600">Total Cost</div>
          </div>
          <div>
            <div className={`text-2xl font-bold ${budgetUtilization > 100 ? 'text-red-600' : 'text-gray-900'}`}>
              {budgetUtilization.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Budget Used</div>
          </div>
          <div>
            <div className={`text-2xl font-bold ${remainingBudget < 0 ? 'text-red-600' : 'text-green-600'}`}>
              ${Math.abs(remainingBudget).toFixed(2)}
            </div>
            <div className="text-sm text-gray-600">
              {remainingBudget < 0 ? 'Over Budget' : 'Remaining'}
            </div>
          </div>
        </div>
        
        {/* Budget Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                budgetUtilization > 100 ? 'bg-red-500' : 
                budgetUtilization > 80 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(budgetUtilization, 100)}%` }}
            />
          </div>
        </div>
      </div>

      {/* Budget Warning */}
      {budgetUtilization > 100 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 font-medium">Budget Exceeded</span>
          </div>
          <p className="text-red-700 mt-1">
            Your shopping list is ${(totalCost - budget).toFixed(2)} over budget. 
            Consider removing some items or choosing alternatives.
          </p>
        </div>
      )}

      {/* Shopping List Items */}
      <div className="space-y-4">
        {items.map((item) => (
          <div key={item.product.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start space-x-4">
              {/* Product Image */}
              <img
                src={item.product.imageUrl}
                alt={item.product.name}
                className="w-20 h-20 object-cover rounded-lg"
              />
              
              {/* Product Details */}
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900">{item.product.name}</h3>
                    <p className="text-gray-600 text-sm mt-1">{item.product.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-lg font-bold text-gray-900">
                        ${item.product.price}
                      </span>
                      <span className="text-sm text-gray-500 capitalize">
                        {item.product.retailer}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                        {item.priority}
                      </span>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => removeItem(item.product.id)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                {/* Quantity and Total */}
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Quantity:</span>
                    <button
                      onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                      className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                    >
                      <Minus className="w-3 h-3" />
                    </button>
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <button
                      onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                      className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                    >
                      <Plus className="w-3 h-3" />
                    </button>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <span className="font-semibold text-gray-900">
                      Total: ${(item.product.price * item.quantity).toFixed(2)}
                    </span>
                    <button
                      onClick={() => handleAffiliateClick(item.product)}
                      className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Buy Now</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {items.length === 0 && (
        <div className="text-center py-8">
          <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Your shopping list is empty</p>
        </div>
      )}
    </div>
  );
}
