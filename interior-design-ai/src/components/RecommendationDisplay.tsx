'use client';

import { useState, useEffect } from 'react';
import { ArrowLeft, RefreshCw, Heart, ExternalLink, ShoppingCart, Filter, Grid, List } from 'lucide-react';
import { Room, FurnitureRecommendation } from '@/types';

interface RecommendationDisplayProps {
  room: Room;
  onStartOver: () => void;
  onBack: () => void;
}

export function RecommendationDisplay({ room, onStartOver, onBack }: RecommendationDisplayProps) {
  const [recommendations, setRecommendations] = useState<FurnitureRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());
  const [summary, setSummary] = useState<any>(null);

  useEffect(() => {
    generateRecommendations();
  }, []);

  const generateRecommendations = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roomAnalysis: room.analysisResult,
          roomType: room.type,
          budget: room.budget,
          style: room.style,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate recommendations');
      }

      const result = await response.json();
      
      if (result.success) {
        setRecommendations(result.data.recommendations);
        setSummary(result.data.summary);
      } else {
        throw new Error(result.error || 'Failed to generate recommendations');
      }

    } catch (error) {
      console.error('Recommendations error:', error);
      // Mock recommendations for demo
      const mockRecommendations: FurnitureRecommendation[] = [
        {
          id: 'sofa-1',
          name: 'Modern 3-Seat Sofa',
          category: 'seating',
          description: 'Comfortable modern sofa with clean lines',
          price: 899,
          currency: 'USD',
          imageUrl: '/api/placeholder/400/300',
          productUrl: '#',
          retailer: 'ikea',
          dimensions: { width: 228, height: 83, depth: 95 },
          style: ['modern', 'contemporary'],
          placement: {
            suggestedPosition: { x: 200, y: 300 },
            zone: 'seating-area'
          },
          compatibility: {
            roomType: [room.type!],
            existingFurniture: [],
            styleMatch: 0.9
          }
        },
        {
          id: 'table-1',
          name: 'Glass Coffee Table',
          category: 'tables',
          description: 'Sleek glass coffee table with metal legs',
          price: 299,
          currency: 'USD',
          imageUrl: '/api/placeholder/400/300',
          productUrl: '#',
          retailer: 'amazon',
          dimensions: { width: 120, height: 45, depth: 60 },
          style: ['modern', 'contemporary'],
          placement: {
            suggestedPosition: { x: 280, y: 420 },
            zone: 'seating-area'
          },
          compatibility: {
            roomType: [room.type!],
            existingFurniture: [],
            styleMatch: 0.85
          }
        }
      ];
      setRecommendations(mockRecommendations);
      setSummary({
        totalItems: 2,
        totalCost: 1198,
        budget: room.budget,
        budgetUtilization: (1198 / room.budget!) * 100,
        remainingBudget: room.budget! - 1198
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleWishlist = (itemId: string) => {
    const newWishlist = new Set(wishlist);
    if (newWishlist.has(itemId)) {
      newWishlist.delete(itemId);
    } else {
      newWishlist.add(itemId);
    }
    setWishlist(newWishlist);
  };

  const categories = ['all', ...Array.from(new Set(recommendations.map(r => r.category)))];
  const filteredRecommendations = selectedCategory === 'all' 
    ? recommendations 
    : recommendations.filter(r => r.category === selectedCategory);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Generating Recommendations
          </h3>
          <p className="text-gray-600">
            Finding the perfect furniture for your space...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Your Personalized Recommendations
        </h2>
        <p className="text-gray-600">
          Based on your {room.type?.replace('_', ' ')} in {room.style} style with a ${room.budget} budget
        </p>
      </div>

      {/* Summary */}
      {summary && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-900">{summary.totalItems}</div>
              <div className="text-sm text-blue-700">Items</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-900">${summary.totalCost}</div>
              <div className="text-sm text-blue-700">Total Cost</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-900">{Math.round(summary.budgetUtilization)}%</div>
              <div className="text-sm text-blue-700">Budget Used</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-900">${summary.remainingBudget}</div>
              <div className="text-sm text-blue-700">Remaining</div>
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        {/* Category Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Recommendations Grid/List */}
      <div className={`
        ${viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
          : 'space-y-4'
        }
      `}>
        {filteredRecommendations.map((item) => (
          <div
            key={item.id}
            className={`
              bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow
              ${viewMode === 'list' ? 'flex' : ''}
            `}
          >
            {/* Image */}
            <div className={`${viewMode === 'list' ? 'w-48 flex-shrink-0' : 'aspect-square'} bg-gray-100`}>
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="p-4 flex-1">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-gray-900 line-clamp-2">{item.name}</h3>
                <button
                  onClick={() => toggleWishlist(item.id)}
                  className={`p-1 rounded-full ${
                    wishlist.has(item.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${wishlist.has(item.id) ? 'fill-current' : ''}`} />
                </button>
              </div>

              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{item.description}</p>

              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl font-bold text-gray-900">${item.price}</span>
                <span className="text-sm text-gray-500 capitalize">{item.retailer}</span>
              </div>

              <div className="flex items-center space-x-2 mb-3">
                {item.style.slice(0, 2).map((style) => (
                  <span
                    key={style}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {style}
                  </span>
                ))}
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => window.open(item.productUrl, '_blank')}
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>View Product</span>
                </button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <ShoppingCart className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>

        <button
          onClick={onStartOver}
          className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Start New Room</span>
        </button>
      </div>
    </div>
  );
}
