'use client';

import React, { useState, useRef, useEffect } from 'react';
import { FurnitureRecommendation } from '@/types';
import { Wand2, RotateCcw, Download, Eye, EyeOff } from 'lucide-react';
import { getFallbackImage } from '@/utils/image-helpers';

interface AIFurniturePlacementProps {
  roomImage: string;
  recommendations: FurnitureRecommendation[];
  onPlacementUpdate?: (placements: FurniturePlacement[]) => void;
}

interface FurniturePlacement {
  id: string;
  furnitureId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  scale: number;
  visible: boolean;
}

export function AIFurniturePlacement({ 
  roomImage, 
  recommendations, 
  onPlacementUpdate 
}: AIFurniturePlacementProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [placements, setPlacements] = useState<FurniturePlacement[]>([]);
  const [selectedPlacement, setSelectedPlacement] = useState<string | null>(null);
  const [isAutoPlacing, setIsAutoPlacing] = useState(false);
  const [roomImageLoaded, setRoomImageLoaded] = useState(false);

  // Auto-generate furniture placements using AI
  const generateAIPlacements = async () => {
    setIsAutoPlacing(true);
    
    try {
      // Simulate AI analysis delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newPlacements: FurniturePlacement[] = recommendations.slice(0, 5).map((rec, index) => {
        // Smart placement based on furniture type and room analysis
        const placement = calculateOptimalPlacement(rec, index);
        return {
          id: `placement-${rec.id}-${Date.now()}`,
          furnitureId: rec.id,
          ...placement,
          visible: true
        };
      });
      
      setPlacements(newPlacements);
      onPlacementUpdate?.(newPlacements);
    } catch (error) {
      console.error('Error generating AI placements:', error);
    } finally {
      setIsAutoPlacing(false);
    }
  };

  // Calculate optimal placement based on furniture type and room layout
  const calculateOptimalPlacement = (furniture: FurnitureRecommendation, index: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 100, y: 100, width: 100, height: 100, rotation: 0, scale: 1 };

    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Define placement zones based on furniture category
    const placementZones = {
      seating: { x: 0.2, y: 0.6, width: 0.3, height: 0.25 },
      tables: { x: 0.4, y: 0.7, width: 0.2, height: 0.15 },
      electronics: { x: 0.1, y: 0.3, width: 0.4, height: 0.2 },
      storage: { x: 0.7, y: 0.4, width: 0.25, height: 0.3 },
      lighting: { x: 0.8, y: 0.2, width: 0.15, height: 0.15 },
      decor: { x: 0.5 + (index * 0.1), y: 0.2, width: 0.1, height: 0.1 }
    };

    const zone = placementZones[furniture.category as keyof typeof placementZones] || placementZones.decor;
    
    // Add some randomness to avoid overlapping
    const randomOffset = index * 0.05;
    
    return {
      x: (zone.x + randomOffset) * canvasWidth,
      y: (zone.y + randomOffset) * canvasHeight,
      width: zone.width * canvasWidth,
      height: zone.height * canvasHeight,
      rotation: 0,
      scale: 1
    };
  };

  // Draw the room with furniture overlays
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !roomImageLoaded) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw room image
    const roomImg = new Image();
    roomImg.onload = () => {
      ctx.drawImage(roomImg, 0, 0, canvas.width, canvas.height);
      
      // Draw furniture placements
      placements.forEach(placement => {
        if (!placement.visible) return;
        
        const furniture = recommendations.find(r => r.id === placement.furnitureId);
        if (!furniture) return;

        drawFurnitureOverlay(ctx, placement, furniture);
      });
    };
    roomImg.src = roomImage;
  };

  // Draw furniture overlay on canvas
  const drawFurnitureOverlay = (
    ctx: CanvasRenderingContext2D, 
    placement: FurniturePlacement, 
    furniture: FurnitureRecommendation
  ) => {
    ctx.save();
    
    // Apply transformations
    ctx.translate(placement.x + placement.width / 2, placement.y + placement.height / 2);
    ctx.rotate((placement.rotation * Math.PI) / 180);
    ctx.scale(placement.scale, placement.scale);
    
    // Draw furniture representation
    const isSelected = selectedPlacement === placement.id;
    
    // Draw furniture image if available
    const furnitureImg = new Image();
    furnitureImg.crossOrigin = 'anonymous';
    furnitureImg.onload = () => {
      ctx.globalAlpha = 0.8;
      ctx.drawImage(
        furnitureImg,
        -placement.width / 2,
        -placement.height / 2,
        placement.width,
        placement.height
      );
      ctx.globalAlpha = 1;

      // Draw selection border
      if (isSelected) {
        ctx.strokeStyle = '#3B82F6';
        ctx.lineWidth = 3;
        ctx.strokeRect(-placement.width / 2, -placement.height / 2, placement.width, placement.height);
      }
    };
    furnitureImg.onerror = () => {
      // Use fallback image
      const fallbackImg = new Image();
      fallbackImg.crossOrigin = 'anonymous';
      fallbackImg.onload = () => {
        ctx.globalAlpha = 0.8;
        ctx.drawImage(
          fallbackImg,
          -placement.width / 2,
          -placement.height / 2,
          placement.width,
          placement.height
        );
        ctx.globalAlpha = 1;

        if (isSelected) {
          ctx.strokeStyle = '#3B82F6';
          ctx.lineWidth = 3;
          ctx.strokeRect(-placement.width / 2, -placement.height / 2, placement.width, placement.height);
        }
      };
      fallbackImg.src = getFallbackImage(furniture.category, furniture.name);
    };
    
    // Fallback: draw colored rectangle with label
    ctx.fillStyle = isSelected ? '#3B82F6' : '#6B7280';
    ctx.globalAlpha = 0.3;
    ctx.fillRect(-placement.width / 2, -placement.height / 2, placement.width, placement.height);
    ctx.globalAlpha = 1;
    
    // Draw furniture label
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(furniture.name.substring(0, 20), 0, 0);
    
    // Try original image first, fallback handled in onerror
    furnitureImg.src = furniture.imageUrl;
    
    ctx.restore();
  };

  // Toggle furniture visibility
  const toggleFurnitureVisibility = (placementId: string) => {
    setPlacements(prev => prev.map(p => 
      p.id === placementId ? { ...p, visible: !p.visible } : p
    ));
  };

  // Download the composed image
  const downloadComposedImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = 'room-design.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  // Reset all placements
  const resetPlacements = () => {
    setPlacements([]);
    setSelectedPlacement(null);
  };

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setRoomImageLoaded(true);
      const canvas = canvasRef.current;
      if (canvas) {
        canvas.width = 800;
        canvas.height = 600;
      }
    };
    img.src = roomImage;
  }, [roomImage]);

  useEffect(() => {
    drawCanvas();
  }, [placements, selectedPlacement, roomImageLoaded]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900">AI Furniture Placement</h3>
        <div className="flex space-x-2">
          <button
            onClick={generateAIPlacements}
            disabled={isAutoPlacing}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Wand2 className="w-4 h-4" />
            <span>{isAutoPlacing ? 'Placing...' : 'Auto Place'}</span>
          </button>
          <button
            onClick={resetPlacements}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          <button
            onClick={downloadComposedImage}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <Download className="w-4 h-4" />
            <span>Download</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Canvas Area */}
        <div className="lg:col-span-3">
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              className="w-full h-auto max-w-full"
              style={{ maxHeight: '600px' }}
            />
          </div>
        </div>

        {/* Furniture Controls */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Placed Furniture</h4>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {placements.map(placement => {
              const furniture = recommendations.find(r => r.id === placement.furnitureId);
              if (!furniture) return null;

              return (
                <div
                  key={placement.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedPlacement === placement.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedPlacement(
                    selectedPlacement === placement.id ? null : placement.id
                  )}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {furniture.name}
                      </p>
                      <p className="text-xs text-gray-500">${furniture.price}</p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFurnitureVisibility(placement.id);
                      }}
                      className="ml-2 p-1 text-gray-400 hover:text-gray-600"
                    >
                      {placement.visible ? (
                        <Eye className="w-4 h-4" />
                      ) : (
                        <EyeOff className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {placements.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Wand2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Click "Auto Place" to add furniture to your room</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
