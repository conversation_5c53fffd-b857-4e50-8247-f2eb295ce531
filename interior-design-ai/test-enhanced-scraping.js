// Test script for enhanced anti-bot scraping
const { IkeaScraper } = require('./src/lib/scrapers/ikea-scraper');

async function testEnhancedScraping() {
  console.log('🚀 Testing enhanced anti-bot scraping...');
  
  const scraper = new IkeaScraper();
  
  try {
    console.log('\n📦 Testing IKEA scraper with anti-bot measures...');
    
    // Test scraping a small number of products
    const products = await scraper.scrapeProducts('seating', 3);
    
    console.log(`\n✅ Successfully scraped ${products.length} products!`);
    
    products.forEach((product, index) => {
      console.log(`\n📋 Product ${index + 1}:`);
      console.log(`   Name: ${product.name}`);
      console.log(`   Price: ${product.currency} ${product.price}`);
      console.log(`   Category: ${product.category}`);
      console.log(`   Brand: ${product.brand}`);
      console.log(`   In Stock: ${product.inStock}`);
      console.log(`   URL: ${product.productUrl}`);
      console.log(`   Image: ${product.imageUrl.substring(0, 80)}...`);
    });
    
    if (products.length > 0) {
      console.log('\n🎉 Anti-bot measures are working! Real data scraped successfully.');
    } else {
      console.log('\n⚠️  No products scraped. May need further anti-bot improvements.');
    }
    
  } catch (error) {
    console.error('\n❌ Enhanced scraping test failed:', error.message);
    
    if (error.message.includes('timeout')) {
      console.log('💡 Suggestion: The site may be slow. Try increasing timeout values.');
    } else if (error.message.includes('blocked') || error.message.includes('captcha')) {
      console.log('💡 Suggestion: Anti-bot detection triggered. May need proxy rotation.');
    } else {
      console.log('💡 Suggestion: Check network connection and site availability.');
    }
  } finally {
    await scraper.cleanup();
    console.log('\n🧹 Cleanup completed');
  }
}

// Run the test
testEnhancedScraping().catch(console.error);
