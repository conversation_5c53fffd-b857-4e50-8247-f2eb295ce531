"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-selector";
exports.ids = ["vendor-chunks/file-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js":
/*!*****************************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file-selector.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* binding */ fromEvent)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file */ \"(ssr)/./node_modules/file-selector/dist/es2015/file.js\");\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nfunction fromEvent(evt) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9maWxlLXNlbGVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUNNO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RkFBNkYsTUFBTTtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsZ0RBQVM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxxREFBYztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGdEQUFTO0FBQ3BCO0FBQ0EsaUNBQWlDLHFEQUFjO0FBQy9DLEtBQUs7QUFDTDtBQUNBO0FBQ0EsV0FBVyxnREFBUztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHFEQUFjO0FBQ3ZDLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxnREFBUztBQUNwQjtBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLE1BQU07QUFDekM7QUFDQTtBQUNBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQSx1QkFBdUIscURBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsTUFBTTtBQUNyQztBQUNBLG9CQUFvQixxREFBYztBQUNsQztBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxXQUFXLGdEQUFTO0FBQ3BCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxnREFBUztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZ0RBQVM7QUFDcEI7QUFDQTtBQUNBLDRCQUE0QixxREFBYztBQUMxQztBQUNBLGFBQWE7QUFDYjtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL25vZGVfbW9kdWxlcy9maWxlLXNlbGVjdG9yL2Rpc3QvZXMyMDE1L2ZpbGUtc2VsZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19hd2FpdGVyIH0gZnJvbSBcInRzbGliXCI7XG5pbXBvcnQgeyB0b0ZpbGVXaXRoUGF0aCB9IGZyb20gJy4vZmlsZSc7XG5jb25zdCBGSUxFU19UT19JR05PUkUgPSBbXG4gICAgLy8gVGh1bWJuYWlsIGNhY2hlIGZpbGVzIGZvciBtYWNPUyBhbmQgV2luZG93c1xuICAgICcuRFNfU3RvcmUnLCAvLyBtYWNPc1xuICAgICdUaHVtYnMuZGInIC8vIFdpbmRvd3Ncbl07XG4vKipcbiAqIENvbnZlcnQgYSBEcmFnRXZlbnQncyBEYXRhVHJhc2ZlciBvYmplY3QgdG8gYSBsaXN0IG9mIEZpbGUgb2JqZWN0c1xuICogTk9URTogSWYgc29tZSBvZiB0aGUgaXRlbXMgYXJlIGZvbGRlcnMsXG4gKiBldmVyeXRoaW5nIHdpbGwgYmUgZmxhdHRlbmVkIGFuZCBwbGFjZWQgaW4gdGhlIHNhbWUgbGlzdCBidXQgdGhlIHBhdGhzIHdpbGwgYmUga2VwdCBhcyBhIHtwYXRofSBwcm9wZXJ0eS5cbiAqXG4gKiBFWFBFUklNRU5UQUw6IEEgbGlzdCBvZiBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRmlsZVN5c3RlbUhhbmRsZSBvYmplY3RzIGNhbiBhbHNvIGJlIHBhc3NlZCBhcyBhbiBhcmdcbiAqIGFuZCBhIGxpc3Qgb2YgRmlsZSBvYmplY3RzIHdpbGwgYmUgcmV0dXJuZWQuXG4gKlxuICogQHBhcmFtIGV2dFxuICovXG5leHBvcnQgZnVuY3Rpb24gZnJvbUV2ZW50KGV2dCkge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIGlmIChpc09iamVjdChldnQpICYmIGlzRGF0YVRyYW5zZmVyKGV2dC5kYXRhVHJhbnNmZXIpKSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0RGF0YVRyYW5zZmVyRmlsZXMoZXZ0LmRhdGFUcmFuc2ZlciwgZXZ0LnR5cGUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGlzQ2hhbmdlRXZ0KGV2dCkpIHtcbiAgICAgICAgICAgIHJldHVybiBnZXRJbnB1dEZpbGVzKGV2dCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShldnQpICYmIGV2dC5ldmVyeShpdGVtID0+ICdnZXRGaWxlJyBpbiBpdGVtICYmIHR5cGVvZiBpdGVtLmdldEZpbGUgPT09ICdmdW5jdGlvbicpKSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0RnNIYW5kbGVGaWxlcyhldnQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBbXTtcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIGlzRGF0YVRyYW5zZmVyKHZhbHVlKSB7XG4gICAgcmV0dXJuIGlzT2JqZWN0KHZhbHVlKTtcbn1cbmZ1bmN0aW9uIGlzQ2hhbmdlRXZ0KHZhbHVlKSB7XG4gICAgcmV0dXJuIGlzT2JqZWN0KHZhbHVlKSAmJiBpc09iamVjdCh2YWx1ZS50YXJnZXQpO1xufVxuZnVuY3Rpb24gaXNPYmplY3Qodikge1xuICAgIHJldHVybiB0eXBlb2YgdiA9PT0gJ29iamVjdCcgJiYgdiAhPT0gbnVsbDtcbn1cbmZ1bmN0aW9uIGdldElucHV0RmlsZXMoZXZ0KSB7XG4gICAgcmV0dXJuIGZyb21MaXN0KGV2dC50YXJnZXQuZmlsZXMpLm1hcChmaWxlID0+IHRvRmlsZVdpdGhQYXRoKGZpbGUpKTtcbn1cbi8vIEVlIGV4cGVjdCBlYWNoIGhhbmRsZSB0byBiZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRmlsZVN5c3RlbUZpbGVIYW5kbGVcbmZ1bmN0aW9uIGdldEZzSGFuZGxlRmlsZXMoaGFuZGxlcykge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIGNvbnN0IGZpbGVzID0geWllbGQgUHJvbWlzZS5hbGwoaGFuZGxlcy5tYXAoaCA9PiBoLmdldEZpbGUoKSkpO1xuICAgICAgICByZXR1cm4gZmlsZXMubWFwKGZpbGUgPT4gdG9GaWxlV2l0aFBhdGgoZmlsZSkpO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gZ2V0RGF0YVRyYW5zZmVyRmlsZXMoZHQsIHR5cGUpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAvLyBJRTExIGRvZXMgbm90IHN1cHBvcnQgZGF0YVRyYW5zZmVyLml0ZW1zXG4gICAgICAgIC8vIFNlZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRGF0YVRyYW5zZmVyL2l0ZW1zI0Jyb3dzZXJfY29tcGF0aWJpbGl0eVxuICAgICAgICBpZiAoZHQuaXRlbXMpIHtcbiAgICAgICAgICAgIGNvbnN0IGl0ZW1zID0gZnJvbUxpc3QoZHQuaXRlbXMpXG4gICAgICAgICAgICAgICAgLmZpbHRlcihpdGVtID0+IGl0ZW0ua2luZCA9PT0gJ2ZpbGUnKTtcbiAgICAgICAgICAgIC8vIEFjY29yZGluZyB0byBodHRwczovL2h0bWwuc3BlYy53aGF0d2cub3JnL211bHRpcGFnZS9kbmQuaHRtbCNkbmRldmVudHMsXG4gICAgICAgICAgICAvLyBvbmx5ICdkcmFnc3RhcnQnIGFuZCAnZHJvcCcgaGFzIGFjY2VzcyB0byB0aGUgZGF0YSAoc291cmNlIG5vZGUpXG4gICAgICAgICAgICBpZiAodHlwZSAhPT0gJ2Ryb3AnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW1zO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZmlsZXMgPSB5aWVsZCBQcm9taXNlLmFsbChpdGVtcy5tYXAodG9GaWxlUHJvbWlzZXMpKTtcbiAgICAgICAgICAgIHJldHVybiBub0lnbm9yZWRGaWxlcyhmbGF0dGVuKGZpbGVzKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5vSWdub3JlZEZpbGVzKGZyb21MaXN0KGR0LmZpbGVzKVxuICAgICAgICAgICAgLm1hcChmaWxlID0+IHRvRmlsZVdpdGhQYXRoKGZpbGUpKSk7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBub0lnbm9yZWRGaWxlcyhmaWxlcykge1xuICAgIHJldHVybiBmaWxlcy5maWx0ZXIoZmlsZSA9PiBGSUxFU19UT19JR05PUkUuaW5kZXhPZihmaWxlLm5hbWUpID09PSAtMSk7XG59XG4vLyBJRTExIGRvZXMgbm90IHN1cHBvcnQgQXJyYXkuZnJvbSgpXG4vLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9KYXZhU2NyaXB0L1JlZmVyZW5jZS9HbG9iYWxfT2JqZWN0cy9BcnJheS9mcm9tI0Jyb3dzZXJfY29tcGF0aWJpbGl0eVxuLy8gaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0ZpbGVMaXN0XG4vLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRGF0YVRyYW5zZmVySXRlbUxpc3RcbmZ1bmN0aW9uIGZyb21MaXN0KGl0ZW1zKSB7XG4gICAgaWYgKGl0ZW1zID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gICAgY29uc3QgZmlsZXMgPSBbXTtcbiAgICAvLyB0c2xpbnQ6ZGlzYWJsZTogcHJlZmVyLWZvci1vZlxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaXRlbXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgZmlsZSA9IGl0ZW1zW2ldO1xuICAgICAgICBmaWxlcy5wdXNoKGZpbGUpO1xuICAgIH1cbiAgICByZXR1cm4gZmlsZXM7XG59XG4vLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRGF0YVRyYW5zZmVySXRlbVxuZnVuY3Rpb24gdG9GaWxlUHJvbWlzZXMoaXRlbSkge1xuICAgIGlmICh0eXBlb2YgaXRlbS53ZWJraXRHZXRBc0VudHJ5ICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiBmcm9tRGF0YVRyYW5zZmVySXRlbShpdGVtKTtcbiAgICB9XG4gICAgY29uc3QgZW50cnkgPSBpdGVtLndlYmtpdEdldEFzRW50cnkoKTtcbiAgICAvLyBTYWZhcmkgc3VwcG9ydHMgZHJvcHBpbmcgYW4gaW1hZ2Ugbm9kZSBmcm9tIGEgZGlmZmVyZW50IHdpbmRvdyBhbmQgY2FuIGJlIHJldHJpZXZlZCB1c2luZ1xuICAgIC8vIHRoZSBEYXRhVHJhbnNmZXJJdGVtLmdldEFzRmlsZSgpIEFQSVxuICAgIC8vIE5PVEU6IEZpbGVTeXN0ZW1FbnRyeS5maWxlKCkgdGhyb3dzIGlmIHRyeWluZyB0byBnZXQgdGhlIGZpbGVcbiAgICBpZiAoZW50cnkgJiYgZW50cnkuaXNEaXJlY3RvcnkpIHtcbiAgICAgICAgcmV0dXJuIGZyb21EaXJFbnRyeShlbnRyeSk7XG4gICAgfVxuICAgIHJldHVybiBmcm9tRGF0YVRyYW5zZmVySXRlbShpdGVtLCBlbnRyeSk7XG59XG5mdW5jdGlvbiBmbGF0dGVuKGl0ZW1zKSB7XG4gICAgcmV0dXJuIGl0ZW1zLnJlZHVjZSgoYWNjLCBmaWxlcykgPT4gW1xuICAgICAgICAuLi5hY2MsXG4gICAgICAgIC4uLihBcnJheS5pc0FycmF5KGZpbGVzKSA/IGZsYXR0ZW4oZmlsZXMpIDogW2ZpbGVzXSlcbiAgICBdLCBbXSk7XG59XG5mdW5jdGlvbiBmcm9tRGF0YVRyYW5zZmVySXRlbShpdGVtLCBlbnRyeSkge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gYSBzZWN1cmUgY29udGV4dDsgZHVlIHRvIGEgYnVnIGluIENocm9tZSAoYXMgZmFyIGFzIHdlIGtub3cpXG4gICAgICAgIC8vIHRoZSBicm93c2VyIGNyYXNoZXMgd2hlbiBjYWxsaW5nIHRoaXMgQVBJICh5ZXQgdG8gYmUgY29uZmlybWVkIGFzIGEgY29uc2lzdGVudCBiZWhhdmlvdXIpLlxuICAgICAgICAvL1xuICAgICAgICAvLyBTZWU6XG4gICAgICAgIC8vIC0gaHR0cHM6Ly9pc3N1ZXMuY2hyb21pdW0ub3JnL2lzc3Vlcy80MDE4NjI0MlxuICAgICAgICAvLyAtIGh0dHBzOi8vZ2l0aHViLmNvbS9yZWFjdC1kcm9wem9uZS9yZWFjdC1kcm9wem9uZS9pc3N1ZXMvMTM5N1xuICAgICAgICBpZiAoZ2xvYmFsVGhpcy5pc1NlY3VyZUNvbnRleHQgJiYgdHlwZW9mIGl0ZW0uZ2V0QXNGaWxlU3lzdGVtSGFuZGxlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICBjb25zdCBoID0geWllbGQgaXRlbS5nZXRBc0ZpbGVTeXN0ZW1IYW5kbGUoKTtcbiAgICAgICAgICAgIGlmIChoID09PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGAke2l0ZW19IGlzIG5vdCBhIEZpbGVgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEl0IHNlZW1zIHRoYXQgdGhlIGhhbmRsZSBjYW4gYmUgYHVuZGVmaW5lZGAgKHNlZSBodHRwczovL2dpdGh1Yi5jb20vcmVhY3QtZHJvcHpvbmUvZmlsZS1zZWxlY3Rvci9pc3N1ZXMvMTIwKSxcbiAgICAgICAgICAgIC8vIHNvIHdlIGNoZWNrIGlmIGl0IGlzbid0OyBpZiBpdCBpcywgdGhlIGNvZGUgcGF0aCBjb250aW51ZXMgdG8gdGhlIG5leHQgQVBJIChgZ2V0QXNGaWxlYCkuXG4gICAgICAgICAgICBpZiAoaCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmlsZSA9IHlpZWxkIGguZ2V0RmlsZSgpO1xuICAgICAgICAgICAgICAgIGZpbGUuaGFuZGxlID0gaDtcbiAgICAgICAgICAgICAgICByZXR1cm4gdG9GaWxlV2l0aFBhdGgoZmlsZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZmlsZSA9IGl0ZW0uZ2V0QXNGaWxlKCk7XG4gICAgICAgIGlmICghZmlsZSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGAke2l0ZW19IGlzIG5vdCBhIEZpbGVgKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBmd3AgPSB0b0ZpbGVXaXRoUGF0aChmaWxlLCAoX2EgPSBlbnRyeSA9PT0gbnVsbCB8fCBlbnRyeSA9PT0gdm9pZCAwID8gdm9pZCAwIDogZW50cnkuZnVsbFBhdGgpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHVuZGVmaW5lZCk7XG4gICAgICAgIHJldHVybiBmd3A7XG4gICAgfSk7XG59XG4vLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRmlsZVN5c3RlbUVudHJ5XG5mdW5jdGlvbiBmcm9tRW50cnkoZW50cnkpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gZW50cnkuaXNEaXJlY3RvcnkgPyBmcm9tRGlyRW50cnkoZW50cnkpIDogZnJvbUZpbGVFbnRyeShlbnRyeSk7XG4gICAgfSk7XG59XG4vLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvRmlsZVN5c3RlbURpcmVjdG9yeUVudHJ5XG5mdW5jdGlvbiBmcm9tRGlyRW50cnkoZW50cnkpIHtcbiAgICBjb25zdCByZWFkZXIgPSBlbnRyeS5jcmVhdGVSZWFkZXIoKTtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBjb25zdCBlbnRyaWVzID0gW107XG4gICAgICAgIGZ1bmN0aW9uIHJlYWRFbnRyaWVzKCkge1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0ZpbGVTeXN0ZW1EaXJlY3RvcnlFbnRyeS9jcmVhdGVSZWFkZXJcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9GaWxlU3lzdGVtRGlyZWN0b3J5UmVhZGVyL3JlYWRFbnRyaWVzXG4gICAgICAgICAgICByZWFkZXIucmVhZEVudHJpZXMoKGJhdGNoKSA9PiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICAgICAgaWYgKCFiYXRjaC5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gRG9uZSByZWFkaW5nIGRpcmVjdG9yeVxuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZXMgPSB5aWVsZCBQcm9taXNlLmFsbChlbnRyaWVzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmUoZmlsZXMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlamVjdChlcnIpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVtcyA9IFByb21pc2UuYWxsKGJhdGNoLm1hcChmcm9tRW50cnkpKTtcbiAgICAgICAgICAgICAgICAgICAgZW50cmllcy5wdXNoKGl0ZW1zKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gQ29udGludWUgcmVhZGluZ1xuICAgICAgICAgICAgICAgICAgICByZWFkRW50cmllcygpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pLCAoZXJyKSA9PiB7XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZWFkRW50cmllcygpO1xuICAgIH0pO1xufVxuLy8gaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQVBJL0ZpbGVTeXN0ZW1GaWxlRW50cnlcbmZ1bmN0aW9uIGZyb21GaWxlRW50cnkoZW50cnkpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgZW50cnkuZmlsZSgoZmlsZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZ3cCA9IHRvRmlsZVdpdGhQYXRoKGZpbGUsIGVudHJ5LmZ1bGxQYXRoKTtcbiAgICAgICAgICAgICAgICByZXNvbHZlKGZ3cCk7XG4gICAgICAgICAgICB9LCAoZXJyKSA9PiB7XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1maWxlLXNlbGVjdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file.js":
/*!********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_MIME_TYPES: () => (/* binding */ COMMON_MIME_TYPES),\n/* harmony export */   toFileWithPath: () => (/* binding */ toFileWithPath)\n/* harmony export */ });\nconst COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nfunction toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* reexport safe */ _file_selector__WEBPACK_IMPORTED_MODULE_0__.fromEvent)\n/* harmony export */ });\n/* harmony import */ var _file_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUM1QyIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9NeSBiaWcgaWRlYS9pbnRlcmlvci1kZXNpZ24tYWkvbm9kZV9tb2R1bGVzL2ZpbGUtc2VsZWN0b3IvZGlzdC9lczIwMTUvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZnJvbUV2ZW50IH0gZnJvbSAnLi9maWxlLXNlbGVjdG9yJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/index.js\n");

/***/ })

};
;