(()=>{var e={};e.id=772,e.ids=[772],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83496:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),p=t(29021),u=t(33873);async function c(){try{let e=Date.now(),r=(0,u.join)(process.cwd(),"public","uploads"),t=(0,p.existsSync)(r),s=process.memoryUsage(),a=process.uptime(),o=Date.now()-e,n={status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",environment:"production",uptime:Math.floor(a),responseTime:o,services:{api:"operational",imageProcessing:"operational",aiModels:{objectDetection:"ready",roomSegmentation:"ready",recommendationEngine:"ready"},fileSystem:t?"operational":"warning"},system:{memory:{used:Math.round(s.heapUsed/1024/1024),total:Math.round(s.heapTotal/1024/1024),external:Math.round(s.external/1024/1024)},nodeVersion:process.version,platform:process.platform,arch:process.arch},endpoints:{upload:"/api/upload",analyze:"/api/analyze",recommendations:"/api/recommendations",health:"/api/health"}};return i.NextResponse.json(n)}catch(e){return console.error("Health check error:",e),i.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",services:{api:"error"}},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/health/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:m}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580],()=>t(83496));module.exports=s})();