globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/shopping/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"1007":{"*":{"id":"21376","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":4147,"name":"*","chunks":["177","static/chunks/app/layout-9914f7f137e9d414.js"],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":8489,"name":"*","chunks":["177","static/chunks/app/layout-9914f7f137e9d414.js"],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-9914f7f137e9d414.js"],"async":false},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx":{"id":1007,"name":"*","chunks":["961","static/chunks/961-0c477365845af19f.js","974","static/chunks/app/page-4c201b6f49c246ce.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/":[],"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout":[{"inlined":false,"path":"static/css/18877f882ff7b729.css"}],"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page":[],"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/shopping/route":[]},"rscModuleMapping":{"347":{"*":{"id":"61135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"1007":{"*":{"id":"21204","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}