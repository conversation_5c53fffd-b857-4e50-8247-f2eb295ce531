(()=>{var e={};e.id=682,e.ids=[682],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17733:(e,t,r)=>{"use strict";r.d(t,{$y:()=>n,Kf:()=>s,WX:()=>i});var a=r(32190);class o extends Error{constructor(e,t=500,r){super(e),this.statusCode=t,this.code=r,this.name="APIError"}}function i(e,t=500){let r,n,s;e instanceof o?(r=e.message,n=e.statusCode,s=e.code):(r=e instanceof Error?e.message:e,n=t);let c={success:!1,error:r,...s&&{code:s}};return a.NextResponse.json(c,{status:n})}function n(e,t){let r={success:!0,data:e,...t&&{message:t}};return a.NextResponse.json(r)}function s(e,t){let r=t.filter(t=>void 0===e[t]||null===e[t]||""===e[t]);if(r.length>0)throw new o(`Missing required fields: ${r.join(", ")}`,400,"MISSING_FIELDS")}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60798:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>I,serverHooks:()=>P,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>C});var a={};r.r(a),r.d(a,{GET:()=>y,OPTIONS:()=>v,POST:()=>p});var o=r(96559),i=r(48088),n=r(37719),s=r(32190);class c{constructor(){this.affiliateConfig={amazon:{tag:process.env.AMAZON_AFFILIATE_TAG||"interiorai-20",accessKey:process.env.AMAZON_ACCESS_KEY,secretKey:process.env.AMAZON_SECRET_KEY},ikea:{partnerId:process.env.IKEA_PARTNER_ID||"interior-ai",apiKey:process.env.IKEA_API_KEY}}}createShoppingList(e,t,r){let a=e.map((e,t)=>({id:`item_${Date.now()}_${t}`,product:e,quantity:1,priority:t<3?"high":t<6?"medium":"low",addedAt:new Date,alternatives:[]})),o=a.reduce((e,t)=>e+t.product.price*t.quantity,0);return{id:`list_${Date.now()}`,name:`Room Design - ${new Date().toLocaleDateString()}`,items:a,totalCost:o,budget:t,createdAt:new Date,updatedAt:new Date,roomId:r}}generateAffiliateUrl(e){try{if("amazon"===e.retailer)return this.generateAmazonAffiliateUrl(e.productUrl);if("ikea"===e.retailer)return this.generateIkeaAffiliateUrl(e.productUrl);return e.productUrl}catch(t){return console.error("Error generating affiliate URL:",t),e.productUrl}}generateAmazonAffiliateUrl(e){try{let t=new URL(e);return t.searchParams.set("tag",this.affiliateConfig.amazon.tag),t.searchParams.set("linkCode","as2"),t.searchParams.set("camp","1789"),t.searchParams.set("creative","9325"),t.toString()}catch(t){return console.error("Error generating Amazon affiliate URL:",t),e}}generateIkeaAffiliateUrl(e){try{let t=new URL(e);return t.searchParams.set("partner",this.affiliateConfig.ikea.partnerId),t.searchParams.set("source","interior-ai"),t.toString()}catch(t){return console.error("Error generating IKEA affiliate URL:",t),e}}async trackAffiliateClick(e,t,r){try{let a={productId:e,retailer:t,userId:r||"anonymous",timestamp:new Date().toISOString(),userAgent:"server",referrer:""};console.log("Affiliate click tracked:",a)}catch(e){console.error("Error tracking affiliate click:",e)}}async getPriceComparison(e){return{amazon:{price:Math.floor(500*Math.random())+100,url:`https://amazon.com/search?k=${encodeURIComponent(e)}`},ikea:{price:Math.floor(400*Math.random())+80,url:`https://ikea.com/search?q=${encodeURIComponent(e)}`}}}async checkPriceAlerts(e){let t=[];for(let r of e)try{let e=await this.getCurrentPrice(r.productId),a={...r,currentPrice:e};e<=r.targetPrice&&r.isActive&&console.log(`Price alert triggered for product ${r.productId}: $${e}`),t.push(a)}catch(e){console.error(`Error checking price for product ${r.productId}:`,e),t.push(r)}return t}async getCurrentPrice(e){return Math.floor(1e3*Math.random())+50}exportShoppingListCSV(e){return[["Item Name","Category","Price","Quantity","Total","Retailer","Priority","Product URL"],...e.items.map(e=>[e.product.name,e.product.category,`$${e.product.price}`,e.quantity.toString(),`$${e.product.price*e.quantity}`,e.product.retailer,e.priority,this.generateAffiliateUrl(e.product)])].map(e=>e.map(e=>`"${e}"`).join(",")).join("\n")}calculateShoppingStats(e){let t={totalItems:e.items.length,totalCost:e.totalCost,budgetUtilization:e.totalCost/e.budget*100,remainingBudget:e.budget-e.totalCost,categoryBreakdown:{},retailerBreakdown:{},priorityBreakdown:{}};return e.items.forEach(e=>{let r=e.product.price*e.quantity;t.categoryBreakdown[e.product.category]||(t.categoryBreakdown[e.product.category]={count:0,cost:0}),t.categoryBreakdown[e.product.category].count+=e.quantity,t.categoryBreakdown[e.product.category].cost+=r,t.retailerBreakdown[e.product.retailer]||(t.retailerBreakdown[e.product.retailer]={count:0,cost:0}),t.retailerBreakdown[e.product.retailer].count+=e.quantity,t.retailerBreakdown[e.product.retailer].cost+=r,t.priorityBreakdown[e.priority]||(t.priorityBreakdown[e.priority]={count:0,cost:0}),t.priorityBreakdown[e.priority].count+=e.quantity,t.priorityBreakdown[e.priority].cost+=r}),t}generateShareableLink(e){let t=btoa(JSON.stringify({id:e.id,name:e.name,items:e.items.map(e=>({productId:e.product.id,quantity:e.quantity,priority:e.priority}))}));return`http://localhost:3001/shopping-list/shared?data=${t}`}}let l=new c;var u=r(17733);async function p(e){try{let t=await e.json(),{action:r}=t;switch(r){case"create-list":return d(t);case"track-click":return g(t);case"price-comparison":return f(t);case"export-csv":return h(t);case"generate-share-link":return m(t);default:return(0,u.WX)("Invalid action",400)}}catch(e){return console.error("Shopping API error:",e),(0,u.WX)(e)}}async function d(e){let{recommendations:t,budget:r,roomId:a,name:o}=e;if((0,u.Kf)(e,["recommendations","budget"]),!Array.isArray(t))return(0,u.WX)("Recommendations must be an array",400);let i=l.createShoppingList(t,r,a);o&&(i.name=o),i.items.forEach(e=>{e.product.affiliateUrl=l.generateAffiliateUrl(e.product)});let n=l.calculateShoppingStats(i);return(0,u.$y)({shoppingList:i,stats:n,shareableLink:l.generateShareableLink(i)})}async function g(e){let{productId:t,retailer:r,userId:a}=e;return(0,u.Kf)(e,["productId","retailer"]),await l.trackAffiliateClick(t,r,a),(0,u.$y)({message:"Click tracked successfully",productId:t,retailer:r,timestamp:new Date().toISOString()})}async function f(e){let{productName:t,productId:r}=e;if(!t&&!r)return(0,u.WX)("Product name or ID is required",400);let a=t||r,o=await l.getPriceComparison(a);return(0,u.$y)({searchTerm:a,priceComparison:o,timestamp:new Date().toISOString()})}async function h(e){let{shoppingList:t}=e;(0,u.Kf)(e,["shoppingList"]);let r=l.exportShoppingListCSV(t);return new s.NextResponse(r,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="shopping-list-${t.id}.csv"`}})}async function m(e){let{shoppingList:t}=e;(0,u.Kf)(e,["shoppingList"]);let r=l.generateShareableLink(t);return(0,u.$y)({shareableLink:r,expiresAt:new Date(Date.now()+2592e6).toISOString()})}async function y(e){try{let{searchParams:t}=new URL(e.url);switch(t.get("action")){case"affiliate-config":return A();case"price-alerts":return k(t);case"stats":return w(t);default:return(0,u.WX)("Invalid action parameter",400)}}catch(e){return console.error("Shopping GET API error:",e),(0,u.WX)(e)}}async function A(){let e={amazon:{tag:process.env.AMAZON_AFFILIATE_TAG||"interiorai-20",hasApiAccess:!!(process.env.AMAZON_ACCESS_KEY&&process.env.AMAZON_SECRET_KEY)},ikea:{partnerId:process.env.IKEA_PARTNER_ID||"interior-ai",hasApiAccess:!!process.env.IKEA_API_KEY},supportedRetailers:["amazon","ikea"],trackingEnabled:!0};return(0,u.$y)(e)}async function k(e){let t=[{productId:"ikea-kivik-sofa",targetPrice:500,currentPrice:599,isActive:!0,createdAt:new Date(Date.now()-6048e5)},{productId:"amazon-rivet-sofa",targetPrice:800,currentPrice:750,isActive:!0,createdAt:new Date(Date.now()-2592e5)}],r=await l.checkPriceAlerts(t);return(0,u.$y)({alerts:r,total:r.length,activeAlerts:r.filter(e=>e.isActive).length})}async function w(e){let t={period:e.get("period")||"30d",totalClicks:Math.floor(1e3*Math.random())+100,totalConversions:Math.floor(50*Math.random())+10,conversionRate:0,totalCommission:Math.floor(500*Math.random())+50,topProducts:[{productId:"ikea-kivik-sofa",clicks:45,conversions:3},{productId:"amazon-rivet-sofa",clicks:38,conversions:2},{productId:"ikea-lack-coffee-table",clicks:32,conversions:4}],retailerBreakdown:{amazon:{clicks:234,conversions:12,commission:156},ikea:{clicks:189,conversions:8,commission:89}}};return t.conversionRate=t.totalConversions/t.totalClicks*100,(0,u.$y)(t)}async function v(){return new s.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let I=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/shopping/route",pathname:"/api/shopping",filename:"route",bundlePath:"app/api/shopping/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/shopping/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:S,workUnitAsyncStorage:C,serverHooks:P}=I;function E(){return(0,n.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:C})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[243,580],()=>r(60798));module.exports=a})();