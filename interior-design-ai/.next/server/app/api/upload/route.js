(()=>{var e={};e.id=413,e.ids=[413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17084:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>h,serverHooks:()=>v,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>w,POST:()=>f});var o=r(96559),a=r(48088),i=r(37719),n=r(32190),u=r(9288),p=r.n(u),l=r(79748),d=r(33873),c=r(29021);let g=(0,d.join)(process.cwd(),"public","uploads"),m=["image/jpeg","image/png","image/webp"];async function x(){(0,c.existsSync)(g)||await (0,l.mkdir)(g,{recursive:!0})}async function f(e){try{let t=await e.formData(),r=t.get("image"),s=t.get("roomType"),o=t.get("budget"),a=t.get("style");if(!r)return n.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!m.includes(r.type))return n.NextResponse.json({success:!1,error:"Invalid file type. Only JPEG, PNG, and WebP are allowed."},{status:400});if(r.size>0xa00000)return n.NextResponse.json({success:!1,error:"File too large. Maximum size is 10MB."},{status:400});await x();let i=Date.now(),u=`room_${i}.jpg`,c=(0,d.join)(g,u),f=await r.arrayBuffer(),w=Buffer.from(f),h=await p()(w).resize(1024,768,{fit:"inside",withoutEnlargement:!0}).jpeg({quality:85,progressive:!0}).toBuffer();await (0,l.writeFile)(c,h);let j=`thumb_${i}.jpg`,y=(0,d.join)(g,j);await p()(w).resize(300,300,{fit:"cover",position:"center"}).jpeg({quality:80}).toFile(y);let v=await p()(w).metadata(),q={success:!0,data:{id:i.toString(),filename:u,thumbnailFilename:j,originalName:r.name,size:r.size,processedSize:h.length,dimensions:{width:v.width,height:v.height},format:v.format,url:`/uploads/${u}`,thumbnailUrl:`/uploads/${j}`,roomType:s||null,budget:o?parseFloat(o):null,style:a||null,uploadedAt:new Date().toISOString()}};return n.NextResponse.json(q)}catch(e){return console.error("Upload error:",e),n.NextResponse.json({success:!1,error:"Failed to process upload",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function w(){return n.NextResponse.json({success:!1,error:"Method not allowed. Use POST to upload images."},{status:405})}let h=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/upload/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:y,serverHooks:v}=h;function q(){return(0,i.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:y})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79748:e=>{"use strict";e.exports=require("fs/promises")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,580],()=>r(17084));module.exports=s})();