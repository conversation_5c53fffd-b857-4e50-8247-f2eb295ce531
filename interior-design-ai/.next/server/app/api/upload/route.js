/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/upload/route";
exports.ids = ["app/api/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/upload/route.ts */ \"(rsc)/./src/app/api/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/upload/route\",\n        pathname: \"/api/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/upload/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/upload/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/upload/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/upload/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_4__);\n// API endpoint for image upload and initial processing\n\n\n\n\n\nconst UPLOAD_DIR = (0,path__WEBPACK_IMPORTED_MODULE_3__.join)(process.cwd(), 'public', 'uploads');\nconst MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB\nconst ALLOWED_TYPES = [\n    'image/jpeg',\n    'image/png',\n    'image/webp'\n];\n// Ensure upload directory exists\nasync function ensureUploadDir() {\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_4__.existsSync)(UPLOAD_DIR)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_2__.mkdir)(UPLOAD_DIR, {\n            recursive: true\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const file = formData.get('image');\n        const roomType = formData.get('roomType');\n        const budget = formData.get('budget');\n        const style = formData.get('style');\n        // Validate file\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'No file provided'\n            }, {\n                status: 400\n            });\n        }\n        if (!ALLOWED_TYPES.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.'\n            }, {\n                status: 400\n            });\n        }\n        if (file.size > MAX_FILE_SIZE) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'File too large. Maximum size is 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        // Ensure upload directory exists\n        await ensureUploadDir();\n        // Generate unique filename\n        const timestamp = Date.now();\n        const filename = `room_${timestamp}.jpg`;\n        const filepath = (0,path__WEBPACK_IMPORTED_MODULE_3__.join)(UPLOAD_DIR, filename);\n        // Convert file to buffer\n        const bytes = await file.arrayBuffer();\n        const buffer = Buffer.from(bytes);\n        // Process image with Sharp\n        const processedBuffer = await sharp__WEBPACK_IMPORTED_MODULE_1___default()(buffer).resize(1024, 768, {\n            fit: 'inside',\n            withoutEnlargement: true\n        }).jpeg({\n            quality: 85,\n            progressive: true\n        }).toBuffer();\n        // Save processed image\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_2__.writeFile)(filepath, processedBuffer);\n        // Create thumbnail\n        const thumbnailFilename = `thumb_${timestamp}.jpg`;\n        const thumbnailPath = (0,path__WEBPACK_IMPORTED_MODULE_3__.join)(UPLOAD_DIR, thumbnailFilename);\n        await sharp__WEBPACK_IMPORTED_MODULE_1___default()(buffer).resize(300, 300, {\n            fit: 'cover',\n            position: 'center'\n        }).jpeg({\n            quality: 80\n        }).toFile(thumbnailPath);\n        // Extract image metadata\n        const metadata = await sharp__WEBPACK_IMPORTED_MODULE_1___default()(buffer).metadata();\n        const response = {\n            success: true,\n            data: {\n                id: timestamp.toString(),\n                filename,\n                thumbnailFilename,\n                originalName: file.name,\n                size: file.size,\n                processedSize: processedBuffer.length,\n                dimensions: {\n                    width: metadata.width,\n                    height: metadata.height\n                },\n                format: metadata.format,\n                url: `/uploads/${filename}`,\n                thumbnailUrl: `/uploads/${thumbnailFilename}`,\n                roomType: roomType || null,\n                budget: budget ? parseFloat(budget) : null,\n                style: style || null,\n                uploadedAt: new Date().toISOString()\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Upload error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to process upload',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        error: 'Method not allowed. Use POST to upload images.'\n    }, {\n        status: 405\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/upload/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fupload%2Froute&page=%2Fapi%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fupload%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();