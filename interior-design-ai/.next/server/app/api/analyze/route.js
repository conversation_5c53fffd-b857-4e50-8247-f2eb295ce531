(()=>{var e={};e.id=786,e.ids=[786],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31743:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>f,routeModule:()=>y,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var r={};i.r(r),i.d(r,{GET:()=>g,POST:()=>p});var n=i(96559),o=i(48088),s=i(37719),a=i(32190),d=i(33873),u=i(79748),c=i(9288),h=i.n(c);async function l(e){try{let t=await (0,u.readFile)(e),i=await h()(t).metadata();if(!i.width||!i.height)throw Error("Could not read image dimensions");let r=[{id:"obj_1",type:"door",boundingBox:{x:50,y:200,width:80,height:200},confidence:.85},{id:"obj_2",type:"window",boundingBox:{x:300,y:50,width:150,height:100},confidence:.92},{id:"obj_3",type:"window",boundingBox:{x:600,y:50,width:120,height:100},confidence:.88}],n=[{id:"furn_1",type:"couch",category:"seating",boundingBox:{x:200,y:300,width:250,height:120},confidence:.91,estimatedSize:{width:220,height:85,depth:95},condition:"good"},{id:"furn_2",type:"coffee table",category:"tables",boundingBox:{x:280,y:420,width:100,height:60},confidence:.78,estimatedSize:{width:120,height:45,depth:60}},{id:"furn_3",type:"tv",category:"decor",boundingBox:{x:150,y:80,width:180,height:100},confidence:.95,estimatedSize:{width:140,height:80,depth:10}}],o=[{id:"wall_1",start:{x:0,y:0},end:{x:i.width,y:0},length:i.width,hasOpenings:!0,openings:r.filter(e=>"window"===e.type)},{id:"wall_2",start:{x:i.width,y:0},end:{x:i.width,y:i.height},length:i.height,hasOpenings:!1,openings:[]},{id:"wall_3",start:{x:i.width,y:i.height},end:{x:0,y:i.height},length:i.width,hasOpenings:!1,openings:[]},{id:"wall_4",start:{x:0,y:i.height},end:{x:0,y:0},length:i.height,hasOpenings:!0,openings:r.filter(e=>"door"===e.type)}],s=i.width*i.height,a=n.reduce((e,t)=>e+t.boundingBox.width*t.boundingBox.height,0);return{layout:{doors:r.filter(e=>"door"===e.type),windows:r.filter(e=>"window"===e.type),walls:o,floorArea:s-a},existingFurniture:n,lightingSources:[{type:"natural",position:{x:375,y:50},intensity:.8,direction:"south"},{type:"natural",position:{x:660,y:50},intensity:.7,direction:"south"}],colorPalette:["#F5F5F5","#8B7355","#2F4F4F","#D2B48C","#228B22"],spatialFlow:{entryPoints:[{x:90,y:300}],trafficPaths:[{points:[{x:90,y:300},{x:200,y:350},{x:400,y:350},{x:500,y:200}],width:80}],functionalZones:[{id:"seating_area",type:"relaxation",area:[{x:150,y:250},{x:450,y:250},{x:450,y:500},{x:150,y:500}],function:"seating and entertainment"},{id:"circulation",type:"movement",area:[{x:50,y:200},{x:150,y:200},{x:150,y:400},{x:50,y:400}],function:"entry and circulation"}]}}}catch(e){throw console.error("Room analysis error:",e),Error("Failed to analyze room image")}}async function p(e){try{let{imageId:t,imagePath:i}=await e.json();if(!t||!i)return a.NextResponse.json({success:!1,error:"Image ID and path are required"},{status:400});let r=(0,d.join)(process.cwd(),"public",i),n=await l(r),o=function(e){let t=e.map(e=>e.type.toLowerCase()),i={living_room:0,bedroom:0,kitchen:0,bathroom:0,dining_room:0,office:0,nursery:0,guest_room:0};(t.includes("couch")||t.includes("sofa"))&&(i.living_room+=3,i.guest_room+=1),t.includes("tv")&&(i.living_room+=2,i.bedroom+=1),t.includes("bed")&&(i.bedroom+=4,i.guest_room+=3,i.nursery+=2),t.includes("dining table")&&(i.dining_room+=4),t.includes("desk")&&(i.office+=3),(t.includes("refrigerator")||t.includes("oven"))&&(i.kitchen+=4),(t.includes("toilet")||t.includes("bathtub"))&&(i.bathroom+=4);let r="living_room",n=0;for(let[e,t]of Object.entries(i))t>n&&(n=t,r=e);return r}(n.existingFurniture),s={success:!0,data:{imageId:t,analysis:n,predictedRoomType:o,confidence:.85,processingTime:2e3*Math.random()+1e3,analyzedAt:new Date().toISOString()}};return a.NextResponse.json(s)}catch(e){return console.error("Analysis error:",e),a.NextResponse.json({success:!1,error:"Failed to analyze room",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function g(){return a.NextResponse.json({success:!1,error:"Method not allowed. Use POST to analyze images."},{status:405})}let y=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/analyze/route",pathname:"/api/analyze",filename:"route",bundlePath:"app/api/analyze/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/analyze/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:m}=y;function f(){return(0,s.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79748:e=>{"use strict";e.exports=require("fs/promises")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[243,580],()=>i(31743));module.exports=r})();