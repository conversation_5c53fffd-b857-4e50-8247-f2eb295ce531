/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze/route";
exports.ids = ["app/api/analyze/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze/route.ts */ \"(rsc)/./src/app/api/analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze/route\",\n        pathname: \"/api/analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/analyze/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analyze/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/analyze/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_3__);\n// API endpoint for room analysis using AI models\n\n\n\n\n// Mock AI analysis - in production, this would use actual AI models\nasync function analyzeRoomImage(imagePath) {\n    try {\n        // Read and process image\n        const imageBuffer = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_2__.readFile)(imagePath);\n        const metadata = await sharp__WEBPACK_IMPORTED_MODULE_3___default()(imageBuffer).metadata();\n        if (!metadata.width || !metadata.height) {\n            throw new Error('Could not read image dimensions');\n        }\n        // Mock object detection results\n        const mockDetectedObjects = [\n            {\n                id: 'obj_1',\n                type: 'door',\n                boundingBox: {\n                    x: 50,\n                    y: 200,\n                    width: 80,\n                    height: 200\n                },\n                confidence: 0.85\n            },\n            {\n                id: 'obj_2',\n                type: 'window',\n                boundingBox: {\n                    x: 300,\n                    y: 50,\n                    width: 150,\n                    height: 100\n                },\n                confidence: 0.92\n            },\n            {\n                id: 'obj_3',\n                type: 'window',\n                boundingBox: {\n                    x: 600,\n                    y: 50,\n                    width: 120,\n                    height: 100\n                },\n                confidence: 0.88\n            }\n        ];\n        // Mock furniture detection\n        const mockFurniture = [\n            {\n                id: 'furn_1',\n                type: 'couch',\n                category: 'seating',\n                boundingBox: {\n                    x: 200,\n                    y: 300,\n                    width: 250,\n                    height: 120\n                },\n                confidence: 0.91,\n                estimatedSize: {\n                    width: 220,\n                    height: 85,\n                    depth: 95\n                },\n                condition: 'good'\n            },\n            {\n                id: 'furn_2',\n                type: 'coffee table',\n                category: 'tables',\n                boundingBox: {\n                    x: 280,\n                    y: 420,\n                    width: 100,\n                    height: 60\n                },\n                confidence: 0.78,\n                estimatedSize: {\n                    width: 120,\n                    height: 45,\n                    depth: 60\n                }\n            },\n            {\n                id: 'furn_3',\n                type: 'tv',\n                category: 'decor',\n                boundingBox: {\n                    x: 150,\n                    y: 80,\n                    width: 180,\n                    height: 100\n                },\n                confidence: 0.95,\n                estimatedSize: {\n                    width: 140,\n                    height: 80,\n                    depth: 10\n                }\n            }\n        ];\n        // Mock wall detection\n        const walls = [\n            {\n                id: 'wall_1',\n                start: {\n                    x: 0,\n                    y: 0\n                },\n                end: {\n                    x: metadata.width,\n                    y: 0\n                },\n                length: metadata.width,\n                hasOpenings: true,\n                openings: mockDetectedObjects.filter((obj)=>obj.type === 'window')\n            },\n            {\n                id: 'wall_2',\n                start: {\n                    x: metadata.width,\n                    y: 0\n                },\n                end: {\n                    x: metadata.width,\n                    y: metadata.height\n                },\n                length: metadata.height,\n                hasOpenings: false,\n                openings: []\n            },\n            {\n                id: 'wall_3',\n                start: {\n                    x: metadata.width,\n                    y: metadata.height\n                },\n                end: {\n                    x: 0,\n                    y: metadata.height\n                },\n                length: metadata.width,\n                hasOpenings: false,\n                openings: []\n            },\n            {\n                id: 'wall_4',\n                start: {\n                    x: 0,\n                    y: metadata.height\n                },\n                end: {\n                    x: 0,\n                    y: 0\n                },\n                length: metadata.height,\n                hasOpenings: true,\n                openings: mockDetectedObjects.filter((obj)=>obj.type === 'door')\n            }\n        ];\n        // Mock lighting analysis\n        const lightingSources = [\n            {\n                type: 'natural',\n                position: {\n                    x: 375,\n                    y: 50\n                },\n                intensity: 0.8,\n                direction: 'south'\n            },\n            {\n                type: 'natural',\n                position: {\n                    x: 660,\n                    y: 50\n                },\n                intensity: 0.7,\n                direction: 'south'\n            }\n        ];\n        // Mock color palette extraction\n        const colorPalette = [\n            '#F5F5F5',\n            '#8B7355',\n            '#2F4F4F',\n            '#D2B48C',\n            '#228B22'\n        ];\n        // Calculate floor area (excluding furniture)\n        const totalArea = metadata.width * metadata.height;\n        const furnitureArea = mockFurniture.reduce((sum, item)=>sum + item.boundingBox.width * item.boundingBox.height, 0);\n        const floorArea = totalArea - furnitureArea;\n        // Mock spatial flow analysis\n        const spatialFlow = {\n            entryPoints: [\n                {\n                    x: 90,\n                    y: 300\n                }\n            ],\n            trafficPaths: [\n                {\n                    points: [\n                        {\n                            x: 90,\n                            y: 300\n                        },\n                        {\n                            x: 200,\n                            y: 350\n                        },\n                        {\n                            x: 400,\n                            y: 350\n                        },\n                        {\n                            x: 500,\n                            y: 200\n                        }\n                    ],\n                    width: 80\n                }\n            ],\n            functionalZones: [\n                {\n                    id: 'seating_area',\n                    type: 'relaxation',\n                    area: [\n                        {\n                            x: 150,\n                            y: 250\n                        },\n                        {\n                            x: 450,\n                            y: 250\n                        },\n                        {\n                            x: 450,\n                            y: 500\n                        },\n                        {\n                            x: 150,\n                            y: 500\n                        }\n                    ],\n                    function: 'seating and entertainment'\n                },\n                {\n                    id: 'circulation',\n                    type: 'movement',\n                    area: [\n                        {\n                            x: 50,\n                            y: 200\n                        },\n                        {\n                            x: 150,\n                            y: 200\n                        },\n                        {\n                            x: 150,\n                            y: 400\n                        },\n                        {\n                            x: 50,\n                            y: 400\n                        }\n                    ],\n                    function: 'entry and circulation'\n                }\n            ]\n        };\n        const analysis = {\n            layout: {\n                doors: mockDetectedObjects.filter((obj)=>obj.type === 'door'),\n                windows: mockDetectedObjects.filter((obj)=>obj.type === 'window'),\n                walls,\n                floorArea\n            },\n            existingFurniture: mockFurniture,\n            lightingSources,\n            colorPalette,\n            spatialFlow\n        };\n        return analysis;\n    } catch (error) {\n        console.error('Room analysis error:', error);\n        throw new Error('Failed to analyze room image');\n    }\n}\n// Predict room type based on detected furniture\nfunction predictRoomType(furniture) {\n    const furnitureTypes = furniture.map((item)=>item.type.toLowerCase());\n    // Room type scoring based on furniture presence\n    const roomScores = {\n        living_room: 0,\n        bedroom: 0,\n        kitchen: 0,\n        bathroom: 0,\n        dining_room: 0,\n        office: 0,\n        nursery: 0,\n        guest_room: 0\n    };\n    // Score based on furniture indicators\n    if (furnitureTypes.includes('couch') || furnitureTypes.includes('sofa')) {\n        roomScores.living_room += 3;\n        roomScores.guest_room += 1;\n    }\n    if (furnitureTypes.includes('tv')) {\n        roomScores.living_room += 2;\n        roomScores.bedroom += 1;\n    }\n    if (furnitureTypes.includes('bed')) {\n        roomScores.bedroom += 4;\n        roomScores.guest_room += 3;\n        roomScores.nursery += 2;\n    }\n    if (furnitureTypes.includes('dining table')) {\n        roomScores.dining_room += 4;\n    }\n    if (furnitureTypes.includes('desk')) {\n        roomScores.office += 3;\n    }\n    if (furnitureTypes.includes('refrigerator') || furnitureTypes.includes('oven')) {\n        roomScores.kitchen += 4;\n    }\n    if (furnitureTypes.includes('toilet') || furnitureTypes.includes('bathtub')) {\n        roomScores.bathroom += 4;\n    }\n    // Find room type with highest score\n    let bestRoomType = 'living_room';\n    let maxScore = 0;\n    for (const [roomType, score] of Object.entries(roomScores)){\n        if (score > maxScore) {\n            maxScore = score;\n            bestRoomType = roomType;\n        }\n    }\n    return bestRoomType;\n}\nasync function POST(request) {\n    try {\n        const { imageId, imagePath } = await request.json();\n        if (!imageId || !imagePath) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Image ID and path are required'\n            }, {\n                status: 400\n            });\n        }\n        // Construct full image path\n        const fullImagePath = (0,path__WEBPACK_IMPORTED_MODULE_1__.join)(process.cwd(), 'public', imagePath);\n        // Perform room analysis\n        const analysis = await analyzeRoomImage(fullImagePath);\n        // Predict room type\n        const predictedRoomType = predictRoomType(analysis.existingFurniture);\n        const response = {\n            success: true,\n            data: {\n                imageId,\n                analysis,\n                predictedRoomType,\n                confidence: 0.85,\n                processingTime: Math.random() * 2000 + 1000,\n                analyzedAt: new Date().toISOString()\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Analysis error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to analyze room',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        error: 'Method not allowed. Use POST to analyze images.'\n    }, {\n        status: 405\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();