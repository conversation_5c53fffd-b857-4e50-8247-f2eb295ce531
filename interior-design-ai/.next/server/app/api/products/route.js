/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/products/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_real_product_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/real-product-service */ \"(rsc)/./src/lib/real-product-service.ts\");\n/* harmony import */ var _lib_style_matcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/style-matcher */ \"(rsc)/./src/lib/style-matcher.ts\");\n/* harmony import */ var _utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api-helpers */ \"(rsc)/./src/utils/api-helpers.ts\");\n// API endpoint for product search and alternatives\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        switch(action){\n            case 'search':\n                return handleProductSearch(searchParams);\n            case 'similar':\n                return handleSimilarProducts(searchParams);\n            case 'alternatives':\n                return handleStyleAlternatives(searchParams);\n            case 'random':\n                return handleRandomProducts(searchParams);\n            default:\n                return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Invalid action parameter', 400);\n        }\n    } catch (error) {\n        console.error('Products API error:', error);\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(error);\n    }\n}\nasync function handleProductSearch(searchParams) {\n    const category = searchParams.get('category');\n    const style = searchParams.get('style');\n    const minPrice = searchParams.get('minPrice');\n    const maxPrice = searchParams.get('maxPrice');\n    const retailer = searchParams.get('retailer');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const priceRange = minPrice && maxPrice ? {\n        min: parseFloat(minPrice),\n        max: parseFloat(maxPrice)\n    } : undefined;\n    const products = await _lib_real_product_service__WEBPACK_IMPORTED_MODULE_0__.RealProductService.getRecommendations({\n        category: category || undefined,\n        style: style || undefined,\n        budget: priceRange?.max,\n        limit\n    });\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        products,\n        total: products.length,\n        filters: {\n            category,\n            style,\n            priceRange,\n            retailer\n        }\n    });\n}\nasync function handleSimilarProducts(searchParams) {\n    const productId = searchParams.get('productId');\n    const limit = parseInt(searchParams.get('limit') || '5');\n    if (!productId) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Product ID is required', 400);\n    }\n    const product = ProductDatabase.getProductById(productId);\n    if (!product) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Product not found', 404);\n    }\n    const similarProducts = ProductDatabase.getSimilarProducts(productId, limit);\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        originalProduct: product,\n        similarProducts,\n        total: similarProducts.length\n    });\n}\nasync function handleStyleAlternatives(searchParams) {\n    const productId = searchParams.get('productId');\n    const targetStyle = searchParams.get('targetStyle');\n    const limit = parseInt(searchParams.get('limit') || '3');\n    if (!productId || !targetStyle) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Product ID and target style are required', 400);\n    }\n    const product = ProductDatabase.getProductById(productId);\n    if (!product) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Product not found', 404);\n    }\n    // Get all products for alternatives\n    const allProducts = ProductDatabase.searchProducts();\n    // Create a mock FurnitureRecommendation for the style matcher\n    const mockRecommendation = {\n        ...product,\n        placement: {\n            suggestedPosition: {\n                x: 0,\n                y: 0\n            },\n            zone: 'main'\n        },\n        compatibility: {\n            roomType: [],\n            existingFurniture: [],\n            styleMatch: 0\n        }\n    };\n    const alternatives = _lib_style_matcher__WEBPACK_IMPORTED_MODULE_1__.StyleMatcher.generateStyleAlternatives(mockRecommendation, targetStyle, allProducts).slice(0, limit);\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        originalProduct: product,\n        targetStyle,\n        alternatives,\n        total: alternatives.length\n    });\n}\nasync function handleRandomProducts(searchParams) {\n    const count = parseInt(searchParams.get('count') || '10');\n    const category = searchParams.get('category');\n    let products = ProductDatabase.getRandomProducts(count * 2); // Get more to filter\n    if (category) {\n        products = products.filter((p)=>p.category === category);\n    }\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        products: products.slice(0, count),\n        total: products.length\n    });\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action } = body;\n        switch(action){\n            case 'bulk-search':\n                return handleBulkSearch(body);\n            case 'style-analysis':\n                return handleStyleAnalysis(body);\n            default:\n                return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Invalid action', 400);\n        }\n    } catch (error) {\n        console.error('Products POST API error:', error);\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)(error);\n    }\n}\nasync function handleBulkSearch(body) {\n    const { searches } = body;\n    if (!Array.isArray(searches)) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Searches must be an array', 400);\n    }\n    const results = searches.map((search)=>{\n        const { category, style, priceRange, retailer, limit = 10 } = search;\n        const products = ProductDatabase.searchProducts(category, style, priceRange, retailer).slice(0, limit);\n        return {\n            query: search,\n            products,\n            total: products.length\n        };\n    });\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        results,\n        totalQueries: searches.length\n    });\n}\nasync function handleStyleAnalysis(body) {\n    const { productIds, targetStyle } = body;\n    (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.validateRequired)(body, [\n        'productIds',\n        'targetStyle'\n    ]);\n    if (!Array.isArray(productIds)) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('Product IDs must be an array', 400);\n    }\n    const products = productIds.map((id)=>ProductDatabase.getProductById(id)).filter(Boolean);\n    if (products.length === 0) {\n        return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createErrorResponse)('No valid products found', 404);\n    }\n    // Create mock recommendations for style analysis\n    const mockRecommendations = products.map((product)=>({\n            ...product,\n            placement: {\n                suggestedPosition: {\n                    x: 0,\n                    y: 0\n                },\n                zone: 'main'\n            },\n            compatibility: {\n                roomType: [],\n                existingFurniture: [],\n                styleMatch: 0\n            }\n        }));\n    const styleAnalysis = _lib_style_matcher__WEBPACK_IMPORTED_MODULE_1__.StyleMatcher.scoreStyleCoherence(mockRecommendations, targetStyle);\n    // Get style guide for the target style\n    const styleGuide = _lib_style_matcher__WEBPACK_IMPORTED_MODULE_1__.StyleMatcher.getStyleGuide(targetStyle);\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        products,\n        targetStyle,\n        styleAnalysis,\n        styleGuide,\n        recommendations: styleAnalysis.suggestions\n    });\n}\n// Helper endpoint to get all available categories and styles\nasync function OPTIONS() {\n    const categories = [\n        'seating',\n        'tables',\n        'storage',\n        'lighting',\n        'decor',\n        'textiles',\n        'plants'\n    ];\n    const styles = [\n        'modern',\n        'minimalist',\n        'boho',\n        'industrial',\n        'scandinavian',\n        'traditional',\n        'contemporary',\n        'rustic'\n    ];\n    const retailers = [\n        'amazon',\n        'ikea'\n    ];\n    return (0,_utils_api_helpers__WEBPACK_IMPORTED_MODULE_2__.createSuccessResponse)({\n        categories,\n        styles,\n        retailers,\n        endpoints: {\n            search: '/api/products?action=search&category=seating&style=modern',\n            similar: '/api/products?action=similar&productId=ikea-kivik-sofa',\n            alternatives: '/api/products?action=alternatives&productId=ikea-kivik-sofa&targetStyle=modern',\n            random: '/api/products?action=random&count=10'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n// Database service using Prisma\n\n// Global Prisma instance to prevent multiple connections in development\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\nclass DatabaseService {\n    // Room Management\n    static async createRoom(data) {\n        return await prisma.room.create({\n            data: {\n                ...data\n            }\n        });\n    }\n    static async getRoomById(id) {\n        return await prisma.room.findUnique({\n            where: {\n                id\n            },\n            include: {\n                recommendations: {\n                    include: {\n                        product: true\n                    }\n                },\n                shoppingLists: {\n                    include: {\n                        items: {\n                            include: {\n                                product: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    static async updateRoomAnalysis(roomId, analysisResult) {\n        return await prisma.room.update({\n            where: {\n                id: roomId\n            },\n            data: {\n                analysisResult,\n                updatedAt: new Date()\n            }\n        });\n    }\n    static async getUserRooms(userId) {\n        return await prisma.room.findMany({\n            where: {\n                userId\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            },\n            include: {\n                recommendations: {\n                    take: 3,\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    // Product Management\n    static async createProduct(data) {\n        return await prisma.product.create({\n            data: {\n                ...data,\n                styles: data.styles || [],\n                colors: data.colors || [],\n                materials: data.materials || [],\n                tags: data.tags || []\n            }\n        });\n    }\n    static async getProductByExternalId(externalId) {\n        return await prisma.product.findUnique({\n            where: {\n                externalId\n            }\n        });\n    }\n    static async updateProduct(id, data) {\n        return await prisma.product.update({\n            where: {\n                id\n            },\n            data\n        });\n    }\n    static async getProductById(id) {\n        return await prisma.product.findUnique({\n            where: {\n                id\n            },\n            include: {\n                priceHistory: {\n                    orderBy: {\n                        recordedAt: 'desc'\n                    },\n                    take: 10\n                }\n            }\n        });\n    }\n    static async searchProducts(filters) {\n        const where = {};\n        if (filters.category) where.category = filters.category;\n        if (filters.retailer) where.retailer = filters.retailer;\n        if (filters.isAvailable !== undefined) where.isAvailable = filters.isAvailable;\n        if (filters.minPrice || filters.maxPrice) {\n            where.price = {};\n            if (filters.minPrice) where.price.gte = filters.minPrice;\n            if (filters.maxPrice) where.price.lte = filters.maxPrice;\n        }\n        if (filters.styles && filters.styles.length > 0) {\n            where.styles = {\n                hasSome: filters.styles\n            };\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 20,\n            skip: filters.offset || 0,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n    static async getProducts(filters) {\n        const where = {};\n        if (filters.category) {\n            where.category = filters.category;\n        }\n        if (filters.retailer) {\n            where.retailer = filters.retailer;\n        }\n        if (filters.inStock !== undefined) {\n            where.isAvailable = filters.inStock;\n        }\n        const orderBy = {};\n        if (filters.orderBy) {\n            orderBy[filters.orderBy] = filters.orderDirection || 'desc';\n        } else {\n            orderBy.createdAt = 'desc';\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 100,\n            orderBy\n        });\n    }\n    // Recommendations\n    static async saveRecommendations(roomId, recommendations) {\n        // Delete existing recommendations for this room\n        await prisma.recommendation.deleteMany({\n            where: {\n                roomId\n            }\n        });\n        // Create new recommendations\n        return await prisma.recommendation.createMany({\n            data: recommendations.map((rec)=>({\n                    ...rec,\n                    roomId,\n                    placement: rec.placement || undefined\n                }))\n        });\n    }\n    static async getRoomRecommendations(roomId) {\n        const recommendations = await prisma.recommendation.findMany({\n            where: {\n                roomId\n            },\n            include: {\n                product: true\n            },\n            orderBy: {\n                rank: 'asc'\n            }\n        });\n        return recommendations;\n    }\n    // Shopping Lists\n    static async createShoppingList(data) {\n        const totalCost = data.items.reduce((sum, item)=>sum + item.priceAtAdd * item.quantity, 0);\n        return await prisma.shoppingList.create({\n            data: {\n                name: data.name,\n                budget: data.budget,\n                totalCost,\n                userId: data.userId,\n                roomId: data.roomId,\n                items: {\n                    create: data.items\n                }\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    static async getShoppingList(id) {\n        const shoppingList = await prisma.shoppingList.findUnique({\n            where: {\n                id\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                },\n                user: true,\n                room: true\n            }\n        });\n        if (shoppingList) {\n            return {\n                ...shoppingList,\n                items: shoppingList.items\n            };\n        }\n        return null;\n    }\n    // User Management\n    static async createUser(data) {\n        return await prisma.user.create({\n            data\n        });\n    }\n    static async getUserByEmail(email) {\n        return await prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                preferences: true\n            }\n        });\n    }\n    static async updateUserPreferences(userId, preferences) {\n        return await prisma.userPreferences.upsert({\n            where: {\n                userId\n            },\n            update: {\n                ...preferences,\n                updatedAt: new Date()\n            },\n            create: {\n                userId,\n                ...preferences,\n                preferredStyles: preferences.preferredStyles || [],\n                favoriteRetailers: preferences.favoriteRetailers || [],\n                colorPreferences: preferences.colorPreferences || []\n            }\n        });\n    }\n    // Analytics\n    static async trackEvent(data) {\n        return await prisma.analyticsEvent.create({\n            data: {\n                ...data,\n                eventData: data.eventData || undefined\n            }\n        });\n    }\n    // Price Tracking\n    static async addPriceHistory(productId, price, source) {\n        return await prisma.priceHistory.create({\n            data: {\n                productId,\n                price,\n                source\n            }\n        });\n    }\n    static async createPriceAlert(data) {\n        return await prisma.priceAlert.create({\n            data\n        });\n    }\n    // Utility methods\n    static async cleanup() {\n        await prisma.$disconnect();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/product-database.ts":
/*!*************************************!*\
  !*** ./src/lib/product-database.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENHANCED_FURNITURE_DATABASE: () => (/* binding */ ENHANCED_FURNITURE_DATABASE),\n/* harmony export */   ProductDatabase: () => (/* binding */ ProductDatabase)\n/* harmony export */ });\n// Enhanced product database with real retailer integration\n// Extended furniture database with more realistic products\nconst ENHANCED_FURNITURE_DATABASE = [\n    // Living Room - Seating\n    {\n        id: 'ikea-kivik-sofa',\n        name: 'KIVIK 3-seat sofa',\n        category: 'seating',\n        description: 'A generous seating series with a soft, deep seat and comfortable support for your back.',\n        price: 599,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/kivik-3-seat-sofa-hillared-beige__0818588_pe774490_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 228,\n            height: 83,\n            depth: 95\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'scandinavian'\n        ]\n    },\n    {\n        id: 'amazon-rivet-sofa',\n        name: 'Rivet Revolve Modern Upholstered Sofa',\n        category: 'seating',\n        description: 'Mid-century modern style with clean lines and button tufting.',\n        price: 899,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 213,\n            height: 86,\n            depth: 89\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'ikea-poang-chair',\n        name: 'POÄNG Armchair',\n        category: 'seating',\n        description: 'Layer-glued bent birch frame gives comfortable resilience.',\n        price: 149,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/poang-armchair-birch-veneer-knisa-light-beige__0818334_pe774558_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/poang-armchair-birch-veneer-knisa-light-beige-s59932209/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 68,\n            height: 100,\n            depth: 82\n        },\n        style: [\n            'scandinavian',\n            'modern',\n            'minimalist'\n        ]\n    },\n    // Electronics - TVs\n    {\n        id: 'samsung-55-4k-tv',\n        name: 'Samsung 55\" 4K Smart TV',\n        category: 'electronics',\n        description: 'Crystal clear 4K display with smart TV features and HDR support.',\n        price: 599,\n        currency: 'USD',\n        imageUrl: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=600&h=400&fit=crop',\n        productUrl: 'https://www.amazon.com/dp/B08N5WRWNW',\n        retailer: 'amazon',\n        dimensions: {\n            width: 123,\n            height: 71,\n            depth: 6\n        },\n        style: [\n            'modern',\n            'minimalist',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'lg-65-oled-tv',\n        name: 'LG 65\" OLED 4K TV',\n        category: 'electronics',\n        description: 'Premium OLED display with perfect blacks and infinite contrast.',\n        price: 1299,\n        currency: 'USD',\n        imageUrl: 'https://images.unsplash.com/photo-1567690187548-f07b1d7bf5a9?w=600&h=400&fit=crop',\n        productUrl: 'https://www.amazon.com/dp/B08WFK81RH',\n        retailer: 'amazon',\n        dimensions: {\n            width: 144,\n            height: 83,\n            depth: 5\n        },\n        style: [\n            'modern',\n            'luxury',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'tcl-43-roku-tv',\n        name: 'TCL 43\" Roku Smart TV',\n        category: 'electronics',\n        description: 'Affordable smart TV with built-in Roku streaming platform.',\n        price: 299,\n        currency: 'USD',\n        imageUrl: 'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=600&h=400&fit=crop',\n        productUrl: 'https://www.amazon.com/dp/B08GW982QS',\n        retailer: 'amazon',\n        dimensions: {\n            width: 96,\n            height: 56,\n            depth: 8\n        },\n        style: [\n            'modern',\n            'budget-friendly',\n            'minimalist'\n        ]\n    },\n    // Living Room - Tables\n    {\n        id: 'ikea-lack-coffee-table',\n        name: 'LACK Coffee table',\n        category: 'tables',\n        description: 'Simple design that is easy to match with other furniture.',\n        price: 49,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/lack-coffee-table-white__0818318_pe774542_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/lack-coffee-table-white-s49932210/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 118,\n            height: 45,\n            depth: 78\n        },\n        style: [\n            'minimalist',\n            'modern',\n            'scandinavian'\n        ]\n    },\n    {\n        id: 'amazon-glass-coffee-table',\n        name: 'Walker Edison Glass Coffee Table',\n        category: 'tables',\n        description: 'Tempered glass top with sleek metal frame.',\n        price: 299,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07CQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 122,\n            height: 46,\n            depth: 61\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'industrial'\n        ]\n    },\n    // Bedroom Furniture\n    {\n        id: 'ikea-malm-bed',\n        name: 'MALM Bed frame',\n        category: 'seating',\n        description: 'A clean design that fits right in, in the bedroom or guest room.',\n        price: 179,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/malm-bed-frame-oak-veneer__0818302_pe774526_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932211/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 209,\n            height: 38,\n            depth: 156\n        },\n        style: [\n            'scandinavian',\n            'minimalist',\n            'modern'\n        ]\n    },\n    {\n        id: 'amazon-platform-bed',\n        name: 'Zinus Modern Studio Platform Bed',\n        category: 'seating',\n        description: 'Strong steel frame with wood slat support.',\n        price: 299,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07DQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 203,\n            height: 35,\n            depth: 152\n        },\n        style: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    // Storage\n    {\n        id: 'ikea-hemnes-bookcase',\n        name: 'HEMNES Bookcase',\n        category: 'storage',\n        description: 'Solid wood has a natural feel. Adjustable shelves.',\n        price: 199,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/hemnes-bookcase-white-stain__0818286_pe774510_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932212/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 90,\n            height: 197,\n            depth: 37\n        },\n        style: [\n            'traditional',\n            'scandinavian',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-cube-organizer',\n        name: 'ClosetMaid Cubeicals Organizer',\n        category: 'storage',\n        description: 'Versatile storage solution with multiple cubes.',\n        price: 89,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/81QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07EQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 106,\n            height: 106,\n            depth: 30\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    // Lighting\n    {\n        id: 'ikea-foto-pendant',\n        name: 'FOTO Pendant lamp',\n        category: 'lighting',\n        description: 'Gives a directed light; good for lighting dining tables or bar tops.',\n        price: 39,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/foto-pendant-lamp-aluminum__0818270_pe774494_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/foto-pendant-lamp-aluminum-s49932213/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 25,\n            height: 23,\n            depth: 25\n        },\n        style: [\n            'industrial',\n            'modern',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'amazon-arc-floor-lamp',\n        name: 'Brightech Sparq LED Arc Floor Lamp',\n        category: 'lighting',\n        description: 'Modern arc design with energy-efficient LED.',\n        price: 199,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07FQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 40,\n            height: 180,\n            depth: 40\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'industrial'\n        ]\n    },\n    // Decor\n    {\n        id: 'ikea-fejka-plant',\n        name: 'FEJKA Artificial potted plant',\n        category: 'plants',\n        description: 'Lifelike artificial plant that stays fresh year after year.',\n        price: 24,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/fejka-artificial-potted-plant-eucalyptus__0818254_pe774478_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/fejka-artificial-potted-plant-eucalyptus-s49932214/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 12,\n            height: 65,\n            depth: 12\n        },\n        style: [\n            'scandinavian',\n            'boho',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-wall-art-set',\n        name: 'Americanflat Gallery Wall Set',\n        category: 'decor',\n        description: 'Set of 6 framed prints for creating a gallery wall.',\n        price: 129,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/91QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07GQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 20,\n            height: 25,\n            depth: 2\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    // Textiles\n    {\n        id: 'ikea-gurli-cushion',\n        name: 'GURLI Cushion cover',\n        category: 'textiles',\n        description: 'The zipper makes the cover easy to remove.',\n        price: 7,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/gurli-cushion-cover-beige__0818238_pe774462_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/gurli-cushion-cover-beige-s49932215/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 50,\n            height: 50,\n            depth: 2\n        },\n        style: [\n            'scandinavian',\n            'minimalist',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-throw-blanket',\n        name: 'Bedsure Knitted Throw Blanket',\n        category: 'textiles',\n        description: 'Soft knitted throw perfect for sofas and beds.',\n        price: 35,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07HQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 127,\n            height: 152,\n            depth: 1\n        },\n        style: [\n            'boho',\n            'scandinavian',\n            'contemporary'\n        ]\n    }\n];\n// Product search and filtering utilities\nclass ProductDatabase {\n    static searchProducts(category, style, priceRange, retailer) {\n        let results = [\n            ...ENHANCED_FURNITURE_DATABASE\n        ];\n        if (category) {\n            results = results.filter((product)=>product.category === category);\n        }\n        if (style) {\n            results = results.filter((product)=>product.style.includes(style));\n        }\n        if (priceRange) {\n            results = results.filter((product)=>product.price >= priceRange.min && product.price <= priceRange.max);\n        }\n        if (retailer) {\n            results = results.filter((product)=>product.retailer === retailer);\n        }\n        return results;\n    }\n    static getProductById(id) {\n        return ENHANCED_FURNITURE_DATABASE.find((product)=>product.id === id) || null;\n    }\n    static getProductsByCategory(category) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.category === category);\n    }\n    static getProductsByStyle(style) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.style.includes(style));\n    }\n    static getProductsByPriceRange(min, max) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.price >= min && product.price <= max);\n    }\n    static getRandomProducts(count = 10) {\n        const shuffled = [\n            ...ENHANCED_FURNITURE_DATABASE\n        ].sort(()=>0.5 - Math.random());\n        return shuffled.slice(0, count);\n    }\n    static getSimilarProducts(productId, count = 5) {\n        const product = this.getProductById(productId);\n        if (!product) return [];\n        // Find similar products based on category and style\n        const similar = ENHANCED_FURNITURE_DATABASE.filter((p)=>p.id !== productId).filter((p)=>p.category === product.category || p.style.some((style)=>product.style.includes(style))).sort((a, b)=>{\n            // Score based on style overlap and price similarity\n            const aStyleOverlap = a.style.filter((style)=>product.style.includes(style)).length;\n            const bStyleOverlap = b.style.filter((style)=>product.style.includes(style)).length;\n            const aPriceDiff = Math.abs(a.price - product.price);\n            const bPriceDiff = Math.abs(b.price - product.price);\n            const aScore = aStyleOverlap * 10 - aPriceDiff / 100;\n            const bScore = bStyleOverlap * 10 - bPriceDiff / 100;\n            return bScore - aScore;\n        });\n        return similar.slice(0, count);\n    }\n    // Generate affiliate URLs\n    static generateAffiliateUrl(productUrl, retailer) {\n        const affiliateIds = {\n            amazon: process.env.AMAZON_AFFILIATE_ID || 'interiorai-20',\n            ikea: process.env.IKEA_PARTNER_ID || 'interior-ai-partner'\n        };\n        if (retailer === 'amazon' && productUrl.includes('amazon.com')) {\n            const url = new URL(productUrl);\n            url.searchParams.set('tag', affiliateIds.amazon);\n            return url.toString();\n        }\n        if (retailer === 'ikea' && productUrl.includes('ikea.com')) {\n            // IKEA affiliate links work differently\n            return `${productUrl}?partner=${affiliateIds.ikea}`;\n        }\n        return productUrl;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/product-database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/real-product-service.ts":
/*!*****************************************!*\
  !*** ./src/lib/real-product-service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealProductService: () => (/* binding */ RealProductService)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _product_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./product-database */ \"(rsc)/./src/lib/product-database.ts\");\n\n\nclass RealProductService {\n    static{\n        this.database = new _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService();\n    }\n    static async getRecommendations(filters) {\n        try {\n            // First try to get real scraped products\n            const scrapedProducts = await this.getScrapedProducts(filters);\n            // If we have enough scraped products, use them\n            if (scrapedProducts.length >= (filters.limit || 10)) {\n                return scrapedProducts.slice(0, filters.limit || 10);\n            }\n            // Otherwise, supplement with mock data\n            const mockProducts = this.getMockProducts(filters);\n            const combined = [\n                ...scrapedProducts,\n                ...mockProducts\n            ];\n            return combined.slice(0, filters.limit || 10);\n        } catch (error) {\n            console.error('Failed to get recommendations:', error);\n            // Fallback to mock data only\n            return this.getMockProducts(filters);\n        }\n    }\n    static async getScrapedProducts(filters) {\n        const searchFilters = {\n            isAvailable: true,\n            limit: filters.limit || 50\n        };\n        if (filters.category) {\n            searchFilters.category = filters.category;\n        }\n        if (filters.budget) {\n            searchFilters.maxPrice = filters.budget;\n        }\n        const dbProducts = await this.database.searchProducts(searchFilters);\n        return dbProducts.map(this.convertDbProductToRecommendation);\n    }\n    static getMockProducts(filters) {\n        let products = [\n            ..._product_database__WEBPACK_IMPORTED_MODULE_1__.ENHANCED_FURNITURE_DATABASE\n        ];\n        // Apply filters\n        if (filters.category) {\n            products = products.filter((p)=>p.category === filters.category);\n        }\n        if (filters.budget) {\n            products = products.filter((p)=>p.price <= filters.budget);\n        }\n        if (filters.style) {\n            products = products.filter((p)=>p.style.some((s)=>s.toLowerCase().includes(filters.style.toLowerCase())));\n        }\n        // Add mock placement and compatibility data\n        return products.map((product)=>({\n                ...product,\n                placement: {\n                    x: Math.random() * 400 + 100,\n                    y: Math.random() * 300 + 100,\n                    rotation: 0,\n                    scale: 1\n                },\n                compatibility: {\n                    styleMatch: 0.8 + Math.random() * 0.2,\n                    sizeMatch: 0.7 + Math.random() * 0.3,\n                    budgetMatch: filters.budget ? Math.min(1, filters.budget / product.price) : 1,\n                    overallScore: 0.75 + Math.random() * 0.25\n                }\n            })).slice(0, filters.limit || 10);\n    }\n    static convertDbProductToRecommendation(dbProduct) {\n        return {\n            id: dbProduct.id,\n            name: dbProduct.name,\n            category: dbProduct.category,\n            description: dbProduct.description || '',\n            price: dbProduct.price,\n            currency: dbProduct.currency,\n            imageUrl: dbProduct.imageUrl,\n            productUrl: dbProduct.productUrl,\n            retailer: dbProduct.retailer,\n            style: dbProduct.styles || [\n                'modern'\n            ],\n            dimensions: {\n                width: dbProduct.width || 100,\n                height: dbProduct.height || 100,\n                depth: dbProduct.depth || 50\n            },\n            placement: {\n                x: Math.random() * 400 + 100,\n                y: Math.random() * 300 + 100,\n                rotation: 0,\n                scale: 1\n            },\n            compatibility: {\n                styleMatch: 0.8 + Math.random() * 0.2,\n                sizeMatch: 0.7 + Math.random() * 0.3,\n                budgetMatch: 1,\n                overallScore: 0.75 + Math.random() * 0.25\n            }\n        };\n    }\n    static async searchProducts(query, filters) {\n        try {\n            // Search in scraped products first\n            const searchFilters = {\n                isAvailable: true,\n                limit: filters?.limit || 20\n            };\n            if (filters?.category) {\n                searchFilters.category = filters.category;\n            }\n            if (filters?.retailer) {\n                searchFilters.retailer = filters.retailer;\n            }\n            if (filters?.maxPrice) {\n                searchFilters.maxPrice = filters.maxPrice;\n            }\n            const dbProducts = await this.database.searchProducts(searchFilters);\n            // Filter by search query\n            const filteredProducts = dbProducts.filter((product)=>product.name.toLowerCase().includes(query.toLowerCase()) || product.description?.toLowerCase().includes(query.toLowerCase()) || product.category.toLowerCase().includes(query.toLowerCase()));\n            const scrapedResults = filteredProducts.map(this.convertDbProductToRecommendation);\n            // If we don't have enough results, supplement with mock data\n            if (scrapedResults.length < (filters?.limit || 20)) {\n                const mockResults = _product_database__WEBPACK_IMPORTED_MODULE_1__.ENHANCED_FURNITURE_DATABASE.filter((product)=>product.name.toLowerCase().includes(query.toLowerCase()) || product.description.toLowerCase().includes(query.toLowerCase())).map((product)=>({\n                        ...product,\n                        placement: {\n                            x: Math.random() * 400 + 100,\n                            y: Math.random() * 300 + 100,\n                            rotation: 0,\n                            scale: 1\n                        },\n                        compatibility: {\n                            styleMatch: 0.8 + Math.random() * 0.2,\n                            sizeMatch: 0.7 + Math.random() * 0.3,\n                            budgetMatch: 1,\n                            overallScore: 0.75 + Math.random() * 0.25\n                        }\n                    }));\n                return [\n                    ...scrapedResults,\n                    ...mockResults\n                ].slice(0, filters?.limit || 20);\n            }\n            return scrapedResults;\n        } catch (error) {\n            console.error('Failed to search products:', error);\n            // Fallback to mock data search\n            return _product_database__WEBPACK_IMPORTED_MODULE_1__.ENHANCED_FURNITURE_DATABASE.filter((product)=>product.name.toLowerCase().includes(query.toLowerCase()) || product.description.toLowerCase().includes(query.toLowerCase())).map((product)=>({\n                    ...product,\n                    placement: {\n                        x: Math.random() * 400 + 100,\n                        y: Math.random() * 300 + 100,\n                        rotation: 0,\n                        scale: 1\n                    },\n                    compatibility: {\n                        styleMatch: 0.8 + Math.random() * 0.2,\n                        sizeMatch: 0.7 + Math.random() * 0.3,\n                        budgetMatch: 1,\n                        overallScore: 0.75 + Math.random() * 0.25\n                    }\n                })).slice(0, filters?.limit || 20);\n        }\n    }\n    static async getProductsByCategory(category, limit = 20) {\n        return this.getRecommendations({\n            category,\n            limit\n        });\n    }\n    static async getProductsByRetailer(retailer, limit = 20) {\n        try {\n            const dbProducts = await this.database.searchProducts({\n                retailer,\n                isAvailable: true,\n                limit\n            });\n            return dbProducts.map(this.convertDbProductToRecommendation);\n        } catch (error) {\n            console.error('Failed to get products by retailer:', error);\n            return _product_database__WEBPACK_IMPORTED_MODULE_1__.ENHANCED_FURNITURE_DATABASE.filter((p)=>p.retailer === retailer).map((product)=>({\n                    ...product,\n                    placement: {\n                        x: Math.random() * 400 + 100,\n                        y: Math.random() * 300 + 100,\n                        rotation: 0,\n                        scale: 1\n                    },\n                    compatibility: {\n                        styleMatch: 0.8 + Math.random() * 0.2,\n                        sizeMatch: 0.7 + Math.random() * 0.3,\n                        budgetMatch: 1,\n                        overallScore: 0.75 + Math.random() * 0.25\n                    }\n                })).slice(0, limit);\n        }\n    }\n    static async getScrapingStats() {\n        try {\n            const allProducts = await this.database.searchProducts({\n                isAvailable: true,\n                limit: 1000\n            });\n            const stats = {\n                totalProducts: allProducts.length,\n                productsByRetailer: {},\n                productsByCategory: {},\n                lastScrapedAt: undefined\n            };\n            // Calculate stats\n            allProducts.forEach((product)=>{\n                // Count by retailer\n                stats.productsByRetailer[product.retailer] = (stats.productsByRetailer[product.retailer] || 0) + 1;\n                // Count by category\n                stats.productsByCategory[product.category] = (stats.productsByCategory[product.category] || 0) + 1;\n                // Track latest scrape date\n                if (product.scrapedAt) {\n                    const scrapedDate = new Date(product.scrapedAt);\n                    if (!stats.lastScrapedAt || scrapedDate > stats.lastScrapedAt) {\n                        stats.lastScrapedAt = scrapedDate;\n                    }\n                }\n            });\n            return stats;\n        } catch (error) {\n            console.error('Failed to get scraping stats:', error);\n            return {\n                totalProducts: 0,\n                productsByRetailer: {},\n                productsByCategory: {},\n                lastScrapedAt: undefined\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/real-product-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/style-matcher.ts":
/*!**********************************!*\
  !*** ./src/lib/style-matcher.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleMatcher: () => (/* binding */ StyleMatcher)\n/* harmony export */ });\n// Advanced AI-powered style matching and compatibility analysis\n// Style characteristics and compatibility matrix\nconst STYLE_CHARACTERISTICS = {\n    modern: {\n        colors: [\n            '#FFFFFF',\n            '#000000',\n            '#808080',\n            '#C0C0C0'\n        ],\n        materials: [\n            'glass',\n            'steel',\n            'chrome',\n            'leather'\n        ],\n        patterns: [\n            'geometric',\n            'solid',\n            'minimal'\n        ],\n        keywords: [\n            'clean',\n            'sleek',\n            'minimal',\n            'geometric',\n            'functional'\n        ],\n        compatibility: [\n            'contemporary',\n            'minimalist',\n            'industrial'\n        ],\n        opposites: [\n            'traditional',\n            'rustic',\n            'boho'\n        ]\n    },\n    minimalist: {\n        colors: [\n            '#FFFFFF',\n            '#F5F5F5',\n            '#E8E8E8',\n            '#D3D3D3'\n        ],\n        materials: [\n            'wood',\n            'concrete',\n            'linen',\n            'cotton'\n        ],\n        patterns: [\n            'solid',\n            'subtle texture'\n        ],\n        keywords: [\n            'simple',\n            'clean',\n            'uncluttered',\n            'functional',\n            'serene'\n        ],\n        compatibility: [\n            'modern',\n            'scandinavian',\n            'contemporary'\n        ],\n        opposites: [\n            'boho',\n            'traditional',\n            'rustic'\n        ]\n    },\n    boho: {\n        colors: [\n            '#8B4513',\n            '#DAA520',\n            '#CD853F',\n            '#DEB887',\n            '#F4A460'\n        ],\n        materials: [\n            'rattan',\n            'macrame',\n            'velvet',\n            'brass',\n            'natural fiber'\n        ],\n        patterns: [\n            'paisley',\n            'tribal',\n            'floral',\n            'geometric'\n        ],\n        keywords: [\n            'eclectic',\n            'colorful',\n            'textured',\n            'artistic',\n            'layered'\n        ],\n        compatibility: [\n            'rustic',\n            'traditional'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    industrial: {\n        colors: [\n            '#2F4F4F',\n            '#696969',\n            '#A9A9A9',\n            '#8B4513'\n        ],\n        materials: [\n            'metal',\n            'concrete',\n            'brick',\n            'leather',\n            'reclaimed wood'\n        ],\n        patterns: [\n            'exposed',\n            'raw',\n            'weathered'\n        ],\n        keywords: [\n            'raw',\n            'exposed',\n            'urban',\n            'functional',\n            'edgy'\n        ],\n        compatibility: [\n            'modern',\n            'contemporary'\n        ],\n        opposites: [\n            'traditional',\n            'boho',\n            'scandinavian'\n        ]\n    },\n    scandinavian: {\n        colors: [\n            '#FFFFFF',\n            '#F0F8FF',\n            '#E6E6FA',\n            '#FFFAF0'\n        ],\n        materials: [\n            'light wood',\n            'wool',\n            'linen',\n            'cotton'\n        ],\n        patterns: [\n            'natural',\n            'simple',\n            'cozy'\n        ],\n        keywords: [\n            'cozy',\n            'functional',\n            'light',\n            'natural',\n            'hygge'\n        ],\n        compatibility: [\n            'minimalist',\n            'contemporary',\n            'modern'\n        ],\n        opposites: [\n            'industrial',\n            'boho'\n        ]\n    },\n    traditional: {\n        colors: [\n            '#8B0000',\n            '#006400',\n            '#191970',\n            '#8B4513'\n        ],\n        materials: [\n            'mahogany',\n            'oak',\n            'velvet',\n            'silk',\n            'brass'\n        ],\n        patterns: [\n            'floral',\n            'damask',\n            'stripes',\n            'plaid'\n        ],\n        keywords: [\n            'classic',\n            'elegant',\n            'formal',\n            'timeless',\n            'refined'\n        ],\n        compatibility: [\n            'contemporary',\n            'rustic'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    contemporary: {\n        colors: [\n            '#FFFFFF',\n            '#000000',\n            '#808080',\n            '#4169E1'\n        ],\n        materials: [\n            'mixed',\n            'glass',\n            'metal',\n            'fabric'\n        ],\n        patterns: [\n            'mixed',\n            'current',\n            'sophisticated'\n        ],\n        keywords: [\n            'current',\n            'sophisticated',\n            'mixed',\n            'balanced',\n            'updated'\n        ],\n        compatibility: [\n            'modern',\n            'traditional',\n            'scandinavian'\n        ],\n        opposites: []\n    },\n    rustic: {\n        colors: [\n            '#8B4513',\n            '#A0522D',\n            '#CD853F',\n            '#DEB887'\n        ],\n        materials: [\n            'reclaimed wood',\n            'stone',\n            'iron',\n            'natural fiber'\n        ],\n        patterns: [\n            'natural',\n            'weathered',\n            'textured'\n        ],\n        keywords: [\n            'natural',\n            'warm',\n            'cozy',\n            'weathered',\n            'handcrafted'\n        ],\n        compatibility: [\n            'traditional',\n            'boho'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    }\n};\nclass StyleMatcher {\n    /**\n   * Analyze room colors and suggest compatible styles\n   */ static analyzeRoomStyle(roomAnalysis) {\n        const colorPalette = roomAnalysis.colorPalette || [];\n        const styleScores = {};\n        const reasoning = [];\n        // Initialize scores\n        Object.keys(STYLE_CHARACTERISTICS).forEach((style)=>{\n            styleScores[style] = 0;\n        });\n        // Analyze color compatibility\n        colorPalette.forEach((color)=>{\n            Object.entries(STYLE_CHARACTERISTICS).forEach(([style, characteristics])=>{\n                const colorMatch = this.calculateColorSimilarity(color, characteristics.colors);\n                styleScores[style] += colorMatch * 10;\n            });\n        });\n        // Analyze furniture styles if available\n        if (roomAnalysis.existingFurniture) {\n            roomAnalysis.existingFurniture.forEach((furniture)=>{\n                // Simple heuristic based on furniture type\n                if (furniture.type.includes('modern') || furniture.type.includes('sleek')) {\n                    styleScores.modern += 5;\n                    styleScores.contemporary += 3;\n                }\n                if (furniture.type.includes('wood') || furniture.type.includes('natural')) {\n                    styleScores.scandinavian += 5;\n                    styleScores.rustic += 3;\n                }\n            });\n        }\n        // Analyze lighting for style hints\n        if (roomAnalysis.lightingSources) {\n            const naturalLight = roomAnalysis.lightingSources.filter((l)=>l.type === 'natural').length;\n            if (naturalLight > 0) {\n                styleScores.scandinavian += naturalLight * 2;\n                styleScores.minimalist += naturalLight * 1.5;\n                reasoning.push(`Good natural lighting suggests Scandinavian or minimalist styles`);\n            }\n        }\n        // Normalize scores\n        const maxScore = Math.max(...Object.values(styleScores));\n        if (maxScore > 0) {\n            Object.keys(styleScores).forEach((style)=>{\n                styleScores[style] = styleScores[style] / maxScore;\n            });\n        }\n        // Get top 3 suggested styles\n        const sortedStyles = Object.entries(styleScores).sort(([, a], [, b])=>b - a).slice(0, 3).map(([style])=>style);\n        return {\n            suggestedStyles: sortedStyles,\n            confidence: styleScores,\n            reasoning\n        };\n    }\n    /**\n   * Calculate style compatibility between furniture items\n   */ static calculateStyleCompatibility(item1Style, item2Style) {\n        let compatibility = 0;\n        let totalComparisons = 0;\n        item1Style.forEach((style1)=>{\n            item2Style.forEach((style2)=>{\n                totalComparisons++;\n                if (style1 === style2) {\n                    compatibility += 1.0; // Perfect match\n                } else if (STYLE_CHARACTERISTICS[style1].compatibility.includes(style2)) {\n                    compatibility += 0.7; // Good compatibility\n                } else if (STYLE_CHARACTERISTICS[style1].opposites.includes(style2)) {\n                    compatibility += 0.1; // Poor compatibility\n                } else {\n                    compatibility += 0.4; // Neutral\n                }\n            });\n        });\n        return totalComparisons > 0 ? compatibility / totalComparisons : 0;\n    }\n    /**\n   * Score furniture recommendations based on style coherence\n   */ static scoreStyleCoherence(recommendations, targetStyle) {\n        const itemScores = {};\n        const suggestions = [];\n        let totalScore = 0;\n        recommendations.forEach((item)=>{\n            let itemScore = 0;\n            // Direct style match\n            if (item.style.includes(targetStyle)) {\n                itemScore += 1.0;\n            }\n            // Compatible style match\n            const compatibleStyles = STYLE_CHARACTERISTICS[targetStyle].compatibility;\n            const compatibilityBonus = item.style.filter((style)=>compatibleStyles.includes(style)).length * 0.7;\n            itemScore += compatibilityBonus;\n            // Opposite style penalty\n            const oppositeStyles = STYLE_CHARACTERISTICS[targetStyle].opposites;\n            const oppositesPenalty = item.style.filter((style)=>oppositeStyles.includes(style)).length * 0.5;\n            itemScore -= oppositesPenalty;\n            // Normalize score\n            itemScore = Math.max(0, Math.min(1, itemScore));\n            itemScores[item.id] = itemScore;\n            totalScore += itemScore;\n            // Generate suggestions\n            if (itemScore < 0.5) {\n                suggestions.push(`Consider replacing ${item.name} with a more ${targetStyle}-style alternative`);\n            }\n        });\n        const overallScore = recommendations.length > 0 ? totalScore / recommendations.length : 0;\n        return {\n            overallScore,\n            itemScores,\n            suggestions\n        };\n    }\n    /**\n   * Generate style-based product alternatives\n   */ static generateStyleAlternatives(currentItem, targetStyle, availableProducts) {\n        return availableProducts.filter((product)=>product.category === currentItem.category && product.id !== currentItem.id && product.style.includes(targetStyle)).sort((a, b)=>{\n            // Score based on style match and price similarity\n            const aStyleScore = a.style.includes(targetStyle) ? 1 : 0.5;\n            const bStyleScore = b.style.includes(targetStyle) ? 1 : 0.5;\n            const aPriceDiff = Math.abs(a.price - currentItem.price) / currentItem.price;\n            const bPriceDiff = Math.abs(b.price - currentItem.price) / currentItem.price;\n            const aScore = aStyleScore - aPriceDiff * 0.3;\n            const bScore = bStyleScore - bPriceDiff * 0.3;\n            return bScore - aScore;\n        }).slice(0, 3);\n    }\n    /**\n   * Calculate color similarity using simple RGB distance\n   */ static calculateColorSimilarity(color1, colorPalette) {\n        const rgb1 = this.hexToRgb(color1);\n        if (!rgb1) return 0;\n        let maxSimilarity = 0;\n        colorPalette.forEach((color2)=>{\n            const rgb2 = this.hexToRgb(color2);\n            if (!rgb2) return;\n            const distance = Math.sqrt(Math.pow(rgb1.r - rgb2.r, 2) + Math.pow(rgb1.g - rgb2.g, 2) + Math.pow(rgb1.b - rgb2.b, 2));\n            // Normalize distance (max distance is ~441 for RGB)\n            const similarity = 1 - distance / 441;\n            maxSimilarity = Math.max(maxSimilarity, similarity);\n        });\n        return maxSimilarity;\n    }\n    /**\n   * Convert hex color to RGB\n   */ static hexToRgb(hex) {\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16)\n        } : null;\n    }\n    /**\n   * Get style description and tips\n   */ static getStyleGuide(style) {\n        const guides = {\n            modern: {\n                description: 'Clean lines, minimal clutter, and a focus on function over form.',\n                tips: [\n                    'Use neutral colors with bold accent pieces',\n                    'Choose furniture with geometric shapes',\n                    'Minimize decorative elements',\n                    'Focus on quality over quantity'\n                ],\n                doAndDonts: {\n                    dos: [\n                        'Use glass and metal materials',\n                        'Keep surfaces clutter-free',\n                        'Choose statement lighting'\n                    ],\n                    donts: [\n                        'Mix too many patterns',\n                        'Use ornate decorations',\n                        'Overcrowd the space'\n                    ]\n                }\n            },\n            minimalist: {\n                description: 'Less is more philosophy with emphasis on simplicity and functionality.',\n                tips: [\n                    'Stick to a neutral color palette',\n                    'Choose multi-functional furniture',\n                    'Keep decorations to a minimum',\n                    'Focus on natural light'\n                ],\n                doAndDonts: {\n                    dos: [\n                        'Use hidden storage',\n                        'Choose quality basics',\n                        'Embrace negative space'\n                    ],\n                    donts: [\n                        'Display too many items',\n                        'Use bold patterns',\n                        'Add unnecessary furniture'\n                    ]\n                }\n            }\n        };\n        return guides[style] || {\n            description: 'A unique style with its own characteristics.',\n            tips: [\n                'Follow your personal taste',\n                'Mix and match thoughtfully'\n            ],\n            doAndDonts: {\n                dos: [\n                    'Be creative'\n                ],\n                donts: [\n                    'Overthink it'\n                ]\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/style-matcher.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/api-helpers.ts":
/*!**********************************!*\
  !*** ./src/utils/api-helpers.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateUniqueId: () => (/* binding */ generateUniqueId),\n/* harmony export */   isValidDesignStyle: () => (/* binding */ isValidDesignStyle),\n/* harmony export */   isValidRoomType: () => (/* binding */ isValidRoomType),\n/* harmony export */   logAPICall: () => (/* binding */ logAPICall),\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit),\n/* harmony export */   sanitizeFilename: () => (/* binding */ sanitizeFilename),\n/* harmony export */   validateBudget: () => (/* binding */ validateBudget),\n/* harmony export */   validateFileUpload: () => (/* binding */ validateFileUpload),\n/* harmony export */   validateRequired: () => (/* binding */ validateRequired),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// API utility functions and error handling\n\nclass APIError extends Error {\n    constructor(message, statusCode = 500, code){\n        super(message), this.statusCode = statusCode, this.code = code;\n        this.name = 'APIError';\n    }\n}\nfunction createErrorResponse(error, statusCode = 500) {\n    let message;\n    let code;\n    let errorCode;\n    if (error instanceof APIError) {\n        message = error.message;\n        code = error.statusCode;\n        errorCode = error.code;\n    } else if (error instanceof Error) {\n        message = error.message;\n        code = statusCode;\n    } else {\n        message = error;\n        code = statusCode;\n    }\n    const response = {\n        success: false,\n        error: message,\n        ...errorCode && {\n            code: errorCode\n        }\n    };\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n        status: code\n    });\n}\nfunction createSuccessResponse(data, message) {\n    const response = {\n        success: true,\n        data,\n        ...message && {\n            message\n        }\n    };\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n}\nasync function withErrorHandling(handler) {\n    try {\n        const result = await handler();\n        return createSuccessResponse(result);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createErrorResponse(error);\n    }\n}\nfunction validateRequired(data, requiredFields) {\n    const missingFields = requiredFields.filter((field)=>data[field] === undefined || data[field] === null || data[field] === '');\n    if (missingFields.length > 0) {\n        throw new APIError(`Missing required fields: ${missingFields.join(', ')}`, 400, 'MISSING_FIELDS');\n    }\n}\nfunction validateFileUpload(file) {\n    const MAX_SIZE = 10 * 1024 * 1024; // 10MB\n    const ALLOWED_TYPES = [\n        'image/jpeg',\n        'image/png',\n        'image/webp'\n    ];\n    if (!file) {\n        throw new APIError('No file provided', 400, 'NO_FILE');\n    }\n    if (!ALLOWED_TYPES.includes(file.type)) {\n        throw new APIError('Invalid file type. Only JPEG, PNG, and WebP are allowed.', 400, 'INVALID_FILE_TYPE');\n    }\n    if (file.size > MAX_SIZE) {\n        throw new APIError('File too large. Maximum size is 10MB.', 400, 'FILE_TOO_LARGE');\n    }\n}\nfunction validateBudget(budget) {\n    if (typeof budget !== 'number' || isNaN(budget)) {\n        throw new APIError('Budget must be a valid number', 400, 'INVALID_BUDGET');\n    }\n    if (budget <= 0) {\n        throw new APIError('Budget must be greater than 0', 400, 'BUDGET_TOO_LOW');\n    }\n    if (budget > 100000) {\n        throw new APIError('Budget cannot exceed $100,000', 400, 'BUDGET_TOO_HIGH');\n    }\n}\nfunction sanitizeFilename(filename) {\n    // Remove or replace unsafe characters\n    return filename.replace(/[^a-zA-Z0-9.-]/g, '_').replace(/_{2,}/g, '_').toLowerCase();\n}\nfunction generateUniqueId() {\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\nfunction formatFileSize(bytes) {\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n}\nfunction isValidRoomType(roomType) {\n    const validTypes = [\n        'living_room',\n        'bedroom',\n        'kitchen',\n        'bathroom',\n        'dining_room',\n        'office',\n        'nursery',\n        'guest_room'\n    ];\n    return validTypes.includes(roomType);\n}\nfunction isValidDesignStyle(style) {\n    const validStyles = [\n        'modern',\n        'minimalist',\n        'boho',\n        'industrial',\n        'scandinavian',\n        'traditional',\n        'contemporary',\n        'rustic'\n    ];\n    return validStyles.includes(style);\n}\nfunction logAPICall(method, endpoint, duration, success, userId) {\n    const logData = {\n        timestamp: new Date().toISOString(),\n        method,\n        endpoint,\n        duration,\n        success,\n        userId: userId || 'anonymous'\n    };\n    // In production, you would send this to a logging service\n    console.log('API Call:', logData);\n}\nfunction rateLimit(identifier, maxRequests = 100, windowMs = 15 * 60 * 1000 // 15 minutes\n) {\n    // Simple in-memory rate limiting\n    // In production, use Redis or a proper rate limiting service\n    const now = Date.now();\n    const windowStart = now - windowMs;\n    // This is a simplified implementation\n    // In production, implement proper rate limiting\n    return true; // Allow all requests for now\n}\nfunction corsHeaders() {\n    return {\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvYXBpLWhlbHBlcnMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJDQUEyQztBQUVBO0FBR3BDLE1BQU1DLGlCQUFpQkM7SUFDNUJDLFlBQ0VDLE9BQWUsRUFDZixhQUE0QixHQUFHLEVBQy9CLElBQW9CLENBQ3BCO1FBQ0EsS0FBSyxDQUFDQSxlQUhDQyxhQUFBQSxpQkFDQUMsT0FBQUE7UUFHUCxJQUFJLENBQUNDLElBQUksR0FBRztJQUNkO0FBQ0Y7QUFFTyxTQUFTQyxvQkFDZEMsS0FBZ0MsRUFDaENKLGFBQXFCLEdBQUc7SUFFeEIsSUFBSUQ7SUFDSixJQUFJRTtJQUNKLElBQUlJO0lBRUosSUFBSUQsaUJBQWlCUixVQUFVO1FBQzdCRyxVQUFVSyxNQUFNTCxPQUFPO1FBQ3ZCRSxPQUFPRyxNQUFNSixVQUFVO1FBQ3ZCSyxZQUFZRCxNQUFNSCxJQUFJO0lBQ3hCLE9BQU8sSUFBSUcsaUJBQWlCUCxPQUFPO1FBQ2pDRSxVQUFVSyxNQUFNTCxPQUFPO1FBQ3ZCRSxPQUFPRDtJQUNULE9BQU87UUFDTEQsVUFBVUs7UUFDVkgsT0FBT0Q7SUFDVDtJQUVBLE1BQU1NLFdBQStCO1FBQ25DQyxTQUFTO1FBQ1RILE9BQU9MO1FBQ1AsR0FBSU0sYUFBYTtZQUFFSixNQUFNSTtRQUFVLENBQUM7SUFDdEM7SUFFQSxPQUFPVixxREFBWUEsQ0FBQ2EsSUFBSSxDQUFDRixVQUFVO1FBQUVHLFFBQVFSO0lBQUs7QUFDcEQ7QUFFTyxTQUFTUyxzQkFDZEMsSUFBTyxFQUNQWixPQUFnQjtJQUVoQixNQUFNTyxXQUEyQjtRQUMvQkMsU0FBUztRQUNUSTtRQUNBLEdBQUlaLFdBQVc7WUFBRUE7UUFBUSxDQUFDO0lBQzVCO0lBRUEsT0FBT0oscURBQVlBLENBQUNhLElBQUksQ0FBQ0Y7QUFDM0I7QUFFTyxlQUFlTSxrQkFDcEJDLE9BQXlCO0lBRXpCLElBQUk7UUFDRixNQUFNQyxTQUFTLE1BQU1EO1FBQ3JCLE9BQU9ILHNCQUFzQkk7SUFDL0IsRUFBRSxPQUFPVixPQUFPO1FBQ2RXLFFBQVFYLEtBQUssQ0FBQyxjQUFjQTtRQUM1QixPQUFPRCxvQkFBb0JDO0lBQzdCO0FBQ0Y7QUFFTyxTQUFTWSxpQkFDZEwsSUFBeUIsRUFDekJNLGNBQXdCO0lBRXhCLE1BQU1DLGdCQUFnQkQsZUFBZUUsTUFBTSxDQUFDQyxDQUFBQSxRQUMxQ1QsSUFBSSxDQUFDUyxNQUFNLEtBQUtDLGFBQWFWLElBQUksQ0FBQ1MsTUFBTSxLQUFLLFFBQVFULElBQUksQ0FBQ1MsTUFBTSxLQUFLO0lBR3ZFLElBQUlGLGNBQWNJLE1BQU0sR0FBRyxHQUFHO1FBQzVCLE1BQU0sSUFBSTFCLFNBQ1IsQ0FBQyx5QkFBeUIsRUFBRXNCLGNBQWNLLElBQUksQ0FBQyxPQUFPLEVBQ3RELEtBQ0E7SUFFSjtBQUNGO0FBRU8sU0FBU0MsbUJBQW1CQyxJQUFVO0lBQzNDLE1BQU1DLFdBQVcsS0FBSyxPQUFPLE1BQU0sT0FBTztJQUMxQyxNQUFNQyxnQkFBZ0I7UUFBQztRQUFjO1FBQWE7S0FBYTtJQUUvRCxJQUFJLENBQUNGLE1BQU07UUFDVCxNQUFNLElBQUk3QixTQUFTLG9CQUFvQixLQUFLO0lBQzlDO0lBRUEsSUFBSSxDQUFDK0IsY0FBY0MsUUFBUSxDQUFDSCxLQUFLSSxJQUFJLEdBQUc7UUFDdEMsTUFBTSxJQUFJakMsU0FDUiw0REFDQSxLQUNBO0lBRUo7SUFFQSxJQUFJNkIsS0FBS0ssSUFBSSxHQUFHSixVQUFVO1FBQ3hCLE1BQU0sSUFBSTlCLFNBQ1IseUNBQ0EsS0FDQTtJQUVKO0FBQ0Y7QUFFTyxTQUFTbUMsZUFBZUMsTUFBYztJQUMzQyxJQUFJLE9BQU9BLFdBQVcsWUFBWUMsTUFBTUQsU0FBUztRQUMvQyxNQUFNLElBQUlwQyxTQUFTLGlDQUFpQyxLQUFLO0lBQzNEO0lBRUEsSUFBSW9DLFVBQVUsR0FBRztRQUNmLE1BQU0sSUFBSXBDLFNBQVMsaUNBQWlDLEtBQUs7SUFDM0Q7SUFFQSxJQUFJb0MsU0FBUyxRQUFRO1FBQ25CLE1BQU0sSUFBSXBDLFNBQVMsaUNBQWlDLEtBQUs7SUFDM0Q7QUFDRjtBQUVPLFNBQVNzQyxpQkFBaUJDLFFBQWdCO0lBQy9DLHNDQUFzQztJQUN0QyxPQUFPQSxTQUNKQyxPQUFPLENBQUMsbUJBQW1CLEtBQzNCQSxPQUFPLENBQUMsVUFBVSxLQUNsQkMsV0FBVztBQUNoQjtBQUVPLFNBQVNDO0lBQ2QsT0FBTyxHQUFHQyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO0FBQ25FO0FBRU8sU0FBU0MsZUFBZUMsS0FBYTtJQUMxQyxNQUFNQyxRQUFRO1FBQUM7UUFBUztRQUFNO1FBQU07S0FBSztJQUN6QyxJQUFJRCxVQUFVLEdBQUcsT0FBTztJQUV4QixNQUFNRSxJQUFJUCxLQUFLUSxLQUFLLENBQUNSLEtBQUtTLEdBQUcsQ0FBQ0osU0FBU0wsS0FBS1MsR0FBRyxDQUFDO0lBQ2hELE9BQU9ULEtBQUtVLEtBQUssQ0FBQ0wsUUFBUUwsS0FBS1csR0FBRyxDQUFDLE1BQU1KLEtBQUssT0FBTyxNQUFNLE1BQU1ELEtBQUssQ0FBQ0MsRUFBRTtBQUMzRTtBQUVPLFNBQVNLLGdCQUFnQkMsUUFBZ0I7SUFDOUMsTUFBTUMsYUFBYTtRQUNqQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDRCxPQUFPQSxXQUFXM0IsUUFBUSxDQUFDMEI7QUFDN0I7QUFFTyxTQUFTRSxtQkFBbUJDLEtBQWE7SUFDOUMsTUFBTUMsY0FBYztRQUNsQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDRCxPQUFPQSxZQUFZOUIsUUFBUSxDQUFDNkI7QUFDOUI7QUFFTyxTQUFTRSxXQUNkQyxNQUFjLEVBQ2RDLFFBQWdCLEVBQ2hCQyxRQUFnQixFQUNoQnZELE9BQWdCLEVBQ2hCd0QsTUFBZTtJQUVmLE1BQU1DLFVBQVU7UUFDZEMsV0FBVyxJQUFJMUIsT0FBTzJCLFdBQVc7UUFDakNOO1FBQ0FDO1FBQ0FDO1FBQ0F2RDtRQUNBd0QsUUFBUUEsVUFBVTtJQUNwQjtJQUVBLDBEQUEwRDtJQUMxRGhELFFBQVFtQyxHQUFHLENBQUMsYUFBYWM7QUFDM0I7QUFFTyxTQUFTRyxVQUNkQyxVQUFrQixFQUNsQkMsY0FBc0IsR0FBRyxFQUN6QkMsV0FBbUIsS0FBSyxLQUFLLEtBQUssYUFBYTtBQUFkO0lBRWpDLGlDQUFpQztJQUNqQyw2REFBNkQ7SUFFN0QsTUFBTTlCLE1BQU1ELEtBQUtDLEdBQUc7SUFDcEIsTUFBTStCLGNBQWMvQixNQUFNOEI7SUFFMUIsc0NBQXNDO0lBQ3RDLGdEQUFnRDtJQUNoRCxPQUFPLE1BQU0sNkJBQTZCO0FBQzVDO0FBRU8sU0FBU0U7SUFDZCxPQUFPO1FBQ0wsK0JBQStCO1FBQy9CLGdDQUFnQztRQUNoQyxnQ0FBZ0M7SUFDbEM7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9NeSBiaWcgaWRlYS9pbnRlcmlvci1kZXNpZ24tYWkvc3JjL3V0aWxzL2FwaS1oZWxwZXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFQSSB1dGlsaXR5IGZ1bmN0aW9ucyBhbmQgZXJyb3IgaGFuZGxpbmdcblxuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgQVBJUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzJztcblxuZXhwb3J0IGNsYXNzIEFQSUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihcbiAgICBtZXNzYWdlOiBzdHJpbmcsXG4gICAgcHVibGljIHN0YXR1c0NvZGU6IG51bWJlciA9IDUwMCxcbiAgICBwdWJsaWMgY29kZT86IHN0cmluZ1xuICApIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzLm5hbWUgPSAnQVBJRXJyb3InO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVFcnJvclJlc3BvbnNlKFxuICBlcnJvcjogc3RyaW5nIHwgRXJyb3IgfCBBUElFcnJvcixcbiAgc3RhdHVzQ29kZTogbnVtYmVyID0gNTAwXG4pOiBOZXh0UmVzcG9uc2U8QVBJUmVzcG9uc2U8bmV2ZXI+PiB7XG4gIGxldCBtZXNzYWdlOiBzdHJpbmc7XG4gIGxldCBjb2RlOiBudW1iZXI7XG4gIGxldCBlcnJvckNvZGU6IHN0cmluZyB8IHVuZGVmaW5lZDtcblxuICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBBUElFcnJvcikge1xuICAgIG1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlO1xuICAgIGNvZGUgPSBlcnJvci5zdGF0dXNDb2RlO1xuICAgIGVycm9yQ29kZSA9IGVycm9yLmNvZGU7XG4gIH0gZWxzZSBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgIG1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlO1xuICAgIGNvZGUgPSBzdGF0dXNDb2RlO1xuICB9IGVsc2Uge1xuICAgIG1lc3NhZ2UgPSBlcnJvcjtcbiAgICBjb2RlID0gc3RhdHVzQ29kZTtcbiAgfVxuXG4gIGNvbnN0IHJlc3BvbnNlOiBBUElSZXNwb25zZTxuZXZlcj4gPSB7XG4gICAgc3VjY2VzczogZmFsc2UsXG4gICAgZXJyb3I6IG1lc3NhZ2UsXG4gICAgLi4uKGVycm9yQ29kZSAmJiB7IGNvZGU6IGVycm9yQ29kZSB9KVxuICB9O1xuXG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXNwb25zZSwgeyBzdGF0dXM6IGNvZGUgfSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdWNjZXNzUmVzcG9uc2U8VD4oXG4gIGRhdGE6IFQsXG4gIG1lc3NhZ2U/OiBzdHJpbmdcbik6IE5leHRSZXNwb25zZTxBUElSZXNwb25zZTxUPj4ge1xuICBjb25zdCByZXNwb25zZTogQVBJUmVzcG9uc2U8VD4gPSB7XG4gICAgc3VjY2VzczogdHJ1ZSxcbiAgICBkYXRhLFxuICAgIC4uLihtZXNzYWdlICYmIHsgbWVzc2FnZSB9KVxuICB9O1xuXG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXNwb25zZSk7XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB3aXRoRXJyb3JIYW5kbGluZzxUPihcbiAgaGFuZGxlcjogKCkgPT4gUHJvbWlzZTxUPlxuKTogUHJvbWlzZTxOZXh0UmVzcG9uc2U8QVBJUmVzcG9uc2U8VD4+IHwgTmV4dFJlc3BvbnNlPEFQSVJlc3BvbnNlPG5ldmVyPj4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBoYW5kbGVyKCk7XG4gICAgcmV0dXJuIGNyZWF0ZVN1Y2Nlc3NSZXNwb25zZShyZXN1bHQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0FQSSBFcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIGNyZWF0ZUVycm9yUmVzcG9uc2UoZXJyb3IgYXMgRXJyb3IpO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVJlcXVpcmVkKFxuICBkYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LFxuICByZXF1aXJlZEZpZWxkczogc3RyaW5nW11cbik6IHZvaWQge1xuICBjb25zdCBtaXNzaW5nRmllbGRzID0gcmVxdWlyZWRGaWVsZHMuZmlsdGVyKGZpZWxkID0+IFxuICAgIGRhdGFbZmllbGRdID09PSB1bmRlZmluZWQgfHwgZGF0YVtmaWVsZF0gPT09IG51bGwgfHwgZGF0YVtmaWVsZF0gPT09ICcnXG4gICk7XG5cbiAgaWYgKG1pc3NpbmdGaWVsZHMubGVuZ3RoID4gMCkge1xuICAgIHRocm93IG5ldyBBUElFcnJvcihcbiAgICAgIGBNaXNzaW5nIHJlcXVpcmVkIGZpZWxkczogJHttaXNzaW5nRmllbGRzLmpvaW4oJywgJyl9YCxcbiAgICAgIDQwMCxcbiAgICAgICdNSVNTSU5HX0ZJRUxEUydcbiAgICApO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUZpbGVVcGxvYWQoZmlsZTogRmlsZSk6IHZvaWQge1xuICBjb25zdCBNQVhfU0laRSA9IDEwICogMTAyNCAqIDEwMjQ7IC8vIDEwTUJcbiAgY29uc3QgQUxMT1dFRF9UWVBFUyA9IFsnaW1hZ2UvanBlZycsICdpbWFnZS9wbmcnLCAnaW1hZ2Uvd2VicCddO1xuXG4gIGlmICghZmlsZSkge1xuICAgIHRocm93IG5ldyBBUElFcnJvcignTm8gZmlsZSBwcm92aWRlZCcsIDQwMCwgJ05PX0ZJTEUnKTtcbiAgfVxuXG4gIGlmICghQUxMT1dFRF9UWVBFUy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgdGhyb3cgbmV3IEFQSUVycm9yKFxuICAgICAgJ0ludmFsaWQgZmlsZSB0eXBlLiBPbmx5IEpQRUcsIFBORywgYW5kIFdlYlAgYXJlIGFsbG93ZWQuJyxcbiAgICAgIDQwMCxcbiAgICAgICdJTlZBTElEX0ZJTEVfVFlQRSdcbiAgICApO1xuICB9XG5cbiAgaWYgKGZpbGUuc2l6ZSA+IE1BWF9TSVpFKSB7XG4gICAgdGhyb3cgbmV3IEFQSUVycm9yKFxuICAgICAgJ0ZpbGUgdG9vIGxhcmdlLiBNYXhpbXVtIHNpemUgaXMgMTBNQi4nLFxuICAgICAgNDAwLFxuICAgICAgJ0ZJTEVfVE9PX0xBUkdFJ1xuICAgICk7XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlQnVkZ2V0KGJ1ZGdldDogbnVtYmVyKTogdm9pZCB7XG4gIGlmICh0eXBlb2YgYnVkZ2V0ICE9PSAnbnVtYmVyJyB8fCBpc05hTihidWRnZXQpKSB7XG4gICAgdGhyb3cgbmV3IEFQSUVycm9yKCdCdWRnZXQgbXVzdCBiZSBhIHZhbGlkIG51bWJlcicsIDQwMCwgJ0lOVkFMSURfQlVER0VUJyk7XG4gIH1cblxuICBpZiAoYnVkZ2V0IDw9IDApIHtcbiAgICB0aHJvdyBuZXcgQVBJRXJyb3IoJ0J1ZGdldCBtdXN0IGJlIGdyZWF0ZXIgdGhhbiAwJywgNDAwLCAnQlVER0VUX1RPT19MT1cnKTtcbiAgfVxuXG4gIGlmIChidWRnZXQgPiAxMDAwMDApIHtcbiAgICB0aHJvdyBuZXcgQVBJRXJyb3IoJ0J1ZGdldCBjYW5ub3QgZXhjZWVkICQxMDAsMDAwJywgNDAwLCAnQlVER0VUX1RPT19ISUdIJyk7XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNhbml0aXplRmlsZW5hbWUoZmlsZW5hbWU6IHN0cmluZyk6IHN0cmluZyB7XG4gIC8vIFJlbW92ZSBvciByZXBsYWNlIHVuc2FmZSBjaGFyYWN0ZXJzXG4gIHJldHVybiBmaWxlbmFtZVxuICAgIC5yZXBsYWNlKC9bXmEtekEtWjAtOS4tXS9nLCAnXycpXG4gICAgLnJlcGxhY2UoL197Mix9L2csICdfJylcbiAgICAudG9Mb3dlckNhc2UoKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlVW5pcXVlSWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIGAke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRGaWxlU2l6ZShieXRlczogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3Qgc2l6ZXMgPSBbJ0J5dGVzJywgJ0tCJywgJ01CJywgJ0dCJ107XG4gIGlmIChieXRlcyA9PT0gMCkgcmV0dXJuICcwIEJ5dGVzJztcbiAgXG4gIGNvbnN0IGkgPSBNYXRoLmZsb29yKE1hdGgubG9nKGJ5dGVzKSAvIE1hdGgubG9nKDEwMjQpKTtcbiAgcmV0dXJuIE1hdGgucm91bmQoYnl0ZXMgLyBNYXRoLnBvdygxMDI0LCBpKSAqIDEwMCkgLyAxMDAgKyAnICcgKyBzaXplc1tpXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzVmFsaWRSb29tVHlwZShyb29tVHlwZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IHZhbGlkVHlwZXMgPSBbXG4gICAgJ2xpdmluZ19yb29tJyxcbiAgICAnYmVkcm9vbScsIFxuICAgICdraXRjaGVuJyxcbiAgICAnYmF0aHJvb20nLFxuICAgICdkaW5pbmdfcm9vbScsXG4gICAgJ29mZmljZScsXG4gICAgJ251cnNlcnknLFxuICAgICdndWVzdF9yb29tJ1xuICBdO1xuICByZXR1cm4gdmFsaWRUeXBlcy5pbmNsdWRlcyhyb29tVHlwZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkRGVzaWduU3R5bGUoc3R5bGU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICBjb25zdCB2YWxpZFN0eWxlcyA9IFtcbiAgICAnbW9kZXJuJyxcbiAgICAnbWluaW1hbGlzdCcsXG4gICAgJ2JvaG8nLFxuICAgICdpbmR1c3RyaWFsJyxcbiAgICAnc2NhbmRpbmF2aWFuJyxcbiAgICAndHJhZGl0aW9uYWwnLFxuICAgICdjb250ZW1wb3JhcnknLFxuICAgICdydXN0aWMnXG4gIF07XG4gIHJldHVybiB2YWxpZFN0eWxlcy5pbmNsdWRlcyhzdHlsZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBsb2dBUElDYWxsKFxuICBtZXRob2Q6IHN0cmluZyxcbiAgZW5kcG9pbnQ6IHN0cmluZyxcbiAgZHVyYXRpb246IG51bWJlcixcbiAgc3VjY2VzczogYm9vbGVhbixcbiAgdXNlcklkPzogc3RyaW5nXG4pOiB2b2lkIHtcbiAgY29uc3QgbG9nRGF0YSA9IHtcbiAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICBtZXRob2QsXG4gICAgZW5kcG9pbnQsXG4gICAgZHVyYXRpb24sXG4gICAgc3VjY2VzcyxcbiAgICB1c2VySWQ6IHVzZXJJZCB8fCAnYW5vbnltb3VzJ1xuICB9O1xuXG4gIC8vIEluIHByb2R1Y3Rpb24sIHlvdSB3b3VsZCBzZW5kIHRoaXMgdG8gYSBsb2dnaW5nIHNlcnZpY2VcbiAgY29uc29sZS5sb2coJ0FQSSBDYWxsOicsIGxvZ0RhdGEpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcmF0ZUxpbWl0KFxuICBpZGVudGlmaWVyOiBzdHJpbmcsXG4gIG1heFJlcXVlc3RzOiBudW1iZXIgPSAxMDAsXG4gIHdpbmRvd01zOiBudW1iZXIgPSAxNSAqIDYwICogMTAwMCAvLyAxNSBtaW51dGVzXG4pOiBib29sZWFuIHtcbiAgLy8gU2ltcGxlIGluLW1lbW9yeSByYXRlIGxpbWl0aW5nXG4gIC8vIEluIHByb2R1Y3Rpb24sIHVzZSBSZWRpcyBvciBhIHByb3BlciByYXRlIGxpbWl0aW5nIHNlcnZpY2VcbiAgXG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IHdpbmRvd1N0YXJ0ID0gbm93IC0gd2luZG93TXM7XG4gIFxuICAvLyBUaGlzIGlzIGEgc2ltcGxpZmllZCBpbXBsZW1lbnRhdGlvblxuICAvLyBJbiBwcm9kdWN0aW9uLCBpbXBsZW1lbnQgcHJvcGVyIHJhdGUgbGltaXRpbmdcbiAgcmV0dXJuIHRydWU7IC8vIEFsbG93IGFsbCByZXF1ZXN0cyBmb3Igbm93XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb3JzSGVhZGVycygpIHtcbiAgcmV0dXJuIHtcbiAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctT3JpZ2luJzogJyonLFxuICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1NZXRob2RzJzogJ0dFVCwgUE9TVCwgUFVULCBERUxFVEUsIE9QVElPTlMnLFxuICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1IZWFkZXJzJzogJ0NvbnRlbnQtVHlwZSwgQXV0aG9yaXphdGlvbicsXG4gIH07XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQVBJRXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwibWVzc2FnZSIsInN0YXR1c0NvZGUiLCJjb2RlIiwibmFtZSIsImNyZWF0ZUVycm9yUmVzcG9uc2UiLCJlcnJvciIsImVycm9yQ29kZSIsInJlc3BvbnNlIiwic3VjY2VzcyIsImpzb24iLCJzdGF0dXMiLCJjcmVhdGVTdWNjZXNzUmVzcG9uc2UiLCJkYXRhIiwid2l0aEVycm9ySGFuZGxpbmciLCJoYW5kbGVyIiwicmVzdWx0IiwiY29uc29sZSIsInZhbGlkYXRlUmVxdWlyZWQiLCJyZXF1aXJlZEZpZWxkcyIsIm1pc3NpbmdGaWVsZHMiLCJmaWx0ZXIiLCJmaWVsZCIsInVuZGVmaW5lZCIsImxlbmd0aCIsImpvaW4iLCJ2YWxpZGF0ZUZpbGVVcGxvYWQiLCJmaWxlIiwiTUFYX1NJWkUiLCJBTExPV0VEX1RZUEVTIiwiaW5jbHVkZXMiLCJ0eXBlIiwic2l6ZSIsInZhbGlkYXRlQnVkZ2V0IiwiYnVkZ2V0IiwiaXNOYU4iLCJzYW5pdGl6ZUZpbGVuYW1lIiwiZmlsZW5hbWUiLCJyZXBsYWNlIiwidG9Mb3dlckNhc2UiLCJnZW5lcmF0ZVVuaXF1ZUlkIiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsImZvcm1hdEZpbGVTaXplIiwiYnl0ZXMiLCJzaXplcyIsImkiLCJmbG9vciIsImxvZyIsInJvdW5kIiwicG93IiwiaXNWYWxpZFJvb21UeXBlIiwicm9vbVR5cGUiLCJ2YWxpZFR5cGVzIiwiaXNWYWxpZERlc2lnblN0eWxlIiwic3R5bGUiLCJ2YWxpZFN0eWxlcyIsImxvZ0FQSUNhbGwiLCJtZXRob2QiLCJlbmRwb2ludCIsImR1cmF0aW9uIiwidXNlcklkIiwibG9nRGF0YSIsInRpbWVzdGFtcCIsInRvSVNPU3RyaW5nIiwicmF0ZUxpbWl0IiwiaWRlbnRpZmllciIsIm1heFJlcXVlc3RzIiwid2luZG93TXMiLCJ3aW5kb3dTdGFydCIsImNvcnNIZWFkZXJzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/api-helpers.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();