"use strict";(()=>{var e={};e.id=146,e.ids=[146],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15688:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>v,serverHooks:()=>k,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>I});var n={};r.r(n),r.d(n,{GET:()=>l,OPTIONS:()=>P,POST:()=>m});var a=r(96559),s=r(48088),o=r(37719),i=r(34108),u=r(5689),c=r(17733);async function l(e){try{let{searchParams:t}=new URL(e.url);switch(t.get("action")){case"search":return d(t);case"similar":return p(t);case"alternatives":return g(t);case"random":return y(t);default:return(0,c.WX)("Invalid action parameter",400)}}catch(e){return console.error("Products API error:",e),(0,c.WX)(e)}}async function d(e){let t=e.get("category"),r=e.get("style"),n=e.get("minPrice"),a=e.get("maxPrice"),s=e.get("retailer"),o=parseInt(e.get("limit")||"20"),u=n&&a?{min:parseFloat(n),max:parseFloat(a)}:void 0,l=i.T.searchProducts(t||void 0,r||void 0,u,s||void 0).slice(0,o);return(0,c.$y)({products:l,total:l.length,filters:{category:t,style:r,priceRange:u,retailer:s}})}async function p(e){let t=e.get("productId"),r=parseInt(e.get("limit")||"5");if(!t)return(0,c.WX)("Product ID is required",400);let n=i.T.getProductById(t);if(!n)return(0,c.WX)("Product not found",404);let a=i.T.getSimilarProducts(t,r);return(0,c.$y)({originalProduct:n,similarProducts:a,total:a.length})}async function g(e){let t=e.get("productId"),r=e.get("targetStyle"),n=parseInt(e.get("limit")||"3");if(!t||!r)return(0,c.WX)("Product ID and target style are required",400);let a=i.T.getProductById(t);if(!a)return(0,c.WX)("Product not found",404);let s=i.T.searchProducts(),o={...a,placement:{suggestedPosition:{x:0,y:0},zone:"main"},compatibility:{roomType:[],existingFurniture:[],styleMatch:0}},l=u.q.generateStyleAlternatives(o,r,s).slice(0,n);return(0,c.$y)({originalProduct:a,targetStyle:r,alternatives:l,total:l.length})}async function y(e){let t=parseInt(e.get("count")||"10"),r=e.get("category"),n=i.T.getRandomProducts(2*t);return r&&(n=n.filter(e=>e.category===r)),(0,c.$y)({products:n.slice(0,t),total:n.length})}async function m(e){try{let t=await e.json(),{action:r}=t;switch(r){case"bulk-search":return f(t);case"style-analysis":return h(t);default:return(0,c.WX)("Invalid action",400)}}catch(e){return console.error("Products POST API error:",e),(0,c.WX)(e)}}async function f(e){let{searches:t}=e;if(!Array.isArray(t))return(0,c.WX)("Searches must be an array",400);let r=t.map(e=>{let{category:t,style:r,priceRange:n,retailer:a,limit:s=10}=e,o=i.T.searchProducts(t,r,n,a).slice(0,s);return{query:e,products:o,total:o.length}});return(0,c.$y)({results:r,totalQueries:t.length})}async function h(e){let{productIds:t,targetStyle:r}=e;if((0,c.Kf)(e,["productIds","targetStyle"]),!Array.isArray(t))return(0,c.WX)("Product IDs must be an array",400);let n=t.map(e=>i.T.getProductById(e)).filter(Boolean);if(0===n.length)return(0,c.WX)("No valid products found",404);let a=n.map(e=>({...e,placement:{suggestedPosition:{x:0,y:0},zone:"main"},compatibility:{roomType:[],existingFurniture:[],styleMatch:0}})),s=u.q.scoreStyleCoherence(a,r),o=u.q.getStyleGuide(r);return(0,c.$y)({products:n,targetStyle:r,styleAnalysis:s,styleGuide:o,recommendations:s.suggestions})}async function P(){return(0,c.$y)({categories:["seating","tables","storage","lighting","decor","textiles","plants"],styles:["modern","minimalist","boho","industrial","scandinavian","traditional","contemporary","rustic"],retailers:["amazon","ikea"],endpoints:{search:"/api/products?action=search&category=seating&style=modern",similar:"/api/products?action=similar&productId=ikea-kivik-sofa",alternatives:"/api/products?action=alternatives&productId=ikea-kivik-sofa&targetStyle=modern",random:"/api/products?action=random&count=10"}})}let v=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/products/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:I,serverHooks:k}=v;function S(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:I})}},17733:(e,t,r)=>{r.d(t,{$y:()=>o,Kf:()=>i,WX:()=>s});var n=r(32190);class a extends Error{constructor(e,t=500,r){super(e),this.statusCode=t,this.code=r,this.name="APIError"}}function s(e,t=500){let r,o,i;e instanceof a?(r=e.message,o=e.statusCode,i=e.code):(r=e instanceof Error?e.message:e,o=t);let u={success:!1,error:r,...i&&{code:i}};return n.NextResponse.json(u,{status:o})}function o(e,t){let r={success:!0,data:e,...t&&{message:t}};return n.NextResponse.json(r)}function i(e,t){let r=t.filter(t=>void 0===e[t]||null===e[t]||""===e[t]);if(r.length>0)throw new a(`Missing required fields: ${r.join(", ")}`,400,"MISSING_FIELDS")}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[243,580,568],()=>r(15688));module.exports=n})();