/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scrape/route";
exports.ids = ["app/api/scrape/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/scrape/route.ts */ \"(rsc)/./src/app/api/scrape/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scrape/route\",\n        pathname: \"/api/scrape\",\n        filename: \"route\",\n        bundlePath: \"app/api/scrape/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/scrape/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzY3JhcGUlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnNjcmFwZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnNjcmFwZSUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZNeSUyMGJpZyUyMGlkZWElMkZpbnRlcmlvci1kZXNpZ24tYWklMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDeUQ7QUFDdEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGLHFDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9hcHAvYXBpL3NjcmFwZS9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc2NyYXBlL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc2NyYXBlXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9zY3JhcGUvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9zcmMvYXBwL2FwaS9zY3JhcGUvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scrape/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/scrape/route.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_scrapers_scraper_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/scrapers/scraper-manager */ \"(rsc)/./src/lib/scrapers/scraper-manager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_scrapers_scraper_manager__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_scrapers_scraper_manager__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, retailer, category, query, limit } = body;\n        const scraperManager = new _lib_scrapers_scraper_manager__WEBPACK_IMPORTED_MODULE_1__.ScraperManager();\n        switch(action){\n            case 'scrape_category':\n                if (!retailer || !category) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Retailer and category are required for scraping'\n                    }, {\n                        status: 400\n                    });\n                }\n                const job = await scraperManager.startScrapingJob(retailer, category, limit || 20);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    job,\n                    message: `Started scraping ${retailer} - ${category}`\n                });\n            case 'scrape_all':\n                const categories = [\n                    'seating',\n                    'tables',\n                    'storage',\n                    'lighting',\n                    'decor'\n                ];\n                const jobs = await scraperManager.scrapeAllRetailers(categories);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    jobs,\n                    message: `Started scraping all retailers for ${categories.length} categories`\n                });\n            case 'search':\n                if (!query) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Query is required for search'\n                    }, {\n                        status: 400\n                    });\n                }\n                const retailers = retailer ? [\n                    retailer\n                ] : [\n                    'ikea',\n                    'amazon',\n                    'daraz'\n                ];\n                const searchResults = await scraperManager.searchProducts(query, retailers);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    products: searchResults,\n                    count: searchResults.length,\n                    message: `Found ${searchResults.length} products for \"${query}\"`\n                });\n            case 'get_jobs':\n                const allJobs = await scraperManager.getAllJobs();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    jobs: allJobs\n                });\n            case 'get_products':\n                const products = await scraperManager.getProductsFromDatabase(category, limit || 100);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    products,\n                    count: products.length\n                });\n            case 'update_prices':\n                await scraperManager.updateProductPrices(retailer);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: `Started price update for ${retailer || 'all retailers'}`\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action. Supported actions: scrape_category, scrape_all, search, get_jobs, get_products, update_prices'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Scraping API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        const category = searchParams.get('category');\n        const retailer = searchParams.get('retailer');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const scraperManager = new _lib_scrapers_scraper_manager__WEBPACK_IMPORTED_MODULE_1__.ScraperManager();\n        switch(action){\n            case 'products':\n                const products = await scraperManager.getProductsFromDatabase(category || undefined, limit);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    products,\n                    count: products.length\n                });\n            case 'jobs':\n                const jobs = await scraperManager.getAllJobs();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    jobs\n                });\n            case 'status':\n                const jobId = searchParams.get('jobId');\n                if (!jobId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Job ID is required'\n                    }, {\n                        status: 400\n                    });\n                }\n                const job = await scraperManager.getJobStatus(jobId);\n                if (!job) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Job not found'\n                    }, {\n                        status: 404\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    job\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: 'Scraping API is running',\n                    endpoints: {\n                        'GET /api/scrape?action=products&category=seating': 'Get scraped products',\n                        'GET /api/scrape?action=jobs': 'Get all scraping jobs',\n                        'GET /api/scrape?action=status&jobId=xxx': 'Get job status',\n                        'POST /api/scrape': 'Start scraping operations'\n                    }\n                });\n        }\n    } catch (error) {\n        console.error('Scraping API GET error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scrape/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n// Database service using Prisma\n\n// Global Prisma instance to prevent multiple connections in development\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\nclass DatabaseService {\n    // Room Management\n    static async createRoom(data) {\n        return await prisma.room.create({\n            data: {\n                ...data\n            }\n        });\n    }\n    static async getRoomById(id) {\n        return await prisma.room.findUnique({\n            where: {\n                id\n            },\n            include: {\n                recommendations: {\n                    include: {\n                        product: true\n                    }\n                },\n                shoppingLists: {\n                    include: {\n                        items: {\n                            include: {\n                                product: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    static async updateRoomAnalysis(roomId, analysisResult) {\n        return await prisma.room.update({\n            where: {\n                id: roomId\n            },\n            data: {\n                analysisResult,\n                updatedAt: new Date()\n            }\n        });\n    }\n    static async getUserRooms(userId) {\n        return await prisma.room.findMany({\n            where: {\n                userId\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            },\n            include: {\n                recommendations: {\n                    take: 3,\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    // Product Management\n    static async createProduct(data) {\n        return await prisma.product.create({\n            data: {\n                ...data,\n                styles: data.styles || [],\n                colors: data.colors || [],\n                materials: data.materials || [],\n                tags: data.tags || []\n            }\n        });\n    }\n    static async getProductByExternalId(externalId) {\n        return await prisma.product.findUnique({\n            where: {\n                externalId\n            }\n        });\n    }\n    static async updateProduct(id, data) {\n        return await prisma.product.update({\n            where: {\n                id\n            },\n            data\n        });\n    }\n    static async getProductById(id) {\n        return await prisma.product.findUnique({\n            where: {\n                id\n            },\n            include: {\n                priceHistory: {\n                    orderBy: {\n                        recordedAt: 'desc'\n                    },\n                    take: 10\n                }\n            }\n        });\n    }\n    static async searchProducts(filters) {\n        const where = {};\n        if (filters.category) where.category = filters.category;\n        if (filters.retailer) where.retailer = filters.retailer;\n        if (filters.isAvailable !== undefined) where.isAvailable = filters.isAvailable;\n        if (filters.minPrice || filters.maxPrice) {\n            where.price = {};\n            if (filters.minPrice) where.price.gte = filters.minPrice;\n            if (filters.maxPrice) where.price.lte = filters.maxPrice;\n        }\n        if (filters.styles && filters.styles.length > 0) {\n            where.styles = {\n                hasSome: filters.styles\n            };\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 20,\n            skip: filters.offset || 0,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n    static async getProducts(filters) {\n        const where = {};\n        if (filters.category) {\n            where.category = filters.category;\n        }\n        if (filters.retailer) {\n            where.retailer = filters.retailer;\n        }\n        if (filters.inStock !== undefined) {\n            where.isAvailable = filters.inStock;\n        }\n        const orderBy = {};\n        if (filters.orderBy) {\n            orderBy[filters.orderBy] = filters.orderDirection || 'desc';\n        } else {\n            orderBy.createdAt = 'desc';\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 100,\n            orderBy\n        });\n    }\n    // Recommendations\n    static async saveRecommendations(roomId, recommendations) {\n        // Delete existing recommendations for this room\n        await prisma.recommendation.deleteMany({\n            where: {\n                roomId\n            }\n        });\n        // Create new recommendations\n        return await prisma.recommendation.createMany({\n            data: recommendations.map((rec)=>({\n                    ...rec,\n                    roomId,\n                    placement: rec.placement || undefined\n                }))\n        });\n    }\n    static async getRoomRecommendations(roomId) {\n        const recommendations = await prisma.recommendation.findMany({\n            where: {\n                roomId\n            },\n            include: {\n                product: true\n            },\n            orderBy: {\n                rank: 'asc'\n            }\n        });\n        return recommendations;\n    }\n    // Shopping Lists\n    static async createShoppingList(data) {\n        const totalCost = data.items.reduce((sum, item)=>sum + item.priceAtAdd * item.quantity, 0);\n        return await prisma.shoppingList.create({\n            data: {\n                name: data.name,\n                budget: data.budget,\n                totalCost,\n                userId: data.userId,\n                roomId: data.roomId,\n                items: {\n                    create: data.items\n                }\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    static async getShoppingList(id) {\n        const shoppingList = await prisma.shoppingList.findUnique({\n            where: {\n                id\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                },\n                user: true,\n                room: true\n            }\n        });\n        if (shoppingList) {\n            return {\n                ...shoppingList,\n                items: shoppingList.items\n            };\n        }\n        return null;\n    }\n    // User Management\n    static async createUser(data) {\n        return await prisma.user.create({\n            data\n        });\n    }\n    static async getUserByEmail(email) {\n        return await prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                preferences: true\n            }\n        });\n    }\n    static async updateUserPreferences(userId, preferences) {\n        return await prisma.userPreferences.upsert({\n            where: {\n                userId\n            },\n            update: {\n                ...preferences,\n                updatedAt: new Date()\n            },\n            create: {\n                userId,\n                ...preferences,\n                preferredStyles: preferences.preferredStyles || [],\n                favoriteRetailers: preferences.favoriteRetailers || [],\n                colorPreferences: preferences.colorPreferences || []\n            }\n        });\n    }\n    // Analytics\n    static async trackEvent(data) {\n        return await prisma.analyticsEvent.create({\n            data: {\n                ...data,\n                eventData: data.eventData || undefined\n            }\n        });\n    }\n    // Price Tracking\n    static async addPriceHistory(productId, price, source) {\n        return await prisma.priceHistory.create({\n            data: {\n                productId,\n                price,\n                source\n            }\n        });\n    }\n    static async createPriceAlert(data) {\n        return await prisma.priceAlert.create({\n            data\n        });\n    }\n    // Utility methods\n    static async cleanup() {\n        await prisma.$disconnect();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scrapers/amazon-scraper.ts":
/*!********************************************!*\
  !*** ./src/lib/scrapers/amazon-scraper.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AmazonScraper: () => (/* binding */ AmazonScraper)\n/* harmony export */ });\n/* harmony import */ var _base_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-scraper */ \"(rsc)/./src/lib/scrapers/base-scraper.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_base_scraper__WEBPACK_IMPORTED_MODULE_0__]);\n_base_scraper__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass AmazonScraper extends _base_scraper__WEBPACK_IMPORTED_MODULE_0__.BaseScraper {\n    async scrapeProducts(category, limit = 50) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            // Map categories to Amazon search terms\n            const categoryQueries = {\n                'seating': 'furniture chairs sofas',\n                'tables': 'furniture tables coffee dining',\n                'storage': 'furniture storage cabinets shelves',\n                'lighting': 'home lighting lamps fixtures',\n                'decor': 'home decor decorative accessories',\n                'bedroom': 'bedroom furniture bed nightstand',\n                'electronics': 'tv television smart 4k'\n            };\n            const query = categoryQueries[category.toLowerCase()] || category;\n            const searchUrl = `${this.baseUrl}/s?k=${encodeURIComponent(query)}&rh=n%3A1055398`;\n            await page.goto(searchUrl, {\n                waitUntil: 'networkidle2'\n            });\n            // Handle potential captcha or bot detection\n            await this.handleBotDetection(page);\n            // Wait for search results\n            await page.waitForSelector('[data-component-type=\"s-search-result\"]', {\n                timeout: 15000\n            });\n            // Extract product links\n            const productLinks = await page.$$eval('[data-component-type=\"s-search-result\"] h2 a', (links)=>links.map((link)=>'https://www.amazon.com' + link.getAttribute('href')).slice(0, 50));\n            console.log(`Found ${productLinks.length} Amazon products for: ${query}`);\n            // Scrape each product\n            for(let i = 0; i < Math.min(productLinks.length, limit); i++){\n                try {\n                    const product = await this.scrapeProduct(productLinks[i]);\n                    if (product) {\n                        products.push(product);\n                        console.log(`Scraped Amazon product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);\n                    }\n                    await this.delay(1000); // Be more conservative with Amazon\n                } catch (error) {\n                    console.warn(`Failed to scrape Amazon product: ${productLinks[i]}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to scrape Amazon category: ${category}`, 'amazon');\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async scrapeProduct(url) {\n        const page = await this.createPage();\n        try {\n            await page.goto(url, {\n                waitUntil: 'networkidle2'\n            });\n            // Handle potential captcha or bot detection\n            await this.handleBotDetection(page);\n            // Wait for product details to load\n            await page.waitForSelector('#productTitle', {\n                timeout: 15000\n            });\n            // Extract product information\n            const name = await this.safeGetText(page, '#productTitle');\n            const priceText = await this.safeGetText(page, '.a-price-current .a-offscreen, .a-price .a-offscreen');\n            // Try multiple selectors for description\n            let description = await this.safeGetText(page, '#feature-bullets ul');\n            if (!description) {\n                description = await this.safeGetText(page, '#productDescription');\n            }\n            if (!description) {\n                description = await this.safeGetText(page, '[data-feature-name=\"productDescription\"]');\n            }\n            // Get main product image\n            let imageUrl = await this.safeGetAttribute(page, '#landingImage', 'src');\n            if (!imageUrl) {\n                imageUrl = await this.safeGetAttribute(page, '#imgBlkFront', 'src');\n            }\n            // Parse price\n            const { price, currency } = this.parsePrice(priceText);\n            // Extract brand\n            const brand = await this.safeGetText(page, '#bylineInfo, .a-row .a-size-base');\n            // Extract rating and review count\n            const rating = await this.extractRating(page);\n            const reviewCount = await this.extractReviewCount(page);\n            // Check availability\n            const inStock = await this.checkAvailability(page);\n            // Extract dimensions from product details\n            const dimensions = await this.extractDimensions(page);\n            // Determine category\n            const category = await this.extractCategory(page);\n            // Add affiliate tag to URL\n            const affiliateUrl = this.addAffiliateTag(url);\n            if (!name || !price) {\n                console.warn(`Incomplete Amazon product data for: ${url}`);\n                return null;\n            }\n            const product = {\n                id: this.generateProductId(name, 'amazon'),\n                name: name.trim(),\n                price,\n                currency,\n                imageUrl: imageUrl || '',\n                productUrl: affiliateUrl,\n                description: description.trim().substring(0, 500),\n                category,\n                brand: brand.replace(/^by\\s+/i, '').trim(),\n                dimensions,\n                rating,\n                reviewCount,\n                inStock,\n                retailer: 'amazon',\n                styles: [\n                    'modern',\n                    'contemporary'\n                ],\n                scrapedAt: new Date()\n            };\n            return product;\n        } catch (error) {\n            console.error(`Failed to scrape Amazon product: ${url}`, error);\n            return null;\n        } finally{\n            await page.close();\n        }\n    }\n    async searchProducts(query, limit = 20) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            const searchUrl = `${this.baseUrl}/s?k=${encodeURIComponent(query)}&rh=n%3A1055398`;\n            await page.goto(searchUrl, {\n                waitUntil: 'networkidle2'\n            });\n            await this.handleBotDetection(page);\n            await page.waitForSelector('[data-component-type=\"s-search-result\"]', {\n                timeout: 15000\n            });\n            const productLinks = await page.$$eval('[data-component-type=\"s-search-result\"] h2 a', (links)=>links.map((link)=>'https://www.amazon.com' + link.getAttribute('href')).slice(0, limit));\n            for (const link of productLinks){\n                try {\n                    const product = await this.scrapeProduct(link);\n                    if (product) {\n                        products.push(product);\n                    }\n                    await this.delay(1000);\n                } catch (error) {\n                    console.warn(`Failed to scrape Amazon search result: ${link}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to search Amazon products: ${query}`, 'amazon');\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async handleBotDetection(page) {\n        try {\n            // Check for captcha or bot detection\n            const captcha = await page.$('form[action*=\"validateCaptcha\"]');\n            if (captcha) {\n                console.warn('Amazon captcha detected, waiting...');\n                await this.delay(5000);\n            }\n            // Check for \"Sorry, we just need to make sure you're not a robot\"\n            const robotCheck = await page.$('input[id=\"captchacharacters\"]');\n            if (robotCheck) {\n                console.warn('Amazon robot check detected');\n                throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError('Amazon robot check detected', 'amazon');\n            }\n        } catch (error) {\n            // Continue if we can't handle bot detection\n            console.warn('Bot detection handling failed:', error);\n        }\n    }\n    async extractRating(page) {\n        try {\n            const ratingText = await this.safeGetText(page, '[data-hook=\"average-star-rating\"] .a-icon-alt, .a-icon-alt');\n            const match = ratingText.match(/(\\d+\\.?\\d*)\\s*out\\s*of\\s*5/i);\n            return match ? parseFloat(match[1]) : undefined;\n        } catch  {\n            return undefined;\n        }\n    }\n    async extractReviewCount(page) {\n        try {\n            const reviewText = await this.safeGetText(page, '[data-hook=\"total-review-count\"], #acrCustomerReviewText');\n            const match = reviewText.match(/(\\d+(?:,\\d+)*)/);\n            return match ? parseInt(match[1].replace(/,/g, '')) : undefined;\n        } catch  {\n            return undefined;\n        }\n    }\n    async checkAvailability(page) {\n        try {\n            const availabilityText = await this.safeGetText(page, '#availability span, #outOfStock');\n            return !availabilityText.toLowerCase().includes('out of stock') && !availabilityText.toLowerCase().includes('unavailable');\n        } catch  {\n            return true;\n        }\n    }\n    async extractDimensions(page) {\n        try {\n            // Look for dimensions in product details table\n            const detailsText = await page.$$eval('#productDetails_detailBullets_sections1 tr, #productDetails_techSpec_section_1 tr', (rows)=>rows.map((row)=>row.textContent || '').join(' '));\n            const dimensions = {};\n            // Parse dimension patterns\n            const dimensionMatch = detailsText.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)/i);\n            if (dimensionMatch) {\n                dimensions.width = parseFloat(dimensionMatch[1]);\n                dimensions.height = parseFloat(dimensionMatch[2]);\n                dimensions.depth = parseFloat(dimensionMatch[3]);\n            }\n            return Object.keys(dimensions).length > 0 ? dimensions : undefined;\n        } catch  {\n            return undefined;\n        }\n    }\n    async extractCategory(page) {\n        try {\n            const breadcrumbs = await page.$$eval('#wayfinding-breadcrumbs_feature_div a', (links)=>links.map((link)=>link.textContent?.trim() || ''));\n            // Map Amazon categories to our categories\n            const categoryMap = {\n                'furniture': 'furniture',\n                'home': 'decor',\n                'kitchen': 'kitchen',\n                'lighting': 'lighting',\n                'electronics': 'electronics',\n                'tv': 'electronics'\n            };\n            for (const breadcrumb of breadcrumbs){\n                const lower = breadcrumb.toLowerCase();\n                for (const [key, value] of Object.entries(categoryMap)){\n                    if (lower.includes(key)) {\n                        return value;\n                    }\n                }\n            }\n            return 'furniture'; // Default\n        } catch  {\n            return 'furniture';\n        }\n    }\n    addAffiliateTag(url) {\n        try {\n            const urlObj = new URL(url);\n            urlObj.searchParams.set('tag', this.affiliateTag);\n            return urlObj.toString();\n        } catch  {\n            return url;\n        }\n    }\n    constructor(...args){\n        super(...args), this.baseUrl = 'https://www.amazon.com', this.affiliateTag = process.env.AMAZON_AFFILIATE_TAG || 'youraffid-20';\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scrapers/amazon-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scrapers/base-scraper.ts":
/*!******************************************!*\
  !*** ./src/lib/scrapers/base-scraper.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseScraper: () => (/* binding */ BaseScraper),\n/* harmony export */   ScraperError: () => (/* binding */ ScraperError)\n/* harmony export */ });\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\n/* harmony import */ var user_agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! user-agents */ \"(rsc)/./node_modules/user-agents/dist/index.js\");\n/* harmony import */ var user_agents__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(user_agents__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_0__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nclass BaseScraper {\n    constructor(config = {}){\n        this.browser = null;\n        this.config = {\n            headless: true,\n            timeout: 30000,\n            retries: 3,\n            delay: 1000,\n            userAgent: new (user_agents__WEBPACK_IMPORTED_MODULE_1___default())().toString(),\n            viewport: {\n                width: 1920,\n                height: 1080\n            },\n            ...config\n        };\n    }\n    async initialize() {\n        try {\n            this.browser = await puppeteer__WEBPACK_IMPORTED_MODULE_0__[\"default\"].launch({\n                headless: this.config.headless,\n                args: [\n                    '--no-sandbox',\n                    '--disable-setuid-sandbox',\n                    '--disable-dev-shm-usage',\n                    '--disable-accelerated-2d-canvas',\n                    '--no-first-run',\n                    '--no-zygote',\n                    '--disable-gpu'\n                ]\n            });\n        } catch (error) {\n            console.error('Failed to initialize browser:', error);\n            throw error;\n        }\n    }\n    async createPage() {\n        if (!this.browser) {\n            await this.initialize();\n        }\n        const page = await this.browser.newPage();\n        // Set user agent and viewport\n        await page.setUserAgent(this.config.userAgent);\n        await page.setViewport(this.config.viewport);\n        // Set timeout\n        page.setDefaultTimeout(this.config.timeout);\n        // Block unnecessary resources to speed up scraping\n        await page.setRequestInterception(true);\n        page.on('request', (req)=>{\n            const resourceType = req.resourceType();\n            if ([\n                'stylesheet',\n                'font',\n                'media'\n            ].includes(resourceType)) {\n                req.abort();\n            } else {\n                req.continue();\n            }\n        });\n        return page;\n    }\n    async delay(ms = this.config.delay) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    async retryOperation(operation, retries = this.config.retries) {\n        for(let i = 0; i < retries; i++){\n            try {\n                return await operation();\n            } catch (error) {\n                console.warn(`Operation failed (attempt ${i + 1}/${retries}):`, error);\n                if (i === retries - 1) throw error;\n                await this.delay(1000 * (i + 1)); // Exponential backoff\n            }\n        }\n        throw new Error('All retry attempts failed');\n    }\n    async safeGetText(page, selector) {\n        try {\n            const element = await page.$(selector);\n            if (!element) return '';\n            return await page.evaluate((el)=>el.textContent?.trim() || '', element);\n        } catch  {\n            return '';\n        }\n    }\n    async safeGetAttribute(page, selector, attribute) {\n        try {\n            const element = await page.$(selector);\n            if (!element) return '';\n            return await page.evaluate((el, attr)=>el.getAttribute(attr) || '', element, attribute);\n        } catch  {\n            return '';\n        }\n    }\n    parsePrice(priceText) {\n        // Remove common price formatting\n        const cleanPrice = priceText.replace(/[^\\d.,₹$€£¥]/g, '');\n        // Extract currency\n        let currency = 'USD';\n        if (priceText.includes('₹')) currency = 'INR';\n        else if (priceText.includes('€')) currency = 'EUR';\n        else if (priceText.includes('£')) currency = 'GBP';\n        else if (priceText.includes('¥')) currency = 'JPY';\n        else if (priceText.includes('$')) currency = 'USD';\n        // Parse price\n        const price = parseFloat(cleanPrice.replace(/,/g, '')) || 0;\n        return {\n            price,\n            currency\n        };\n    }\n    generateProductId(name, retailer) {\n        const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');\n        const timestamp = Date.now().toString(36);\n        return `${retailer}-${cleanName}-${timestamp}`;\n    }\n    async cleanup() {\n        if (this.browser) {\n            await this.browser.close();\n            this.browser = null;\n        }\n    }\n}\nclass ScraperError extends Error {\n    constructor(message, retailer, url){\n        super(message), this.retailer = retailer, this.url = url;\n        this.name = 'ScraperError';\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scrapers/base-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scrapers/daraz-scraper.ts":
/*!*******************************************!*\
  !*** ./src/lib/scrapers/daraz-scraper.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DarazScraper: () => (/* binding */ DarazScraper)\n/* harmony export */ });\n/* harmony import */ var _base_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-scraper */ \"(rsc)/./src/lib/scrapers/base-scraper.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_base_scraper__WEBPACK_IMPORTED_MODULE_0__]);\n_base_scraper__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass DarazScraper extends _base_scraper__WEBPACK_IMPORTED_MODULE_0__.BaseScraper {\n    async scrapeProducts(category, limit = 50) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            // Map categories to Daraz category URLs\n            const categoryUrls1 = {\n                'seating': '/furniture-decor/living-room/sofas-chairs/',\n                'tables': '/furniture-decor/living-room/tables/',\n                'storage': '/furniture-decor/storage-organization/',\n                'lighting': '/furniture-decor/lighting/',\n                'decor': '/furniture-decor/home-decor/',\n                'bedroom': '/furniture-decor/bedroom/',\n                'kitchen': '/furniture-decor/kitchen-dining/'\n            };\n            const categoryUrl = categoryUrls1[category.toLowerCase()];\n            if (!categoryUrl) {\n                throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Unknown category: ${category}`, 'daraz');\n            }\n            await page.goto(this.baseUrl + categoryUrl, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for products to load\n            await page.waitForSelector('[data-qa-locator=\"product-item\"]', {\n                timeout: 15000\n            });\n            // Scroll to load more products\n            await this.scrollToLoadProducts(page, limit);\n            // Extract product links\n            const productLinks = await page.$$eval('[data-qa-locator=\"product-item\"] a', (links)=>links.map((link)=>{\n                    const href = link.href;\n                    return href.startsWith('http') ? href : 'https://www.daraz.pk' + href;\n                }).slice(0, 50));\n            console.log(`Found ${productLinks.length} Daraz products in category: ${category}`);\n            // Scrape each product\n            for(let i = 0; i < Math.min(productLinks.length, limit); i++){\n                try {\n                    const product = await this.scrapeProduct(productLinks[i]);\n                    if (product) {\n                        products.push(product);\n                        console.log(`Scraped Daraz product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);\n                    }\n                    await this.delay(800); // Be respectful to Daraz's servers\n                } catch (error) {\n                    console.warn(`Failed to scrape Daraz product: ${productLinks[i]}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to scrape Daraz category: ${category}`, 'daraz', categoryUrls[category.toLowerCase()]);\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async scrapeProduct(url) {\n        const page = await this.createPage();\n        try {\n            await page.goto(url, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for product details to load\n            await page.waitForSelector('[data-qa-locator=\"product-detail-name\"]', {\n                timeout: 15000\n            });\n            // Extract product information\n            const name = await this.safeGetText(page, '[data-qa-locator=\"product-detail-name\"], .pdp-product-name');\n            const priceText = await this.safeGetText(page, '[data-qa-locator=\"product-price\"], .pdp-price');\n            // Try multiple selectors for description\n            let description = await this.safeGetText(page, '[data-qa-locator=\"product-detail-desc\"], .html-content');\n            if (!description) {\n                description = await this.safeGetText(page, '.product-description, .pdp-product-desc');\n            }\n            // Get main product image\n            let imageUrl = await this.safeGetAttribute(page, '.gallery-preview-panel img, .pdp-gallery img', 'src');\n            if (!imageUrl) {\n                imageUrl = await this.safeGetAttribute(page, '.item-gallery img', 'src');\n            }\n            // Parse price (Daraz uses PKR)\n            const { price, currency } = this.parsePrice(priceText);\n            // Extract brand\n            const brand = await this.safeGetText(page, '.pdp-product-brand, [data-qa-locator=\"product-brand\"]');\n            // Extract rating and review count\n            const rating = await this.extractRating(page);\n            const reviewCount = await this.extractReviewCount(page);\n            // Check availability\n            const inStock = await this.checkAvailability(page);\n            // Extract specifications for dimensions and materials\n            const specifications = await this.extractSpecifications(page);\n            // Determine category from breadcrumbs\n            const category = await this.extractCategory(page);\n            if (!name || !price) {\n                console.warn(`Incomplete Daraz product data for: ${url}`);\n                return null;\n            }\n            const product = {\n                id: this.generateProductId(name, 'daraz'),\n                name: name.trim(),\n                price,\n                currency: currency || 'PKR',\n                imageUrl: imageUrl.startsWith('//') ? 'https:' + imageUrl : imageUrl,\n                productUrl: url,\n                description: description.trim().substring(0, 500),\n                category,\n                brand: brand.trim(),\n                dimensions: specifications.dimensions,\n                colors: specifications.colors,\n                materials: specifications.materials,\n                rating,\n                reviewCount,\n                inStock,\n                retailer: 'daraz',\n                styles: [\n                    'modern',\n                    'contemporary'\n                ],\n                scrapedAt: new Date()\n            };\n            return product;\n        } catch (error) {\n            console.error(`Failed to scrape Daraz product: ${url}`, error);\n            return null;\n        } finally{\n            await page.close();\n        }\n    }\n    async searchProducts(query, limit = 20) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            const searchUrl = `${this.baseUrl}/catalog/?q=${encodeURIComponent(query)}`;\n            await page.goto(searchUrl, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for search results\n            await page.waitForSelector('[data-qa-locator=\"product-item\"]', {\n                timeout: 15000\n            });\n            // Extract product links\n            const productLinks = await page.$$eval('[data-qa-locator=\"product-item\"] a', (links)=>links.map((link)=>{\n                    const href = link.href;\n                    return href.startsWith('http') ? href : 'https://www.daraz.pk' + href;\n                }).slice(0, limit));\n            // Scrape each product\n            for (const link of productLinks){\n                try {\n                    const product = await this.scrapeProduct(link);\n                    if (product) {\n                        products.push(product);\n                    }\n                    await this.delay(800);\n                } catch (error) {\n                    console.warn(`Failed to scrape Daraz search result: ${link}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to search Daraz products: ${query}`, 'daraz');\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async scrollToLoadProducts(page, targetCount) {\n        let previousCount = 0;\n        let currentCount = 0;\n        let attempts = 0;\n        const maxAttempts = 8;\n        while(currentCount < targetCount && attempts < maxAttempts){\n            await page.evaluate(()=>window.scrollTo(0, document.body.scrollHeight));\n            await this.delay(2000);\n            currentCount = await page.$$eval('[data-qa-locator=\"product-item\"]', (items)=>items.length);\n            if (currentCount === previousCount) {\n                attempts++;\n            } else {\n                attempts = 0;\n            }\n            previousCount = currentCount;\n        }\n    }\n    async extractRating(page) {\n        try {\n            const ratingText = await this.safeGetText(page, '.score-average, .pdp-review-summary .score');\n            const rating = parseFloat(ratingText);\n            return isNaN(rating) ? undefined : rating;\n        } catch  {\n            return undefined;\n        }\n    }\n    async extractReviewCount(page) {\n        try {\n            const reviewText = await this.safeGetText(page, '.review-count, .pdp-review-summary .count');\n            const match = reviewText.match(/(\\d+)/);\n            return match ? parseInt(match[1]) : undefined;\n        } catch  {\n            return undefined;\n        }\n    }\n    async checkAvailability(page) {\n        try {\n            const stockText = await this.safeGetText(page, '.inventory-status, .pdp-stock');\n            return !stockText.toLowerCase().includes('out of stock') && !stockText.toLowerCase().includes('unavailable');\n        } catch  {\n            return true;\n        }\n    }\n    async extractSpecifications(page) {\n        try {\n            const specs = await page.$$eval('.specification-keys, .pdp-product-detail .key-title', (elements)=>elements.map((el)=>({\n                        key: el.textContent?.trim() || '',\n                        value: el.nextElementSibling?.textContent?.trim() || ''\n                    })));\n            const result = {};\n            // Extract dimensions\n            const dimensionSpec = specs.find((spec)=>spec.key.toLowerCase().includes('dimension') || spec.key.toLowerCase().includes('size'));\n            if (dimensionSpec) {\n                const dimensionMatch = dimensionSpec.value.match(/(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)\\s*x\\s*(\\d+(?:\\.\\d+)?)/);\n                if (dimensionMatch) {\n                    result.dimensions = {\n                        width: parseFloat(dimensionMatch[1]),\n                        height: parseFloat(dimensionMatch[2]),\n                        depth: parseFloat(dimensionMatch[3])\n                    };\n                }\n            }\n            // Extract colors\n            const colorSpec = specs.find((spec)=>spec.key.toLowerCase().includes('color'));\n            if (colorSpec) {\n                result.colors = [\n                    colorSpec.value\n                ];\n            }\n            // Extract materials\n            const materialSpec = specs.find((spec)=>spec.key.toLowerCase().includes('material') || spec.key.toLowerCase().includes('fabric'));\n            if (materialSpec) {\n                result.materials = [\n                    materialSpec.value\n                ];\n            }\n            return result;\n        } catch  {\n            return {};\n        }\n    }\n    async extractCategory(page) {\n        try {\n            const breadcrumbs = await page.$$eval('.breadcrumb a, .breadcrumb-item', (links)=>links.map((link)=>link.textContent?.trim() || ''));\n            // Map Daraz categories to our categories\n            const categoryMap = {\n                'furniture': 'furniture',\n                'living room': 'seating',\n                'bedroom': 'bedroom',\n                'kitchen': 'kitchen',\n                'lighting': 'lighting',\n                'decor': 'decor',\n                'storage': 'storage'\n            };\n            for (const breadcrumb of breadcrumbs){\n                const lower = breadcrumb.toLowerCase();\n                for (const [key, value] of Object.entries(categoryMap)){\n                    if (lower.includes(key)) {\n                        return value;\n                    }\n                }\n            }\n            return 'furniture'; // Default\n        } catch  {\n            return 'furniture';\n        }\n    }\n    constructor(...args){\n        super(...args), this.baseUrl = 'https://www.daraz.pk';\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scrapers/daraz-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scrapers/ikea-scraper.ts":
/*!******************************************!*\
  !*** ./src/lib/scrapers/ikea-scraper.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IkeaScraper: () => (/* binding */ IkeaScraper)\n/* harmony export */ });\n/* harmony import */ var _base_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-scraper */ \"(rsc)/./src/lib/scrapers/base-scraper.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_base_scraper__WEBPACK_IMPORTED_MODULE_0__]);\n_base_scraper__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass IkeaScraper extends _base_scraper__WEBPACK_IMPORTED_MODULE_0__.BaseScraper {\n    async scrapeProducts(category, limit = 50) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            // Map categories to IKEA category URLs\n            const categoryUrls1 = {\n                'seating': '/us/en/cat/chairs-fu003/',\n                'sofas': '/us/en/cat/sofas-armchairs-fu003/',\n                'tables': '/us/en/cat/tables-desks-fu004/',\n                'storage': '/us/en/cat/storage-furniture-st002/',\n                'lighting': '/us/en/cat/lighting-li001/',\n                'decor': '/us/en/cat/decoration-de001/',\n                'bedroom': '/us/en/cat/bedroom-furniture-bm003/',\n                'kitchen': '/us/en/cat/kitchen-cabinets-fronts-ka001/'\n            };\n            const categoryUrl = categoryUrls1[category.toLowerCase()];\n            if (!categoryUrl) {\n                throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Unknown category: ${category}`, 'ikea');\n            }\n            await page.goto(this.baseUrl + categoryUrl, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for products to load\n            await page.waitForSelector('[data-testid=\"plp-product-list\"]', {\n                timeout: 10000\n            });\n            // Scroll to load more products\n            await this.scrollToLoadProducts(page, limit);\n            // Extract product links\n            const productLinks = await page.$$eval('[data-testid=\"plp-product-list\"] a[href*=\"/products/\"]', (links)=>links.map((link)=>link.href).slice(0, 50));\n            console.log(`Found ${productLinks.length} IKEA products in category: ${category}`);\n            // Scrape each product\n            for(let i = 0; i < Math.min(productLinks.length, limit); i++){\n                try {\n                    const product = await this.scrapeProduct(productLinks[i]);\n                    if (product) {\n                        products.push(product);\n                        console.log(`Scraped IKEA product ${i + 1}/${Math.min(productLinks.length, limit)}: ${product.name}`);\n                    }\n                    await this.delay(500); // Be respectful to IKEA's servers\n                } catch (error) {\n                    console.warn(`Failed to scrape IKEA product: ${productLinks[i]}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to scrape IKEA category: ${category}`, 'ikea', categoryUrls[category.toLowerCase()]);\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async scrapeProduct(url) {\n        const page = await this.createPage();\n        try {\n            await page.goto(url, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for product details to load\n            await page.waitForSelector('[data-testid=\"pip-product-name\"]', {\n                timeout: 10000\n            });\n            // Extract product information\n            const name = await this.safeGetText(page, '[data-testid=\"pip-product-name\"]');\n            const priceText = await this.safeGetText(page, '[data-testid=\"pip-price-current-price\"]');\n            const description = await this.safeGetText(page, '[data-testid=\"pip-product-description\"]');\n            // Get main product image\n            const imageUrl = await this.safeGetAttribute(page, '[data-testid=\"pip-product-image\"] img', 'src');\n            // Parse price\n            const { price, currency } = this.parsePrice(priceText);\n            // Extract dimensions if available\n            const dimensions = await this.extractDimensions(page);\n            // Extract colors and materials\n            const colors = await this.extractColors(page);\n            const materials = await this.extractMaterials(page);\n            // Check stock status\n            const inStock = await this.checkStock(page);\n            // Determine category and subcategory\n            const breadcrumbs = await page.$$eval('[data-testid=\"pip-breadcrumb\"] a', (links)=>links.map((link)=>link.textContent?.trim() || ''));\n            const category = this.mapIkeaCategory(breadcrumbs);\n            if (!name || !price) {\n                console.warn(`Incomplete product data for: ${url}`);\n                return null;\n            }\n            const product = {\n                id: this.generateProductId(name, 'ikea'),\n                name: name.trim(),\n                price,\n                currency,\n                imageUrl: imageUrl.startsWith('//') ? 'https:' + imageUrl : imageUrl,\n                productUrl: url,\n                description: description.trim(),\n                category,\n                brand: 'IKEA',\n                dimensions,\n                colors,\n                materials,\n                styles: [\n                    'scandinavian',\n                    'modern',\n                    'minimalist'\n                ],\n                inStock,\n                retailer: 'ikea',\n                scrapedAt: new Date()\n            };\n            return product;\n        } catch (error) {\n            console.error(`Failed to scrape IKEA product: ${url}`, error);\n            return null;\n        } finally{\n            await page.close();\n        }\n    }\n    async searchProducts(query, limit = 20) {\n        const page = await this.createPage();\n        const products = [];\n        try {\n            const searchUrl1 = `${this.searchUrl}?q=${encodeURIComponent(query)}`;\n            await page.goto(searchUrl1, {\n                waitUntil: 'networkidle2'\n            });\n            // Wait for search results\n            await page.waitForSelector('[data-testid=\"plp-product-list\"]', {\n                timeout: 10000\n            });\n            // Extract product links\n            const productLinks = await page.$$eval('[data-testid=\"plp-product-list\"] a[href*=\"/products/\"]', (links)=>links.map((link)=>link.href).slice(0, limit));\n            // Scrape each product\n            for (const link of productLinks){\n                try {\n                    const product = await this.scrapeProduct(link);\n                    if (product) {\n                        products.push(product);\n                    }\n                    await this.delay(500);\n                } catch (error) {\n                    console.warn(`Failed to scrape IKEA search result: ${link}`, error);\n                }\n            }\n        } catch (error) {\n            throw new _base_scraper__WEBPACK_IMPORTED_MODULE_0__.ScraperError(`Failed to search IKEA products: ${query}`, 'ikea', searchUrl);\n        } finally{\n            await page.close();\n        }\n        return products;\n    }\n    async scrollToLoadProducts(page, targetCount) {\n        let previousCount = 0;\n        let currentCount = 0;\n        let attempts = 0;\n        const maxAttempts = 10;\n        while(currentCount < targetCount && attempts < maxAttempts){\n            await page.evaluate(()=>window.scrollTo(0, document.body.scrollHeight));\n            await this.delay(2000);\n            currentCount = await page.$$eval('[data-testid=\"plp-product-list\"] a[href*=\"/products/\"]', (links)=>links.length);\n            if (currentCount === previousCount) {\n                attempts++;\n            } else {\n                attempts = 0;\n            }\n            previousCount = currentCount;\n        }\n    }\n    async extractDimensions(page) {\n        try {\n            const dimensionText = await this.safeGetText(page, '[data-testid=\"pip-product-dimensions\"]');\n            if (!dimensionText) return undefined;\n            const dimensions = {};\n            // Parse common dimension formats\n            const widthMatch = dimensionText.match(/width[:\\s]*(\\d+(?:\\.\\d+)?)/i);\n            const heightMatch = dimensionText.match(/height[:\\s]*(\\d+(?:\\.\\d+)?)/i);\n            const depthMatch = dimensionText.match(/depth[:\\s]*(\\d+(?:\\.\\d+)?)/i);\n            if (widthMatch) dimensions.width = parseFloat(widthMatch[1]);\n            if (heightMatch) dimensions.height = parseFloat(heightMatch[1]);\n            if (depthMatch) dimensions.depth = parseFloat(depthMatch[1]);\n            return Object.keys(dimensions).length > 0 ? dimensions : undefined;\n        } catch  {\n            return undefined;\n        }\n    }\n    async extractColors(page) {\n        try {\n            const colors = await page.$$eval('[data-testid=\"pip-product-color-options\"] button', (buttons)=>buttons.map((btn)=>btn.getAttribute('aria-label') || '').filter(Boolean));\n            return colors;\n        } catch  {\n            return [];\n        }\n    }\n    async extractMaterials(page) {\n        try {\n            const materialsText = await this.safeGetText(page, '[data-testid=\"pip-product-materials\"]');\n            if (!materialsText) return [];\n            // Extract common materials\n            const materials = [\n                'wood',\n                'metal',\n                'plastic',\n                'fabric',\n                'glass',\n                'leather'\n            ];\n            return materials.filter((material)=>materialsText.toLowerCase().includes(material));\n        } catch  {\n            return [];\n        }\n    }\n    async checkStock(page) {\n        try {\n            const stockText = await this.safeGetText(page, '[data-testid=\"pip-product-availability\"]');\n            return !stockText.toLowerCase().includes('out of stock');\n        } catch  {\n            return true; // Assume in stock if we can't determine\n        }\n    }\n    mapIkeaCategory(breadcrumbs) {\n        const categoryMap = {\n            'chairs': 'seating',\n            'sofas': 'seating',\n            'armchairs': 'seating',\n            'tables': 'tables',\n            'desks': 'tables',\n            'storage': 'storage',\n            'lighting': 'lighting',\n            'decoration': 'decor',\n            'bedroom': 'bedroom',\n            'kitchen': 'kitchen'\n        };\n        for (const breadcrumb of breadcrumbs){\n            const lower = breadcrumb.toLowerCase();\n            for (const [key, value] of Object.entries(categoryMap)){\n                if (lower.includes(key)) {\n                    return value;\n                }\n            }\n        }\n        return 'furniture'; // Default category\n    }\n    constructor(...args){\n        super(...args), this.baseUrl = 'https://www.ikea.com', this.searchUrl = 'https://www.ikea.com/us/en/search/products/';\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NjcmFwZXJzL2lrZWEtc2NyYXBlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEyRTtBQUdwRSxNQUFNRSxvQkFBb0JGLHNEQUFXQTtJQUkxQyxNQUFNRyxlQUFlQyxRQUFnQixFQUFFQyxRQUFnQixFQUFFLEVBQTZCO1FBQ3BGLE1BQU1DLE9BQU8sTUFBTSxJQUFJLENBQUNDLFVBQVU7UUFDbEMsTUFBTUMsV0FBNkIsRUFBRTtRQUVyQyxJQUFJO1lBQ0YsdUNBQXVDO1lBQ3ZDLE1BQU1DLGdCQUF1QztnQkFDM0MsV0FBVztnQkFDWCxTQUFTO2dCQUNULFVBQVU7Z0JBQ1YsV0FBVztnQkFDWCxZQUFZO2dCQUNaLFNBQVM7Z0JBQ1QsV0FBVztnQkFDWCxXQUFXO1lBQ2I7WUFFQSxNQUFNQyxjQUFjRCxhQUFZLENBQUNMLFNBQVNPLFdBQVcsR0FBRztZQUN4RCxJQUFJLENBQUNELGFBQWE7Z0JBQ2hCLE1BQU0sSUFBSVQsdURBQVlBLENBQUMsQ0FBQyxrQkFBa0IsRUFBRUcsVUFBVSxFQUFFO1lBQzFEO1lBRUEsTUFBTUUsS0FBS00sSUFBSSxDQUFDLElBQUksQ0FBQ0MsT0FBTyxHQUFHSCxhQUFhO2dCQUFFSSxXQUFXO1lBQWU7WUFFeEUsNEJBQTRCO1lBQzVCLE1BQU1SLEtBQUtTLGVBQWUsQ0FBQyxvQ0FBb0M7Z0JBQUVDLFNBQVM7WUFBTTtZQUVoRiwrQkFBK0I7WUFDL0IsTUFBTSxJQUFJLENBQUNDLG9CQUFvQixDQUFDWCxNQUFNRDtZQUV0Qyx3QkFBd0I7WUFDeEIsTUFBTWEsZUFBZSxNQUFNWixLQUFLYSxNQUFNLENBQ3BDLDBEQUNBLENBQUNDLFFBQVVBLE1BQU1DLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUSxLQUE0QkMsSUFBSSxFQUFFQyxLQUFLLENBQUMsR0FBRztZQUcxRUMsUUFBUUMsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFUixhQUFhUyxNQUFNLENBQUMsNEJBQTRCLEVBQUV2QixVQUFVO1lBRWpGLHNCQUFzQjtZQUN0QixJQUFLLElBQUl3QixJQUFJLEdBQUdBLElBQUlDLEtBQUtDLEdBQUcsQ0FBQ1osYUFBYVMsTUFBTSxFQUFFdEIsUUFBUXVCLElBQUs7Z0JBQzdELElBQUk7b0JBQ0YsTUFBTUcsVUFBVSxNQUFNLElBQUksQ0FBQ0MsYUFBYSxDQUFDZCxZQUFZLENBQUNVLEVBQUU7b0JBQ3hELElBQUlHLFNBQVM7d0JBQ1h2QixTQUFTeUIsSUFBSSxDQUFDRjt3QkFDZE4sUUFBUUMsR0FBRyxDQUFDLENBQUMscUJBQXFCLEVBQUVFLElBQUksRUFBRSxDQUFDLEVBQUVDLEtBQUtDLEdBQUcsQ0FBQ1osYUFBYVMsTUFBTSxFQUFFdEIsT0FBTyxFQUFFLEVBQUUwQixRQUFRRyxJQUFJLEVBQUU7b0JBQ3RHO29CQUNBLE1BQU0sSUFBSSxDQUFDQyxLQUFLLENBQUMsTUFBTSxrQ0FBa0M7Z0JBQzNELEVBQUUsT0FBT0MsT0FBTztvQkFDZFgsUUFBUVksSUFBSSxDQUFDLENBQUMsK0JBQStCLEVBQUVuQixZQUFZLENBQUNVLEVBQUUsRUFBRSxFQUFFUTtnQkFDcEU7WUFDRjtRQUVGLEVBQUUsT0FBT0EsT0FBTztZQUNkLE1BQU0sSUFBSW5DLHVEQUFZQSxDQUFDLENBQUMsZ0NBQWdDLEVBQUVHLFVBQVUsRUFBRSxRQUFRSyxZQUFZLENBQUNMLFNBQVNPLFdBQVcsR0FBRztRQUNwSCxTQUFVO1lBQ1IsTUFBTUwsS0FBS2dDLEtBQUs7UUFDbEI7UUFFQSxPQUFPOUI7SUFDVDtJQUVBLE1BQU13QixjQUFjTyxHQUFXLEVBQWtDO1FBQy9ELE1BQU1qQyxPQUFPLE1BQU0sSUFBSSxDQUFDQyxVQUFVO1FBRWxDLElBQUk7WUFDRixNQUFNRCxLQUFLTSxJQUFJLENBQUMyQixLQUFLO2dCQUFFekIsV0FBVztZQUFlO1lBRWpELG1DQUFtQztZQUNuQyxNQUFNUixLQUFLUyxlQUFlLENBQUMsb0NBQW9DO2dCQUFFQyxTQUFTO1lBQU07WUFFaEYsOEJBQThCO1lBQzlCLE1BQU1rQixPQUFPLE1BQU0sSUFBSSxDQUFDTSxXQUFXLENBQUNsQyxNQUFNO1lBQzFDLE1BQU1tQyxZQUFZLE1BQU0sSUFBSSxDQUFDRCxXQUFXLENBQUNsQyxNQUFNO1lBQy9DLE1BQU1vQyxjQUFjLE1BQU0sSUFBSSxDQUFDRixXQUFXLENBQUNsQyxNQUFNO1lBRWpELHlCQUF5QjtZQUN6QixNQUFNcUMsV0FBVyxNQUFNLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUN0QyxNQUFNLHlDQUF5QztZQUU1RixjQUFjO1lBQ2QsTUFBTSxFQUFFdUMsS0FBSyxFQUFFQyxRQUFRLEVBQUUsR0FBRyxJQUFJLENBQUNDLFVBQVUsQ0FBQ047WUFFNUMsa0NBQWtDO1lBQ2xDLE1BQU1PLGFBQWEsTUFBTSxJQUFJLENBQUNDLGlCQUFpQixDQUFDM0M7WUFFaEQsK0JBQStCO1lBQy9CLE1BQU00QyxTQUFTLE1BQU0sSUFBSSxDQUFDQyxhQUFhLENBQUM3QztZQUN4QyxNQUFNOEMsWUFBWSxNQUFNLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUMvQztZQUU5QyxxQkFBcUI7WUFDckIsTUFBTWdELFVBQVUsTUFBTSxJQUFJLENBQUNDLFVBQVUsQ0FBQ2pEO1lBRXRDLHFDQUFxQztZQUNyQyxNQUFNa0QsY0FBYyxNQUFNbEQsS0FBS2EsTUFBTSxDQUNuQyxvQ0FDQSxDQUFDQyxRQUFVQSxNQUFNQyxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUttQyxXQUFXLEVBQUVDLFVBQVU7WUFHM0QsTUFBTXRELFdBQVcsSUFBSSxDQUFDdUQsZUFBZSxDQUFDSDtZQUV0QyxJQUFJLENBQUN0QixRQUFRLENBQUNXLE9BQU87Z0JBQ25CcEIsUUFBUVksSUFBSSxDQUFDLENBQUMsNkJBQTZCLEVBQUVFLEtBQUs7Z0JBQ2xELE9BQU87WUFDVDtZQUVBLE1BQU1SLFVBQTBCO2dCQUM5QjZCLElBQUksSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQzNCLE1BQU07Z0JBQ2pDQSxNQUFNQSxLQUFLd0IsSUFBSTtnQkFDZmI7Z0JBQ0FDO2dCQUNBSCxVQUFVQSxTQUFTbUIsVUFBVSxDQUFDLFFBQVEsV0FBV25CLFdBQVdBO2dCQUM1RG9CLFlBQVl4QjtnQkFDWkcsYUFBYUEsWUFBWWdCLElBQUk7Z0JBQzdCdEQ7Z0JBQ0E0RCxPQUFPO2dCQUNQaEI7Z0JBQ0FFO2dCQUNBRTtnQkFDQWEsUUFBUTtvQkFBQztvQkFBZ0I7b0JBQVU7aUJBQWE7Z0JBQ2hEWDtnQkFDQVksVUFBVTtnQkFDVkMsV0FBVyxJQUFJQztZQUNqQjtZQUVBLE9BQU9yQztRQUVULEVBQUUsT0FBT0ssT0FBTztZQUNkWCxRQUFRVyxLQUFLLENBQUMsQ0FBQywrQkFBK0IsRUFBRUcsS0FBSyxFQUFFSDtZQUN2RCxPQUFPO1FBQ1QsU0FBVTtZQUNSLE1BQU05QixLQUFLZ0MsS0FBSztRQUNsQjtJQUNGO0lBRUEsTUFBTStCLGVBQWVDLEtBQWEsRUFBRWpFLFFBQWdCLEVBQUUsRUFBNkI7UUFDakYsTUFBTUMsT0FBTyxNQUFNLElBQUksQ0FBQ0MsVUFBVTtRQUNsQyxNQUFNQyxXQUE2QixFQUFFO1FBRXJDLElBQUk7WUFDRixNQUFNK0QsYUFBWSxHQUFHLElBQUksQ0FBQ0EsU0FBUyxDQUFDLEdBQUcsRUFBRUMsbUJBQW1CRixRQUFRO1lBQ3BFLE1BQU1oRSxLQUFLTSxJQUFJLENBQUMyRCxZQUFXO2dCQUFFekQsV0FBVztZQUFlO1lBRXZELDBCQUEwQjtZQUMxQixNQUFNUixLQUFLUyxlQUFlLENBQUMsb0NBQW9DO2dCQUFFQyxTQUFTO1lBQU07WUFFaEYsd0JBQXdCO1lBQ3hCLE1BQU1FLGVBQWUsTUFBTVosS0FBS2EsTUFBTSxDQUNwQywwREFDQSxDQUFDQyxRQUFVQSxNQUFNQyxHQUFHLENBQUNDLENBQUFBLE9BQVEsS0FBNEJDLElBQUksRUFBRUMsS0FBSyxDQUFDLEdBQUduQjtZQUcxRSxzQkFBc0I7WUFDdEIsS0FBSyxNQUFNaUIsUUFBUUosYUFBYztnQkFDL0IsSUFBSTtvQkFDRixNQUFNYSxVQUFVLE1BQU0sSUFBSSxDQUFDQyxhQUFhLENBQUNWO29CQUN6QyxJQUFJUyxTQUFTO3dCQUNYdkIsU0FBU3lCLElBQUksQ0FBQ0Y7b0JBQ2hCO29CQUNBLE1BQU0sSUFBSSxDQUFDSSxLQUFLLENBQUM7Z0JBQ25CLEVBQUUsT0FBT0MsT0FBTztvQkFDZFgsUUFBUVksSUFBSSxDQUFDLENBQUMscUNBQXFDLEVBQUVmLE1BQU0sRUFBRWM7Z0JBQy9EO1lBQ0Y7UUFFRixFQUFFLE9BQU9BLE9BQU87WUFDZCxNQUFNLElBQUluQyx1REFBWUEsQ0FBQyxDQUFDLGdDQUFnQyxFQUFFcUUsT0FBTyxFQUFFLFFBQVFDO1FBQzdFLFNBQVU7WUFDUixNQUFNakUsS0FBS2dDLEtBQUs7UUFDbEI7UUFFQSxPQUFPOUI7SUFDVDtJQUVBLE1BQWNTLHFCQUFxQlgsSUFBVSxFQUFFbUUsV0FBbUIsRUFBaUI7UUFDakYsSUFBSUMsZ0JBQWdCO1FBQ3BCLElBQUlDLGVBQWU7UUFDbkIsSUFBSUMsV0FBVztRQUNmLE1BQU1DLGNBQWM7UUFFcEIsTUFBT0YsZUFBZUYsZUFBZUcsV0FBV0MsWUFBYTtZQUMzRCxNQUFNdkUsS0FBS3dFLFFBQVEsQ0FBQyxJQUFNQyxPQUFPQyxRQUFRLENBQUMsR0FBR0MsU0FBU0MsSUFBSSxDQUFDQyxZQUFZO1lBQ3ZFLE1BQU0sSUFBSSxDQUFDaEQsS0FBSyxDQUFDO1lBRWpCd0MsZUFBZSxNQUFNckUsS0FBS2EsTUFBTSxDQUM5QiwwREFDQSxDQUFDQyxRQUFVQSxNQUFNTyxNQUFNO1lBR3pCLElBQUlnRCxpQkFBaUJELGVBQWU7Z0JBQ2xDRTtZQUNGLE9BQU87Z0JBQ0xBLFdBQVc7WUFDYjtZQUVBRixnQkFBZ0JDO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFjMUIsa0JBQWtCM0MsSUFBVSxFQUE0RTtRQUNwSCxJQUFJO1lBQ0YsTUFBTThFLGdCQUFnQixNQUFNLElBQUksQ0FBQzVDLFdBQVcsQ0FBQ2xDLE1BQU07WUFDbkQsSUFBSSxDQUFDOEUsZUFBZSxPQUFPQztZQUUzQixNQUFNckMsYUFBa0IsQ0FBQztZQUV6QixpQ0FBaUM7WUFDakMsTUFBTXNDLGFBQWFGLGNBQWNHLEtBQUssQ0FBQztZQUN2QyxNQUFNQyxjQUFjSixjQUFjRyxLQUFLLENBQUM7WUFDeEMsTUFBTUUsYUFBYUwsY0FBY0csS0FBSyxDQUFDO1lBRXZDLElBQUlELFlBQVl0QyxXQUFXMEMsS0FBSyxHQUFHQyxXQUFXTCxVQUFVLENBQUMsRUFBRTtZQUMzRCxJQUFJRSxhQUFheEMsV0FBVzRDLE1BQU0sR0FBR0QsV0FBV0gsV0FBVyxDQUFDLEVBQUU7WUFDOUQsSUFBSUMsWUFBWXpDLFdBQVc2QyxLQUFLLEdBQUdGLFdBQVdGLFVBQVUsQ0FBQyxFQUFFO1lBRTNELE9BQU9LLE9BQU9DLElBQUksQ0FBQy9DLFlBQVlyQixNQUFNLEdBQUcsSUFBSXFCLGFBQWFxQztRQUMzRCxFQUFFLE9BQU07WUFDTixPQUFPQTtRQUNUO0lBQ0Y7SUFFQSxNQUFjbEMsY0FBYzdDLElBQVUsRUFBcUI7UUFDekQsSUFBSTtZQUNGLE1BQU00QyxTQUFTLE1BQU01QyxLQUFLYSxNQUFNLENBQzlCLG9EQUNBLENBQUM2RSxVQUFZQSxRQUFRM0UsR0FBRyxDQUFDNEUsQ0FBQUEsTUFBT0EsSUFBSUMsWUFBWSxDQUFDLGlCQUFpQixJQUFJQyxNQUFNLENBQUNDO1lBRS9FLE9BQU9sRDtRQUNULEVBQUUsT0FBTTtZQUNOLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQSxNQUFjRyxpQkFBaUIvQyxJQUFVLEVBQXFCO1FBQzVELElBQUk7WUFDRixNQUFNK0YsZ0JBQWdCLE1BQU0sSUFBSSxDQUFDN0QsV0FBVyxDQUFDbEMsTUFBTTtZQUNuRCxJQUFJLENBQUMrRixlQUFlLE9BQU8sRUFBRTtZQUU3QiwyQkFBMkI7WUFDM0IsTUFBTWpELFlBQVk7Z0JBQUM7Z0JBQVE7Z0JBQVM7Z0JBQVc7Z0JBQVU7Z0JBQVM7YUFBVTtZQUM1RSxPQUFPQSxVQUFVK0MsTUFBTSxDQUFDRyxDQUFBQSxXQUN0QkQsY0FBYzFGLFdBQVcsR0FBRzRGLFFBQVEsQ0FBQ0Q7UUFFekMsRUFBRSxPQUFNO1lBQ04sT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBLE1BQWMvQyxXQUFXakQsSUFBVSxFQUFvQjtRQUNyRCxJQUFJO1lBQ0YsTUFBTWtHLFlBQVksTUFBTSxJQUFJLENBQUNoRSxXQUFXLENBQUNsQyxNQUFNO1lBQy9DLE9BQU8sQ0FBQ2tHLFVBQVU3RixXQUFXLEdBQUc0RixRQUFRLENBQUM7UUFDM0MsRUFBRSxPQUFNO1lBQ04sT0FBTyxNQUFNLHdDQUF3QztRQUN2RDtJQUNGO0lBRVE1QyxnQkFBZ0JILFdBQXFCLEVBQVU7UUFDckQsTUFBTWlELGNBQXNDO1lBQzFDLFVBQVU7WUFDVixTQUFTO1lBQ1QsYUFBYTtZQUNiLFVBQVU7WUFDVixTQUFTO1lBQ1QsV0FBVztZQUNYLFlBQVk7WUFDWixjQUFjO1lBQ2QsV0FBVztZQUNYLFdBQVc7UUFDYjtRQUVBLEtBQUssTUFBTUMsY0FBY2xELFlBQWE7WUFDcEMsTUFBTW1ELFFBQVFELFdBQVcvRixXQUFXO1lBQ3BDLEtBQUssTUFBTSxDQUFDaUcsS0FBS0MsTUFBTSxJQUFJZixPQUFPZ0IsT0FBTyxDQUFDTCxhQUFjO2dCQUN0RCxJQUFJRSxNQUFNSixRQUFRLENBQUNLLE1BQU07b0JBQ3ZCLE9BQU9DO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLE9BQU8sYUFBYSxtQkFBbUI7SUFDekM7O1FBM1JLLHFCQUNHaEcsVUFBVSw2QkFDVjBELFlBQVk7O0FBMFJ0QiIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9NeSBiaWcgaWRlYS9pbnRlcmlvci1kZXNpZ24tYWkvc3JjL2xpYi9zY3JhcGVycy9pa2VhLXNjcmFwZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZVNjcmFwZXIsIFNjcmFwZWRQcm9kdWN0LCBTY3JhcGVyRXJyb3IgfSBmcm9tICcuL2Jhc2Utc2NyYXBlcic7XG5pbXBvcnQgeyBQYWdlIH0gZnJvbSAncHVwcGV0ZWVyJztcblxuZXhwb3J0IGNsYXNzIElrZWFTY3JhcGVyIGV4dGVuZHMgQmFzZVNjcmFwZXIge1xuICBwcml2YXRlIGJhc2VVcmwgPSAnaHR0cHM6Ly93d3cuaWtlYS5jb20nO1xuICBwcml2YXRlIHNlYXJjaFVybCA9ICdodHRwczovL3d3dy5pa2VhLmNvbS91cy9lbi9zZWFyY2gvcHJvZHVjdHMvJztcblxuICBhc3luYyBzY3JhcGVQcm9kdWN0cyhjYXRlZ29yeTogc3RyaW5nLCBsaW1pdDogbnVtYmVyID0gNTApOiBQcm9taXNlPFNjcmFwZWRQcm9kdWN0W10+IHtcbiAgICBjb25zdCBwYWdlID0gYXdhaXQgdGhpcy5jcmVhdGVQYWdlKCk7XG4gICAgY29uc3QgcHJvZHVjdHM6IFNjcmFwZWRQcm9kdWN0W10gPSBbXTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBNYXAgY2F0ZWdvcmllcyB0byBJS0VBIGNhdGVnb3J5IFVSTHNcbiAgICAgIGNvbnN0IGNhdGVnb3J5VXJsczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgICAgJ3NlYXRpbmcnOiAnL3VzL2VuL2NhdC9jaGFpcnMtZnUwMDMvJyxcbiAgICAgICAgJ3NvZmFzJzogJy91cy9lbi9jYXQvc29mYXMtYXJtY2hhaXJzLWZ1MDAzLycsXG4gICAgICAgICd0YWJsZXMnOiAnL3VzL2VuL2NhdC90YWJsZXMtZGVza3MtZnUwMDQvJyxcbiAgICAgICAgJ3N0b3JhZ2UnOiAnL3VzL2VuL2NhdC9zdG9yYWdlLWZ1cm5pdHVyZS1zdDAwMi8nLFxuICAgICAgICAnbGlnaHRpbmcnOiAnL3VzL2VuL2NhdC9saWdodGluZy1saTAwMS8nLFxuICAgICAgICAnZGVjb3InOiAnL3VzL2VuL2NhdC9kZWNvcmF0aW9uLWRlMDAxLycsXG4gICAgICAgICdiZWRyb29tJzogJy91cy9lbi9jYXQvYmVkcm9vbS1mdXJuaXR1cmUtYm0wMDMvJyxcbiAgICAgICAgJ2tpdGNoZW4nOiAnL3VzL2VuL2NhdC9raXRjaGVuLWNhYmluZXRzLWZyb250cy1rYTAwMS8nXG4gICAgICB9O1xuXG4gICAgICBjb25zdCBjYXRlZ29yeVVybCA9IGNhdGVnb3J5VXJsc1tjYXRlZ29yeS50b0xvd2VyQ2FzZSgpXTtcbiAgICAgIGlmICghY2F0ZWdvcnlVcmwpIHtcbiAgICAgICAgdGhyb3cgbmV3IFNjcmFwZXJFcnJvcihgVW5rbm93biBjYXRlZ29yeTogJHtjYXRlZ29yeX1gLCAnaWtlYScpO1xuICAgICAgfVxuXG4gICAgICBhd2FpdCBwYWdlLmdvdG8odGhpcy5iYXNlVXJsICsgY2F0ZWdvcnlVcmwsIHsgd2FpdFVudGlsOiAnbmV0d29ya2lkbGUyJyB9KTtcbiAgICAgIFxuICAgICAgLy8gV2FpdCBmb3IgcHJvZHVjdHMgdG8gbG9hZFxuICAgICAgYXdhaXQgcGFnZS53YWl0Rm9yU2VsZWN0b3IoJ1tkYXRhLXRlc3RpZD1cInBscC1wcm9kdWN0LWxpc3RcIl0nLCB7IHRpbWVvdXQ6IDEwMDAwIH0pO1xuXG4gICAgICAvLyBTY3JvbGwgdG8gbG9hZCBtb3JlIHByb2R1Y3RzXG4gICAgICBhd2FpdCB0aGlzLnNjcm9sbFRvTG9hZFByb2R1Y3RzKHBhZ2UsIGxpbWl0KTtcblxuICAgICAgLy8gRXh0cmFjdCBwcm9kdWN0IGxpbmtzXG4gICAgICBjb25zdCBwcm9kdWN0TGlua3MgPSBhd2FpdCBwYWdlLiQkZXZhbChcbiAgICAgICAgJ1tkYXRhLXRlc3RpZD1cInBscC1wcm9kdWN0LWxpc3RcIl0gYVtocmVmKj1cIi9wcm9kdWN0cy9cIl0nLFxuICAgICAgICAobGlua3MpID0+IGxpbmtzLm1hcChsaW5rID0+IChsaW5rIGFzIEhUTUxBbmNob3JFbGVtZW50KS5ocmVmKS5zbGljZSgwLCA1MClcbiAgICAgICk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGBGb3VuZCAke3Byb2R1Y3RMaW5rcy5sZW5ndGh9IElLRUEgcHJvZHVjdHMgaW4gY2F0ZWdvcnk6ICR7Y2F0ZWdvcnl9YCk7XG5cbiAgICAgIC8vIFNjcmFwZSBlYWNoIHByb2R1Y3RcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgTWF0aC5taW4ocHJvZHVjdExpbmtzLmxlbmd0aCwgbGltaXQpOyBpKyspIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBwcm9kdWN0ID0gYXdhaXQgdGhpcy5zY3JhcGVQcm9kdWN0KHByb2R1Y3RMaW5rc1tpXSk7XG4gICAgICAgICAgaWYgKHByb2R1Y3QpIHtcbiAgICAgICAgICAgIHByb2R1Y3RzLnB1c2gocHJvZHVjdCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU2NyYXBlZCBJS0VBIHByb2R1Y3QgJHtpICsgMX0vJHtNYXRoLm1pbihwcm9kdWN0TGlua3MubGVuZ3RoLCBsaW1pdCl9OiAke3Byb2R1Y3QubmFtZX1gKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYXdhaXQgdGhpcy5kZWxheSg1MDApOyAvLyBCZSByZXNwZWN0ZnVsIHRvIElLRUEncyBzZXJ2ZXJzXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gc2NyYXBlIElLRUEgcHJvZHVjdDogJHtwcm9kdWN0TGlua3NbaV19YCwgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IFNjcmFwZXJFcnJvcihgRmFpbGVkIHRvIHNjcmFwZSBJS0VBIGNhdGVnb3J5OiAke2NhdGVnb3J5fWAsICdpa2VhJywgY2F0ZWdvcnlVcmxzW2NhdGVnb3J5LnRvTG93ZXJDYXNlKCldKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgYXdhaXQgcGFnZS5jbG9zZSgpO1xuICAgIH1cblxuICAgIHJldHVybiBwcm9kdWN0cztcbiAgfVxuXG4gIGFzeW5jIHNjcmFwZVByb2R1Y3QodXJsOiBzdHJpbmcpOiBQcm9taXNlPFNjcmFwZWRQcm9kdWN0IHwgbnVsbD4ge1xuICAgIGNvbnN0IHBhZ2UgPSBhd2FpdCB0aGlzLmNyZWF0ZVBhZ2UoKTtcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBwYWdlLmdvdG8odXJsLCB7IHdhaXRVbnRpbDogJ25ldHdvcmtpZGxlMicgfSk7XG5cbiAgICAgIC8vIFdhaXQgZm9yIHByb2R1Y3QgZGV0YWlscyB0byBsb2FkXG4gICAgICBhd2FpdCBwYWdlLndhaXRGb3JTZWxlY3RvcignW2RhdGEtdGVzdGlkPVwicGlwLXByb2R1Y3QtbmFtZVwiXScsIHsgdGltZW91dDogMTAwMDAgfSk7XG5cbiAgICAgIC8vIEV4dHJhY3QgcHJvZHVjdCBpbmZvcm1hdGlvblxuICAgICAgY29uc3QgbmFtZSA9IGF3YWl0IHRoaXMuc2FmZUdldFRleHQocGFnZSwgJ1tkYXRhLXRlc3RpZD1cInBpcC1wcm9kdWN0LW5hbWVcIl0nKTtcbiAgICAgIGNvbnN0IHByaWNlVGV4dCA9IGF3YWl0IHRoaXMuc2FmZUdldFRleHQocGFnZSwgJ1tkYXRhLXRlc3RpZD1cInBpcC1wcmljZS1jdXJyZW50LXByaWNlXCJdJyk7XG4gICAgICBjb25zdCBkZXNjcmlwdGlvbiA9IGF3YWl0IHRoaXMuc2FmZUdldFRleHQocGFnZSwgJ1tkYXRhLXRlc3RpZD1cInBpcC1wcm9kdWN0LWRlc2NyaXB0aW9uXCJdJyk7XG4gICAgICBcbiAgICAgIC8vIEdldCBtYWluIHByb2R1Y3QgaW1hZ2VcbiAgICAgIGNvbnN0IGltYWdlVXJsID0gYXdhaXQgdGhpcy5zYWZlR2V0QXR0cmlidXRlKHBhZ2UsICdbZGF0YS10ZXN0aWQ9XCJwaXAtcHJvZHVjdC1pbWFnZVwiXSBpbWcnLCAnc3JjJyk7XG4gICAgICBcbiAgICAgIC8vIFBhcnNlIHByaWNlXG4gICAgICBjb25zdCB7IHByaWNlLCBjdXJyZW5jeSB9ID0gdGhpcy5wYXJzZVByaWNlKHByaWNlVGV4dCk7XG5cbiAgICAgIC8vIEV4dHJhY3QgZGltZW5zaW9ucyBpZiBhdmFpbGFibGVcbiAgICAgIGNvbnN0IGRpbWVuc2lvbnMgPSBhd2FpdCB0aGlzLmV4dHJhY3REaW1lbnNpb25zKHBhZ2UpO1xuXG4gICAgICAvLyBFeHRyYWN0IGNvbG9ycyBhbmQgbWF0ZXJpYWxzXG4gICAgICBjb25zdCBjb2xvcnMgPSBhd2FpdCB0aGlzLmV4dHJhY3RDb2xvcnMocGFnZSk7XG4gICAgICBjb25zdCBtYXRlcmlhbHMgPSBhd2FpdCB0aGlzLmV4dHJhY3RNYXRlcmlhbHMocGFnZSk7XG5cbiAgICAgIC8vIENoZWNrIHN0b2NrIHN0YXR1c1xuICAgICAgY29uc3QgaW5TdG9jayA9IGF3YWl0IHRoaXMuY2hlY2tTdG9jayhwYWdlKTtcblxuICAgICAgLy8gRGV0ZXJtaW5lIGNhdGVnb3J5IGFuZCBzdWJjYXRlZ29yeVxuICAgICAgY29uc3QgYnJlYWRjcnVtYnMgPSBhd2FpdCBwYWdlLiQkZXZhbChcbiAgICAgICAgJ1tkYXRhLXRlc3RpZD1cInBpcC1icmVhZGNydW1iXCJdIGEnLFxuICAgICAgICAobGlua3MpID0+IGxpbmtzLm1hcChsaW5rID0+IGxpbmsudGV4dENvbnRlbnQ/LnRyaW0oKSB8fCAnJylcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gdGhpcy5tYXBJa2VhQ2F0ZWdvcnkoYnJlYWRjcnVtYnMpO1xuXG4gICAgICBpZiAoIW5hbWUgfHwgIXByaWNlKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgSW5jb21wbGV0ZSBwcm9kdWN0IGRhdGEgZm9yOiAke3VybH1gKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHByb2R1Y3Q6IFNjcmFwZWRQcm9kdWN0ID0ge1xuICAgICAgICBpZDogdGhpcy5nZW5lcmF0ZVByb2R1Y3RJZChuYW1lLCAnaWtlYScpLFxuICAgICAgICBuYW1lOiBuYW1lLnRyaW0oKSxcbiAgICAgICAgcHJpY2UsXG4gICAgICAgIGN1cnJlbmN5LFxuICAgICAgICBpbWFnZVVybDogaW1hZ2VVcmwuc3RhcnRzV2l0aCgnLy8nKSA/ICdodHRwczonICsgaW1hZ2VVcmwgOiBpbWFnZVVybCxcbiAgICAgICAgcHJvZHVjdFVybDogdXJsLFxuICAgICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24udHJpbSgpLFxuICAgICAgICBjYXRlZ29yeSxcbiAgICAgICAgYnJhbmQ6ICdJS0VBJyxcbiAgICAgICAgZGltZW5zaW9ucyxcbiAgICAgICAgY29sb3JzLFxuICAgICAgICBtYXRlcmlhbHMsXG4gICAgICAgIHN0eWxlczogWydzY2FuZGluYXZpYW4nLCAnbW9kZXJuJywgJ21pbmltYWxpc3QnXSxcbiAgICAgICAgaW5TdG9jayxcbiAgICAgICAgcmV0YWlsZXI6ICdpa2VhJyxcbiAgICAgICAgc2NyYXBlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICB9O1xuXG4gICAgICByZXR1cm4gcHJvZHVjdDtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gc2NyYXBlIElLRUEgcHJvZHVjdDogJHt1cmx9YCwgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGF3YWl0IHBhZ2UuY2xvc2UoKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBzZWFyY2hQcm9kdWN0cyhxdWVyeTogc3RyaW5nLCBsaW1pdDogbnVtYmVyID0gMjApOiBQcm9taXNlPFNjcmFwZWRQcm9kdWN0W10+IHtcbiAgICBjb25zdCBwYWdlID0gYXdhaXQgdGhpcy5jcmVhdGVQYWdlKCk7XG4gICAgY29uc3QgcHJvZHVjdHM6IFNjcmFwZWRQcm9kdWN0W10gPSBbXTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZWFyY2hVcmwgPSBgJHt0aGlzLnNlYXJjaFVybH0/cT0ke2VuY29kZVVSSUNvbXBvbmVudChxdWVyeSl9YDtcbiAgICAgIGF3YWl0IHBhZ2UuZ290byhzZWFyY2hVcmwsIHsgd2FpdFVudGlsOiAnbmV0d29ya2lkbGUyJyB9KTtcblxuICAgICAgLy8gV2FpdCBmb3Igc2VhcmNoIHJlc3VsdHNcbiAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKCdbZGF0YS10ZXN0aWQ9XCJwbHAtcHJvZHVjdC1saXN0XCJdJywgeyB0aW1lb3V0OiAxMDAwMCB9KTtcblxuICAgICAgLy8gRXh0cmFjdCBwcm9kdWN0IGxpbmtzXG4gICAgICBjb25zdCBwcm9kdWN0TGlua3MgPSBhd2FpdCBwYWdlLiQkZXZhbChcbiAgICAgICAgJ1tkYXRhLXRlc3RpZD1cInBscC1wcm9kdWN0LWxpc3RcIl0gYVtocmVmKj1cIi9wcm9kdWN0cy9cIl0nLFxuICAgICAgICAobGlua3MpID0+IGxpbmtzLm1hcChsaW5rID0+IChsaW5rIGFzIEhUTUxBbmNob3JFbGVtZW50KS5ocmVmKS5zbGljZSgwLCBsaW1pdClcbiAgICAgICk7XG5cbiAgICAgIC8vIFNjcmFwZSBlYWNoIHByb2R1Y3RcbiAgICAgIGZvciAoY29uc3QgbGluayBvZiBwcm9kdWN0TGlua3MpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBwcm9kdWN0ID0gYXdhaXQgdGhpcy5zY3JhcGVQcm9kdWN0KGxpbmspO1xuICAgICAgICAgIGlmIChwcm9kdWN0KSB7XG4gICAgICAgICAgICBwcm9kdWN0cy5wdXNoKHByb2R1Y3QpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBhd2FpdCB0aGlzLmRlbGF5KDUwMCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gc2NyYXBlIElLRUEgc2VhcmNoIHJlc3VsdDogJHtsaW5rfWAsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBTY3JhcGVyRXJyb3IoYEZhaWxlZCB0byBzZWFyY2ggSUtFQSBwcm9kdWN0czogJHtxdWVyeX1gLCAnaWtlYScsIHNlYXJjaFVybCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGF3YWl0IHBhZ2UuY2xvc2UoKTtcbiAgICB9XG5cbiAgICByZXR1cm4gcHJvZHVjdHM7XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHNjcm9sbFRvTG9hZFByb2R1Y3RzKHBhZ2U6IFBhZ2UsIHRhcmdldENvdW50OiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBsZXQgcHJldmlvdXNDb3VudCA9IDA7XG4gICAgbGV0IGN1cnJlbnRDb3VudCA9IDA7XG4gICAgbGV0IGF0dGVtcHRzID0gMDtcbiAgICBjb25zdCBtYXhBdHRlbXB0cyA9IDEwO1xuXG4gICAgd2hpbGUgKGN1cnJlbnRDb3VudCA8IHRhcmdldENvdW50ICYmIGF0dGVtcHRzIDwgbWF4QXR0ZW1wdHMpIHtcbiAgICAgIGF3YWl0IHBhZ2UuZXZhbHVhdGUoKCkgPT4gd2luZG93LnNjcm9sbFRvKDAsIGRvY3VtZW50LmJvZHkuc2Nyb2xsSGVpZ2h0KSk7XG4gICAgICBhd2FpdCB0aGlzLmRlbGF5KDIwMDApO1xuXG4gICAgICBjdXJyZW50Q291bnQgPSBhd2FpdCBwYWdlLiQkZXZhbChcbiAgICAgICAgJ1tkYXRhLXRlc3RpZD1cInBscC1wcm9kdWN0LWxpc3RcIl0gYVtocmVmKj1cIi9wcm9kdWN0cy9cIl0nLFxuICAgICAgICAobGlua3MpID0+IGxpbmtzLmxlbmd0aFxuICAgICAgKTtcblxuICAgICAgaWYgKGN1cnJlbnRDb3VudCA9PT0gcHJldmlvdXNDb3VudCkge1xuICAgICAgICBhdHRlbXB0cysrO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYXR0ZW1wdHMgPSAwO1xuICAgICAgfVxuXG4gICAgICBwcmV2aW91c0NvdW50ID0gY3VycmVudENvdW50O1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZXh0cmFjdERpbWVuc2lvbnMocGFnZTogUGFnZSk6IFByb21pc2U8eyB3aWR0aD86IG51bWJlcjsgaGVpZ2h0PzogbnVtYmVyOyBkZXB0aD86IG51bWJlciB9IHwgdW5kZWZpbmVkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRpbWVuc2lvblRleHQgPSBhd2FpdCB0aGlzLnNhZmVHZXRUZXh0KHBhZ2UsICdbZGF0YS10ZXN0aWQ9XCJwaXAtcHJvZHVjdC1kaW1lbnNpb25zXCJdJyk7XG4gICAgICBpZiAoIWRpbWVuc2lvblRleHQpIHJldHVybiB1bmRlZmluZWQ7XG5cbiAgICAgIGNvbnN0IGRpbWVuc2lvbnM6IGFueSA9IHt9O1xuICAgICAgXG4gICAgICAvLyBQYXJzZSBjb21tb24gZGltZW5zaW9uIGZvcm1hdHNcbiAgICAgIGNvbnN0IHdpZHRoTWF0Y2ggPSBkaW1lbnNpb25UZXh0Lm1hdGNoKC93aWR0aFs6XFxzXSooXFxkKyg/OlxcLlxcZCspPykvaSk7XG4gICAgICBjb25zdCBoZWlnaHRNYXRjaCA9IGRpbWVuc2lvblRleHQubWF0Y2goL2hlaWdodFs6XFxzXSooXFxkKyg/OlxcLlxcZCspPykvaSk7XG4gICAgICBjb25zdCBkZXB0aE1hdGNoID0gZGltZW5zaW9uVGV4dC5tYXRjaCgvZGVwdGhbOlxcc10qKFxcZCsoPzpcXC5cXGQrKT8pL2kpO1xuXG4gICAgICBpZiAod2lkdGhNYXRjaCkgZGltZW5zaW9ucy53aWR0aCA9IHBhcnNlRmxvYXQod2lkdGhNYXRjaFsxXSk7XG4gICAgICBpZiAoaGVpZ2h0TWF0Y2gpIGRpbWVuc2lvbnMuaGVpZ2h0ID0gcGFyc2VGbG9hdChoZWlnaHRNYXRjaFsxXSk7XG4gICAgICBpZiAoZGVwdGhNYXRjaCkgZGltZW5zaW9ucy5kZXB0aCA9IHBhcnNlRmxvYXQoZGVwdGhNYXRjaFsxXSk7XG5cbiAgICAgIHJldHVybiBPYmplY3Qua2V5cyhkaW1lbnNpb25zKS5sZW5ndGggPiAwID8gZGltZW5zaW9ucyA6IHVuZGVmaW5lZDtcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBleHRyYWN0Q29sb3JzKHBhZ2U6IFBhZ2UpOiBQcm9taXNlPHN0cmluZ1tdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvbG9ycyA9IGF3YWl0IHBhZ2UuJCRldmFsKFxuICAgICAgICAnW2RhdGEtdGVzdGlkPVwicGlwLXByb2R1Y3QtY29sb3Itb3B0aW9uc1wiXSBidXR0b24nLFxuICAgICAgICAoYnV0dG9ucykgPT4gYnV0dG9ucy5tYXAoYnRuID0+IGJ0bi5nZXRBdHRyaWJ1dGUoJ2FyaWEtbGFiZWwnKSB8fCAnJykuZmlsdGVyKEJvb2xlYW4pXG4gICAgICApO1xuICAgICAgcmV0dXJuIGNvbG9ycztcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGV4dHJhY3RNYXRlcmlhbHMocGFnZTogUGFnZSk6IFByb21pc2U8c3RyaW5nW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgbWF0ZXJpYWxzVGV4dCA9IGF3YWl0IHRoaXMuc2FmZUdldFRleHQocGFnZSwgJ1tkYXRhLXRlc3RpZD1cInBpcC1wcm9kdWN0LW1hdGVyaWFsc1wiXScpO1xuICAgICAgaWYgKCFtYXRlcmlhbHNUZXh0KSByZXR1cm4gW107XG5cbiAgICAgIC8vIEV4dHJhY3QgY29tbW9uIG1hdGVyaWFsc1xuICAgICAgY29uc3QgbWF0ZXJpYWxzID0gWyd3b29kJywgJ21ldGFsJywgJ3BsYXN0aWMnLCAnZmFicmljJywgJ2dsYXNzJywgJ2xlYXRoZXInXTtcbiAgICAgIHJldHVybiBtYXRlcmlhbHMuZmlsdGVyKG1hdGVyaWFsID0+IFxuICAgICAgICBtYXRlcmlhbHNUZXh0LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobWF0ZXJpYWwpXG4gICAgICApO1xuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgY2hlY2tTdG9jayhwYWdlOiBQYWdlKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN0b2NrVGV4dCA9IGF3YWl0IHRoaXMuc2FmZUdldFRleHQocGFnZSwgJ1tkYXRhLXRlc3RpZD1cInBpcC1wcm9kdWN0LWF2YWlsYWJpbGl0eVwiXScpO1xuICAgICAgcmV0dXJuICFzdG9ja1RleHQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnb3V0IG9mIHN0b2NrJyk7XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gdHJ1ZTsgLy8gQXNzdW1lIGluIHN0b2NrIGlmIHdlIGNhbid0IGRldGVybWluZVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgbWFwSWtlYUNhdGVnb3J5KGJyZWFkY3J1bWJzOiBzdHJpbmdbXSk6IHN0cmluZyB7XG4gICAgY29uc3QgY2F0ZWdvcnlNYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgICAnY2hhaXJzJzogJ3NlYXRpbmcnLFxuICAgICAgJ3NvZmFzJzogJ3NlYXRpbmcnLFxuICAgICAgJ2FybWNoYWlycyc6ICdzZWF0aW5nJyxcbiAgICAgICd0YWJsZXMnOiAndGFibGVzJyxcbiAgICAgICdkZXNrcyc6ICd0YWJsZXMnLFxuICAgICAgJ3N0b3JhZ2UnOiAnc3RvcmFnZScsXG4gICAgICAnbGlnaHRpbmcnOiAnbGlnaHRpbmcnLFxuICAgICAgJ2RlY29yYXRpb24nOiAnZGVjb3InLFxuICAgICAgJ2JlZHJvb20nOiAnYmVkcm9vbScsXG4gICAgICAna2l0Y2hlbic6ICdraXRjaGVuJ1xuICAgIH07XG5cbiAgICBmb3IgKGNvbnN0IGJyZWFkY3J1bWIgb2YgYnJlYWRjcnVtYnMpIHtcbiAgICAgIGNvbnN0IGxvd2VyID0gYnJlYWRjcnVtYi50b0xvd2VyQ2FzZSgpO1xuICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoY2F0ZWdvcnlNYXApKSB7XG4gICAgICAgIGlmIChsb3dlci5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuICdmdXJuaXR1cmUnOyAvLyBEZWZhdWx0IGNhdGVnb3J5XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJCYXNlU2NyYXBlciIsIlNjcmFwZXJFcnJvciIsIklrZWFTY3JhcGVyIiwic2NyYXBlUHJvZHVjdHMiLCJjYXRlZ29yeSIsImxpbWl0IiwicGFnZSIsImNyZWF0ZVBhZ2UiLCJwcm9kdWN0cyIsImNhdGVnb3J5VXJscyIsImNhdGVnb3J5VXJsIiwidG9Mb3dlckNhc2UiLCJnb3RvIiwiYmFzZVVybCIsIndhaXRVbnRpbCIsIndhaXRGb3JTZWxlY3RvciIsInRpbWVvdXQiLCJzY3JvbGxUb0xvYWRQcm9kdWN0cyIsInByb2R1Y3RMaW5rcyIsIiQkZXZhbCIsImxpbmtzIiwibWFwIiwibGluayIsImhyZWYiLCJzbGljZSIsImNvbnNvbGUiLCJsb2ciLCJsZW5ndGgiLCJpIiwiTWF0aCIsIm1pbiIsInByb2R1Y3QiLCJzY3JhcGVQcm9kdWN0IiwicHVzaCIsIm5hbWUiLCJkZWxheSIsImVycm9yIiwid2FybiIsImNsb3NlIiwidXJsIiwic2FmZUdldFRleHQiLCJwcmljZVRleHQiLCJkZXNjcmlwdGlvbiIsImltYWdlVXJsIiwic2FmZUdldEF0dHJpYnV0ZSIsInByaWNlIiwiY3VycmVuY3kiLCJwYXJzZVByaWNlIiwiZGltZW5zaW9ucyIsImV4dHJhY3REaW1lbnNpb25zIiwiY29sb3JzIiwiZXh0cmFjdENvbG9ycyIsIm1hdGVyaWFscyIsImV4dHJhY3RNYXRlcmlhbHMiLCJpblN0b2NrIiwiY2hlY2tTdG9jayIsImJyZWFkY3J1bWJzIiwidGV4dENvbnRlbnQiLCJ0cmltIiwibWFwSWtlYUNhdGVnb3J5IiwiaWQiLCJnZW5lcmF0ZVByb2R1Y3RJZCIsInN0YXJ0c1dpdGgiLCJwcm9kdWN0VXJsIiwiYnJhbmQiLCJzdHlsZXMiLCJyZXRhaWxlciIsInNjcmFwZWRBdCIsIkRhdGUiLCJzZWFyY2hQcm9kdWN0cyIsInF1ZXJ5Iiwic2VhcmNoVXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwidGFyZ2V0Q291bnQiLCJwcmV2aW91c0NvdW50IiwiY3VycmVudENvdW50IiwiYXR0ZW1wdHMiLCJtYXhBdHRlbXB0cyIsImV2YWx1YXRlIiwid2luZG93Iiwic2Nyb2xsVG8iLCJkb2N1bWVudCIsImJvZHkiLCJzY3JvbGxIZWlnaHQiLCJkaW1lbnNpb25UZXh0IiwidW5kZWZpbmVkIiwid2lkdGhNYXRjaCIsIm1hdGNoIiwiaGVpZ2h0TWF0Y2giLCJkZXB0aE1hdGNoIiwid2lkdGgiLCJwYXJzZUZsb2F0IiwiaGVpZ2h0IiwiZGVwdGgiLCJPYmplY3QiLCJrZXlzIiwiYnV0dG9ucyIsImJ0biIsImdldEF0dHJpYnV0ZSIsImZpbHRlciIsIkJvb2xlYW4iLCJtYXRlcmlhbHNUZXh0IiwibWF0ZXJpYWwiLCJpbmNsdWRlcyIsInN0b2NrVGV4dCIsImNhdGVnb3J5TWFwIiwiYnJlYWRjcnVtYiIsImxvd2VyIiwia2V5IiwidmFsdWUiLCJlbnRyaWVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scrapers/ikea-scraper.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/scrapers/scraper-manager.ts":
/*!*********************************************!*\
  !*** ./src/lib/scrapers/scraper-manager.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScraperManager: () => (/* binding */ ScraperManager)\n/* harmony export */ });\n/* harmony import */ var _ikea_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ikea-scraper */ \"(rsc)/./src/lib/scrapers/ikea-scraper.ts\");\n/* harmony import */ var _amazon_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./amazon-scraper */ \"(rsc)/./src/lib/scrapers/amazon-scraper.ts\");\n/* harmony import */ var _daraz_scraper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./daraz-scraper */ \"(rsc)/./src/lib/scrapers/daraz-scraper.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ikea_scraper__WEBPACK_IMPORTED_MODULE_0__, _amazon_scraper__WEBPACK_IMPORTED_MODULE_1__, _daraz_scraper__WEBPACK_IMPORTED_MODULE_2__]);\n([_ikea_scraper__WEBPACK_IMPORTED_MODULE_0__, _amazon_scraper__WEBPACK_IMPORTED_MODULE_1__, _daraz_scraper__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nclass ScraperManager {\n    constructor(){\n        this.scrapers = new Map([\n            [\n                'ikea',\n                new _ikea_scraper__WEBPACK_IMPORTED_MODULE_0__.IkeaScraper()\n            ],\n            [\n                'amazon',\n                new _amazon_scraper__WEBPACK_IMPORTED_MODULE_1__.AmazonScraper()\n            ],\n            [\n                'daraz',\n                new _daraz_scraper__WEBPACK_IMPORTED_MODULE_2__.DarazScraper()\n            ]\n        ]);\n        this.database = new _database__WEBPACK_IMPORTED_MODULE_3__.DatabaseService();\n        this.activeJobs = new Map();\n    }\n    async scrapeAllRetailers(categories = [\n        'seating',\n        'tables',\n        'storage',\n        'lighting',\n        'decor'\n    ]) {\n        const jobs = [];\n        for (const retailer of this.scrapers.keys()){\n            for (const category of categories){\n                const job = await this.startScrapingJob(retailer, category);\n                jobs.push(job);\n            }\n        }\n        // Wait for all jobs to complete\n        await Promise.allSettled(jobs.map((job)=>this.waitForJob(job.id)));\n        return jobs;\n    }\n    async startScrapingJob(retailer, category, limit = 20) {\n        const jobId = `${retailer}-${category}-${Date.now()}`;\n        const job = {\n            id: jobId,\n            retailer,\n            category,\n            status: 'pending',\n            productsScraped: 0\n        };\n        this.activeJobs.set(jobId, job);\n        // Start scraping in background\n        this.runScrapingJob(jobId, limit).catch((error)=>{\n            console.error(`Scraping job ${jobId} failed:`, error);\n            const failedJob = this.activeJobs.get(jobId);\n            if (failedJob) {\n                failedJob.status = 'failed';\n                failedJob.error = error.message;\n                failedJob.completedAt = new Date();\n            }\n        });\n        return job;\n    }\n    async runScrapingJob(jobId, limit) {\n        const job = this.activeJobs.get(jobId);\n        if (!job) throw new Error(`Job ${jobId} not found`);\n        const scraper = this.scrapers.get(job.retailer);\n        if (!scraper) throw new Error(`Scraper for ${job.retailer} not found`);\n        try {\n            job.status = 'running';\n            job.startedAt = new Date();\n            console.log(`Starting scraping job: ${job.retailer} - ${job.category}`);\n            // Scrape products\n            const products = await scraper.scrapeProducts(job.category, limit);\n            console.log(`Scraped ${products.length} products from ${job.retailer} - ${job.category}`);\n            // Save to database\n            for (const product of products){\n                try {\n                    await this.saveProductToDatabase(product);\n                    job.productsScraped++;\n                } catch (error) {\n                    console.warn(`Failed to save product ${product.id}:`, error);\n                }\n            }\n            job.status = 'completed';\n            job.completedAt = new Date();\n            console.log(`Completed scraping job: ${job.retailer} - ${job.category} (${job.productsScraped} products)`);\n        } catch (error) {\n            job.status = 'failed';\n            job.error = error instanceof Error ? error.message : 'Unknown error';\n            job.completedAt = new Date();\n            throw error;\n        } finally{\n            // Cleanup scraper resources\n            await scraper.cleanup();\n        }\n    }\n    async saveProductToDatabase(product) {\n        try {\n            // Check if product already exists\n            const existingProduct = await this.database.getProductByExternalId(product.id);\n            if (existingProduct) {\n                // Update existing product\n                await this.database.updateProduct(existingProduct.id, {\n                    name: product.name,\n                    price: product.price,\n                    currency: product.currency,\n                    imageUrl: product.imageUrl,\n                    productUrl: product.productUrl,\n                    description: product.description,\n                    inStock: product.inStock,\n                    updatedAt: new Date()\n                });\n            } else {\n                // Create new product\n                await this.database.createProduct({\n                    externalId: product.id,\n                    name: product.name,\n                    category: product.category,\n                    subcategory: product.subcategory,\n                    price: product.price,\n                    currency: product.currency,\n                    imageUrl: product.imageUrl,\n                    productUrl: product.productUrl,\n                    description: product.description,\n                    brand: product.brand,\n                    dimensions: product.dimensions ? JSON.stringify(product.dimensions) : null,\n                    colors: product.colors || [],\n                    materials: product.materials || [],\n                    styles: product.styles || [],\n                    rating: product.rating,\n                    reviewCount: product.reviewCount,\n                    inStock: product.inStock,\n                    retailer: product.retailer,\n                    scrapedAt: product.scrapedAt\n                });\n            }\n        } catch (error) {\n            console.error(`Failed to save product ${product.id} to database:`, error);\n            throw error;\n        }\n    }\n    async getJobStatus(jobId) {\n        return this.activeJobs.get(jobId) || null;\n    }\n    async getAllJobs() {\n        return Array.from(this.activeJobs.values());\n    }\n    async waitForJob(jobId, timeout = 300000) {\n        const startTime = Date.now();\n        while(Date.now() - startTime < timeout){\n            const job = this.activeJobs.get(jobId);\n            if (!job) throw new Error(`Job ${jobId} not found`);\n            if (job.status === 'completed' || job.status === 'failed') {\n                return job;\n            }\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n        throw new Error(`Job ${jobId} timed out`);\n    }\n    async searchProducts(query, retailers = [\n        'ikea',\n        'amazon',\n        'daraz'\n    ]) {\n        const allProducts = [];\n        for (const retailerName of retailers){\n            const scraper = this.scrapers.get(retailerName);\n            if (!scraper) continue;\n            try {\n                console.log(`Searching ${retailerName} for: ${query}`);\n                const products = await scraper.searchProducts(query, 10);\n                allProducts.push(...products);\n                // Save products to database\n                for (const product of products){\n                    try {\n                        await this.saveProductToDatabase(product);\n                    } catch (error) {\n                        console.warn(`Failed to save search result ${product.id}:`, error);\n                    }\n                }\n            } catch (error) {\n                console.warn(`Search failed for ${retailerName}:`, error);\n            } finally{\n                await scraper.cleanup();\n            }\n        }\n        return allProducts;\n    }\n    async getProductsFromDatabase(category, limit = 100) {\n        try {\n            return await this.database.getProducts({\n                category,\n                limit,\n                orderBy: 'createdAt',\n                orderDirection: 'desc'\n            });\n        } catch (error) {\n            console.error('Failed to get products from database:', error);\n            return [];\n        }\n    }\n    async updateProductPrices(retailer) {\n        try {\n            const products = await this.database.getProducts({\n                retailer,\n                inStock: true,\n                limit: 100\n            });\n            for (const product of products){\n                const scraper = this.scrapers.get(product.retailer);\n                if (!scraper) continue;\n                try {\n                    const updatedProduct = await scraper.scrapeProduct(product.productUrl);\n                    if (updatedProduct && updatedProduct.price !== product.price) {\n                        await this.database.updateProduct(product.id, {\n                            price: updatedProduct.price,\n                            inStock: updatedProduct.inStock,\n                            updatedAt: new Date()\n                        });\n                        console.log(`Updated price for ${product.name}: ${product.price} -> ${updatedProduct.price}`);\n                    }\n                } catch (error) {\n                    console.warn(`Failed to update price for product ${product.id}:`, error);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to update product prices:', error);\n        }\n    }\n    async cleanup() {\n        for (const scraper of this.scrapers.values()){\n            await scraper.cleanup();\n        }\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/scrapers/scraper-manager.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/user-agents"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();