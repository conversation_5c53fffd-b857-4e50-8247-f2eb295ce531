/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scrape/route";
exports.ids = ["app/api/scrape/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/scrape/route.ts */ \"(rsc)/./src/app/api/scrape/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scrape/route\",\n        pathname: \"/api/scrape\",\n        filename: \"route\",\n        bundlePath: \"app/api/scrape/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/scrape/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_scrape_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scrape/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/scrape/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_server_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/server-scraper */ \"(rsc)/./src/lib/server-scraper.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, retailer, category, query, limit } = body;\n        const serverScraper = new _lib_server_scraper__WEBPACK_IMPORTED_MODULE_1__.ServerScraper();\n        switch(action){\n            case 'scrape_category':\n                if (!retailer || !category) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Retailer and category are required for scraping'\n                    }, {\n                        status: 400\n                    });\n                }\n                const result = await serverScraper.scrapeProducts(retailer, category, limit || 10);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: result.success,\n                    productsScraped: result.productsScraped,\n                    message: result.message\n                });\n            case 'scrape_all':\n                const categories = [\n                    'seating',\n                    'tables',\n                    'storage',\n                    'lighting',\n                    'decor'\n                ];\n                const retailers = [\n                    'ikea',\n                    'amazon',\n                    'daraz'\n                ];\n                const results = [];\n                for (const ret of retailers){\n                    for (const cat of categories){\n                        const result = await serverScraper.scrapeProducts(ret, cat, 5);\n                        results.push({\n                            retailer: ret,\n                            category: cat,\n                            ...result\n                        });\n                    }\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    results,\n                    message: `Completed scraping all retailers for ${categories.length} categories`\n                });\n            case 'get_products':\n                const database = new _lib_database__WEBPACK_IMPORTED_MODULE_2__.DatabaseService();\n                const products = await database.searchProducts({\n                    category: category || undefined,\n                    retailer: retailer || undefined,\n                    limit: limit || 100,\n                    isAvailable: true\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    products,\n                    count: products.length\n                });\n            case 'stats':\n                const stats = await serverScraper.getScrapingStats();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    stats\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action. Supported actions: scrape_category, scrape_all, get_products, stats'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Scraping API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        const category = searchParams.get('category');\n        const retailer = searchParams.get('retailer');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const serverScraper = new _lib_server_scraper__WEBPACK_IMPORTED_MODULE_1__.ServerScraper();\n        const database = new _lib_database__WEBPACK_IMPORTED_MODULE_2__.DatabaseService();\n        switch(action){\n            case 'products':\n                const products = await database.searchProducts({\n                    category: category || undefined,\n                    retailer: retailer || undefined,\n                    limit,\n                    isAvailable: true\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    products,\n                    count: products.length\n                });\n            case 'stats':\n                const stats = await serverScraper.getScrapingStats();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    stats\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: 'Scraping API is running',\n                    endpoints: {\n                        'GET /api/scrape?action=products&category=seating': 'Get scraped products',\n                        'GET /api/scrape?action=stats': 'Get scraping statistics',\n                        'POST /api/scrape': 'Start scraping operations'\n                    }\n                });\n        }\n    } catch (error) {\n        console.error('Scraping API GET error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scrape/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n// Database service using Prisma\n\n// Global Prisma instance to prevent multiple connections in development\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\nclass DatabaseService {\n    // Room Management\n    static async createRoom(data) {\n        return await prisma.room.create({\n            data: {\n                ...data\n            }\n        });\n    }\n    static async getRoomById(id) {\n        return await prisma.room.findUnique({\n            where: {\n                id\n            },\n            include: {\n                recommendations: {\n                    include: {\n                        product: true\n                    }\n                },\n                shoppingLists: {\n                    include: {\n                        items: {\n                            include: {\n                                product: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n    }\n    static async updateRoomAnalysis(roomId, analysisResult) {\n        return await prisma.room.update({\n            where: {\n                id: roomId\n            },\n            data: {\n                analysisResult,\n                updatedAt: new Date()\n            }\n        });\n    }\n    static async getUserRooms(userId) {\n        return await prisma.room.findMany({\n            where: {\n                userId\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            },\n            include: {\n                recommendations: {\n                    take: 3,\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    // Product Management\n    static async createProduct(data) {\n        return await prisma.product.create({\n            data: {\n                ...data,\n                styles: data.styles || [],\n                colors: data.colors || [],\n                materials: data.materials || [],\n                tags: data.tags || []\n            }\n        });\n    }\n    static async getProductByExternalId(externalId) {\n        return await prisma.product.findUnique({\n            where: {\n                externalId\n            }\n        });\n    }\n    static async updateProduct(id, data) {\n        return await prisma.product.update({\n            where: {\n                id\n            },\n            data\n        });\n    }\n    static async getProductById(id) {\n        return await prisma.product.findUnique({\n            where: {\n                id\n            },\n            include: {\n                priceHistory: {\n                    orderBy: {\n                        recordedAt: 'desc'\n                    },\n                    take: 10\n                }\n            }\n        });\n    }\n    static async searchProducts(filters) {\n        const where = {};\n        if (filters.category) where.category = filters.category;\n        if (filters.retailer) where.retailer = filters.retailer;\n        if (filters.isAvailable !== undefined) where.isAvailable = filters.isAvailable;\n        if (filters.minPrice || filters.maxPrice) {\n            where.price = {};\n            if (filters.minPrice) where.price.gte = filters.minPrice;\n            if (filters.maxPrice) where.price.lte = filters.maxPrice;\n        }\n        if (filters.styles && filters.styles.length > 0) {\n            where.styles = {\n                hasSome: filters.styles\n            };\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 20,\n            skip: filters.offset || 0,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n    static async getProducts(filters) {\n        const where = {};\n        if (filters.category) {\n            where.category = filters.category;\n        }\n        if (filters.retailer) {\n            where.retailer = filters.retailer;\n        }\n        if (filters.inStock !== undefined) {\n            where.isAvailable = filters.inStock;\n        }\n        const orderBy = {};\n        if (filters.orderBy) {\n            orderBy[filters.orderBy] = filters.orderDirection || 'desc';\n        } else {\n            orderBy.createdAt = 'desc';\n        }\n        return await prisma.product.findMany({\n            where,\n            take: filters.limit || 100,\n            orderBy\n        });\n    }\n    // Recommendations\n    static async saveRecommendations(roomId, recommendations) {\n        // Delete existing recommendations for this room\n        await prisma.recommendation.deleteMany({\n            where: {\n                roomId\n            }\n        });\n        // Create new recommendations\n        return await prisma.recommendation.createMany({\n            data: recommendations.map((rec)=>({\n                    ...rec,\n                    roomId,\n                    placement: rec.placement || undefined\n                }))\n        });\n    }\n    static async getRoomRecommendations(roomId) {\n        const recommendations = await prisma.recommendation.findMany({\n            where: {\n                roomId\n            },\n            include: {\n                product: true\n            },\n            orderBy: {\n                rank: 'asc'\n            }\n        });\n        return recommendations;\n    }\n    // Shopping Lists\n    static async createShoppingList(data) {\n        const totalCost = data.items.reduce((sum, item)=>sum + item.priceAtAdd * item.quantity, 0);\n        return await prisma.shoppingList.create({\n            data: {\n                name: data.name,\n                budget: data.budget,\n                totalCost,\n                userId: data.userId,\n                roomId: data.roomId,\n                items: {\n                    create: data.items\n                }\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n    }\n    static async getShoppingList(id) {\n        const shoppingList = await prisma.shoppingList.findUnique({\n            where: {\n                id\n            },\n            include: {\n                items: {\n                    include: {\n                        product: true\n                    }\n                },\n                user: true,\n                room: true\n            }\n        });\n        if (shoppingList) {\n            return {\n                ...shoppingList,\n                items: shoppingList.items\n            };\n        }\n        return null;\n    }\n    // User Management\n    static async createUser(data) {\n        return await prisma.user.create({\n            data\n        });\n    }\n    static async getUserByEmail(email) {\n        return await prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                preferences: true\n            }\n        });\n    }\n    static async updateUserPreferences(userId, preferences) {\n        return await prisma.userPreferences.upsert({\n            where: {\n                userId\n            },\n            update: {\n                ...preferences,\n                updatedAt: new Date()\n            },\n            create: {\n                userId,\n                ...preferences,\n                preferredStyles: preferences.preferredStyles || [],\n                favoriteRetailers: preferences.favoriteRetailers || [],\n                colorPreferences: preferences.colorPreferences || []\n            }\n        });\n    }\n    // Analytics\n    static async trackEvent(data) {\n        return await prisma.analyticsEvent.create({\n            data: {\n                ...data,\n                eventData: data.eventData || undefined\n            }\n        });\n    }\n    // Price Tracking\n    static async addPriceHistory(productId, price, source) {\n        return await prisma.priceHistory.create({\n            data: {\n                productId,\n                price,\n                source\n            }\n        });\n    }\n    static async createPriceAlert(data) {\n        return await prisma.priceAlert.create({\n            data\n        });\n    }\n    // Utility methods\n    static async cleanup() {\n        await prisma.$disconnect();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/server-scraper.ts":
/*!***********************************!*\
  !*** ./src/lib/server-scraper.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServerScraper: () => (/* binding */ ServerScraper)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n// Server-side only scraping service\n\n// Simple scraping simulation for demonstration\n// In production, this would run the actual scrapers in a separate process\nclass ServerScraper {\n    async scrapeProducts(retailer, category, limit = 10) {\n        try {\n            console.log(`🚀 Starting scraping: ${retailer} - ${category} (limit: ${limit})`);\n            // For now, we'll simulate scraping with realistic data\n            // In production, this would call the actual scrapers\n            const scrapedProducts = await this.simulateRealScraping(retailer, category, limit);\n            // Save to database\n            let savedCount = 0;\n            for (const product of scrapedProducts){\n                try {\n                    await this.saveProductToDatabase(product);\n                    savedCount++;\n                } catch (error) {\n                    console.warn(`Failed to save product ${product.id}:`, error);\n                }\n            }\n            return {\n                success: true,\n                productsScraped: savedCount,\n                message: `Successfully scraped and saved ${savedCount} products from ${retailer}`\n            };\n        } catch (error) {\n            console.error(`Scraping failed for ${retailer} - ${category}:`, error);\n            return {\n                success: false,\n                productsScraped: 0,\n                message: `Scraping failed: ${error instanceof Error ? error.message : 'Unknown error'}`\n            };\n        }\n    }\n    async simulateRealScraping(retailer, category, limit) {\n        // Simulate realistic scraping data based on retailer and category\n        const products = [];\n        const baseProducts = this.getBaseProductData(retailer, category);\n        for(let i = 0; i < Math.min(limit, baseProducts.length); i++){\n            const baseProduct = baseProducts[i];\n            // Add realistic variations\n            const product = {\n                ...baseProduct,\n                id: `${retailer}-${category}-${Date.now()}-${i}`,\n                price: baseProduct.price + (Math.random() - 0.5) * 100,\n                scrapedAt: new Date(),\n                inStock: Math.random() > 0.1,\n                rating: 3.5 + Math.random() * 1.5,\n                reviewCount: Math.floor(Math.random() * 1000) + 50\n            };\n            products.push(product);\n        }\n        // Simulate scraping delay\n        await new Promise((resolve)=>setTimeout(resolve, 2000 + Math.random() * 3000));\n        return products;\n    }\n    getBaseProductData(retailer, category) {\n        const productTemplates = {\n            ikea: {\n                seating: [\n                    {\n                        name: 'KIVIK 3-seat sofa',\n                        description: 'A generous seating series with a soft, deep seat and comfortable support for your back.',\n                        price: 599,\n                        currency: 'USD',\n                        imageUrl: 'https://www.ikea.com/us/en/images/products/kivik-3-seat-sofa-hillared-beige__0818588_pe774490_s5.jpg',\n                        productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',\n                        brand: 'IKEA',\n                        category: 'seating',\n                        subcategory: 'sofas',\n                        styles: [\n                            'scandinavian',\n                            'modern'\n                        ],\n                        colors: [\n                            'beige',\n                            'gray',\n                            'blue'\n                        ],\n                        materials: [\n                            'fabric',\n                            'wood'\n                        ],\n                        dimensions: {\n                            width: 228,\n                            height: 83,\n                            depth: 95\n                        }\n                    },\n                    {\n                        name: 'EKTORP 3-seat sofa',\n                        description: 'A timeless sofa with soft, generous proportions and deep seats.',\n                        price: 449,\n                        currency: 'USD',\n                        imageUrl: 'https://www.ikea.com/us/en/images/products/ektorp-3-seat-sofa-totebo-light-beige__0818582_pe774484_s5.jpg',\n                        productUrl: 'https://www.ikea.com/us/en/p/ektorp-sofa-totebo-light-beige-s79318702/',\n                        brand: 'IKEA',\n                        category: 'seating',\n                        subcategory: 'sofas',\n                        styles: [\n                            'traditional',\n                            'classic'\n                        ],\n                        colors: [\n                            'beige',\n                            'white',\n                            'dark gray'\n                        ],\n                        materials: [\n                            'fabric',\n                            'wood'\n                        ],\n                        dimensions: {\n                            width: 218,\n                            height: 88,\n                            depth: 88\n                        }\n                    }\n                ],\n                tables: [\n                    {\n                        name: 'HEMNES Coffee table',\n                        description: 'Solid wood coffee table with drawers for extra storage.',\n                        price: 179,\n                        currency: 'USD',\n                        imageUrl: 'https://www.ikea.com/us/en/images/products/hemnes-coffee-table-white-stain__0318329_pe515964_s5.jpg',\n                        productUrl: 'https://www.ikea.com/us/en/p/hemnes-coffee-table-white-stain-80104294/',\n                        brand: 'IKEA',\n                        category: 'tables',\n                        subcategory: 'coffee_tables',\n                        styles: [\n                            'traditional',\n                            'scandinavian'\n                        ],\n                        colors: [\n                            'white',\n                            'brown'\n                        ],\n                        materials: [\n                            'pine',\n                            'wood'\n                        ],\n                        dimensions: {\n                            width: 118,\n                            height: 46,\n                            depth: 75\n                        }\n                    }\n                ]\n            },\n            amazon: {\n                seating: [\n                    {\n                        name: 'Rivet Revolve Modern Upholstered Sofa',\n                        description: 'Mid-century modern style with clean lines and button tufting.',\n                        price: 899,\n                        currency: 'USD',\n                        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n                        productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ?tag=youraffid-20',\n                        brand: 'Rivet',\n                        category: 'seating',\n                        subcategory: 'sofas',\n                        styles: [\n                            'modern',\n                            'mid-century'\n                        ],\n                        colors: [\n                            'storm',\n                            'navy',\n                            'charcoal'\n                        ],\n                        materials: [\n                            'fabric',\n                            'wood'\n                        ],\n                        dimensions: {\n                            width: 203,\n                            height: 86,\n                            depth: 91\n                        }\n                    }\n                ],\n                electronics: [\n                    {\n                        name: 'Samsung 55\" Class 4K UHD Smart TV',\n                        description: 'Crystal clear 4K display with smart TV features and HDR support.',\n                        price: 599,\n                        currency: 'USD',\n                        imageUrl: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=600&h=400&fit=crop',\n                        productUrl: 'https://www.amazon.com/dp/B08N5WRWNW?tag=youraffid-20',\n                        brand: 'Samsung',\n                        category: 'electronics',\n                        subcategory: 'televisions',\n                        styles: [\n                            'modern',\n                            'minimalist'\n                        ],\n                        colors: [\n                            'black'\n                        ],\n                        materials: [\n                            'plastic',\n                            'metal'\n                        ],\n                        dimensions: {\n                            width: 123,\n                            height: 71,\n                            depth: 6\n                        }\n                    }\n                ]\n            },\n            daraz: {\n                seating: [\n                    {\n                        name: 'Premium Wooden Dining Chair Set',\n                        description: 'Elegant wooden dining chairs with comfortable cushioned seats.',\n                        price: 299,\n                        currency: 'USD',\n                        imageUrl: 'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=600&h=400&fit=crop',\n                        productUrl: 'https://www.daraz.pk/products/wooden-dining-chair-set-i123456789.html',\n                        brand: 'HomeStyle',\n                        category: 'seating',\n                        subcategory: 'dining_chairs',\n                        styles: [\n                            'traditional',\n                            'rustic'\n                        ],\n                        colors: [\n                            'brown',\n                            'natural wood'\n                        ],\n                        materials: [\n                            'wood',\n                            'fabric'\n                        ],\n                        dimensions: {\n                            width: 45,\n                            height: 85,\n                            depth: 50\n                        }\n                    }\n                ]\n            }\n        };\n        const retailerProducts = productTemplates[retailer];\n        if (!retailerProducts) return [];\n        const categoryProducts = retailerProducts[category];\n        if (!categoryProducts) return [];\n        return categoryProducts;\n    }\n    async saveProductToDatabase(product) {\n        try {\n            console.log(`💾 Saving product to database: ${product.name}`);\n            // Check if product already exists\n            const existingProduct = await _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getProductByExternalId(product.id);\n            if (existingProduct) {\n                console.log(`🔄 Updating existing product: ${product.name}`);\n                // Update existing product\n                await _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.updateProduct(existingProduct.id, {\n                    name: product.name,\n                    price: product.price,\n                    currency: product.currency,\n                    imageUrl: product.imageUrl,\n                    productUrl: product.productUrl,\n                    description: product.description,\n                    isAvailable: product.inStock,\n                    rating: product.rating,\n                    reviewCount: product.reviewCount,\n                    scrapedAt: product.scrapedAt,\n                    updatedAt: new Date()\n                });\n            } else {\n                console.log(`➕ Creating new product: ${product.name}`);\n                // Create new product\n                await _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.createProduct({\n                    externalId: product.id,\n                    name: product.name,\n                    category: product.category,\n                    subcategory: product.subcategory,\n                    price: product.price,\n                    currency: product.currency,\n                    imageUrl: product.imageUrl,\n                    productUrl: product.productUrl,\n                    description: product.description,\n                    brand: product.brand,\n                    width: product.dimensions?.width,\n                    height: product.dimensions?.height,\n                    depth: product.dimensions?.depth,\n                    colors: product.colors || [],\n                    materials: product.materials || [],\n                    styles: product.styles || [],\n                    rating: product.rating,\n                    reviewCount: product.reviewCount,\n                    // isAvailable: product.inStock, // This field might not exist in the schema\n                    retailer: product.retailer,\n                    scrapedAt: product.scrapedAt\n                });\n            }\n            console.log(`✅ Successfully saved product: ${product.name}`);\n        } catch (error) {\n            console.error(`❌ Failed to save product ${product.id} to database:`, error);\n            throw error;\n        }\n    }\n    async getScrapingStats() {\n        try {\n            const allProducts = await _database__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.searchProducts({\n                // isAvailable: true, // This field might not exist\n                limit: 1000\n            });\n            const stats = {\n                totalProducts: allProducts.length,\n                productsByRetailer: {},\n                productsByCategory: {},\n                lastScrapedAt: undefined\n            };\n            allProducts.forEach((product)=>{\n                // Count by retailer\n                stats.productsByRetailer[product.retailer] = (stats.productsByRetailer[product.retailer] || 0) + 1;\n                // Count by category\n                stats.productsByCategory[product.category] = (stats.productsByCategory[product.category] || 0) + 1;\n                // Track latest scrape date\n                if (product.scrapedAt) {\n                    const scrapedDate = new Date(product.scrapedAt);\n                    if (!stats.lastScrapedAt || scrapedDate > stats.lastScrapedAt) {\n                        stats.lastScrapedAt = scrapedDate;\n                    }\n                }\n            });\n            return stats;\n        } catch (error) {\n            console.error('Failed to get scraping stats:', error);\n            return {\n                totalProducts: 0,\n                productsByRetailer: {},\n                productsByCategory: {},\n                lastScrapedAt: undefined\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/server-scraper.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscrape%2Froute&page=%2Fapi%2Fscrape%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscrape%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();