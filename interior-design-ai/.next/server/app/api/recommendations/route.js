"use strict";(()=>{var e={};e.id=630,e.ids=[630],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8250:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>b});var i={};r.r(i),r.d(i,{GET:()=>y,POST:()=>h});var a=r(96559),n=r(48088),o=r(37719),s=r(32190),l=r(34108),c=r(5689);let d=[{id:"sofa-modern-1",name:"Modern 3-Seat Sofa",category:"seating",description:"Comfortable modern sofa with clean lines and neutral upholstery",price:899,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/",retailer:"ikea",dimensions:{width:228,height:83,depth:95},style:["modern","minimalist","contemporary"]},{id:"coffee-table-1",name:"Glass Coffee Table",category:"tables",description:"Sleek glass coffee table with metal legs",price:299,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.amazon.com/dp/B08XYZ123",retailer:"amazon",dimensions:{width:120,height:45,depth:60},style:["modern","contemporary","minimalist"]},{id:"accent-chair-1",name:"Velvet Accent Chair",category:"seating",description:"Luxurious velvet accent chair in emerald green",price:449,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.amazon.com/dp/B08ABC456",retailer:"amazon",dimensions:{width:76,height:86,depth:81},style:["boho","contemporary","traditional"]},{id:"bed-frame-1",name:"Platform Bed Frame",category:"seating",description:"Minimalist platform bed frame in natural wood",price:599,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932209/",retailer:"ikea",dimensions:{width:209,height:38,depth:156},style:["scandinavian","minimalist","modern"]},{id:"nightstand-1",name:"Two-Drawer Nightstand",category:"storage",description:"Compact nightstand with two drawers",price:149,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.ikea.com/us/en/p/hemnes-nightstand-white-stain-s49932210/",retailer:"ikea",dimensions:{width:46,height:61,depth:35},style:["scandinavian","traditional","contemporary"]},{id:"floor-lamp-1",name:"Arc Floor Lamp",category:"lighting",description:"Modern arc floor lamp with adjustable height",price:199,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.amazon.com/dp/B08DEF789",retailer:"amazon",dimensions:{width:40,height:180,depth:40},style:["modern","contemporary","industrial"]},{id:"wall-art-1",name:"Abstract Canvas Print Set",category:"decor",description:"Set of 3 abstract canvas prints in neutral tones",price:89,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.amazon.com/dp/B08GHI012",retailer:"amazon",dimensions:{width:40,height:60,depth:2},style:["modern","contemporary","minimalist"]},{id:"plant-1",name:"Fiddle Leaf Fig",category:"plants",description:"Large fiddle leaf fig plant in decorative pot",price:79,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.amazon.com/dp/B08JKL345",retailer:"amazon",dimensions:{width:30,height:150,depth:30},style:["boho","scandinavian","contemporary"]},{id:"bookshelf-1",name:"5-Tier Bookshelf",category:"storage",description:"Tall bookshelf with 5 adjustable shelves",price:179,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932211/",retailer:"ikea",dimensions:{width:90,height:197,depth:37},style:["scandinavian","traditional","contemporary"]}],m={modern:["contemporary","minimalist","industrial"],minimalist:["modern","scandinavian","contemporary"],boho:["rustic","traditional"],industrial:["modern","contemporary"],scandinavian:["minimalist","contemporary","modern"],traditional:["boho","rustic"],contemporary:["modern","minimalist","scandinavian","industrial"],rustic:["boho","traditional"]},p={living_room:["seating","tables","lighting","storage","decor","plants"],bedroom:["seating","storage","lighting","decor","textiles"],kitchen:["storage","lighting","decor"],bathroom:["storage","lighting","textiles"],dining_room:["seating","tables","lighting","storage","decor"],office:["seating","tables","storage","lighting","decor"],nursery:["seating","storage","lighting","textiles","decor"],guest_room:["seating","storage","lighting","decor","textiles"]};class u{async generateRecommendations(e,t,r,i,a=[]){let n=c.q.analyzeRoomStyle(e),o=n.confidence[i]>.5?i:n.suggestedStyles[0],s=l.T.searchProducts(void 0,o,{min:0,max:.4*r}).filter(e=>!a.includes(e.id)).map(i=>({...i,score:this.calculateAdvancedFurnitureScore(i,e,t,o,r,n)})).sort((e,t)=>t.score-e.score);return this.selectOptimalRecommendations(s,r,t,o).map(r=>this.enhanceRecommendation(r,e,t))}filterByRoomAndStyle(e,t){let r=[t,...m[t]],i=p[e];return d.filter(e=>{let t=e.style.some(e=>r.includes(e)),a=i.includes(e.category);return t&&a})}calculateAdvancedFurnitureScore(e,t,r,i,a,n){let o=0,s=e.style.includes(i),l=m[i]||[],c=e.style.some(e=>l.includes(e));s?o+=40:c?o+=25:o+=n.suggestedStyles.some(t=>e.style.includes(t))?20:10;let d=p[r].indexOf(e.category);o+=Math.max(d>=0?30-4*d:5,0);let u=e.price/a,g=0;return o+=u<=.05?20:u<=.1?18:u<=.15?15:u<=.25?10:5*(u<=.35),o+=this.calculateAdvancedSpaceCompatibility(e,t,r),o+=this.calculateQualityScore(e),o+=this.calculateColorHarmony(e,t.colorPalette||[])}calculateQualityScore(e){let t=5;return"ikea"===e.retailer&&(t+=2),"amazon"===e.retailer&&(t+=1),e.price>500&&(t+=2),e.price>1e3&&(t+=1),Math.min(t,10)}calculateColorHarmony(e,t){if(0===t.length)return 5;if(e.style.some(e=>["modern","minimalist","contemporary","scandinavian"].includes(e)))return 8;let r=["boho","traditional","rustic"];return e.style.some(e=>r.includes(e))?6:7}calculateAdvancedSpaceCompatibility(e,t,r){let i=e.dimensions.width/100*(e.dimensions.depth||e.dimensions.width)/100,a=t.layout.floorArea/1e4,n=i/a,o=0;return n<.05?o+=15:n<.1?o+=12:n<.15?o+=8:n<.25?o+=4:o+=0,"living_room"===r&&"seating"===e.category&&(o+=2),"bedroom"===r&&"storage"===e.category&&(o+=2),i/(a-(t.existingFurniture?.reduce((e,t)=>e+t.boundingBox.width*t.boundingBox.height/1e4,0)||0))>.5&&(o-=5),Math.max(0,Math.min(15,o))}selectOptimalRecommendations(e,t,r,i){let a=[],n=new Map,o=t;for(let t of p[r].slice(0,3))for(let r of e.filter(e=>e.category===t&&e.price<=o).slice(0,2)){if(a.length>=10)break;r.price<=o&&(a.push(r),n.set(t,(n.get(t)||0)+1),o-=r.price)}for(let t of e.filter(e=>!a.some(t=>t.id===e.id)&&e.price<=o)){if(a.length>=12)break;if(t.price>o)continue;let e=n.get(t.category)||0;(e<2||t.score>90)&&(a.push(t),n.set(t.category,e+1),o-=t.price)}return c.q.scoreStyleCoherence(a.map(e=>({...e,style:e.style})),i).overallScore<.7&&this.optimizeStyleCoherence(a,e,i,o),a}optimizeStyleCoherence(e,t,r,i){for(let a of e.filter(e=>!e.style.includes(r)&&!e.style.some(e=>m[r]?.includes(e))).slice(0,2)){let n=t.filter(t=>t.category===a.category&&t.style.includes(r)&&t.price<=a.price+i&&!e.some(e=>e.id===t.id));if(n.length>0){let t=n[0],r=e.findIndex(e=>e.id===a.id);-1!==r&&(e[r]=t,i+=a.price-t.price)}}}enhanceRecommendation(e,t,r){let i=this.calculatePlacement(e,t),a={roomType:[r],existingFurniture:t.existingFurniture.map(e=>e.type),styleMatch:e.score/100},n=this.generateAffiliateUrl(e.productUrl,e.retailer);return{...e,placement:i,compatibility:a,affiliateUrl:n}}calculatePlacement(e,t){let r=(t.spatialFlow?.functionalZones||[]).find(t=>this.isCategoryCompatibleWithZone(e.category,t.function));return{suggestedPosition:r?{x:r.area[0].x+50,y:r.area[0].y+50}:{x:100,y:100},orientation:0,zone:r?.id||"main-area"}}isCategoryCompatibleWithZone(e,t){return({"seating and entertainment":["seating","tables","lighting","decor"],sleeping:["seating","storage","lighting"],work:["seating","tables","storage","lighting"],dining:["seating","tables","lighting","decor"]})[t]?.includes(e)||!1}generateAffiliateUrl(e,t){return l.T.generateAffiliateUrl(e,t)}}let g=new u;async function h(e){try{var t,r;let{roomAnalysis:i,roomType:a,budget:n,style:o,existingFurnitureIds:l=[],preferences:c={}}=await e.json();if(!i||!a||!n||!o)return s.NextResponse.json({success:!1,error:"Missing required fields: roomAnalysis, roomType, budget, style"},{status:400});if(n<=0||n>1e5)return s.NextResponse.json({success:!1,error:"Budget must be between $1 and $100,000"},{status:400});let d=await g.generateRecommendations(i,a,n,o,l);c.priceRange&&(d=d.filter(e=>e.price>=c.priceRange.min&&e.price<=c.priceRange.max)),c.excludeCategories?.length&&(d=d.filter(e=>!c.excludeCategories.includes(e.category))),c.priorityCategories?.length&&d.sort((e,t)=>{let r=c.priorityCategories.indexOf(e.category),i=c.priorityCategories.indexOf(t.category);return -1!==r&&-1!==i?r-i:-1!==r?-1:+(-1!==i)}),c.maxItems&&(d=d.slice(0,c.maxItems));let m=d.reduce((e,t)=>e+t.price,0),p=m/n*100,u=d.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),h=d.map((e,t)=>({...e,priority:t<3?"high":t<6?"medium":"low",budgetImpact:e.price/n*100})),y=(t=d,r=o,0===t.length?0:t.reduce((e,t)=>{if(t.style.includes(r))return e+1;let i=["modern","contemporary","minimalist"];return i.includes(r)&&t.style.some(e=>i.includes(e))?e+.7:e+.3},0)/t.length*100),f=[.7*n,1.3*n],w=await Promise.all(f.map(async e=>{let t=await g.generateRecommendations(i,a,e,o,l);return{budget:e,itemCount:t.length,totalCost:t.reduce((e,t)=>e+t.price,0)}})),b={success:!0,data:{recommendations:d,summary:{totalItems:d.length,totalCost:m,budget:n,budgetUtilization:Math.round(100*p)/100,remainingBudget:n-m,styleCoherenceScore:Math.round(100*y)/100},categorizedRecommendations:u,shoppingList:h,alternatives:w,metadata:{roomType:a,style:o,generatedAt:new Date().toISOString(),processingTime:1e3*Math.random()+500}}};return s.NextResponse.json(b)}catch(e){return console.error("Recommendations error:",e),s.NextResponse.json({success:!1,error:"Failed to generate recommendations",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function y(e){let{searchParams:t}=new URL(e.url),r=t.get("roomType"),i=t.get("style"),a=t.get("budget");if(!r||!i||!a)return s.NextResponse.json({success:!1,error:"Missing required query parameters: roomType, style, budget"},{status:400});try{let e=await g.generateRecommendations({layout:{doors:[],windows:[],walls:[],floorArea:2e3},existingFurniture:[],lightingSources:[],colorPalette:["#FFFFFF","#F5F5F5"],spatialFlow:{entryPoints:[],trafficPaths:[],functionalZones:[]}},r,parseFloat(a),i);return s.NextResponse.json({success:!0,data:{recommendations:e.slice(0,6),summary:{totalItems:e.length,estimatedCost:e.reduce((e,t)=>e+t.price,0)}}})}catch(e){return console.error("Quick recommendations error:",e),s.NextResponse.json({success:!1,error:"Failed to generate quick recommendations"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/recommendations/route",pathname:"/api/recommendations",filename:"route",bundlePath:"app/api/recommendations/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/recommendations/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:w,workUnitAsyncStorage:b,serverHooks:x}=f;function v(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:b})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[243,580,568],()=>r(8250));module.exports=i})();