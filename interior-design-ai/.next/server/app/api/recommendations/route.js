/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/recommendations/route";
exports.ids = ["app/api/recommendations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/recommendations/route.ts */ \"(rsc)/./src/app/api/recommendations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/recommendations/route\",\n        pathname: \"/api/recommendations\",\n        filename: \"route\",\n        bundlePath: \"app/api/recommendations/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/recommendations/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tahafarooqui_Documents_augment_projects_My_big_idea_interior_design_ai_src_app_api_recommendations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/recommendations/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/recommendations/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_recommendation_engine__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/recommendation-engine */ \"(rsc)/./src/lib/recommendation-engine.ts\");\n// API endpoint for generating furniture and decor recommendations\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { roomAnalysis, roomType, budget, style, existingFurnitureIds = [], preferences = {} } = body;\n        // Validate required fields\n        if (!roomAnalysis || !roomType || !budget || !style) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields: roomAnalysis, roomType, budget, style'\n            }, {\n                status: 400\n            });\n        }\n        // Validate budget\n        if (budget <= 0 || budget > 100000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Budget must be between $1 and $100,000'\n            }, {\n                status: 400\n            });\n        }\n        // Generate recommendations\n        const recommendations = await _lib_recommendation_engine__WEBPACK_IMPORTED_MODULE_1__.recommendationEngine.generateRecommendations(roomAnalysis, roomType, budget, style, existingFurnitureIds);\n        // Apply user preferences\n        let filteredRecommendations = recommendations;\n        // Filter by price range if specified\n        if (preferences.priceRange) {\n            filteredRecommendations = filteredRecommendations.filter((item)=>item.price >= preferences.priceRange.min && item.price <= preferences.priceRange.max);\n        }\n        // Exclude categories if specified\n        if (preferences.excludeCategories?.length) {\n            filteredRecommendations = filteredRecommendations.filter((item)=>!preferences.excludeCategories.includes(item.category));\n        }\n        // Prioritize categories if specified\n        if (preferences.priorityCategories?.length) {\n            filteredRecommendations.sort((a, b)=>{\n                const aPriority = preferences.priorityCategories.indexOf(a.category);\n                const bPriority = preferences.priorityCategories.indexOf(b.category);\n                if (aPriority !== -1 && bPriority !== -1) {\n                    return aPriority - bPriority;\n                }\n                if (aPriority !== -1) return -1;\n                if (bPriority !== -1) return 1;\n                return 0;\n            });\n        }\n        // Limit number of items if specified\n        if (preferences.maxItems) {\n            filteredRecommendations = filteredRecommendations.slice(0, preferences.maxItems);\n        }\n        // Calculate total cost and budget utilization\n        const totalCost = filteredRecommendations.reduce((sum, item)=>sum + item.price, 0);\n        const budgetUtilization = totalCost / budget * 100;\n        // Group recommendations by category\n        const recommendationsByCategory = filteredRecommendations.reduce((acc, item)=>{\n            if (!acc[item.category]) {\n                acc[item.category] = [];\n            }\n            acc[item.category].push(item);\n            return acc;\n        }, {});\n        // Generate shopping list with priorities\n        const shoppingList = filteredRecommendations.map((item, index)=>({\n                ...item,\n                priority: index < 3 ? 'high' : index < 6 ? 'medium' : 'low',\n                budgetImpact: item.price / budget * 100\n            }));\n        // Calculate style coherence score\n        const styleCoherenceScore = calculateStyleCoherence(filteredRecommendations, style);\n        // Generate alternative suggestions for different budgets\n        const alternativeBudgets = [\n            budget * 0.7,\n            budget * 1.3\n        ];\n        const alternatives = await Promise.all(alternativeBudgets.map(async (altBudget)=>{\n            const altRecommendations = await _lib_recommendation_engine__WEBPACK_IMPORTED_MODULE_1__.recommendationEngine.generateRecommendations(roomAnalysis, roomType, altBudget, style, existingFurnitureIds);\n            return {\n                budget: altBudget,\n                itemCount: altRecommendations.length,\n                totalCost: altRecommendations.reduce((sum, item)=>sum + item.price, 0)\n            };\n        }));\n        const response = {\n            success: true,\n            data: {\n                recommendations: filteredRecommendations,\n                summary: {\n                    totalItems: filteredRecommendations.length,\n                    totalCost,\n                    budget,\n                    budgetUtilization: Math.round(budgetUtilization * 100) / 100,\n                    remainingBudget: budget - totalCost,\n                    styleCoherenceScore: Math.round(styleCoherenceScore * 100) / 100\n                },\n                categorizedRecommendations: recommendationsByCategory,\n                shoppingList,\n                alternatives,\n                metadata: {\n                    roomType,\n                    style,\n                    generatedAt: new Date().toISOString(),\n                    processingTime: Math.random() * 1000 + 500 // Mock processing time\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Recommendations error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to generate recommendations',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Calculate how well the recommendations match the selected style\nfunction calculateStyleCoherence(recommendations, targetStyle) {\n    if (recommendations.length === 0) return 0;\n    const styleMatches = recommendations.reduce((sum, item)=>{\n        if (item.style.includes(targetStyle)) {\n            return sum + 1;\n        }\n        // Partial credit for compatible styles\n        const compatibleStyles = [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ];\n        if (compatibleStyles.includes(targetStyle) && item.style.some((s)=>compatibleStyles.includes(s))) {\n            return sum + 0.7;\n        }\n        return sum + 0.3; // Base score for any style\n    }, 0);\n    return styleMatches / recommendations.length * 100;\n}\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const roomType = searchParams.get('roomType');\n    const style = searchParams.get('style');\n    const budget = searchParams.get('budget');\n    if (!roomType || !style || !budget) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Missing required query parameters: roomType, style, budget'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Return quick recommendations without room analysis\n        const mockAnalysis = {\n            layout: {\n                doors: [],\n                windows: [],\n                walls: [],\n                floorArea: 2000\n            },\n            existingFurniture: [],\n            lightingSources: [],\n            colorPalette: [\n                '#FFFFFF',\n                '#F5F5F5'\n            ],\n            spatialFlow: {\n                entryPoints: [],\n                trafficPaths: [],\n                functionalZones: []\n            }\n        };\n        const recommendations = await _lib_recommendation_engine__WEBPACK_IMPORTED_MODULE_1__.recommendationEngine.generateRecommendations(mockAnalysis, roomType, parseFloat(budget), style);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                recommendations: recommendations.slice(0, 6),\n                summary: {\n                    totalItems: recommendations.length,\n                    estimatedCost: recommendations.reduce((sum, item)=>sum + item.price, 0)\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Quick recommendations error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to generate quick recommendations'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/recommendations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/product-database.ts":
/*!*************************************!*\
  !*** ./src/lib/product-database.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENHANCED_FURNITURE_DATABASE: () => (/* binding */ ENHANCED_FURNITURE_DATABASE),\n/* harmony export */   ProductDatabase: () => (/* binding */ ProductDatabase)\n/* harmony export */ });\n// Enhanced product database with real retailer integration\n// Extended furniture database with more realistic products\nconst ENHANCED_FURNITURE_DATABASE = [\n    // Living Room - Seating\n    {\n        id: 'ikea-kivik-sofa',\n        name: 'KIVIK 3-seat sofa',\n        category: 'seating',\n        description: 'A generous seating series with a soft, deep seat and comfortable support for your back.',\n        price: 599,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/kivik-3-seat-sofa-hillared-beige__0818588_pe774490_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 228,\n            height: 83,\n            depth: 95\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'scandinavian'\n        ]\n    },\n    {\n        id: 'amazon-rivet-sofa',\n        name: 'Rivet Revolve Modern Upholstered Sofa',\n        category: 'seating',\n        description: 'Mid-century modern style with clean lines and button tufting.',\n        price: 899,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07BQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 213,\n            height: 86,\n            depth: 89\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'ikea-poang-chair',\n        name: 'POÄNG Armchair',\n        category: 'seating',\n        description: 'Layer-glued bent birch frame gives comfortable resilience.',\n        price: 149,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/poang-armchair-birch-veneer-knisa-light-beige__0818334_pe774558_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/poang-armchair-birch-veneer-knisa-light-beige-s59932209/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 68,\n            height: 100,\n            depth: 82\n        },\n        style: [\n            'scandinavian',\n            'modern',\n            'minimalist'\n        ]\n    },\n    // Living Room - Tables\n    {\n        id: 'ikea-lack-coffee-table',\n        name: 'LACK Coffee table',\n        category: 'tables',\n        description: 'Simple design that is easy to match with other furniture.',\n        price: 49,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/lack-coffee-table-white__0818318_pe774542_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/lack-coffee-table-white-s49932210/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 118,\n            height: 45,\n            depth: 78\n        },\n        style: [\n            'minimalist',\n            'modern',\n            'scandinavian'\n        ]\n    },\n    {\n        id: 'amazon-glass-coffee-table',\n        name: 'Walker Edison Glass Coffee Table',\n        category: 'tables',\n        description: 'Tempered glass top with sleek metal frame.',\n        price: 299,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07CQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 122,\n            height: 46,\n            depth: 61\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'industrial'\n        ]\n    },\n    // Bedroom Furniture\n    {\n        id: 'ikea-malm-bed',\n        name: 'MALM Bed frame',\n        category: 'seating',\n        description: 'A clean design that fits right in, in the bedroom or guest room.',\n        price: 179,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/malm-bed-frame-oak-veneer__0818302_pe774526_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932211/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 209,\n            height: 38,\n            depth: 156\n        },\n        style: [\n            'scandinavian',\n            'minimalist',\n            'modern'\n        ]\n    },\n    {\n        id: 'amazon-platform-bed',\n        name: 'Zinus Modern Studio Platform Bed',\n        category: 'seating',\n        description: 'Strong steel frame with wood slat support.',\n        price: 299,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07DQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 203,\n            height: 35,\n            depth: 152\n        },\n        style: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    // Storage\n    {\n        id: 'ikea-hemnes-bookcase',\n        name: 'HEMNES Bookcase',\n        category: 'storage',\n        description: 'Solid wood has a natural feel. Adjustable shelves.',\n        price: 199,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/hemnes-bookcase-white-stain__0818286_pe774510_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932212/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 90,\n            height: 197,\n            depth: 37\n        },\n        style: [\n            'traditional',\n            'scandinavian',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-cube-organizer',\n        name: 'ClosetMaid Cubeicals Organizer',\n        category: 'storage',\n        description: 'Versatile storage solution with multiple cubes.',\n        price: 89,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/81QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07EQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 106,\n            height: 106,\n            depth: 30\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    // Lighting\n    {\n        id: 'ikea-foto-pendant',\n        name: 'FOTO Pendant lamp',\n        category: 'lighting',\n        description: 'Gives a directed light; good for lighting dining tables or bar tops.',\n        price: 39,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/foto-pendant-lamp-aluminum__0818270_pe774494_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/foto-pendant-lamp-aluminum-s49932213/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 25,\n            height: 23,\n            depth: 25\n        },\n        style: [\n            'industrial',\n            'modern',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'amazon-arc-floor-lamp',\n        name: 'Brightech Sparq LED Arc Floor Lamp',\n        category: 'lighting',\n        description: 'Modern arc design with energy-efficient LED.',\n        price: 199,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/61QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07FQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 40,\n            height: 180,\n            depth: 40\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'industrial'\n        ]\n    },\n    // Decor\n    {\n        id: 'ikea-fejka-plant',\n        name: 'FEJKA Artificial potted plant',\n        category: 'plants',\n        description: 'Lifelike artificial plant that stays fresh year after year.',\n        price: 24,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/fejka-artificial-potted-plant-eucalyptus__0818254_pe774478_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/fejka-artificial-potted-plant-eucalyptus-s49932214/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 12,\n            height: 65,\n            depth: 12\n        },\n        style: [\n            'scandinavian',\n            'boho',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-wall-art-set',\n        name: 'Americanflat Gallery Wall Set',\n        category: 'decor',\n        description: 'Set of 6 framed prints for creating a gallery wall.',\n        price: 129,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/91QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07GQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 20,\n            height: 25,\n            depth: 2\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    // Textiles\n    {\n        id: 'ikea-gurli-cushion',\n        name: 'GURLI Cushion cover',\n        category: 'textiles',\n        description: 'The zipper makes the cover easy to remove.',\n        price: 7,\n        currency: 'USD',\n        imageUrl: 'https://www.ikea.com/us/en/images/products/gurli-cushion-cover-beige__0818238_pe774462_s5.jpg',\n        productUrl: 'https://www.ikea.com/us/en/p/gurli-cushion-cover-beige-s49932215/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 50,\n            height: 50,\n            depth: 2\n        },\n        style: [\n            'scandinavian',\n            'minimalist',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'amazon-throw-blanket',\n        name: 'Bedsure Knitted Throw Blanket',\n        category: 'textiles',\n        description: 'Soft knitted throw perfect for sofas and beds.',\n        price: 35,\n        currency: 'USD',\n        imageUrl: 'https://m.media-amazon.com/images/I/71QJ5Q5Q5QL._AC_SL1500_.jpg',\n        productUrl: 'https://www.amazon.com/dp/B07HQZXQZQ',\n        retailer: 'amazon',\n        dimensions: {\n            width: 127,\n            height: 152,\n            depth: 1\n        },\n        style: [\n            'boho',\n            'scandinavian',\n            'contemporary'\n        ]\n    }\n];\n// Product search and filtering utilities\nclass ProductDatabase {\n    static searchProducts(category, style, priceRange, retailer) {\n        let results = [\n            ...ENHANCED_FURNITURE_DATABASE\n        ];\n        if (category) {\n            results = results.filter((product)=>product.category === category);\n        }\n        if (style) {\n            results = results.filter((product)=>product.style.includes(style));\n        }\n        if (priceRange) {\n            results = results.filter((product)=>product.price >= priceRange.min && product.price <= priceRange.max);\n        }\n        if (retailer) {\n            results = results.filter((product)=>product.retailer === retailer);\n        }\n        return results;\n    }\n    static getProductById(id) {\n        return ENHANCED_FURNITURE_DATABASE.find((product)=>product.id === id) || null;\n    }\n    static getProductsByCategory(category) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.category === category);\n    }\n    static getProductsByStyle(style) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.style.includes(style));\n    }\n    static getProductsByPriceRange(min, max) {\n        return ENHANCED_FURNITURE_DATABASE.filter((product)=>product.price >= min && product.price <= max);\n    }\n    static getRandomProducts(count = 10) {\n        const shuffled = [\n            ...ENHANCED_FURNITURE_DATABASE\n        ].sort(()=>0.5 - Math.random());\n        return shuffled.slice(0, count);\n    }\n    static getSimilarProducts(productId, count = 5) {\n        const product = this.getProductById(productId);\n        if (!product) return [];\n        // Find similar products based on category and style\n        const similar = ENHANCED_FURNITURE_DATABASE.filter((p)=>p.id !== productId).filter((p)=>p.category === product.category || p.style.some((style)=>product.style.includes(style))).sort((a, b)=>{\n            // Score based on style overlap and price similarity\n            const aStyleOverlap = a.style.filter((style)=>product.style.includes(style)).length;\n            const bStyleOverlap = b.style.filter((style)=>product.style.includes(style)).length;\n            const aPriceDiff = Math.abs(a.price - product.price);\n            const bPriceDiff = Math.abs(b.price - product.price);\n            const aScore = aStyleOverlap * 10 - aPriceDiff / 100;\n            const bScore = bStyleOverlap * 10 - bPriceDiff / 100;\n            return bScore - aScore;\n        });\n        return similar.slice(0, count);\n    }\n    // Generate affiliate URLs\n    static generateAffiliateUrl(productUrl, retailer) {\n        const affiliateIds = {\n            amazon: process.env.AMAZON_AFFILIATE_ID || 'interiorai-20',\n            ikea: process.env.IKEA_PARTNER_ID || 'interior-ai-partner'\n        };\n        if (retailer === 'amazon' && productUrl.includes('amazon.com')) {\n            const url = new URL(productUrl);\n            url.searchParams.set('tag', affiliateIds.amazon);\n            return url.toString();\n        }\n        if (retailer === 'ikea' && productUrl.includes('ikea.com')) {\n            // IKEA affiliate links work differently\n            return `${productUrl}?partner=${affiliateIds.ikea}`;\n        }\n        return productUrl;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/product-database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/recommendation-engine.ts":
/*!******************************************!*\
  !*** ./src/lib/recommendation-engine.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationEngine: () => (/* binding */ RecommendationEngine),\n/* harmony export */   recommendationEngine: () => (/* binding */ recommendationEngine)\n/* harmony export */ });\n/* harmony import */ var _product_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./product-database */ \"(rsc)/./src/lib/product-database.ts\");\n/* harmony import */ var _style_matcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./style-matcher */ \"(rsc)/./src/lib/style-matcher.ts\");\n// Enhanced recommendation engine for furniture and decor suggestions\n\n\n// Enhanced furniture database with more realistic data\nconst FURNITURE_DATABASE = [\n    // Living Room Furniture\n    {\n        id: 'sofa-modern-1',\n        name: 'Modern 3-Seat Sofa',\n        category: 'seating',\n        description: 'Comfortable modern sofa with clean lines and neutral upholstery',\n        price: 899,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.ikea.com/us/en/p/kivik-sofa-hillared-beige-s59318708/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 228,\n            height: 83,\n            depth: 95\n        },\n        style: [\n            'modern',\n            'minimalist',\n            'contemporary'\n        ]\n    },\n    {\n        id: 'coffee-table-1',\n        name: 'Glass Coffee Table',\n        category: 'tables',\n        description: 'Sleek glass coffee table with metal legs',\n        price: 299,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.amazon.com/dp/B08XYZ123',\n        retailer: 'amazon',\n        dimensions: {\n            width: 120,\n            height: 45,\n            depth: 60\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'accent-chair-1',\n        name: 'Velvet Accent Chair',\n        category: 'seating',\n        description: 'Luxurious velvet accent chair in emerald green',\n        price: 449,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.amazon.com/dp/B08ABC456',\n        retailer: 'amazon',\n        dimensions: {\n            width: 76,\n            height: 86,\n            depth: 81\n        },\n        style: [\n            'boho',\n            'contemporary',\n            'traditional'\n        ]\n    },\n    // Bedroom Furniture\n    {\n        id: 'bed-frame-1',\n        name: 'Platform Bed Frame',\n        category: 'seating',\n        description: 'Minimalist platform bed frame in natural wood',\n        price: 599,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.ikea.com/us/en/p/malm-bed-frame-oak-veneer-s49932209/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 209,\n            height: 38,\n            depth: 156\n        },\n        style: [\n            'scandinavian',\n            'minimalist',\n            'modern'\n        ]\n    },\n    {\n        id: 'nightstand-1',\n        name: 'Two-Drawer Nightstand',\n        category: 'storage',\n        description: 'Compact nightstand with two drawers',\n        price: 149,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.ikea.com/us/en/p/hemnes-nightstand-white-stain-s49932210/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 46,\n            height: 61,\n            depth: 35\n        },\n        style: [\n            'scandinavian',\n            'traditional',\n            'contemporary'\n        ]\n    },\n    // Lighting\n    {\n        id: 'floor-lamp-1',\n        name: 'Arc Floor Lamp',\n        category: 'lighting',\n        description: 'Modern arc floor lamp with adjustable height',\n        price: 199,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.amazon.com/dp/B08DEF789',\n        retailer: 'amazon',\n        dimensions: {\n            width: 40,\n            height: 180,\n            depth: 40\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'industrial'\n        ]\n    },\n    // Decor\n    {\n        id: 'wall-art-1',\n        name: 'Abstract Canvas Print Set',\n        category: 'decor',\n        description: 'Set of 3 abstract canvas prints in neutral tones',\n        price: 89,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.amazon.com/dp/B08GHI012',\n        retailer: 'amazon',\n        dimensions: {\n            width: 40,\n            height: 60,\n            depth: 2\n        },\n        style: [\n            'modern',\n            'contemporary',\n            'minimalist'\n        ]\n    },\n    {\n        id: 'plant-1',\n        name: 'Fiddle Leaf Fig',\n        category: 'plants',\n        description: 'Large fiddle leaf fig plant in decorative pot',\n        price: 79,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.amazon.com/dp/B08JKL345',\n        retailer: 'amazon',\n        dimensions: {\n            width: 30,\n            height: 150,\n            depth: 30\n        },\n        style: [\n            'boho',\n            'scandinavian',\n            'contemporary'\n        ]\n    },\n    // Storage\n    {\n        id: 'bookshelf-1',\n        name: '5-Tier Bookshelf',\n        category: 'storage',\n        description: 'Tall bookshelf with 5 adjustable shelves',\n        price: 179,\n        currency: 'USD',\n        imageUrl: '/api/placeholder/400/300',\n        productUrl: 'https://www.ikea.com/us/en/p/hemnes-bookcase-white-stain-s49932211/',\n        retailer: 'ikea',\n        dimensions: {\n            width: 90,\n            height: 197,\n            depth: 37\n        },\n        style: [\n            'scandinavian',\n            'traditional',\n            'contemporary'\n        ]\n    }\n];\n// Style compatibility matrix\nconst STYLE_COMPATIBILITY = {\n    modern: [\n        'contemporary',\n        'minimalist',\n        'industrial'\n    ],\n    minimalist: [\n        'modern',\n        'scandinavian',\n        'contemporary'\n    ],\n    boho: [\n        'rustic',\n        'traditional'\n    ],\n    industrial: [\n        'modern',\n        'contemporary'\n    ],\n    scandinavian: [\n        'minimalist',\n        'contemporary',\n        'modern'\n    ],\n    traditional: [\n        'boho',\n        'rustic'\n    ],\n    contemporary: [\n        'modern',\n        'minimalist',\n        'scandinavian',\n        'industrial'\n    ],\n    rustic: [\n        'boho',\n        'traditional'\n    ]\n};\n// Room-specific furniture priorities\nconst ROOM_FURNITURE_PRIORITIES = {\n    living_room: [\n        'seating',\n        'tables',\n        'lighting',\n        'storage',\n        'decor',\n        'plants'\n    ],\n    bedroom: [\n        'seating',\n        'storage',\n        'lighting',\n        'decor',\n        'textiles'\n    ],\n    kitchen: [\n        'storage',\n        'lighting',\n        'decor'\n    ],\n    bathroom: [\n        'storage',\n        'lighting',\n        'textiles'\n    ],\n    dining_room: [\n        'seating',\n        'tables',\n        'lighting',\n        'storage',\n        'decor'\n    ],\n    office: [\n        'seating',\n        'tables',\n        'storage',\n        'lighting',\n        'decor'\n    ],\n    nursery: [\n        'seating',\n        'storage',\n        'lighting',\n        'textiles',\n        'decor'\n    ],\n    guest_room: [\n        'seating',\n        'storage',\n        'lighting',\n        'decor',\n        'textiles'\n    ]\n};\nclass RecommendationEngine {\n    async generateRecommendations(roomAnalysis, roomType, budget, preferredStyle, existingFurnitureIds = []) {\n        // Analyze room style compatibility\n        const styleAnalysis = _style_matcher__WEBPACK_IMPORTED_MODULE_1__.StyleMatcher.analyzeRoomStyle(roomAnalysis);\n        const effectiveStyle = styleAnalysis.confidence[preferredStyle] > 0.5 ? preferredStyle : styleAnalysis.suggestedStyles[0];\n        // Get products from enhanced database\n        const availableProducts = _product_database__WEBPACK_IMPORTED_MODULE_0__.ProductDatabase.searchProducts(undefined, effectiveStyle, {\n            min: 0,\n            max: budget * 0.4\n        } // Max 40% of budget per item\n        );\n        // Remove existing furniture to avoid duplicates\n        const filteredProducts = availableProducts.filter((item)=>!existingFurnitureIds.includes(item.id));\n        // Score and rank furniture based on multiple factors\n        const scoredFurniture = filteredProducts.map((item)=>({\n                ...item,\n                score: this.calculateAdvancedFurnitureScore(item, roomAnalysis, roomType, effectiveStyle, budget, styleAnalysis)\n            }));\n        // Sort by score and apply budget constraints\n        const sortedFurniture = scoredFurniture.sort((a, b)=>b.score - a.score);\n        // Select diverse recommendations using advanced algorithm\n        const recommendations = this.selectOptimalRecommendations(sortedFurniture, budget, roomType, effectiveStyle);\n        // Add placement and compatibility information\n        return recommendations.map((item)=>this.enhanceRecommendation(item, roomAnalysis, roomType));\n    }\n    filterByRoomAndStyle(roomType, style) {\n        const compatibleStyles = [\n            style,\n            ...STYLE_COMPATIBILITY[style]\n        ];\n        const priorityCategories = ROOM_FURNITURE_PRIORITIES[roomType];\n        return FURNITURE_DATABASE.filter((item)=>{\n            const hasCompatibleStyle = item.style.some((itemStyle)=>compatibleStyles.includes(itemStyle));\n            const isRelevantCategory = priorityCategories.includes(item.category);\n            return hasCompatibleStyle && isRelevantCategory;\n        });\n    }\n    calculateAdvancedFurnitureScore(item, roomAnalysis, roomType, preferredStyle, budget, styleAnalysis) {\n        let score = 0;\n        // Enhanced style match score (0-40 points)\n        const directStyleMatch = item.style.includes(preferredStyle);\n        const compatibleStyles = STYLE_COMPATIBILITY[preferredStyle] || [];\n        const hasCompatibleStyle = item.style.some((s)=>compatibleStyles.includes(s));\n        if (directStyleMatch) {\n            score += 40;\n        } else if (hasCompatibleStyle) {\n            score += 25;\n        } else {\n            // Check against room's suggested styles\n            const roomStyleMatch = styleAnalysis.suggestedStyles.some((s)=>item.style.includes(s));\n            score += roomStyleMatch ? 20 : 10;\n        }\n        // Category priority score with room-specific weighting (0-30 points)\n        const categoryPriority = ROOM_FURNITURE_PRIORITIES[roomType].indexOf(item.category);\n        const categoryScore = categoryPriority >= 0 ? 30 - categoryPriority * 4 : 5;\n        score += Math.max(categoryScore, 0);\n        // Advanced budget efficiency score (0-20 points)\n        const budgetRatio = item.price / budget;\n        let budgetScore = 0;\n        if (budgetRatio <= 0.05) budgetScore = 20; // Very affordable\n        else if (budgetRatio <= 0.1) budgetScore = 18; // Affordable\n        else if (budgetRatio <= 0.15) budgetScore = 15; // Reasonable\n        else if (budgetRatio <= 0.25) budgetScore = 10; // Moderate\n        else if (budgetRatio <= 0.35) budgetScore = 5; // Expensive\n        else budgetScore = 0; // Too expensive\n        score += budgetScore;\n        // Enhanced space compatibility score (0-15 points)\n        const spaceScore = this.calculateAdvancedSpaceCompatibility(item, roomAnalysis, roomType);\n        score += spaceScore;\n        // Quality and retailer bonus (0-10 points)\n        const qualityScore = this.calculateQualityScore(item);\n        score += qualityScore;\n        // Color harmony score (0-10 points)\n        const colorScore = this.calculateColorHarmony(item, roomAnalysis.colorPalette || []);\n        score += colorScore;\n        return score;\n    }\n    calculateQualityScore(item) {\n        let score = 5; // Base score\n        // Retailer reputation bonus\n        if (item.retailer === 'ikea') score += 2; // Known for good value\n        if (item.retailer === 'amazon') score += 1; // Wide selection\n        // Price-quality correlation (higher price often means better quality)\n        if (item.price > 500) score += 2;\n        if (item.price > 1000) score += 1;\n        return Math.min(score, 10);\n    }\n    calculateColorHarmony(item, roomColors) {\n        if (roomColors.length === 0) return 5; // Neutral score if no color data\n        // Simple heuristic: items with neutral colors score higher\n        const neutralColors = [\n            '#FFFFFF',\n            '#000000',\n            '#808080',\n            '#F5F5F5',\n            '#E8E8E8'\n        ];\n        const hasNeutralStyle = item.style.some((style)=>[\n                'modern',\n                'minimalist',\n                'contemporary',\n                'scandinavian'\n            ].includes(style));\n        if (hasNeutralStyle) return 8; // Neutral styles typically work well\n        // For colorful styles, assume they work if they match the style\n        const colorfulStyles = [\n            'boho',\n            'traditional',\n            'rustic'\n        ];\n        const isColorfulStyle = item.style.some((style)=>colorfulStyles.includes(style));\n        return isColorfulStyle ? 6 : 7;\n    }\n    calculateAdvancedSpaceCompatibility(item, roomAnalysis, roomType) {\n        const itemArea = item.dimensions.width / 100 * (item.dimensions.depth || item.dimensions.width) / 100;\n        const roomArea = roomAnalysis.layout.floorArea / 10000; // Convert to square meters\n        const areaRatio = itemArea / roomArea;\n        let spaceScore = 0;\n        // Base space compatibility\n        if (areaRatio < 0.05) spaceScore += 15; // Perfect fit\n        else if (areaRatio < 0.1) spaceScore += 12; // Good fit\n        else if (areaRatio < 0.15) spaceScore += 8; // Acceptable\n        else if (areaRatio < 0.25) spaceScore += 4; // Tight fit\n        else spaceScore += 0; // Too large\n        // Room type specific adjustments\n        if (roomType === 'living_room' && item.category === 'seating') {\n            spaceScore += 2; // Living rooms need seating\n        }\n        if (roomType === 'bedroom' && item.category === 'storage') {\n            spaceScore += 2; // Bedrooms need storage\n        }\n        // Existing furniture consideration\n        const existingFurnitureArea = roomAnalysis.existingFurniture?.reduce((total, furniture)=>{\n            const furnitureArea = furniture.boundingBox.width * furniture.boundingBox.height / 10000;\n            return total + furnitureArea;\n        }, 0) || 0;\n        const remainingSpace = roomArea - existingFurnitureArea;\n        if (itemArea / remainingSpace > 0.5) {\n            spaceScore -= 5; // Penalty for overcrowding\n        }\n        return Math.max(0, Math.min(15, spaceScore));\n    }\n    selectOptimalRecommendations(scoredFurniture, budget, roomType, style) {\n        const selected = [];\n        const categoryCount = new Map();\n        let remainingBudget = budget;\n        // Priority categories for this room type\n        const priorities = ROOM_FURNITURE_PRIORITIES[roomType];\n        // Phase 1: Select high-priority, high-score items\n        for (const category of priorities.slice(0, 3)){\n            const categoryItems = scoredFurniture.filter((item)=>item.category === category && item.price <= remainingBudget).slice(0, 2); // Max 2 items per priority category\n            for (const item of categoryItems){\n                if (selected.length >= 10) break;\n                if (item.price <= remainingBudget) {\n                    selected.push(item);\n                    categoryCount.set(category, (categoryCount.get(category) || 0) + 1);\n                    remainingBudget -= item.price;\n                }\n            }\n        }\n        // Phase 2: Fill remaining budget with diverse, high-scoring items\n        const remainingItems = scoredFurniture.filter((item)=>!selected.some((s)=>s.id === item.id) && item.price <= remainingBudget);\n        for (const item of remainingItems){\n            if (selected.length >= 12) break; // Max 12 total recommendations\n            if (item.price > remainingBudget) continue;\n            const currentCategoryCount = categoryCount.get(item.category) || 0;\n            // Prefer categories we haven't used much\n            if (currentCategoryCount < 2 || item.score > 90) {\n                selected.push(item);\n                categoryCount.set(item.category, currentCategoryCount + 1);\n                remainingBudget -= item.price;\n            }\n        }\n        // Phase 3: Style coherence check and optimization\n        const styleCoherence = _style_matcher__WEBPACK_IMPORTED_MODULE_1__.StyleMatcher.scoreStyleCoherence(selected.map((item)=>({\n                ...item,\n                style: item.style\n            })), style);\n        // If style coherence is low, try to replace some items\n        if (styleCoherence.overallScore < 0.7) {\n            this.optimizeStyleCoherence(selected, scoredFurniture, style, remainingBudget);\n        }\n        return selected;\n    }\n    optimizeStyleCoherence(selected, allItems, targetStyle, remainingBudget) {\n        // Find items with low style scores and try to replace them\n        const lowStyleItems = selected.filter((item)=>!item.style.includes(targetStyle) && !item.style.some((s)=>STYLE_COMPATIBILITY[targetStyle]?.includes(s)));\n        for (const lowItem of lowStyleItems.slice(0, 2)){\n            const alternatives = allItems.filter((alt)=>alt.category === lowItem.category && alt.style.includes(targetStyle) && alt.price <= lowItem.price + remainingBudget && !selected.some((s)=>s.id === alt.id));\n            if (alternatives.length > 0) {\n                const bestAlternative = alternatives[0]; // Already sorted by score\n                const index = selected.findIndex((item)=>item.id === lowItem.id);\n                if (index !== -1) {\n                    selected[index] = bestAlternative;\n                    remainingBudget += lowItem.price - bestAlternative.price;\n                }\n            }\n        }\n    }\n    enhanceRecommendation(item, roomAnalysis, roomType) {\n        // Calculate suggested placement\n        const placement = this.calculatePlacement(item, roomAnalysis);\n        // Calculate compatibility scores\n        const compatibility = {\n            roomType: [\n                roomType\n            ],\n            existingFurniture: roomAnalysis.existingFurniture.map((f)=>f.type),\n            styleMatch: item.score / 100\n        };\n        // Generate affiliate URL (mock)\n        const affiliateUrl = this.generateAffiliateUrl(item.productUrl, item.retailer);\n        return {\n            ...item,\n            placement,\n            compatibility,\n            affiliateUrl\n        };\n    }\n    calculatePlacement(item, roomAnalysis) {\n        // Mock placement calculation - in production, use spatial analysis\n        const zones = roomAnalysis.spatialFlow?.functionalZones || [];\n        const suggestedZone = zones.find((zone)=>this.isCategoryCompatibleWithZone(item.category, zone.function));\n        return {\n            suggestedPosition: suggestedZone ? {\n                x: suggestedZone.area[0].x + 50,\n                y: suggestedZone.area[0].y + 50\n            } : {\n                x: 100,\n                y: 100\n            },\n            orientation: 0,\n            zone: suggestedZone?.id || 'main-area'\n        };\n    }\n    isCategoryCompatibleWithZone(category, zoneFunction) {\n        const compatibility = {\n            'seating and entertainment': [\n                'seating',\n                'tables',\n                'lighting',\n                'decor'\n            ],\n            'sleeping': [\n                'seating',\n                'storage',\n                'lighting'\n            ],\n            'work': [\n                'seating',\n                'tables',\n                'storage',\n                'lighting'\n            ],\n            'dining': [\n                'seating',\n                'tables',\n                'lighting',\n                'decor'\n            ]\n        };\n        return compatibility[zoneFunction]?.includes(category) || false;\n    }\n    generateAffiliateUrl(productUrl, retailer) {\n        return _product_database__WEBPACK_IMPORTED_MODULE_0__.ProductDatabase.generateAffiliateUrl(productUrl, retailer);\n    }\n}\nconst recommendationEngine = new RecommendationEngine();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3JlY29tbWVuZGF0aW9uLWVuZ2luZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEscUVBQXFFO0FBVWhCO0FBQ047QUFHL0MsdURBQXVEO0FBQ3ZELE1BQU1FLHFCQUFxRjtJQUN6Rix3QkFBd0I7SUFDeEI7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsWUFBWTtZQUFFQyxPQUFPO1lBQUtDLFFBQVE7WUFBSUMsT0FBTztRQUFHO1FBQ2hEQyxPQUFPO1lBQUM7WUFBVTtZQUFjO1NBQWU7SUFDakQ7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxZQUFZO1lBQUVDLE9BQU87WUFBS0MsUUFBUTtZQUFJQyxPQUFPO1FBQUc7UUFDaERDLE9BQU87WUFBQztZQUFVO1lBQWdCO1NBQWE7SUFDakQ7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxZQUFZO1lBQUVDLE9BQU87WUFBSUMsUUFBUTtZQUFJQyxPQUFPO1FBQUc7UUFDL0NDLE9BQU87WUFBQztZQUFRO1lBQWdCO1NBQWM7SUFDaEQ7SUFFQSxvQkFBb0I7SUFDcEI7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsWUFBWTtZQUFFQyxPQUFPO1lBQUtDLFFBQVE7WUFBSUMsT0FBTztRQUFJO1FBQ2pEQyxPQUFPO1lBQUM7WUFBZ0I7WUFBYztTQUFTO0lBQ2pEO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsWUFBWTtZQUFFQyxPQUFPO1lBQUlDLFFBQVE7WUFBSUMsT0FBTztRQUFHO1FBQy9DQyxPQUFPO1lBQUM7WUFBZ0I7WUFBZTtTQUFlO0lBQ3hEO0lBRUEsV0FBVztJQUNYO1FBQ0ViLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLFlBQVk7WUFBRUMsT0FBTztZQUFJQyxRQUFRO1lBQUtDLE9BQU87UUFBRztRQUNoREMsT0FBTztZQUFDO1lBQVU7WUFBZ0I7U0FBYTtJQUNqRDtJQUVBLFFBQVE7SUFDUjtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxZQUFZO1lBQUVDLE9BQU87WUFBSUMsUUFBUTtZQUFJQyxPQUFPO1FBQUU7UUFDOUNDLE9BQU87WUFBQztZQUFVO1lBQWdCO1NBQWE7SUFDakQ7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxZQUFZO1lBQUVDLE9BQU87WUFBSUMsUUFBUTtZQUFLQyxPQUFPO1FBQUc7UUFDaERDLE9BQU87WUFBQztZQUFRO1lBQWdCO1NBQWU7SUFDakQ7SUFFQSxVQUFVO0lBQ1Y7UUFDRWIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsWUFBWTtZQUFFQyxPQUFPO1lBQUlDLFFBQVE7WUFBS0MsT0FBTztRQUFHO1FBQ2hEQyxPQUFPO1lBQUM7WUFBZ0I7WUFBZTtTQUFlO0lBQ3hEO0NBQ0Q7QUFFRCw2QkFBNkI7QUFDN0IsTUFBTUMsc0JBQTBEO0lBQzlEQyxRQUFRO1FBQUM7UUFBZ0I7UUFBYztLQUFhO0lBQ3BEQyxZQUFZO1FBQUM7UUFBVTtRQUFnQjtLQUFlO0lBQ3REQyxNQUFNO1FBQUM7UUFBVTtLQUFjO0lBQy9CQyxZQUFZO1FBQUM7UUFBVTtLQUFlO0lBQ3RDQyxjQUFjO1FBQUM7UUFBYztRQUFnQjtLQUFTO0lBQ3REQyxhQUFhO1FBQUM7UUFBUTtLQUFTO0lBQy9CQyxjQUFjO1FBQUM7UUFBVTtRQUFjO1FBQWdCO0tBQWE7SUFDcEVDLFFBQVE7UUFBQztRQUFRO0tBQWM7QUFDakM7QUFFQSxxQ0FBcUM7QUFDckMsTUFBTUMsNEJBQW1FO0lBQ3ZFQyxhQUFhO1FBQUM7UUFBVztRQUFVO1FBQVk7UUFBVztRQUFTO0tBQVM7SUFDNUVDLFNBQVM7UUFBQztRQUFXO1FBQVc7UUFBWTtRQUFTO0tBQVc7SUFDaEVDLFNBQVM7UUFBQztRQUFXO1FBQVk7S0FBUTtJQUN6Q0MsVUFBVTtRQUFDO1FBQVc7UUFBWTtLQUFXO0lBQzdDQyxhQUFhO1FBQUM7UUFBVztRQUFVO1FBQVk7UUFBVztLQUFRO0lBQ2xFQyxRQUFRO1FBQUM7UUFBVztRQUFVO1FBQVc7UUFBWTtLQUFRO0lBQzdEQyxTQUFTO1FBQUM7UUFBVztRQUFXO1FBQVk7UUFBWTtLQUFRO0lBQ2hFQyxZQUFZO1FBQUM7UUFBVztRQUFXO1FBQVk7UUFBUztLQUFXO0FBQ3JFO0FBRU8sTUFBTUM7SUFFWCxNQUFNQyx3QkFDSkMsWUFBMEIsRUFDMUJDLFFBQWtCLEVBQ2xCQyxNQUFjLEVBQ2RDLGNBQTJCLEVBQzNCQyx1QkFBaUMsRUFBRSxFQUNDO1FBRXBDLG1DQUFtQztRQUNuQyxNQUFNQyxnQkFBZ0J6Qyx3REFBWUEsQ0FBQzBDLGdCQUFnQixDQUFDTjtRQUNwRCxNQUFNTyxpQkFBaUJGLGNBQWNHLFVBQVUsQ0FBQ0wsZUFBZSxHQUFHLE1BQzlEQSxpQkFDQUUsY0FBY0ksZUFBZSxDQUFDLEVBQUU7UUFFcEMsc0NBQXNDO1FBQ3RDLE1BQU1DLG9CQUFvQi9DLDhEQUFlQSxDQUFDZ0QsY0FBYyxDQUN0REMsV0FDQUwsZ0JBQ0E7WUFBRU0sS0FBSztZQUFHQyxLQUFLWixTQUFTO1FBQUksRUFBRSw2QkFBNkI7O1FBRzdELGdEQUFnRDtRQUNoRCxNQUFNYSxtQkFBbUJMLGtCQUFrQk0sTUFBTSxDQUMvQ0MsQ0FBQUEsT0FBUSxDQUFDYixxQkFBcUJjLFFBQVEsQ0FBQ0QsS0FBS25ELEVBQUU7UUFHaEQscURBQXFEO1FBQ3JELE1BQU1xRCxrQkFBa0JKLGlCQUFpQkssR0FBRyxDQUFDSCxDQUFBQSxPQUFTO2dCQUNwRCxHQUFHQSxJQUFJO2dCQUNQSSxPQUFPLElBQUksQ0FBQ0MsK0JBQStCLENBQ3pDTCxNQUFNakIsY0FBY0MsVUFBVU0sZ0JBQWdCTCxRQUFRRztZQUUxRDtRQUVBLDZDQUE2QztRQUM3QyxNQUFNa0Isa0JBQWtCSixnQkFDckJLLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxFQUFFTCxLQUFLLEdBQUdJLEVBQUVKLEtBQUs7UUFFbkMsMERBQTBEO1FBQzFELE1BQU1NLGtCQUFrQixJQUFJLENBQUNDLDRCQUE0QixDQUN2REwsaUJBQWlCckIsUUFBUUQsVUFBVU07UUFHckMsOENBQThDO1FBQzlDLE9BQU9vQixnQkFBZ0JQLEdBQUcsQ0FBQ0gsQ0FBQUEsT0FBUSxJQUFJLENBQUNZLHFCQUFxQixDQUFDWixNQUFNakIsY0FBY0M7SUFDcEY7SUFFUTZCLHFCQUFxQjdCLFFBQWtCLEVBQUV0QixLQUFrQixFQUFFO1FBQ25FLE1BQU1vRCxtQkFBbUI7WUFBQ3BEO2VBQVVDLG1CQUFtQixDQUFDRCxNQUFNO1NBQUM7UUFDL0QsTUFBTXFELHFCQUFxQjNDLHlCQUF5QixDQUFDWSxTQUFTO1FBRTlELE9BQU9wQyxtQkFBbUJtRCxNQUFNLENBQUNDLENBQUFBO1lBQy9CLE1BQU1nQixxQkFBcUJoQixLQUFLdEMsS0FBSyxDQUFDdUQsSUFBSSxDQUFDQyxDQUFBQSxZQUN6Q0osaUJBQWlCYixRQUFRLENBQUNpQjtZQUU1QixNQUFNQyxxQkFBcUJKLG1CQUFtQmQsUUFBUSxDQUFDRCxLQUFLakQsUUFBUTtZQUVwRSxPQUFPaUUsc0JBQXNCRztRQUMvQjtJQUNGO0lBRVFkLGdDQUNOTCxJQUFrRSxFQUNsRWpCLFlBQTBCLEVBQzFCQyxRQUFrQixFQUNsQkUsY0FBMkIsRUFDM0JELE1BQWMsRUFDZEcsYUFBa0IsRUFDVjtRQUNSLElBQUlnQixRQUFRO1FBRVosMkNBQTJDO1FBQzNDLE1BQU1nQixtQkFBbUJwQixLQUFLdEMsS0FBSyxDQUFDdUMsUUFBUSxDQUFDZjtRQUM3QyxNQUFNNEIsbUJBQW1CbkQsbUJBQW1CLENBQUN1QixlQUFlLElBQUksRUFBRTtRQUNsRSxNQUFNOEIscUJBQXFCaEIsS0FBS3RDLEtBQUssQ0FBQ3VELElBQUksQ0FBQ0ksQ0FBQUEsSUFBS1AsaUJBQWlCYixRQUFRLENBQUNvQjtRQUUxRSxJQUFJRCxrQkFBa0I7WUFDcEJoQixTQUFTO1FBQ1gsT0FBTyxJQUFJWSxvQkFBb0I7WUFDN0JaLFNBQVM7UUFDWCxPQUFPO1lBQ0wsd0NBQXdDO1lBQ3hDLE1BQU1rQixpQkFBaUJsQyxjQUFjSSxlQUFlLENBQUN5QixJQUFJLENBQUMsQ0FBQ0ksSUFDekRyQixLQUFLdEMsS0FBSyxDQUFDdUMsUUFBUSxDQUFDb0I7WUFFdEJqQixTQUFTa0IsaUJBQWlCLEtBQUs7UUFDakM7UUFFQSxxRUFBcUU7UUFDckUsTUFBTUMsbUJBQW1CbkQseUJBQXlCLENBQUNZLFNBQVMsQ0FBQ3dDLE9BQU8sQ0FBQ3hCLEtBQUtqRCxRQUFRO1FBQ2xGLE1BQU0wRSxnQkFBZ0JGLG9CQUFvQixJQUFLLEtBQUtBLG1CQUFtQixJQUFLO1FBQzVFbkIsU0FBU3NCLEtBQUs3QixHQUFHLENBQUM0QixlQUFlO1FBRWpDLGlEQUFpRDtRQUNqRCxNQUFNRSxjQUFjM0IsS0FBSy9DLEtBQUssR0FBR2dDO1FBQ2pDLElBQUkyQyxjQUFjO1FBQ2xCLElBQUlELGVBQWUsTUFBTUMsY0FBYyxJQUFTLGtCQUFrQjthQUM3RCxJQUFJRCxlQUFlLEtBQUtDLGNBQWMsSUFBSyxhQUFhO2FBQ3hELElBQUlELGVBQWUsTUFBTUMsY0FBYyxJQUFJLGFBQWE7YUFDeEQsSUFBSUQsZUFBZSxNQUFNQyxjQUFjLElBQUksV0FBVzthQUN0RCxJQUFJRCxlQUFlLE1BQU1DLGNBQWMsR0FBSSxZQUFZO2FBQ3ZEQSxjQUFjLEdBQThCLGdCQUFnQjtRQUNqRXhCLFNBQVN3QjtRQUVULG1EQUFtRDtRQUNuRCxNQUFNQyxhQUFhLElBQUksQ0FBQ0MsbUNBQW1DLENBQUM5QixNQUFNakIsY0FBY0M7UUFDaEZvQixTQUFTeUI7UUFFVCwyQ0FBMkM7UUFDM0MsTUFBTUUsZUFBZSxJQUFJLENBQUNDLHFCQUFxQixDQUFDaEM7UUFDaERJLFNBQVMyQjtRQUVULG9DQUFvQztRQUNwQyxNQUFNRSxhQUFhLElBQUksQ0FBQ0MscUJBQXFCLENBQUNsQyxNQUFNakIsYUFBYW9ELFlBQVksSUFBSSxFQUFFO1FBQ25GL0IsU0FBUzZCO1FBRVQsT0FBTzdCO0lBQ1Q7SUFFUTRCLHNCQUFzQmhDLElBQWtFLEVBQVU7UUFDeEcsSUFBSUksUUFBUSxHQUFHLGFBQWE7UUFFNUIsNEJBQTRCO1FBQzVCLElBQUlKLEtBQUszQyxRQUFRLEtBQUssUUFBUStDLFNBQVMsR0FBRyx1QkFBdUI7UUFDakUsSUFBSUosS0FBSzNDLFFBQVEsS0FBSyxVQUFVK0MsU0FBUyxHQUFHLGlCQUFpQjtRQUU3RCxzRUFBc0U7UUFDdEUsSUFBSUosS0FBSy9DLEtBQUssR0FBRyxLQUFLbUQsU0FBUztRQUMvQixJQUFJSixLQUFLL0MsS0FBSyxHQUFHLE1BQU1tRCxTQUFTO1FBRWhDLE9BQU9zQixLQUFLOUIsR0FBRyxDQUFDUSxPQUFPO0lBQ3pCO0lBRVE4QixzQkFDTmxDLElBQWtFLEVBQ2xFb0MsVUFBb0IsRUFDWjtRQUNSLElBQUlBLFdBQVdDLE1BQU0sS0FBSyxHQUFHLE9BQU8sR0FBRyxpQ0FBaUM7UUFFeEUsMkRBQTJEO1FBQzNELE1BQU1DLGdCQUFnQjtZQUFDO1lBQVc7WUFBVztZQUFXO1lBQVc7U0FBVTtRQUM3RSxNQUFNQyxrQkFBa0J2QyxLQUFLdEMsS0FBSyxDQUFDdUQsSUFBSSxDQUFDdkQsQ0FBQUEsUUFDdEM7Z0JBQUM7Z0JBQVU7Z0JBQWM7Z0JBQWdCO2FBQWUsQ0FBQ3VDLFFBQVEsQ0FBQ3ZDO1FBR3BFLElBQUk2RSxpQkFBaUIsT0FBTyxHQUFHLHFDQUFxQztRQUVwRSxnRUFBZ0U7UUFDaEUsTUFBTUMsaUJBQWlCO1lBQUM7WUFBUTtZQUFlO1NBQVM7UUFDeEQsTUFBTUMsa0JBQWtCekMsS0FBS3RDLEtBQUssQ0FBQ3VELElBQUksQ0FBQ3ZELENBQUFBLFFBQVM4RSxlQUFldkMsUUFBUSxDQUFDdkM7UUFFekUsT0FBTytFLGtCQUFrQixJQUFJO0lBQy9CO0lBRVFYLG9DQUNOOUIsSUFBa0UsRUFDbEVqQixZQUEwQixFQUMxQkMsUUFBa0IsRUFDVjtRQUNSLE1BQU0wRCxXQUFXLEtBQU1wRixVQUFVLENBQUNDLEtBQUssR0FBRyxNQUFReUMsQ0FBQUEsS0FBSzFDLFVBQVUsQ0FBQ0csS0FBSyxJQUFJdUMsS0FBSzFDLFVBQVUsQ0FBQ0MsS0FBSyxJQUFJO1FBQ3BHLE1BQU1vRixXQUFXNUQsYUFBYTZELE1BQU0sQ0FBQ0MsU0FBUyxHQUFHLE9BQU8sMkJBQTJCO1FBQ25GLE1BQU1DLFlBQVlKLFdBQVdDO1FBRTdCLElBQUlkLGFBQWE7UUFFakIsMkJBQTJCO1FBQzNCLElBQUlpQixZQUFZLE1BQU1qQixjQUFjLElBQVMsY0FBYzthQUN0RCxJQUFJaUIsWUFBWSxLQUFLakIsY0FBYyxJQUFLLFdBQVc7YUFDbkQsSUFBSWlCLFlBQVksTUFBTWpCLGNBQWMsR0FBSSxhQUFhO2FBQ3JELElBQUlpQixZQUFZLE1BQU1qQixjQUFjLEdBQUksWUFBWTthQUNwREEsY0FBYyxHQUEwQixZQUFZO1FBRXpELGlDQUFpQztRQUNqQyxJQUFJN0MsYUFBYSxpQkFBaUJnQixLQUFLakQsUUFBUSxLQUFLLFdBQVc7WUFDN0Q4RSxjQUFjLEdBQUcsNEJBQTRCO1FBQy9DO1FBQ0EsSUFBSTdDLGFBQWEsYUFBYWdCLEtBQUtqRCxRQUFRLEtBQUssV0FBVztZQUN6RDhFLGNBQWMsR0FBRyx3QkFBd0I7UUFDM0M7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTWtCLHdCQUF3QmhFLGFBQWFpRSxpQkFBaUIsRUFBRUMsT0FBTyxDQUFDQyxPQUFPQztZQUMzRSxNQUFNQyxnQkFBZ0IsVUFBV0MsV0FBVyxDQUFDOUYsS0FBSyxHQUFHNEYsVUFBVUUsV0FBVyxDQUFDN0YsTUFBTSxHQUFJO1lBQ3JGLE9BQU8wRixRQUFRRTtRQUNqQixHQUFHLE1BQU07UUFFVCxNQUFNRSxpQkFBaUJYLFdBQVdJO1FBQ2xDLElBQUlMLFdBQVdZLGlCQUFpQixLQUFLO1lBQ25DekIsY0FBYyxHQUFHLDJCQUEyQjtRQUM5QztRQUVBLE9BQU9ILEtBQUs3QixHQUFHLENBQUMsR0FBRzZCLEtBQUs5QixHQUFHLENBQUMsSUFBSWlDO0lBQ2xDO0lBRVFsQiw2QkFDTlQsZUFBc0IsRUFDdEJqQixNQUFjLEVBQ2RELFFBQWtCLEVBQ2xCdEIsS0FBa0IsRUFDWDtRQUNQLE1BQU02RixXQUFrQixFQUFFO1FBQzFCLE1BQU1DLGdCQUFnQixJQUFJQztRQUMxQixJQUFJQyxrQkFBa0J6RTtRQUV0Qix5Q0FBeUM7UUFDekMsTUFBTTBFLGFBQWF2Rix5QkFBeUIsQ0FBQ1ksU0FBUztRQUV0RCxrREFBa0Q7UUFDbEQsS0FBSyxNQUFNakMsWUFBWTRHLFdBQVdDLEtBQUssQ0FBQyxHQUFHLEdBQUk7WUFDN0MsTUFBTUMsZ0JBQWdCM0QsZ0JBQ25CSCxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtqRCxRQUFRLEtBQUtBLFlBQVlpRCxLQUFLL0MsS0FBSyxJQUFJeUcsaUJBQzNERSxLQUFLLENBQUMsR0FBRyxJQUFJLG9DQUFvQztZQUVwRCxLQUFLLE1BQU01RCxRQUFRNkQsY0FBZTtnQkFDaEMsSUFBSU4sU0FBU2xCLE1BQU0sSUFBSSxJQUFJO2dCQUMzQixJQUFJckMsS0FBSy9DLEtBQUssSUFBSXlHLGlCQUFpQjtvQkFDakNILFNBQVNPLElBQUksQ0FBQzlEO29CQUNkd0QsY0FBY08sR0FBRyxDQUFDaEgsVUFBVSxDQUFDeUcsY0FBY1EsR0FBRyxDQUFDakgsYUFBYSxLQUFLO29CQUNqRTJHLG1CQUFtQjFELEtBQUsvQyxLQUFLO2dCQUMvQjtZQUNGO1FBQ0Y7UUFFQSxrRUFBa0U7UUFDbEUsTUFBTWdILGlCQUFpQi9ELGdCQUFnQkgsTUFBTSxDQUFDQyxDQUFBQSxPQUM1QyxDQUFDdUQsU0FBU3RDLElBQUksQ0FBQ0ksQ0FBQUEsSUFBS0EsRUFBRXhFLEVBQUUsS0FBS21ELEtBQUtuRCxFQUFFLEtBQ3BDbUQsS0FBSy9DLEtBQUssSUFBSXlHO1FBR2hCLEtBQUssTUFBTTFELFFBQVFpRSxlQUFnQjtZQUNqQyxJQUFJVixTQUFTbEIsTUFBTSxJQUFJLElBQUksT0FBTywrQkFBK0I7WUFDakUsSUFBSXJDLEtBQUsvQyxLQUFLLEdBQUd5RyxpQkFBaUI7WUFFbEMsTUFBTVEsdUJBQXVCVixjQUFjUSxHQUFHLENBQUNoRSxLQUFLakQsUUFBUSxLQUFLO1lBRWpFLHlDQUF5QztZQUN6QyxJQUFJbUgsdUJBQXVCLEtBQUtsRSxLQUFLSSxLQUFLLEdBQUcsSUFBSTtnQkFDL0NtRCxTQUFTTyxJQUFJLENBQUM5RDtnQkFDZHdELGNBQWNPLEdBQUcsQ0FBQy9ELEtBQUtqRCxRQUFRLEVBQUVtSCx1QkFBdUI7Z0JBQ3hEUixtQkFBbUIxRCxLQUFLL0MsS0FBSztZQUMvQjtRQUNGO1FBRUEsa0RBQWtEO1FBQ2xELE1BQU1rSCxpQkFBaUJ4SCx3REFBWUEsQ0FBQ3lILG1CQUFtQixDQUNyRGIsU0FBU3BELEdBQUcsQ0FBQ0gsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFdEMsT0FBT3NDLEtBQUt0QyxLQUFLO1lBQUMsS0FDbkRBO1FBR0YsdURBQXVEO1FBQ3ZELElBQUl5RyxlQUFlRSxZQUFZLEdBQUcsS0FBSztZQUNyQyxJQUFJLENBQUNDLHNCQUFzQixDQUFDZixVQUFVckQsaUJBQWlCeEMsT0FBT2dHO1FBQ2hFO1FBRUEsT0FBT0g7SUFDVDtJQUVRZSx1QkFDTmYsUUFBZSxFQUNmZ0IsUUFBZSxFQUNmQyxXQUF3QixFQUN4QmQsZUFBdUIsRUFDakI7UUFDTiwyREFBMkQ7UUFDM0QsTUFBTWUsZ0JBQWdCbEIsU0FBU3hELE1BQU0sQ0FBQ0MsQ0FBQUEsT0FDcEMsQ0FBQ0EsS0FBS3RDLEtBQUssQ0FBQ3VDLFFBQVEsQ0FBQ3VFLGdCQUNyQixDQUFDeEUsS0FBS3RDLEtBQUssQ0FBQ3VELElBQUksQ0FBQyxDQUFDSSxJQUFtQjFELG1CQUFtQixDQUFDNkcsWUFBWSxFQUFFdkUsU0FBU29CO1FBR2xGLEtBQUssTUFBTXFELFdBQVdELGNBQWNiLEtBQUssQ0FBQyxHQUFHLEdBQUk7WUFDL0MsTUFBTWUsZUFBZUosU0FBU3hFLE1BQU0sQ0FBQzZFLENBQUFBLE1BQ25DQSxJQUFJN0gsUUFBUSxLQUFLMkgsUUFBUTNILFFBQVEsSUFDakM2SCxJQUFJbEgsS0FBSyxDQUFDdUMsUUFBUSxDQUFDdUUsZ0JBQ25CSSxJQUFJM0gsS0FBSyxJQUFJeUgsUUFBUXpILEtBQUssR0FBR3lHLG1CQUM3QixDQUFDSCxTQUFTdEMsSUFBSSxDQUFDSSxDQUFBQSxJQUFLQSxFQUFFeEUsRUFBRSxLQUFLK0gsSUFBSS9ILEVBQUU7WUFHckMsSUFBSThILGFBQWF0QyxNQUFNLEdBQUcsR0FBRztnQkFDM0IsTUFBTXdDLGtCQUFrQkYsWUFBWSxDQUFDLEVBQUUsRUFBRSwwQkFBMEI7Z0JBQ25FLE1BQU1HLFFBQVF2QixTQUFTd0IsU0FBUyxDQUFDL0UsQ0FBQUEsT0FBUUEsS0FBS25ELEVBQUUsS0FBSzZILFFBQVE3SCxFQUFFO2dCQUMvRCxJQUFJaUksVUFBVSxDQUFDLEdBQUc7b0JBQ2hCdkIsUUFBUSxDQUFDdUIsTUFBTSxHQUFHRDtvQkFDbEJuQixtQkFBbUJnQixRQUFRekgsS0FBSyxHQUFHNEgsZ0JBQWdCNUgsS0FBSztnQkFDMUQ7WUFDRjtRQUNGO0lBQ0Y7SUFFUTJELHNCQUNOWixJQUFTLEVBQ1RqQixZQUEwQixFQUMxQkMsUUFBa0IsRUFDTztRQUN6QixnQ0FBZ0M7UUFDaEMsTUFBTWdHLFlBQVksSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ2pGLE1BQU1qQjtRQUVoRCxpQ0FBaUM7UUFDakMsTUFBTW1HLGdCQUFnQjtZQUNwQmxHLFVBQVU7Z0JBQUNBO2FBQVM7WUFDcEJnRSxtQkFBbUJqRSxhQUFhaUUsaUJBQWlCLENBQUM3QyxHQUFHLENBQUNnRixDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO1lBQ2pFQyxZQUFZckYsS0FBS0ksS0FBSyxHQUFHO1FBQzNCO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU1rRixlQUFlLElBQUksQ0FBQ0Msb0JBQW9CLENBQUN2RixLQUFLNUMsVUFBVSxFQUFFNEMsS0FBSzNDLFFBQVE7UUFFN0UsT0FBTztZQUNMLEdBQUcyQyxJQUFJO1lBQ1BnRjtZQUNBRTtZQUNBSTtRQUNGO0lBQ0Y7SUFFUUwsbUJBQW1CakYsSUFBUyxFQUFFakIsWUFBMEIsRUFBTztRQUNyRSxtRUFBbUU7UUFDbkUsTUFBTXlHLFFBQVF6RyxhQUFhMEcsV0FBVyxFQUFFQyxtQkFBbUIsRUFBRTtRQUM3RCxNQUFNQyxnQkFBZ0JILE1BQU1JLElBQUksQ0FBQ0MsQ0FBQUEsT0FDL0IsSUFBSSxDQUFDQyw0QkFBNEIsQ0FBQzlGLEtBQUtqRCxRQUFRLEVBQUU4SSxLQUFLRSxRQUFRO1FBR2hFLE9BQU87WUFDTEMsbUJBQW1CTCxnQkFDakI7Z0JBQUVNLEdBQUdOLGNBQWNPLElBQUksQ0FBQyxFQUFFLENBQUNELENBQUMsR0FBRztnQkFBSUUsR0FBR1IsY0FBY08sSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsQ0FBQyxHQUFHO1lBQUcsSUFDbkU7Z0JBQUVGLEdBQUc7Z0JBQUtFLEdBQUc7WUFBSTtZQUNuQkMsYUFBYTtZQUNiUCxNQUFNRixlQUFlOUksTUFBTTtRQUM3QjtJQUNGO0lBRVFpSiw2QkFBNkIvSSxRQUEyQixFQUFFc0osWUFBb0IsRUFBVztRQUMvRixNQUFNbkIsZ0JBQXFEO1lBQ3pELDZCQUE2QjtnQkFBQztnQkFBVztnQkFBVTtnQkFBWTthQUFRO1lBQ3ZFLFlBQVk7Z0JBQUM7Z0JBQVc7Z0JBQVc7YUFBVztZQUM5QyxRQUFRO2dCQUFDO2dCQUFXO2dCQUFVO2dCQUFXO2FBQVc7WUFDcEQsVUFBVTtnQkFBQztnQkFBVztnQkFBVTtnQkFBWTthQUFRO1FBQ3REO1FBRUEsT0FBT0EsYUFBYSxDQUFDbUIsYUFBYSxFQUFFcEcsU0FBU2xELGFBQWE7SUFDNUQ7SUFFUXdJLHFCQUFxQm5JLFVBQWtCLEVBQUVDLFFBQTJCLEVBQVU7UUFDcEYsT0FBT1gsOERBQWVBLENBQUM2SSxvQkFBb0IsQ0FBQ25JLFlBQVlDO0lBQzFEO0FBQ0Y7QUFFTyxNQUFNaUosdUJBQXVCLElBQUl6SCx1QkFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9saWIvcmVjb21tZW5kYXRpb24tZW5naW5lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEVuaGFuY2VkIHJlY29tbWVuZGF0aW9uIGVuZ2luZSBmb3IgZnVybml0dXJlIGFuZCBkZWNvciBzdWdnZXN0aW9uc1xuXG5pbXBvcnQge1xuICBGdXJuaXR1cmVSZWNvbW1lbmRhdGlvbixcbiAgUm9vbUFuYWx5c2lzLFxuICBSb29tVHlwZSxcbiAgRGVzaWduU3R5bGUsXG4gIEZ1cm5pdHVyZUNhdGVnb3J5LFxuICBQb2ludFxufSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IFByb2R1Y3REYXRhYmFzZSB9IGZyb20gJy4vcHJvZHVjdC1kYXRhYmFzZSc7XG5pbXBvcnQgeyBTdHlsZU1hdGNoZXIgfSBmcm9tICcuL3N0eWxlLW1hdGNoZXInO1xuaW1wb3J0IHsgRGF0YWJhc2VTZXJ2aWNlIH0gZnJvbSAnLi9kYXRhYmFzZSc7XG5cbi8vIEVuaGFuY2VkIGZ1cm5pdHVyZSBkYXRhYmFzZSB3aXRoIG1vcmUgcmVhbGlzdGljIGRhdGFcbmNvbnN0IEZVUk5JVFVSRV9EQVRBQkFTRTogT21pdDxGdXJuaXR1cmVSZWNvbW1lbmRhdGlvbiwgJ3BsYWNlbWVudCcgfCAnY29tcGF0aWJpbGl0eSc+W10gPSBbXG4gIC8vIExpdmluZyBSb29tIEZ1cm5pdHVyZVxuICB7XG4gICAgaWQ6ICdzb2ZhLW1vZGVybi0xJyxcbiAgICBuYW1lOiAnTW9kZXJuIDMtU2VhdCBTb2ZhJyxcbiAgICBjYXRlZ29yeTogJ3NlYXRpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnQ29tZm9ydGFibGUgbW9kZXJuIHNvZmEgd2l0aCBjbGVhbiBsaW5lcyBhbmQgbmV1dHJhbCB1cGhvbHN0ZXJ5JyxcbiAgICBwcmljZTogODk5LFxuICAgIGN1cnJlbmN5OiAnVVNEJyxcbiAgICBpbWFnZVVybDogJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzMwMCcsXG4gICAgcHJvZHVjdFVybDogJ2h0dHBzOi8vd3d3LmlrZWEuY29tL3VzL2VuL3Ava2l2aWstc29mYS1oaWxsYXJlZC1iZWlnZS1zNTkzMTg3MDgvJyxcbiAgICByZXRhaWxlcjogJ2lrZWEnLFxuICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDIyOCwgaGVpZ2h0OiA4MywgZGVwdGg6IDk1IH0sXG4gICAgc3R5bGU6IFsnbW9kZXJuJywgJ21pbmltYWxpc3QnLCAnY29udGVtcG9yYXJ5J11cbiAgfSxcbiAge1xuICAgIGlkOiAnY29mZmVlLXRhYmxlLTEnLFxuICAgIG5hbWU6ICdHbGFzcyBDb2ZmZWUgVGFibGUnLFxuICAgIGNhdGVnb3J5OiAndGFibGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1NsZWVrIGdsYXNzIGNvZmZlZSB0YWJsZSB3aXRoIG1ldGFsIGxlZ3MnLFxuICAgIHByaWNlOiAyOTksXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIGltYWdlVXJsOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICBwcm9kdWN0VXJsOiAnaHR0cHM6Ly93d3cuYW1hem9uLmNvbS9kcC9CMDhYWVoxMjMnLFxuICAgIHJldGFpbGVyOiAnYW1hem9uJyxcbiAgICBkaW1lbnNpb25zOiB7IHdpZHRoOiAxMjAsIGhlaWdodDogNDUsIGRlcHRoOiA2MCB9LFxuICAgIHN0eWxlOiBbJ21vZGVybicsICdjb250ZW1wb3JhcnknLCAnbWluaW1hbGlzdCddXG4gIH0sXG4gIHtcbiAgICBpZDogJ2FjY2VudC1jaGFpci0xJyxcbiAgICBuYW1lOiAnVmVsdmV0IEFjY2VudCBDaGFpcicsXG4gICAgY2F0ZWdvcnk6ICdzZWF0aW5nJyxcbiAgICBkZXNjcmlwdGlvbjogJ0x1eHVyaW91cyB2ZWx2ZXQgYWNjZW50IGNoYWlyIGluIGVtZXJhbGQgZ3JlZW4nLFxuICAgIHByaWNlOiA0NDksXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIGltYWdlVXJsOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICBwcm9kdWN0VXJsOiAnaHR0cHM6Ly93d3cuYW1hem9uLmNvbS9kcC9CMDhBQkM0NTYnLFxuICAgIHJldGFpbGVyOiAnYW1hem9uJyxcbiAgICBkaW1lbnNpb25zOiB7IHdpZHRoOiA3NiwgaGVpZ2h0OiA4NiwgZGVwdGg6IDgxIH0sXG4gICAgc3R5bGU6IFsnYm9obycsICdjb250ZW1wb3JhcnknLCAndHJhZGl0aW9uYWwnXVxuICB9LFxuICBcbiAgLy8gQmVkcm9vbSBGdXJuaXR1cmVcbiAge1xuICAgIGlkOiAnYmVkLWZyYW1lLTEnLFxuICAgIG5hbWU6ICdQbGF0Zm9ybSBCZWQgRnJhbWUnLFxuICAgIGNhdGVnb3J5OiAnc2VhdGluZycsXG4gICAgZGVzY3JpcHRpb246ICdNaW5pbWFsaXN0IHBsYXRmb3JtIGJlZCBmcmFtZSBpbiBuYXR1cmFsIHdvb2QnLFxuICAgIHByaWNlOiA1OTksXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIGltYWdlVXJsOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICBwcm9kdWN0VXJsOiAnaHR0cHM6Ly93d3cuaWtlYS5jb20vdXMvZW4vcC9tYWxtLWJlZC1mcmFtZS1vYWstdmVuZWVyLXM0OTkzMjIwOS8nLFxuICAgIHJldGFpbGVyOiAnaWtlYScsXG4gICAgZGltZW5zaW9uczogeyB3aWR0aDogMjA5LCBoZWlnaHQ6IDM4LCBkZXB0aDogMTU2IH0sXG4gICAgc3R5bGU6IFsnc2NhbmRpbmF2aWFuJywgJ21pbmltYWxpc3QnLCAnbW9kZXJuJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnbmlnaHRzdGFuZC0xJyxcbiAgICBuYW1lOiAnVHdvLURyYXdlciBOaWdodHN0YW5kJyxcbiAgICBjYXRlZ29yeTogJ3N0b3JhZ2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnQ29tcGFjdCBuaWdodHN0YW5kIHdpdGggdHdvIGRyYXdlcnMnLFxuICAgIHByaWNlOiAxNDksXG4gICAgY3VycmVuY3k6ICdVU0QnLFxuICAgIGltYWdlVXJsOiAnL2FwaS9wbGFjZWhvbGRlci80MDAvMzAwJyxcbiAgICBwcm9kdWN0VXJsOiAnaHR0cHM6Ly93d3cuaWtlYS5jb20vdXMvZW4vcC9oZW1uZXMtbmlnaHRzdGFuZC13aGl0ZS1zdGFpbi1zNDk5MzIyMTAvJyxcbiAgICByZXRhaWxlcjogJ2lrZWEnLFxuICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDQ2LCBoZWlnaHQ6IDYxLCBkZXB0aDogMzUgfSxcbiAgICBzdHlsZTogWydzY2FuZGluYXZpYW4nLCAndHJhZGl0aW9uYWwnLCAnY29udGVtcG9yYXJ5J11cbiAgfSxcblxuICAvLyBMaWdodGluZ1xuICB7XG4gICAgaWQ6ICdmbG9vci1sYW1wLTEnLFxuICAgIG5hbWU6ICdBcmMgRmxvb3IgTGFtcCcsXG4gICAgY2F0ZWdvcnk6ICdsaWdodGluZycsXG4gICAgZGVzY3JpcHRpb246ICdNb2Rlcm4gYXJjIGZsb29yIGxhbXAgd2l0aCBhZGp1c3RhYmxlIGhlaWdodCcsXG4gICAgcHJpY2U6IDE5OSxcbiAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgaW1hZ2VVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHByb2R1Y3RVcmw6ICdodHRwczovL3d3dy5hbWF6b24uY29tL2RwL0IwOERFRjc4OScsXG4gICAgcmV0YWlsZXI6ICdhbWF6b24nLFxuICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDQwLCBoZWlnaHQ6IDE4MCwgZGVwdGg6IDQwIH0sXG4gICAgc3R5bGU6IFsnbW9kZXJuJywgJ2NvbnRlbXBvcmFyeScsICdpbmR1c3RyaWFsJ11cbiAgfSxcblxuICAvLyBEZWNvclxuICB7XG4gICAgaWQ6ICd3YWxsLWFydC0xJyxcbiAgICBuYW1lOiAnQWJzdHJhY3QgQ2FudmFzIFByaW50IFNldCcsXG4gICAgY2F0ZWdvcnk6ICdkZWNvcicsXG4gICAgZGVzY3JpcHRpb246ICdTZXQgb2YgMyBhYnN0cmFjdCBjYW52YXMgcHJpbnRzIGluIG5ldXRyYWwgdG9uZXMnLFxuICAgIHByaWNlOiA4OSxcbiAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgaW1hZ2VVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHByb2R1Y3RVcmw6ICdodHRwczovL3d3dy5hbWF6b24uY29tL2RwL0IwOEdISTAxMicsXG4gICAgcmV0YWlsZXI6ICdhbWF6b24nLFxuICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDQwLCBoZWlnaHQ6IDYwLCBkZXB0aDogMiB9LFxuICAgIHN0eWxlOiBbJ21vZGVybicsICdjb250ZW1wb3JhcnknLCAnbWluaW1hbGlzdCddXG4gIH0sXG4gIHtcbiAgICBpZDogJ3BsYW50LTEnLFxuICAgIG5hbWU6ICdGaWRkbGUgTGVhZiBGaWcnLFxuICAgIGNhdGVnb3J5OiAncGxhbnRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0xhcmdlIGZpZGRsZSBsZWFmIGZpZyBwbGFudCBpbiBkZWNvcmF0aXZlIHBvdCcsXG4gICAgcHJpY2U6IDc5LFxuICAgIGN1cnJlbmN5OiAnVVNEJyxcbiAgICBpbWFnZVVybDogJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzMwMCcsXG4gICAgcHJvZHVjdFVybDogJ2h0dHBzOi8vd3d3LmFtYXpvbi5jb20vZHAvQjA4SktMMzQ1JyxcbiAgICByZXRhaWxlcjogJ2FtYXpvbicsXG4gICAgZGltZW5zaW9uczogeyB3aWR0aDogMzAsIGhlaWdodDogMTUwLCBkZXB0aDogMzAgfSxcbiAgICBzdHlsZTogWydib2hvJywgJ3NjYW5kaW5hdmlhbicsICdjb250ZW1wb3JhcnknXVxuICB9LFxuXG4gIC8vIFN0b3JhZ2VcbiAge1xuICAgIGlkOiAnYm9va3NoZWxmLTEnLFxuICAgIG5hbWU6ICc1LVRpZXIgQm9va3NoZWxmJyxcbiAgICBjYXRlZ29yeTogJ3N0b3JhZ2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnVGFsbCBib29rc2hlbGYgd2l0aCA1IGFkanVzdGFibGUgc2hlbHZlcycsXG4gICAgcHJpY2U6IDE3OSxcbiAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgaW1hZ2VVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgIHByb2R1Y3RVcmw6ICdodHRwczovL3d3dy5pa2VhLmNvbS91cy9lbi9wL2hlbW5lcy1ib29rY2FzZS13aGl0ZS1zdGFpbi1zNDk5MzIyMTEvJyxcbiAgICByZXRhaWxlcjogJ2lrZWEnLFxuICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDkwLCBoZWlnaHQ6IDE5NywgZGVwdGg6IDM3IH0sXG4gICAgc3R5bGU6IFsnc2NhbmRpbmF2aWFuJywgJ3RyYWRpdGlvbmFsJywgJ2NvbnRlbXBvcmFyeSddXG4gIH1cbl07XG5cbi8vIFN0eWxlIGNvbXBhdGliaWxpdHkgbWF0cml4XG5jb25zdCBTVFlMRV9DT01QQVRJQklMSVRZOiBSZWNvcmQ8RGVzaWduU3R5bGUsIERlc2lnblN0eWxlW10+ID0ge1xuICBtb2Rlcm46IFsnY29udGVtcG9yYXJ5JywgJ21pbmltYWxpc3QnLCAnaW5kdXN0cmlhbCddLFxuICBtaW5pbWFsaXN0OiBbJ21vZGVybicsICdzY2FuZGluYXZpYW4nLCAnY29udGVtcG9yYXJ5J10sXG4gIGJvaG86IFsncnVzdGljJywgJ3RyYWRpdGlvbmFsJ10sXG4gIGluZHVzdHJpYWw6IFsnbW9kZXJuJywgJ2NvbnRlbXBvcmFyeSddLFxuICBzY2FuZGluYXZpYW46IFsnbWluaW1hbGlzdCcsICdjb250ZW1wb3JhcnknLCAnbW9kZXJuJ10sXG4gIHRyYWRpdGlvbmFsOiBbJ2JvaG8nLCAncnVzdGljJ10sXG4gIGNvbnRlbXBvcmFyeTogWydtb2Rlcm4nLCAnbWluaW1hbGlzdCcsICdzY2FuZGluYXZpYW4nLCAnaW5kdXN0cmlhbCddLFxuICBydXN0aWM6IFsnYm9obycsICd0cmFkaXRpb25hbCddXG59O1xuXG4vLyBSb29tLXNwZWNpZmljIGZ1cm5pdHVyZSBwcmlvcml0aWVzXG5jb25zdCBST09NX0ZVUk5JVFVSRV9QUklPUklUSUVTOiBSZWNvcmQ8Um9vbVR5cGUsIEZ1cm5pdHVyZUNhdGVnb3J5W10+ID0ge1xuICBsaXZpbmdfcm9vbTogWydzZWF0aW5nJywgJ3RhYmxlcycsICdsaWdodGluZycsICdzdG9yYWdlJywgJ2RlY29yJywgJ3BsYW50cyddLFxuICBiZWRyb29tOiBbJ3NlYXRpbmcnLCAnc3RvcmFnZScsICdsaWdodGluZycsICdkZWNvcicsICd0ZXh0aWxlcyddLFxuICBraXRjaGVuOiBbJ3N0b3JhZ2UnLCAnbGlnaHRpbmcnLCAnZGVjb3InXSxcbiAgYmF0aHJvb206IFsnc3RvcmFnZScsICdsaWdodGluZycsICd0ZXh0aWxlcyddLFxuICBkaW5pbmdfcm9vbTogWydzZWF0aW5nJywgJ3RhYmxlcycsICdsaWdodGluZycsICdzdG9yYWdlJywgJ2RlY29yJ10sXG4gIG9mZmljZTogWydzZWF0aW5nJywgJ3RhYmxlcycsICdzdG9yYWdlJywgJ2xpZ2h0aW5nJywgJ2RlY29yJ10sXG4gIG51cnNlcnk6IFsnc2VhdGluZycsICdzdG9yYWdlJywgJ2xpZ2h0aW5nJywgJ3RleHRpbGVzJywgJ2RlY29yJ10sXG4gIGd1ZXN0X3Jvb206IFsnc2VhdGluZycsICdzdG9yYWdlJywgJ2xpZ2h0aW5nJywgJ2RlY29yJywgJ3RleHRpbGVzJ11cbn07XG5cbmV4cG9ydCBjbGFzcyBSZWNvbW1lbmRhdGlvbkVuZ2luZSB7XG5cbiAgYXN5bmMgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMoXG4gICAgcm9vbUFuYWx5c2lzOiBSb29tQW5hbHlzaXMsXG4gICAgcm9vbVR5cGU6IFJvb21UeXBlLFxuICAgIGJ1ZGdldDogbnVtYmVyLFxuICAgIHByZWZlcnJlZFN0eWxlOiBEZXNpZ25TdHlsZSxcbiAgICBleGlzdGluZ0Z1cm5pdHVyZUlkczogc3RyaW5nW10gPSBbXVxuICApOiBQcm9taXNlPEZ1cm5pdHVyZVJlY29tbWVuZGF0aW9uW10+IHtcblxuICAgIC8vIEFuYWx5emUgcm9vbSBzdHlsZSBjb21wYXRpYmlsaXR5XG4gICAgY29uc3Qgc3R5bGVBbmFseXNpcyA9IFN0eWxlTWF0Y2hlci5hbmFseXplUm9vbVN0eWxlKHJvb21BbmFseXNpcyk7XG4gICAgY29uc3QgZWZmZWN0aXZlU3R5bGUgPSBzdHlsZUFuYWx5c2lzLmNvbmZpZGVuY2VbcHJlZmVycmVkU3R5bGVdID4gMC41XG4gICAgICA/IHByZWZlcnJlZFN0eWxlXG4gICAgICA6IHN0eWxlQW5hbHlzaXMuc3VnZ2VzdGVkU3R5bGVzWzBdO1xuXG4gICAgLy8gR2V0IHByb2R1Y3RzIGZyb20gZW5oYW5jZWQgZGF0YWJhc2VcbiAgICBjb25zdCBhdmFpbGFibGVQcm9kdWN0cyA9IFByb2R1Y3REYXRhYmFzZS5zZWFyY2hQcm9kdWN0cyhcbiAgICAgIHVuZGVmaW5lZCwgLy8gR2V0IGFsbCBjYXRlZ29yaWVzIGluaXRpYWxseVxuICAgICAgZWZmZWN0aXZlU3R5bGUsXG4gICAgICB7IG1pbjogMCwgbWF4OiBidWRnZXQgKiAwLjQgfSAvLyBNYXggNDAlIG9mIGJ1ZGdldCBwZXIgaXRlbVxuICAgICk7XG5cbiAgICAvLyBSZW1vdmUgZXhpc3RpbmcgZnVybml0dXJlIHRvIGF2b2lkIGR1cGxpY2F0ZXNcbiAgICBjb25zdCBmaWx0ZXJlZFByb2R1Y3RzID0gYXZhaWxhYmxlUHJvZHVjdHMuZmlsdGVyKFxuICAgICAgaXRlbSA9PiAhZXhpc3RpbmdGdXJuaXR1cmVJZHMuaW5jbHVkZXMoaXRlbS5pZClcbiAgICApO1xuXG4gICAgLy8gU2NvcmUgYW5kIHJhbmsgZnVybml0dXJlIGJhc2VkIG9uIG11bHRpcGxlIGZhY3RvcnNcbiAgICBjb25zdCBzY29yZWRGdXJuaXR1cmUgPSBmaWx0ZXJlZFByb2R1Y3RzLm1hcChpdGVtID0+ICh7XG4gICAgICAuLi5pdGVtLFxuICAgICAgc2NvcmU6IHRoaXMuY2FsY3VsYXRlQWR2YW5jZWRGdXJuaXR1cmVTY29yZShcbiAgICAgICAgaXRlbSwgcm9vbUFuYWx5c2lzLCByb29tVHlwZSwgZWZmZWN0aXZlU3R5bGUsIGJ1ZGdldCwgc3R5bGVBbmFseXNpc1xuICAgICAgKVxuICAgIH0pKTtcblxuICAgIC8vIFNvcnQgYnkgc2NvcmUgYW5kIGFwcGx5IGJ1ZGdldCBjb25zdHJhaW50c1xuICAgIGNvbnN0IHNvcnRlZEZ1cm5pdHVyZSA9IHNjb3JlZEZ1cm5pdHVyZVxuICAgICAgLnNvcnQoKGEsIGIpID0+IGIuc2NvcmUgLSBhLnNjb3JlKTtcblxuICAgIC8vIFNlbGVjdCBkaXZlcnNlIHJlY29tbWVuZGF0aW9ucyB1c2luZyBhZHZhbmNlZCBhbGdvcml0aG1cbiAgICBjb25zdCByZWNvbW1lbmRhdGlvbnMgPSB0aGlzLnNlbGVjdE9wdGltYWxSZWNvbW1lbmRhdGlvbnMoXG4gICAgICBzb3J0ZWRGdXJuaXR1cmUsIGJ1ZGdldCwgcm9vbVR5cGUsIGVmZmVjdGl2ZVN0eWxlXG4gICAgKTtcblxuICAgIC8vIEFkZCBwbGFjZW1lbnQgYW5kIGNvbXBhdGliaWxpdHkgaW5mb3JtYXRpb25cbiAgICByZXR1cm4gcmVjb21tZW5kYXRpb25zLm1hcChpdGVtID0+IHRoaXMuZW5oYW5jZVJlY29tbWVuZGF0aW9uKGl0ZW0sIHJvb21BbmFseXNpcywgcm9vbVR5cGUpKTtcbiAgfVxuXG4gIHByaXZhdGUgZmlsdGVyQnlSb29tQW5kU3R5bGUocm9vbVR5cGU6IFJvb21UeXBlLCBzdHlsZTogRGVzaWduU3R5bGUpIHtcbiAgICBjb25zdCBjb21wYXRpYmxlU3R5bGVzID0gW3N0eWxlLCAuLi5TVFlMRV9DT01QQVRJQklMSVRZW3N0eWxlXV07XG4gICAgY29uc3QgcHJpb3JpdHlDYXRlZ29yaWVzID0gUk9PTV9GVVJOSVRVUkVfUFJJT1JJVElFU1tyb29tVHlwZV07XG4gICAgXG4gICAgcmV0dXJuIEZVUk5JVFVSRV9EQVRBQkFTRS5maWx0ZXIoaXRlbSA9PiB7XG4gICAgICBjb25zdCBoYXNDb21wYXRpYmxlU3R5bGUgPSBpdGVtLnN0eWxlLnNvbWUoaXRlbVN0eWxlID0+IFxuICAgICAgICBjb21wYXRpYmxlU3R5bGVzLmluY2x1ZGVzKGl0ZW1TdHlsZSlcbiAgICAgICk7XG4gICAgICBjb25zdCBpc1JlbGV2YW50Q2F0ZWdvcnkgPSBwcmlvcml0eUNhdGVnb3JpZXMuaW5jbHVkZXMoaXRlbS5jYXRlZ29yeSk7XG4gICAgICBcbiAgICAgIHJldHVybiBoYXNDb21wYXRpYmxlU3R5bGUgJiYgaXNSZWxldmFudENhdGVnb3J5O1xuICAgIH0pO1xuICB9XG5cbiAgcHJpdmF0ZSBjYWxjdWxhdGVBZHZhbmNlZEZ1cm5pdHVyZVNjb3JlKFxuICAgIGl0ZW06IE9taXQ8RnVybml0dXJlUmVjb21tZW5kYXRpb24sICdwbGFjZW1lbnQnIHwgJ2NvbXBhdGliaWxpdHknPixcbiAgICByb29tQW5hbHlzaXM6IFJvb21BbmFseXNpcyxcbiAgICByb29tVHlwZTogUm9vbVR5cGUsXG4gICAgcHJlZmVycmVkU3R5bGU6IERlc2lnblN0eWxlLFxuICAgIGJ1ZGdldDogbnVtYmVyLFxuICAgIHN0eWxlQW5hbHlzaXM6IGFueVxuICApOiBudW1iZXIge1xuICAgIGxldCBzY29yZSA9IDA7XG5cbiAgICAvLyBFbmhhbmNlZCBzdHlsZSBtYXRjaCBzY29yZSAoMC00MCBwb2ludHMpXG4gICAgY29uc3QgZGlyZWN0U3R5bGVNYXRjaCA9IGl0ZW0uc3R5bGUuaW5jbHVkZXMocHJlZmVycmVkU3R5bGUpO1xuICAgIGNvbnN0IGNvbXBhdGlibGVTdHlsZXMgPSBTVFlMRV9DT01QQVRJQklMSVRZW3ByZWZlcnJlZFN0eWxlXSB8fCBbXTtcbiAgICBjb25zdCBoYXNDb21wYXRpYmxlU3R5bGUgPSBpdGVtLnN0eWxlLnNvbWUocyA9PiBjb21wYXRpYmxlU3R5bGVzLmluY2x1ZGVzKHMpKTtcblxuICAgIGlmIChkaXJlY3RTdHlsZU1hdGNoKSB7XG4gICAgICBzY29yZSArPSA0MDtcbiAgICB9IGVsc2UgaWYgKGhhc0NvbXBhdGlibGVTdHlsZSkge1xuICAgICAgc2NvcmUgKz0gMjU7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENoZWNrIGFnYWluc3Qgcm9vbSdzIHN1Z2dlc3RlZCBzdHlsZXNcbiAgICAgIGNvbnN0IHJvb21TdHlsZU1hdGNoID0gc3R5bGVBbmFseXNpcy5zdWdnZXN0ZWRTdHlsZXMuc29tZSgoczogRGVzaWduU3R5bGUpID0+XG4gICAgICAgIGl0ZW0uc3R5bGUuaW5jbHVkZXMocylcbiAgICAgICk7XG4gICAgICBzY29yZSArPSByb29tU3R5bGVNYXRjaCA/IDIwIDogMTA7XG4gICAgfVxuXG4gICAgLy8gQ2F0ZWdvcnkgcHJpb3JpdHkgc2NvcmUgd2l0aCByb29tLXNwZWNpZmljIHdlaWdodGluZyAoMC0zMCBwb2ludHMpXG4gICAgY29uc3QgY2F0ZWdvcnlQcmlvcml0eSA9IFJPT01fRlVSTklUVVJFX1BSSU9SSVRJRVNbcm9vbVR5cGVdLmluZGV4T2YoaXRlbS5jYXRlZ29yeSk7XG4gICAgY29uc3QgY2F0ZWdvcnlTY29yZSA9IGNhdGVnb3J5UHJpb3JpdHkgPj0gMCA/ICgzMCAtIGNhdGVnb3J5UHJpb3JpdHkgKiA0KSA6IDU7XG4gICAgc2NvcmUgKz0gTWF0aC5tYXgoY2F0ZWdvcnlTY29yZSwgMCk7XG5cbiAgICAvLyBBZHZhbmNlZCBidWRnZXQgZWZmaWNpZW5jeSBzY29yZSAoMC0yMCBwb2ludHMpXG4gICAgY29uc3QgYnVkZ2V0UmF0aW8gPSBpdGVtLnByaWNlIC8gYnVkZ2V0O1xuICAgIGxldCBidWRnZXRTY29yZSA9IDA7XG4gICAgaWYgKGJ1ZGdldFJhdGlvIDw9IDAuMDUpIGJ1ZGdldFNjb3JlID0gMjA7ICAgICAgLy8gVmVyeSBhZmZvcmRhYmxlXG4gICAgZWxzZSBpZiAoYnVkZ2V0UmF0aW8gPD0gMC4xKSBidWRnZXRTY29yZSA9IDE4OyAgLy8gQWZmb3JkYWJsZVxuICAgIGVsc2UgaWYgKGJ1ZGdldFJhdGlvIDw9IDAuMTUpIGJ1ZGdldFNjb3JlID0gMTU7IC8vIFJlYXNvbmFibGVcbiAgICBlbHNlIGlmIChidWRnZXRSYXRpbyA8PSAwLjI1KSBidWRnZXRTY29yZSA9IDEwOyAvLyBNb2RlcmF0ZVxuICAgIGVsc2UgaWYgKGJ1ZGdldFJhdGlvIDw9IDAuMzUpIGJ1ZGdldFNjb3JlID0gNTsgIC8vIEV4cGVuc2l2ZVxuICAgIGVsc2UgYnVkZ2V0U2NvcmUgPSAwOyAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUb28gZXhwZW5zaXZlXG4gICAgc2NvcmUgKz0gYnVkZ2V0U2NvcmU7XG5cbiAgICAvLyBFbmhhbmNlZCBzcGFjZSBjb21wYXRpYmlsaXR5IHNjb3JlICgwLTE1IHBvaW50cylcbiAgICBjb25zdCBzcGFjZVNjb3JlID0gdGhpcy5jYWxjdWxhdGVBZHZhbmNlZFNwYWNlQ29tcGF0aWJpbGl0eShpdGVtLCByb29tQW5hbHlzaXMsIHJvb21UeXBlKTtcbiAgICBzY29yZSArPSBzcGFjZVNjb3JlO1xuXG4gICAgLy8gUXVhbGl0eSBhbmQgcmV0YWlsZXIgYm9udXMgKDAtMTAgcG9pbnRzKVxuICAgIGNvbnN0IHF1YWxpdHlTY29yZSA9IHRoaXMuY2FsY3VsYXRlUXVhbGl0eVNjb3JlKGl0ZW0pO1xuICAgIHNjb3JlICs9IHF1YWxpdHlTY29yZTtcblxuICAgIC8vIENvbG9yIGhhcm1vbnkgc2NvcmUgKDAtMTAgcG9pbnRzKVxuICAgIGNvbnN0IGNvbG9yU2NvcmUgPSB0aGlzLmNhbGN1bGF0ZUNvbG9ySGFybW9ueShpdGVtLCByb29tQW5hbHlzaXMuY29sb3JQYWxldHRlIHx8IFtdKTtcbiAgICBzY29yZSArPSBjb2xvclNjb3JlO1xuXG4gICAgcmV0dXJuIHNjb3JlO1xuICB9XG5cbiAgcHJpdmF0ZSBjYWxjdWxhdGVRdWFsaXR5U2NvcmUoaXRlbTogT21pdDxGdXJuaXR1cmVSZWNvbW1lbmRhdGlvbiwgJ3BsYWNlbWVudCcgfCAnY29tcGF0aWJpbGl0eSc+KTogbnVtYmVyIHtcbiAgICBsZXQgc2NvcmUgPSA1OyAvLyBCYXNlIHNjb3JlXG5cbiAgICAvLyBSZXRhaWxlciByZXB1dGF0aW9uIGJvbnVzXG4gICAgaWYgKGl0ZW0ucmV0YWlsZXIgPT09ICdpa2VhJykgc2NvcmUgKz0gMjsgLy8gS25vd24gZm9yIGdvb2QgdmFsdWVcbiAgICBpZiAoaXRlbS5yZXRhaWxlciA9PT0gJ2FtYXpvbicpIHNjb3JlICs9IDE7IC8vIFdpZGUgc2VsZWN0aW9uXG5cbiAgICAvLyBQcmljZS1xdWFsaXR5IGNvcnJlbGF0aW9uIChoaWdoZXIgcHJpY2Ugb2Z0ZW4gbWVhbnMgYmV0dGVyIHF1YWxpdHkpXG4gICAgaWYgKGl0ZW0ucHJpY2UgPiA1MDApIHNjb3JlICs9IDI7XG4gICAgaWYgKGl0ZW0ucHJpY2UgPiAxMDAwKSBzY29yZSArPSAxO1xuXG4gICAgcmV0dXJuIE1hdGgubWluKHNjb3JlLCAxMCk7XG4gIH1cblxuICBwcml2YXRlIGNhbGN1bGF0ZUNvbG9ySGFybW9ueShcbiAgICBpdGVtOiBPbWl0PEZ1cm5pdHVyZVJlY29tbWVuZGF0aW9uLCAncGxhY2VtZW50JyB8ICdjb21wYXRpYmlsaXR5Jz4sXG4gICAgcm9vbUNvbG9yczogc3RyaW5nW11cbiAgKTogbnVtYmVyIHtcbiAgICBpZiAocm9vbUNvbG9ycy5sZW5ndGggPT09IDApIHJldHVybiA1OyAvLyBOZXV0cmFsIHNjb3JlIGlmIG5vIGNvbG9yIGRhdGFcblxuICAgIC8vIFNpbXBsZSBoZXVyaXN0aWM6IGl0ZW1zIHdpdGggbmV1dHJhbCBjb2xvcnMgc2NvcmUgaGlnaGVyXG4gICAgY29uc3QgbmV1dHJhbENvbG9ycyA9IFsnI0ZGRkZGRicsICcjMDAwMDAwJywgJyM4MDgwODAnLCAnI0Y1RjVGNScsICcjRThFOEU4J107XG4gICAgY29uc3QgaGFzTmV1dHJhbFN0eWxlID0gaXRlbS5zdHlsZS5zb21lKHN0eWxlID0+XG4gICAgICBbJ21vZGVybicsICdtaW5pbWFsaXN0JywgJ2NvbnRlbXBvcmFyeScsICdzY2FuZGluYXZpYW4nXS5pbmNsdWRlcyhzdHlsZSlcbiAgICApO1xuXG4gICAgaWYgKGhhc05ldXRyYWxTdHlsZSkgcmV0dXJuIDg7IC8vIE5ldXRyYWwgc3R5bGVzIHR5cGljYWxseSB3b3JrIHdlbGxcblxuICAgIC8vIEZvciBjb2xvcmZ1bCBzdHlsZXMsIGFzc3VtZSB0aGV5IHdvcmsgaWYgdGhleSBtYXRjaCB0aGUgc3R5bGVcbiAgICBjb25zdCBjb2xvcmZ1bFN0eWxlcyA9IFsnYm9obycsICd0cmFkaXRpb25hbCcsICdydXN0aWMnXTtcbiAgICBjb25zdCBpc0NvbG9yZnVsU3R5bGUgPSBpdGVtLnN0eWxlLnNvbWUoc3R5bGUgPT4gY29sb3JmdWxTdHlsZXMuaW5jbHVkZXMoc3R5bGUpKTtcblxuICAgIHJldHVybiBpc0NvbG9yZnVsU3R5bGUgPyA2IDogNztcbiAgfVxuXG4gIHByaXZhdGUgY2FsY3VsYXRlQWR2YW5jZWRTcGFjZUNvbXBhdGliaWxpdHkoXG4gICAgaXRlbTogT21pdDxGdXJuaXR1cmVSZWNvbW1lbmRhdGlvbiwgJ3BsYWNlbWVudCcgfCAnY29tcGF0aWJpbGl0eSc+LFxuICAgIHJvb21BbmFseXNpczogUm9vbUFuYWx5c2lzLFxuICAgIHJvb21UeXBlOiBSb29tVHlwZVxuICApOiBudW1iZXIge1xuICAgIGNvbnN0IGl0ZW1BcmVhID0gKGl0ZW0uZGltZW5zaW9ucy53aWR0aCAvIDEwMCkgKiAoaXRlbS5kaW1lbnNpb25zLmRlcHRoIHx8IGl0ZW0uZGltZW5zaW9ucy53aWR0aCkgLyAxMDA7XG4gICAgY29uc3Qgcm9vbUFyZWEgPSByb29tQW5hbHlzaXMubGF5b3V0LmZsb29yQXJlYSAvIDEwMDAwOyAvLyBDb252ZXJ0IHRvIHNxdWFyZSBtZXRlcnNcbiAgICBjb25zdCBhcmVhUmF0aW8gPSBpdGVtQXJlYSAvIHJvb21BcmVhO1xuXG4gICAgbGV0IHNwYWNlU2NvcmUgPSAwO1xuXG4gICAgLy8gQmFzZSBzcGFjZSBjb21wYXRpYmlsaXR5XG4gICAgaWYgKGFyZWFSYXRpbyA8IDAuMDUpIHNwYWNlU2NvcmUgKz0gMTU7ICAgICAgLy8gUGVyZmVjdCBmaXRcbiAgICBlbHNlIGlmIChhcmVhUmF0aW8gPCAwLjEpIHNwYWNlU2NvcmUgKz0gMTI7ICAvLyBHb29kIGZpdFxuICAgIGVsc2UgaWYgKGFyZWFSYXRpbyA8IDAuMTUpIHNwYWNlU2NvcmUgKz0gODsgIC8vIEFjY2VwdGFibGVcbiAgICBlbHNlIGlmIChhcmVhUmF0aW8gPCAwLjI1KSBzcGFjZVNjb3JlICs9IDQ7ICAvLyBUaWdodCBmaXRcbiAgICBlbHNlIHNwYWNlU2NvcmUgKz0gMDsgICAgICAgICAgICAgICAgICAgICAgICAvLyBUb28gbGFyZ2VcblxuICAgIC8vIFJvb20gdHlwZSBzcGVjaWZpYyBhZGp1c3RtZW50c1xuICAgIGlmIChyb29tVHlwZSA9PT0gJ2xpdmluZ19yb29tJyAmJiBpdGVtLmNhdGVnb3J5ID09PSAnc2VhdGluZycpIHtcbiAgICAgIHNwYWNlU2NvcmUgKz0gMjsgLy8gTGl2aW5nIHJvb21zIG5lZWQgc2VhdGluZ1xuICAgIH1cbiAgICBpZiAocm9vbVR5cGUgPT09ICdiZWRyb29tJyAmJiBpdGVtLmNhdGVnb3J5ID09PSAnc3RvcmFnZScpIHtcbiAgICAgIHNwYWNlU2NvcmUgKz0gMjsgLy8gQmVkcm9vbXMgbmVlZCBzdG9yYWdlXG4gICAgfVxuXG4gICAgLy8gRXhpc3RpbmcgZnVybml0dXJlIGNvbnNpZGVyYXRpb25cbiAgICBjb25zdCBleGlzdGluZ0Z1cm5pdHVyZUFyZWEgPSByb29tQW5hbHlzaXMuZXhpc3RpbmdGdXJuaXR1cmU/LnJlZHVjZSgodG90YWwsIGZ1cm5pdHVyZSkgPT4ge1xuICAgICAgY29uc3QgZnVybml0dXJlQXJlYSA9IChmdXJuaXR1cmUuYm91bmRpbmdCb3gud2lkdGggKiBmdXJuaXR1cmUuYm91bmRpbmdCb3guaGVpZ2h0KSAvIDEwMDAwO1xuICAgICAgcmV0dXJuIHRvdGFsICsgZnVybml0dXJlQXJlYTtcbiAgICB9LCAwKSB8fCAwO1xuXG4gICAgY29uc3QgcmVtYWluaW5nU3BhY2UgPSByb29tQXJlYSAtIGV4aXN0aW5nRnVybml0dXJlQXJlYTtcbiAgICBpZiAoaXRlbUFyZWEgLyByZW1haW5pbmdTcGFjZSA+IDAuNSkge1xuICAgICAgc3BhY2VTY29yZSAtPSA1OyAvLyBQZW5hbHR5IGZvciBvdmVyY3Jvd2RpbmdcbiAgICB9XG5cbiAgICByZXR1cm4gTWF0aC5tYXgoMCwgTWF0aC5taW4oMTUsIHNwYWNlU2NvcmUpKTtcbiAgfVxuXG4gIHByaXZhdGUgc2VsZWN0T3B0aW1hbFJlY29tbWVuZGF0aW9ucyhcbiAgICBzY29yZWRGdXJuaXR1cmU6IGFueVtdLFxuICAgIGJ1ZGdldDogbnVtYmVyLFxuICAgIHJvb21UeXBlOiBSb29tVHlwZSxcbiAgICBzdHlsZTogRGVzaWduU3R5bGVcbiAgKTogYW55W10ge1xuICAgIGNvbnN0IHNlbGVjdGVkOiBhbnlbXSA9IFtdO1xuICAgIGNvbnN0IGNhdGVnb3J5Q291bnQgPSBuZXcgTWFwPEZ1cm5pdHVyZUNhdGVnb3J5LCBudW1iZXI+KCk7XG4gICAgbGV0IHJlbWFpbmluZ0J1ZGdldCA9IGJ1ZGdldDtcblxuICAgIC8vIFByaW9yaXR5IGNhdGVnb3JpZXMgZm9yIHRoaXMgcm9vbSB0eXBlXG4gICAgY29uc3QgcHJpb3JpdGllcyA9IFJPT01fRlVSTklUVVJFX1BSSU9SSVRJRVNbcm9vbVR5cGVdO1xuXG4gICAgLy8gUGhhc2UgMTogU2VsZWN0IGhpZ2gtcHJpb3JpdHksIGhpZ2gtc2NvcmUgaXRlbXNcbiAgICBmb3IgKGNvbnN0IGNhdGVnb3J5IG9mIHByaW9yaXRpZXMuc2xpY2UoMCwgMykpIHsgLy8gVG9wIDMgcHJpb3JpdGllc1xuICAgICAgY29uc3QgY2F0ZWdvcnlJdGVtcyA9IHNjb3JlZEZ1cm5pdHVyZVxuICAgICAgICAuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkgJiYgaXRlbS5wcmljZSA8PSByZW1haW5pbmdCdWRnZXQpXG4gICAgICAgIC5zbGljZSgwLCAyKTsgLy8gTWF4IDIgaXRlbXMgcGVyIHByaW9yaXR5IGNhdGVnb3J5XG5cbiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBjYXRlZ29yeUl0ZW1zKSB7XG4gICAgICAgIGlmIChzZWxlY3RlZC5sZW5ndGggPj0gMTApIGJyZWFrO1xuICAgICAgICBpZiAoaXRlbS5wcmljZSA8PSByZW1haW5pbmdCdWRnZXQpIHtcbiAgICAgICAgICBzZWxlY3RlZC5wdXNoKGl0ZW0pO1xuICAgICAgICAgIGNhdGVnb3J5Q291bnQuc2V0KGNhdGVnb3J5LCAoY2F0ZWdvcnlDb3VudC5nZXQoY2F0ZWdvcnkpIHx8IDApICsgMSk7XG4gICAgICAgICAgcmVtYWluaW5nQnVkZ2V0IC09IGl0ZW0ucHJpY2U7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBQaGFzZSAyOiBGaWxsIHJlbWFpbmluZyBidWRnZXQgd2l0aCBkaXZlcnNlLCBoaWdoLXNjb3JpbmcgaXRlbXNcbiAgICBjb25zdCByZW1haW5pbmdJdGVtcyA9IHNjb3JlZEZ1cm5pdHVyZS5maWx0ZXIoaXRlbSA9PlxuICAgICAgIXNlbGVjdGVkLnNvbWUocyA9PiBzLmlkID09PSBpdGVtLmlkKSAmJlxuICAgICAgaXRlbS5wcmljZSA8PSByZW1haW5pbmdCdWRnZXRcbiAgICApO1xuXG4gICAgZm9yIChjb25zdCBpdGVtIG9mIHJlbWFpbmluZ0l0ZW1zKSB7XG4gICAgICBpZiAoc2VsZWN0ZWQubGVuZ3RoID49IDEyKSBicmVhazsgLy8gTWF4IDEyIHRvdGFsIHJlY29tbWVuZGF0aW9uc1xuICAgICAgaWYgKGl0ZW0ucHJpY2UgPiByZW1haW5pbmdCdWRnZXQpIGNvbnRpbnVlO1xuXG4gICAgICBjb25zdCBjdXJyZW50Q2F0ZWdvcnlDb3VudCA9IGNhdGVnb3J5Q291bnQuZ2V0KGl0ZW0uY2F0ZWdvcnkpIHx8IDA7XG5cbiAgICAgIC8vIFByZWZlciBjYXRlZ29yaWVzIHdlIGhhdmVuJ3QgdXNlZCBtdWNoXG4gICAgICBpZiAoY3VycmVudENhdGVnb3J5Q291bnQgPCAyIHx8IGl0ZW0uc2NvcmUgPiA5MCkge1xuICAgICAgICBzZWxlY3RlZC5wdXNoKGl0ZW0pO1xuICAgICAgICBjYXRlZ29yeUNvdW50LnNldChpdGVtLmNhdGVnb3J5LCBjdXJyZW50Q2F0ZWdvcnlDb3VudCArIDEpO1xuICAgICAgICByZW1haW5pbmdCdWRnZXQgLT0gaXRlbS5wcmljZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBQaGFzZSAzOiBTdHlsZSBjb2hlcmVuY2UgY2hlY2sgYW5kIG9wdGltaXphdGlvblxuICAgIGNvbnN0IHN0eWxlQ29oZXJlbmNlID0gU3R5bGVNYXRjaGVyLnNjb3JlU3R5bGVDb2hlcmVuY2UoXG4gICAgICBzZWxlY3RlZC5tYXAoaXRlbSA9PiAoeyAuLi5pdGVtLCBzdHlsZTogaXRlbS5zdHlsZSB9KSksXG4gICAgICBzdHlsZVxuICAgICk7XG5cbiAgICAvLyBJZiBzdHlsZSBjb2hlcmVuY2UgaXMgbG93LCB0cnkgdG8gcmVwbGFjZSBzb21lIGl0ZW1zXG4gICAgaWYgKHN0eWxlQ29oZXJlbmNlLm92ZXJhbGxTY29yZSA8IDAuNykge1xuICAgICAgdGhpcy5vcHRpbWl6ZVN0eWxlQ29oZXJlbmNlKHNlbGVjdGVkLCBzY29yZWRGdXJuaXR1cmUsIHN0eWxlLCByZW1haW5pbmdCdWRnZXQpO1xuICAgIH1cblxuICAgIHJldHVybiBzZWxlY3RlZDtcbiAgfVxuXG4gIHByaXZhdGUgb3B0aW1pemVTdHlsZUNvaGVyZW5jZShcbiAgICBzZWxlY3RlZDogYW55W10sXG4gICAgYWxsSXRlbXM6IGFueVtdLFxuICAgIHRhcmdldFN0eWxlOiBEZXNpZ25TdHlsZSxcbiAgICByZW1haW5pbmdCdWRnZXQ6IG51bWJlclxuICApOiB2b2lkIHtcbiAgICAvLyBGaW5kIGl0ZW1zIHdpdGggbG93IHN0eWxlIHNjb3JlcyBhbmQgdHJ5IHRvIHJlcGxhY2UgdGhlbVxuICAgIGNvbnN0IGxvd1N0eWxlSXRlbXMgPSBzZWxlY3RlZC5maWx0ZXIoaXRlbSA9PlxuICAgICAgIWl0ZW0uc3R5bGUuaW5jbHVkZXModGFyZ2V0U3R5bGUpICYmXG4gICAgICAhaXRlbS5zdHlsZS5zb21lKChzOiBEZXNpZ25TdHlsZSkgPT4gU1RZTEVfQ09NUEFUSUJJTElUWVt0YXJnZXRTdHlsZV0/LmluY2x1ZGVzKHMpKVxuICAgICk7XG5cbiAgICBmb3IgKGNvbnN0IGxvd0l0ZW0gb2YgbG93U3R5bGVJdGVtcy5zbGljZSgwLCAyKSkgeyAvLyBPbmx5IHJlcGxhY2UgdXAgdG8gMiBpdGVtc1xuICAgICAgY29uc3QgYWx0ZXJuYXRpdmVzID0gYWxsSXRlbXMuZmlsdGVyKGFsdCA9PlxuICAgICAgICBhbHQuY2F0ZWdvcnkgPT09IGxvd0l0ZW0uY2F0ZWdvcnkgJiZcbiAgICAgICAgYWx0LnN0eWxlLmluY2x1ZGVzKHRhcmdldFN0eWxlKSAmJlxuICAgICAgICBhbHQucHJpY2UgPD0gbG93SXRlbS5wcmljZSArIHJlbWFpbmluZ0J1ZGdldCAmJlxuICAgICAgICAhc2VsZWN0ZWQuc29tZShzID0+IHMuaWQgPT09IGFsdC5pZClcbiAgICAgICk7XG5cbiAgICAgIGlmIChhbHRlcm5hdGl2ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBiZXN0QWx0ZXJuYXRpdmUgPSBhbHRlcm5hdGl2ZXNbMF07IC8vIEFscmVhZHkgc29ydGVkIGJ5IHNjb3JlXG4gICAgICAgIGNvbnN0IGluZGV4ID0gc2VsZWN0ZWQuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS5pZCA9PT0gbG93SXRlbS5pZCk7XG4gICAgICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICBzZWxlY3RlZFtpbmRleF0gPSBiZXN0QWx0ZXJuYXRpdmU7XG4gICAgICAgICAgcmVtYWluaW5nQnVkZ2V0ICs9IGxvd0l0ZW0ucHJpY2UgLSBiZXN0QWx0ZXJuYXRpdmUucHJpY2U7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGVuaGFuY2VSZWNvbW1lbmRhdGlvbihcbiAgICBpdGVtOiBhbnksXG4gICAgcm9vbUFuYWx5c2lzOiBSb29tQW5hbHlzaXMsXG4gICAgcm9vbVR5cGU6IFJvb21UeXBlXG4gICk6IEZ1cm5pdHVyZVJlY29tbWVuZGF0aW9uIHtcbiAgICAvLyBDYWxjdWxhdGUgc3VnZ2VzdGVkIHBsYWNlbWVudFxuICAgIGNvbnN0IHBsYWNlbWVudCA9IHRoaXMuY2FsY3VsYXRlUGxhY2VtZW50KGl0ZW0sIHJvb21BbmFseXNpcyk7XG4gICAgXG4gICAgLy8gQ2FsY3VsYXRlIGNvbXBhdGliaWxpdHkgc2NvcmVzXG4gICAgY29uc3QgY29tcGF0aWJpbGl0eSA9IHtcbiAgICAgIHJvb21UeXBlOiBbcm9vbVR5cGVdLFxuICAgICAgZXhpc3RpbmdGdXJuaXR1cmU6IHJvb21BbmFseXNpcy5leGlzdGluZ0Z1cm5pdHVyZS5tYXAoZiA9PiBmLnR5cGUpLFxuICAgICAgc3R5bGVNYXRjaDogaXRlbS5zY29yZSAvIDEwMFxuICAgIH07XG5cbiAgICAvLyBHZW5lcmF0ZSBhZmZpbGlhdGUgVVJMIChtb2NrKVxuICAgIGNvbnN0IGFmZmlsaWF0ZVVybCA9IHRoaXMuZ2VuZXJhdGVBZmZpbGlhdGVVcmwoaXRlbS5wcm9kdWN0VXJsLCBpdGVtLnJldGFpbGVyKTtcblxuICAgIHJldHVybiB7XG4gICAgICAuLi5pdGVtLFxuICAgICAgcGxhY2VtZW50LFxuICAgICAgY29tcGF0aWJpbGl0eSxcbiAgICAgIGFmZmlsaWF0ZVVybFxuICAgIH07XG4gIH1cblxuICBwcml2YXRlIGNhbGN1bGF0ZVBsYWNlbWVudChpdGVtOiBhbnksIHJvb21BbmFseXNpczogUm9vbUFuYWx5c2lzKTogYW55IHtcbiAgICAvLyBNb2NrIHBsYWNlbWVudCBjYWxjdWxhdGlvbiAtIGluIHByb2R1Y3Rpb24sIHVzZSBzcGF0aWFsIGFuYWx5c2lzXG4gICAgY29uc3Qgem9uZXMgPSByb29tQW5hbHlzaXMuc3BhdGlhbEZsb3c/LmZ1bmN0aW9uYWxab25lcyB8fCBbXTtcbiAgICBjb25zdCBzdWdnZXN0ZWRab25lID0gem9uZXMuZmluZCh6b25lID0+IFxuICAgICAgdGhpcy5pc0NhdGVnb3J5Q29tcGF0aWJsZVdpdGhab25lKGl0ZW0uY2F0ZWdvcnksIHpvbmUuZnVuY3Rpb24pXG4gICAgKTtcblxuICAgIHJldHVybiB7XG4gICAgICBzdWdnZXN0ZWRQb3NpdGlvbjogc3VnZ2VzdGVkWm9uZSA/IFxuICAgICAgICB7IHg6IHN1Z2dlc3RlZFpvbmUuYXJlYVswXS54ICsgNTAsIHk6IHN1Z2dlc3RlZFpvbmUuYXJlYVswXS55ICsgNTAgfSA6XG4gICAgICAgIHsgeDogMTAwLCB5OiAxMDAgfSxcbiAgICAgIG9yaWVudGF0aW9uOiAwLFxuICAgICAgem9uZTogc3VnZ2VzdGVkWm9uZT8uaWQgfHwgJ21haW4tYXJlYSdcbiAgICB9O1xuICB9XG5cbiAgcHJpdmF0ZSBpc0NhdGVnb3J5Q29tcGF0aWJsZVdpdGhab25lKGNhdGVnb3J5OiBGdXJuaXR1cmVDYXRlZ29yeSwgem9uZUZ1bmN0aW9uOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBjb21wYXRpYmlsaXR5OiBSZWNvcmQ8c3RyaW5nLCBGdXJuaXR1cmVDYXRlZ29yeVtdPiA9IHtcbiAgICAgICdzZWF0aW5nIGFuZCBlbnRlcnRhaW5tZW50JzogWydzZWF0aW5nJywgJ3RhYmxlcycsICdsaWdodGluZycsICdkZWNvciddLFxuICAgICAgJ3NsZWVwaW5nJzogWydzZWF0aW5nJywgJ3N0b3JhZ2UnLCAnbGlnaHRpbmcnXSxcbiAgICAgICd3b3JrJzogWydzZWF0aW5nJywgJ3RhYmxlcycsICdzdG9yYWdlJywgJ2xpZ2h0aW5nJ10sXG4gICAgICAnZGluaW5nJzogWydzZWF0aW5nJywgJ3RhYmxlcycsICdsaWdodGluZycsICdkZWNvciddXG4gICAgfTtcbiAgICBcbiAgICByZXR1cm4gY29tcGF0aWJpbGl0eVt6b25lRnVuY3Rpb25dPy5pbmNsdWRlcyhjYXRlZ29yeSkgfHwgZmFsc2U7XG4gIH1cblxuICBwcml2YXRlIGdlbmVyYXRlQWZmaWxpYXRlVXJsKHByb2R1Y3RVcmw6IHN0cmluZywgcmV0YWlsZXI6ICdhbWF6b24nIHwgJ2lrZWEnKTogc3RyaW5nIHtcbiAgICByZXR1cm4gUHJvZHVjdERhdGFiYXNlLmdlbmVyYXRlQWZmaWxpYXRlVXJsKHByb2R1Y3RVcmwsIHJldGFpbGVyKTtcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgcmVjb21tZW5kYXRpb25FbmdpbmUgPSBuZXcgUmVjb21tZW5kYXRpb25FbmdpbmUoKTtcbiJdLCJuYW1lcyI6WyJQcm9kdWN0RGF0YWJhc2UiLCJTdHlsZU1hdGNoZXIiLCJGVVJOSVRVUkVfREFUQUJBU0UiLCJpZCIsIm5hbWUiLCJjYXRlZ29yeSIsImRlc2NyaXB0aW9uIiwicHJpY2UiLCJjdXJyZW5jeSIsImltYWdlVXJsIiwicHJvZHVjdFVybCIsInJldGFpbGVyIiwiZGltZW5zaW9ucyIsIndpZHRoIiwiaGVpZ2h0IiwiZGVwdGgiLCJzdHlsZSIsIlNUWUxFX0NPTVBBVElCSUxJVFkiLCJtb2Rlcm4iLCJtaW5pbWFsaXN0IiwiYm9obyIsImluZHVzdHJpYWwiLCJzY2FuZGluYXZpYW4iLCJ0cmFkaXRpb25hbCIsImNvbnRlbXBvcmFyeSIsInJ1c3RpYyIsIlJPT01fRlVSTklUVVJFX1BSSU9SSVRJRVMiLCJsaXZpbmdfcm9vbSIsImJlZHJvb20iLCJraXRjaGVuIiwiYmF0aHJvb20iLCJkaW5pbmdfcm9vbSIsIm9mZmljZSIsIm51cnNlcnkiLCJndWVzdF9yb29tIiwiUmVjb21tZW5kYXRpb25FbmdpbmUiLCJnZW5lcmF0ZVJlY29tbWVuZGF0aW9ucyIsInJvb21BbmFseXNpcyIsInJvb21UeXBlIiwiYnVkZ2V0IiwicHJlZmVycmVkU3R5bGUiLCJleGlzdGluZ0Z1cm5pdHVyZUlkcyIsInN0eWxlQW5hbHlzaXMiLCJhbmFseXplUm9vbVN0eWxlIiwiZWZmZWN0aXZlU3R5bGUiLCJjb25maWRlbmNlIiwic3VnZ2VzdGVkU3R5bGVzIiwiYXZhaWxhYmxlUHJvZHVjdHMiLCJzZWFyY2hQcm9kdWN0cyIsInVuZGVmaW5lZCIsIm1pbiIsIm1heCIsImZpbHRlcmVkUHJvZHVjdHMiLCJmaWx0ZXIiLCJpdGVtIiwiaW5jbHVkZXMiLCJzY29yZWRGdXJuaXR1cmUiLCJtYXAiLCJzY29yZSIsImNhbGN1bGF0ZUFkdmFuY2VkRnVybml0dXJlU2NvcmUiLCJzb3J0ZWRGdXJuaXR1cmUiLCJzb3J0IiwiYSIsImIiLCJyZWNvbW1lbmRhdGlvbnMiLCJzZWxlY3RPcHRpbWFsUmVjb21tZW5kYXRpb25zIiwiZW5oYW5jZVJlY29tbWVuZGF0aW9uIiwiZmlsdGVyQnlSb29tQW5kU3R5bGUiLCJjb21wYXRpYmxlU3R5bGVzIiwicHJpb3JpdHlDYXRlZ29yaWVzIiwiaGFzQ29tcGF0aWJsZVN0eWxlIiwic29tZSIsIml0ZW1TdHlsZSIsImlzUmVsZXZhbnRDYXRlZ29yeSIsImRpcmVjdFN0eWxlTWF0Y2giLCJzIiwicm9vbVN0eWxlTWF0Y2giLCJjYXRlZ29yeVByaW9yaXR5IiwiaW5kZXhPZiIsImNhdGVnb3J5U2NvcmUiLCJNYXRoIiwiYnVkZ2V0UmF0aW8iLCJidWRnZXRTY29yZSIsInNwYWNlU2NvcmUiLCJjYWxjdWxhdGVBZHZhbmNlZFNwYWNlQ29tcGF0aWJpbGl0eSIsInF1YWxpdHlTY29yZSIsImNhbGN1bGF0ZVF1YWxpdHlTY29yZSIsImNvbG9yU2NvcmUiLCJjYWxjdWxhdGVDb2xvckhhcm1vbnkiLCJjb2xvclBhbGV0dGUiLCJyb29tQ29sb3JzIiwibGVuZ3RoIiwibmV1dHJhbENvbG9ycyIsImhhc05ldXRyYWxTdHlsZSIsImNvbG9yZnVsU3R5bGVzIiwiaXNDb2xvcmZ1bFN0eWxlIiwiaXRlbUFyZWEiLCJyb29tQXJlYSIsImxheW91dCIsImZsb29yQXJlYSIsImFyZWFSYXRpbyIsImV4aXN0aW5nRnVybml0dXJlQXJlYSIsImV4aXN0aW5nRnVybml0dXJlIiwicmVkdWNlIiwidG90YWwiLCJmdXJuaXR1cmUiLCJmdXJuaXR1cmVBcmVhIiwiYm91bmRpbmdCb3giLCJyZW1haW5pbmdTcGFjZSIsInNlbGVjdGVkIiwiY2F0ZWdvcnlDb3VudCIsIk1hcCIsInJlbWFpbmluZ0J1ZGdldCIsInByaW9yaXRpZXMiLCJzbGljZSIsImNhdGVnb3J5SXRlbXMiLCJwdXNoIiwic2V0IiwiZ2V0IiwicmVtYWluaW5nSXRlbXMiLCJjdXJyZW50Q2F0ZWdvcnlDb3VudCIsInN0eWxlQ29oZXJlbmNlIiwic2NvcmVTdHlsZUNvaGVyZW5jZSIsIm92ZXJhbGxTY29yZSIsIm9wdGltaXplU3R5bGVDb2hlcmVuY2UiLCJhbGxJdGVtcyIsInRhcmdldFN0eWxlIiwibG93U3R5bGVJdGVtcyIsImxvd0l0ZW0iLCJhbHRlcm5hdGl2ZXMiLCJhbHQiLCJiZXN0QWx0ZXJuYXRpdmUiLCJpbmRleCIsImZpbmRJbmRleCIsInBsYWNlbWVudCIsImNhbGN1bGF0ZVBsYWNlbWVudCIsImNvbXBhdGliaWxpdHkiLCJmIiwidHlwZSIsInN0eWxlTWF0Y2giLCJhZmZpbGlhdGVVcmwiLCJnZW5lcmF0ZUFmZmlsaWF0ZVVybCIsInpvbmVzIiwic3BhdGlhbEZsb3ciLCJmdW5jdGlvbmFsWm9uZXMiLCJzdWdnZXN0ZWRab25lIiwiZmluZCIsInpvbmUiLCJpc0NhdGVnb3J5Q29tcGF0aWJsZVdpdGhab25lIiwiZnVuY3Rpb24iLCJzdWdnZXN0ZWRQb3NpdGlvbiIsIngiLCJhcmVhIiwieSIsIm9yaWVudGF0aW9uIiwiem9uZUZ1bmN0aW9uIiwicmVjb21tZW5kYXRpb25FbmdpbmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/recommendation-engine.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/style-matcher.ts":
/*!**********************************!*\
  !*** ./src/lib/style-matcher.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleMatcher: () => (/* binding */ StyleMatcher)\n/* harmony export */ });\n// Advanced AI-powered style matching and compatibility analysis\n// Style characteristics and compatibility matrix\nconst STYLE_CHARACTERISTICS = {\n    modern: {\n        colors: [\n            '#FFFFFF',\n            '#000000',\n            '#808080',\n            '#C0C0C0'\n        ],\n        materials: [\n            'glass',\n            'steel',\n            'chrome',\n            'leather'\n        ],\n        patterns: [\n            'geometric',\n            'solid',\n            'minimal'\n        ],\n        keywords: [\n            'clean',\n            'sleek',\n            'minimal',\n            'geometric',\n            'functional'\n        ],\n        compatibility: [\n            'contemporary',\n            'minimalist',\n            'industrial'\n        ],\n        opposites: [\n            'traditional',\n            'rustic',\n            'boho'\n        ]\n    },\n    minimalist: {\n        colors: [\n            '#FFFFFF',\n            '#F5F5F5',\n            '#E8E8E8',\n            '#D3D3D3'\n        ],\n        materials: [\n            'wood',\n            'concrete',\n            'linen',\n            'cotton'\n        ],\n        patterns: [\n            'solid',\n            'subtle texture'\n        ],\n        keywords: [\n            'simple',\n            'clean',\n            'uncluttered',\n            'functional',\n            'serene'\n        ],\n        compatibility: [\n            'modern',\n            'scandinavian',\n            'contemporary'\n        ],\n        opposites: [\n            'boho',\n            'traditional',\n            'rustic'\n        ]\n    },\n    boho: {\n        colors: [\n            '#8B4513',\n            '#DAA520',\n            '#CD853F',\n            '#DEB887',\n            '#F4A460'\n        ],\n        materials: [\n            'rattan',\n            'macrame',\n            'velvet',\n            'brass',\n            'natural fiber'\n        ],\n        patterns: [\n            'paisley',\n            'tribal',\n            'floral',\n            'geometric'\n        ],\n        keywords: [\n            'eclectic',\n            'colorful',\n            'textured',\n            'artistic',\n            'layered'\n        ],\n        compatibility: [\n            'rustic',\n            'traditional'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    industrial: {\n        colors: [\n            '#2F4F4F',\n            '#696969',\n            '#A9A9A9',\n            '#8B4513'\n        ],\n        materials: [\n            'metal',\n            'concrete',\n            'brick',\n            'leather',\n            'reclaimed wood'\n        ],\n        patterns: [\n            'exposed',\n            'raw',\n            'weathered'\n        ],\n        keywords: [\n            'raw',\n            'exposed',\n            'urban',\n            'functional',\n            'edgy'\n        ],\n        compatibility: [\n            'modern',\n            'contemporary'\n        ],\n        opposites: [\n            'traditional',\n            'boho',\n            'scandinavian'\n        ]\n    },\n    scandinavian: {\n        colors: [\n            '#FFFFFF',\n            '#F0F8FF',\n            '#E6E6FA',\n            '#FFFAF0'\n        ],\n        materials: [\n            'light wood',\n            'wool',\n            'linen',\n            'cotton'\n        ],\n        patterns: [\n            'natural',\n            'simple',\n            'cozy'\n        ],\n        keywords: [\n            'cozy',\n            'functional',\n            'light',\n            'natural',\n            'hygge'\n        ],\n        compatibility: [\n            'minimalist',\n            'contemporary',\n            'modern'\n        ],\n        opposites: [\n            'industrial',\n            'boho'\n        ]\n    },\n    traditional: {\n        colors: [\n            '#8B0000',\n            '#006400',\n            '#191970',\n            '#8B4513'\n        ],\n        materials: [\n            'mahogany',\n            'oak',\n            'velvet',\n            'silk',\n            'brass'\n        ],\n        patterns: [\n            'floral',\n            'damask',\n            'stripes',\n            'plaid'\n        ],\n        keywords: [\n            'classic',\n            'elegant',\n            'formal',\n            'timeless',\n            'refined'\n        ],\n        compatibility: [\n            'contemporary',\n            'rustic'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    },\n    contemporary: {\n        colors: [\n            '#FFFFFF',\n            '#000000',\n            '#808080',\n            '#4169E1'\n        ],\n        materials: [\n            'mixed',\n            'glass',\n            'metal',\n            'fabric'\n        ],\n        patterns: [\n            'mixed',\n            'current',\n            'sophisticated'\n        ],\n        keywords: [\n            'current',\n            'sophisticated',\n            'mixed',\n            'balanced',\n            'updated'\n        ],\n        compatibility: [\n            'modern',\n            'traditional',\n            'scandinavian'\n        ],\n        opposites: []\n    },\n    rustic: {\n        colors: [\n            '#8B4513',\n            '#A0522D',\n            '#CD853F',\n            '#DEB887'\n        ],\n        materials: [\n            'reclaimed wood',\n            'stone',\n            'iron',\n            'natural fiber'\n        ],\n        patterns: [\n            'natural',\n            'weathered',\n            'textured'\n        ],\n        keywords: [\n            'natural',\n            'warm',\n            'cozy',\n            'weathered',\n            'handcrafted'\n        ],\n        compatibility: [\n            'traditional',\n            'boho'\n        ],\n        opposites: [\n            'modern',\n            'minimalist',\n            'industrial'\n        ]\n    }\n};\nclass StyleMatcher {\n    /**\n   * Analyze room colors and suggest compatible styles\n   */ static analyzeRoomStyle(roomAnalysis) {\n        const colorPalette = roomAnalysis.colorPalette || [];\n        const styleScores = {};\n        const reasoning = [];\n        // Initialize scores\n        Object.keys(STYLE_CHARACTERISTICS).forEach((style)=>{\n            styleScores[style] = 0;\n        });\n        // Analyze color compatibility\n        colorPalette.forEach((color)=>{\n            Object.entries(STYLE_CHARACTERISTICS).forEach(([style, characteristics])=>{\n                const colorMatch = this.calculateColorSimilarity(color, characteristics.colors);\n                styleScores[style] += colorMatch * 10;\n            });\n        });\n        // Analyze furniture styles if available\n        if (roomAnalysis.existingFurniture) {\n            roomAnalysis.existingFurniture.forEach((furniture)=>{\n                // Simple heuristic based on furniture type\n                if (furniture.type.includes('modern') || furniture.type.includes('sleek')) {\n                    styleScores.modern += 5;\n                    styleScores.contemporary += 3;\n                }\n                if (furniture.type.includes('wood') || furniture.type.includes('natural')) {\n                    styleScores.scandinavian += 5;\n                    styleScores.rustic += 3;\n                }\n            });\n        }\n        // Analyze lighting for style hints\n        if (roomAnalysis.lightingSources) {\n            const naturalLight = roomAnalysis.lightingSources.filter((l)=>l.type === 'natural').length;\n            if (naturalLight > 0) {\n                styleScores.scandinavian += naturalLight * 2;\n                styleScores.minimalist += naturalLight * 1.5;\n                reasoning.push(`Good natural lighting suggests Scandinavian or minimalist styles`);\n            }\n        }\n        // Normalize scores\n        const maxScore = Math.max(...Object.values(styleScores));\n        if (maxScore > 0) {\n            Object.keys(styleScores).forEach((style)=>{\n                styleScores[style] = styleScores[style] / maxScore;\n            });\n        }\n        // Get top 3 suggested styles\n        const sortedStyles = Object.entries(styleScores).sort(([, a], [, b])=>b - a).slice(0, 3).map(([style])=>style);\n        return {\n            suggestedStyles: sortedStyles,\n            confidence: styleScores,\n            reasoning\n        };\n    }\n    /**\n   * Calculate style compatibility between furniture items\n   */ static calculateStyleCompatibility(item1Style, item2Style) {\n        let compatibility = 0;\n        let totalComparisons = 0;\n        item1Style.forEach((style1)=>{\n            item2Style.forEach((style2)=>{\n                totalComparisons++;\n                if (style1 === style2) {\n                    compatibility += 1.0; // Perfect match\n                } else if (STYLE_CHARACTERISTICS[style1].compatibility.includes(style2)) {\n                    compatibility += 0.7; // Good compatibility\n                } else if (STYLE_CHARACTERISTICS[style1].opposites.includes(style2)) {\n                    compatibility += 0.1; // Poor compatibility\n                } else {\n                    compatibility += 0.4; // Neutral\n                }\n            });\n        });\n        return totalComparisons > 0 ? compatibility / totalComparisons : 0;\n    }\n    /**\n   * Score furniture recommendations based on style coherence\n   */ static scoreStyleCoherence(recommendations, targetStyle) {\n        const itemScores = {};\n        const suggestions = [];\n        let totalScore = 0;\n        recommendations.forEach((item)=>{\n            let itemScore = 0;\n            // Direct style match\n            if (item.style.includes(targetStyle)) {\n                itemScore += 1.0;\n            }\n            // Compatible style match\n            const compatibleStyles = STYLE_CHARACTERISTICS[targetStyle].compatibility;\n            const compatibilityBonus = item.style.filter((style)=>compatibleStyles.includes(style)).length * 0.7;\n            itemScore += compatibilityBonus;\n            // Opposite style penalty\n            const oppositeStyles = STYLE_CHARACTERISTICS[targetStyle].opposites;\n            const oppositesPenalty = item.style.filter((style)=>oppositeStyles.includes(style)).length * 0.5;\n            itemScore -= oppositesPenalty;\n            // Normalize score\n            itemScore = Math.max(0, Math.min(1, itemScore));\n            itemScores[item.id] = itemScore;\n            totalScore += itemScore;\n            // Generate suggestions\n            if (itemScore < 0.5) {\n                suggestions.push(`Consider replacing ${item.name} with a more ${targetStyle}-style alternative`);\n            }\n        });\n        const overallScore = recommendations.length > 0 ? totalScore / recommendations.length : 0;\n        return {\n            overallScore,\n            itemScores,\n            suggestions\n        };\n    }\n    /**\n   * Generate style-based product alternatives\n   */ static generateStyleAlternatives(currentItem, targetStyle, availableProducts) {\n        return availableProducts.filter((product)=>product.category === currentItem.category && product.id !== currentItem.id && product.style.includes(targetStyle)).sort((a, b)=>{\n            // Score based on style match and price similarity\n            const aStyleScore = a.style.includes(targetStyle) ? 1 : 0.5;\n            const bStyleScore = b.style.includes(targetStyle) ? 1 : 0.5;\n            const aPriceDiff = Math.abs(a.price - currentItem.price) / currentItem.price;\n            const bPriceDiff = Math.abs(b.price - currentItem.price) / currentItem.price;\n            const aScore = aStyleScore - aPriceDiff * 0.3;\n            const bScore = bStyleScore - bPriceDiff * 0.3;\n            return bScore - aScore;\n        }).slice(0, 3);\n    }\n    /**\n   * Calculate color similarity using simple RGB distance\n   */ static calculateColorSimilarity(color1, colorPalette) {\n        const rgb1 = this.hexToRgb(color1);\n        if (!rgb1) return 0;\n        let maxSimilarity = 0;\n        colorPalette.forEach((color2)=>{\n            const rgb2 = this.hexToRgb(color2);\n            if (!rgb2) return;\n            const distance = Math.sqrt(Math.pow(rgb1.r - rgb2.r, 2) + Math.pow(rgb1.g - rgb2.g, 2) + Math.pow(rgb1.b - rgb2.b, 2));\n            // Normalize distance (max distance is ~441 for RGB)\n            const similarity = 1 - distance / 441;\n            maxSimilarity = Math.max(maxSimilarity, similarity);\n        });\n        return maxSimilarity;\n    }\n    /**\n   * Convert hex color to RGB\n   */ static hexToRgb(hex) {\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16)\n        } : null;\n    }\n    /**\n   * Get style description and tips\n   */ static getStyleGuide(style) {\n        const guides = {\n            modern: {\n                description: 'Clean lines, minimal clutter, and a focus on function over form.',\n                tips: [\n                    'Use neutral colors with bold accent pieces',\n                    'Choose furniture with geometric shapes',\n                    'Minimize decorative elements',\n                    'Focus on quality over quantity'\n                ],\n                doAndDonts: {\n                    dos: [\n                        'Use glass and metal materials',\n                        'Keep surfaces clutter-free',\n                        'Choose statement lighting'\n                    ],\n                    donts: [\n                        'Mix too many patterns',\n                        'Use ornate decorations',\n                        'Overcrowd the space'\n                    ]\n                }\n            },\n            minimalist: {\n                description: 'Less is more philosophy with emphasis on simplicity and functionality.',\n                tips: [\n                    'Stick to a neutral color palette',\n                    'Choose multi-functional furniture',\n                    'Keep decorations to a minimum',\n                    'Focus on natural light'\n                ],\n                doAndDonts: {\n                    dos: [\n                        'Use hidden storage',\n                        'Choose quality basics',\n                        'Embrace negative space'\n                    ],\n                    donts: [\n                        'Display too many items',\n                        'Use bold patterns',\n                        'Add unnecessary furniture'\n                    ]\n                }\n            }\n        };\n        return guides[style] || {\n            description: 'A unique style with its own characteristics.',\n            tips: [\n                'Follow your personal taste',\n                'Mix and match thoughtfully'\n            ],\n            doAndDonts: {\n                dos: [\n                    'Be creative'\n                ],\n                donts: [\n                    'Overthink it'\n                ]\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/style-matcher.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Frecommendations%2Froute&page=%2Fapi%2Frecommendations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Frecommendations%2Froute.ts&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();