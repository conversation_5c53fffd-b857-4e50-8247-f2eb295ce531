/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGTXklMjBiaWclMjBpZGVhJTJGaW50ZXJpb3ItZGVzaWduLWFpJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnRhaGFmYXJvb3F1aSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZNeSUyMGJpZyUyMGlkZWElMkZpbnRlcmlvci1kZXNpZ24tYWklMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGTXklMjBiaWclMjBpZGVhJTJGaW50ZXJpb3ItZGVzaWduLWFpJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTBLO0FBQzFLO0FBQ0EsME9BQTZLO0FBQzdLO0FBQ0EsME9BQTZLO0FBQzdLO0FBQ0Esb1JBQWtNO0FBQ2xNO0FBQ0Esd09BQTRLO0FBQzVLO0FBQ0EsNFBBQXNMO0FBQ3RMO0FBQ0Esa1FBQXlMO0FBQ3pMO0FBQ0Esc1FBQTJMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGdGFoYWZhcm9vcXVpJTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRk15JTIwYmlnJTIwaWRlYSUyRmludGVyaW9yLWRlc2lnbi1haSUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _components_RoomConfiguration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/RoomConfiguration */ \"(ssr)/./src/components/RoomConfiguration.tsx\");\n/* harmony import */ var _components_RoomAnalysis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/RoomAnalysis */ \"(ssr)/./src/components/RoomAnalysis.tsx\");\n/* harmony import */ var _components_RecommendationDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RecommendationDisplay */ \"(ssr)/./src/components/RecommendationDisplay.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('upload');\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleImageUpload = (uploadData)=>{\n        setRoom((prev)=>({\n                ...prev,\n                id: uploadData.id,\n                imageUrl: uploadData.url,\n                name: `Room ${uploadData.id}`\n            }));\n        setCurrentStep('configure');\n    };\n    const handleConfiguration = (config)=>{\n        setRoom((prev)=>({\n                ...prev,\n                type: config.roomType,\n                budget: config.budget,\n                style: config.style\n            }));\n        setCurrentStep('analyze');\n    };\n    const handleAnalysisComplete = (analysisResult)=>{\n        setRoom((prev)=>({\n                ...prev,\n                analysisResult: analysisResult.analysis\n            }));\n        setCurrentStep('recommendations');\n    };\n    const handleStartOver = ()=>{\n        setRoom({});\n        setCurrentStep('upload');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_6__.Header, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-4\",\n                            children: [\n                                {\n                                    step: 'upload',\n                                    label: 'Upload Photo',\n                                    icon: '📸'\n                                },\n                                {\n                                    step: 'configure',\n                                    label: 'Configure Room',\n                                    icon: '⚙️'\n                                },\n                                {\n                                    step: 'analyze',\n                                    label: 'AI Analysis',\n                                    icon: '🤖'\n                                },\n                                {\n                                    step: 'recommendations',\n                                    label: 'Recommendations',\n                                    icon: '✨'\n                                }\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `\n                  flex items-center justify-center w-12 h-12 rounded-full text-lg\n                  ${currentStep === item.step ? 'bg-blue-600 text-white' : index < [\n                                                'upload',\n                                                'configure',\n                                                'analyze',\n                                                'recommendations'\n                                            ].indexOf(currentStep) ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'}\n                `,\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `\n                  ml-2 text-sm font-medium\n                  ${currentStep === item.step ? 'text-blue-600' : 'text-gray-500'}\n                `,\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this),\n                                        index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `\n                    w-8 h-0.5 mx-4\n                    ${index < [\n                                                'upload',\n                                                'configure',\n                                                'analyze',\n                                                'recommendations'\n                                            ].indexOf(currentStep) ? 'bg-green-500' : 'bg-gray-200'}\n                  `\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.step, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            currentStep === 'upload' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                        children: \"Transform Your Space with AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8\",\n                                        children: \"Upload a photo of your room and get personalized furniture recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_2__.ImageUpload, {\n                                        onUpload: handleImageUpload,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 'configure' && room.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoomConfiguration__WEBPACK_IMPORTED_MODULE_3__.RoomConfiguration, {\n                                imageUrl: room.imageUrl,\n                                onConfiguration: handleConfiguration,\n                                onBack: ()=>setCurrentStep('upload')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 'analyze' && room.type && room.budget && room.style && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoomAnalysis__WEBPACK_IMPORTED_MODULE_4__.RoomAnalysis, {\n                                room: room,\n                                onAnalysisComplete: handleAnalysisComplete,\n                                onBack: ()=>setCurrentStep('configure')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            currentStep === 'recommendations' && room.analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RecommendationDisplay__WEBPACK_IMPORTED_MODULE_5__.RecommendationDisplay, {\n                                room: room,\n                                onStartOver: handleStartOver,\n                                onBack: ()=>setCurrentStep('analyze')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__.Footer, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUVpQztBQUNzQjtBQUNZO0FBQ1Y7QUFDa0I7QUFDOUI7QUFDQTtBQUc5QixTQUFTTztJQUN0QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR1QsK0NBQVFBLENBQXlEO0lBQ3ZHLE1BQU0sQ0FBQ1UsTUFBTUMsUUFBUSxHQUFHWCwrQ0FBUUEsQ0FBZ0IsQ0FBQztJQUNqRCxNQUFNLENBQUNZLFdBQVdDLGFBQWEsR0FBR2IsK0NBQVFBLENBQUM7SUFFM0MsTUFBTWMsb0JBQW9CLENBQUNDO1FBQ3pCSixRQUFRSyxDQUFBQSxPQUFTO2dCQUNmLEdBQUdBLElBQUk7Z0JBQ1BDLElBQUlGLFdBQVdFLEVBQUU7Z0JBQ2pCQyxVQUFVSCxXQUFXSSxHQUFHO2dCQUN4QkMsTUFBTSxDQUFDLEtBQUssRUFBRUwsV0FBV0UsRUFBRSxFQUFFO1lBQy9CO1FBQ0FSLGVBQWU7SUFDakI7SUFFQSxNQUFNWSxzQkFBc0IsQ0FBQ0M7UUFLM0JYLFFBQVFLLENBQUFBLE9BQVM7Z0JBQ2YsR0FBR0EsSUFBSTtnQkFDUE8sTUFBTUQsT0FBT0UsUUFBUTtnQkFDckJDLFFBQVFILE9BQU9HLE1BQU07Z0JBQ3JCQyxPQUFPSixPQUFPSSxLQUFLO1lBQ3JCO1FBQ0FqQixlQUFlO0lBQ2pCO0lBRUEsTUFBTWtCLHlCQUF5QixDQUFDQztRQUM5QmpCLFFBQVFLLENBQUFBLE9BQVM7Z0JBQ2YsR0FBR0EsSUFBSTtnQkFDUFksZ0JBQWdCQSxlQUFlQyxRQUFRO1lBQ3pDO1FBQ0FwQixlQUFlO0lBQ2pCO0lBRUEsTUFBTXFCLGtCQUFrQjtRQUN0Qm5CLFFBQVEsQ0FBQztRQUNURixlQUFlO0lBQ2pCO0lBRUEscUJBQ0UsOERBQUNzQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQzNCLHNEQUFNQTs7Ozs7MEJBRVAsOERBQUM0QjtnQkFBS0QsV0FBVTs7a0NBRWQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjtnQ0FDQztvQ0FBRUUsTUFBTTtvQ0FBVUMsT0FBTztvQ0FBZ0JDLE1BQU07Z0NBQUs7Z0NBQ3BEO29DQUFFRixNQUFNO29DQUFhQyxPQUFPO29DQUFrQkMsTUFBTTtnQ0FBSztnQ0FDekQ7b0NBQUVGLE1BQU07b0NBQVdDLE9BQU87b0NBQWVDLE1BQU07Z0NBQUs7Z0NBQ3BEO29DQUFFRixNQUFNO29DQUFtQkMsT0FBTztvQ0FBbUJDLE1BQU07Z0NBQUk7NkJBQ2hFLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDWCw4REFBQ1I7b0NBQW9CQyxXQUFVOztzREFDN0IsOERBQUNEOzRDQUFJQyxXQUFXLENBQUM7O2tCQUVmLEVBQUV4QixnQkFBZ0I4QixLQUFLSixJQUFJLEdBQ3ZCLDJCQUNBSyxRQUFRO2dEQUFDO2dEQUFVO2dEQUFhO2dEQUFXOzZDQUFrQixDQUFDQyxPQUFPLENBQUNoQyxlQUN0RSw0QkFDQSw0QkFDSDtnQkFDSCxDQUFDO3NEQUNFOEIsS0FBS0YsSUFBSTs7Ozs7O3NEQUVaLDhEQUFDSzs0Q0FBS1QsV0FBVyxDQUFDOztrQkFFaEIsRUFBRXhCLGdCQUFnQjhCLEtBQUtKLElBQUksR0FBRyxrQkFBa0IsZ0JBQWdCO2dCQUNsRSxDQUFDO3NEQUNFSSxLQUFLSCxLQUFLOzs7Ozs7d0NBRVpJLFFBQVEsbUJBQ1AsOERBQUNSOzRDQUFJQyxXQUFXLENBQUM7O29CQUVmLEVBQUVPLFFBQVE7Z0RBQUM7Z0RBQVU7Z0RBQWE7Z0RBQVc7NkNBQWtCLENBQUNDLE9BQU8sQ0FBQ2hDLGVBQ3BFLGlCQUNBLGNBQ0g7a0JBQ0gsQ0FBQzs7Ozs7OzttQ0F6Qks4QixLQUFLSixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7a0NBaUN6Qiw4REFBQ0g7d0JBQUlDLFdBQVU7OzRCQUNaeEIsZ0JBQWdCLDBCQUNmLDhEQUFDdUI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDVTt3Q0FBR1YsV0FBVTtrREFBd0M7Ozs7OztrREFHdEQsOERBQUNXO3dDQUFFWCxXQUFVO2tEQUE2Qjs7Ozs7O2tEQUcxQyw4REFBQy9CLGdFQUFXQTt3Q0FBQzJDLFVBQVU5Qjt3Q0FBbUJGLFdBQVdBOzs7Ozs7Ozs7Ozs7NEJBSXhESixnQkFBZ0IsZUFBZUUsS0FBS1EsUUFBUSxrQkFDM0MsOERBQUNoQiw0RUFBaUJBO2dDQUNoQmdCLFVBQVVSLEtBQUtRLFFBQVE7Z0NBQ3ZCMkIsaUJBQWlCeEI7Z0NBQ2pCeUIsUUFBUSxJQUFNckMsZUFBZTs7Ozs7OzRCQUloQ0QsZ0JBQWdCLGFBQWFFLEtBQUthLElBQUksSUFBSWIsS0FBS2UsTUFBTSxJQUFJZixLQUFLZ0IsS0FBSyxrQkFDbEUsOERBQUN2QixrRUFBWUE7Z0NBQ1hPLE1BQU1BO2dDQUNOcUMsb0JBQW9CcEI7Z0NBQ3BCbUIsUUFBUSxJQUFNckMsZUFBZTs7Ozs7OzRCQUloQ0QsZ0JBQWdCLHFCQUFxQkUsS0FBS2tCLGNBQWMsa0JBQ3ZELDhEQUFDeEIsb0ZBQXFCQTtnQ0FDcEJNLE1BQU1BO2dDQUNOc0MsYUFBYWxCO2dDQUNiZ0IsUUFBUSxJQUFNckMsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1yQyw4REFBQ0gsc0RBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL015IGJpZyBpZGVhL2ludGVyaW9yLWRlc2lnbi1haS9zcmMvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBJbWFnZVVwbG9hZCB9IGZyb20gJ0AvY29tcG9uZW50cy9JbWFnZVVwbG9hZCc7XG5pbXBvcnQgeyBSb29tQ29uZmlndXJhdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9Sb29tQ29uZmlndXJhdGlvbic7XG5pbXBvcnQgeyBSb29tQW5hbHlzaXMgfSBmcm9tICdAL2NvbXBvbmVudHMvUm9vbUFuYWx5c2lzJztcbmltcG9ydCB7IFJlY29tbWVuZGF0aW9uRGlzcGxheSB9IGZyb20gJ0AvY29tcG9uZW50cy9SZWNvbW1lbmRhdGlvbkRpc3BsYXknO1xuaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcic7XG5pbXBvcnQgeyBGb290ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvRm9vdGVyJztcbmltcG9ydCB7IFJvb20sIFJvb21UeXBlLCBEZXNpZ25TdHlsZSB9IGZyb20gJ0AvdHlwZXMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlPCd1cGxvYWQnIHwgJ2NvbmZpZ3VyZScgfCAnYW5hbHl6ZScgfCAncmVjb21tZW5kYXRpb25zJz4oJ3VwbG9hZCcpO1xuICBjb25zdCBbcm9vbSwgc2V0Um9vbV0gPSB1c2VTdGF0ZTxQYXJ0aWFsPFJvb20+Pih7fSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSAodXBsb2FkRGF0YTogYW55KSA9PiB7XG4gICAgc2V0Um9vbShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgaWQ6IHVwbG9hZERhdGEuaWQsXG4gICAgICBpbWFnZVVybDogdXBsb2FkRGF0YS51cmwsXG4gICAgICBuYW1lOiBgUm9vbSAke3VwbG9hZERhdGEuaWR9YFxuICAgIH0pKTtcbiAgICBzZXRDdXJyZW50U3RlcCgnY29uZmlndXJlJyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ29uZmlndXJhdGlvbiA9IChjb25maWc6IHtcbiAgICByb29tVHlwZTogUm9vbVR5cGU7XG4gICAgYnVkZ2V0OiBudW1iZXI7XG4gICAgc3R5bGU6IERlc2lnblN0eWxlO1xuICB9KSA9PiB7XG4gICAgc2V0Um9vbShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgdHlwZTogY29uZmlnLnJvb21UeXBlLFxuICAgICAgYnVkZ2V0OiBjb25maWcuYnVkZ2V0LFxuICAgICAgc3R5bGU6IGNvbmZpZy5zdHlsZVxuICAgIH0pKTtcbiAgICBzZXRDdXJyZW50U3RlcCgnYW5hbHl6ZScpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFuYWx5c2lzQ29tcGxldGUgPSAoYW5hbHlzaXNSZXN1bHQ6IGFueSkgPT4ge1xuICAgIHNldFJvb20ocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFuYWx5c2lzUmVzdWx0OiBhbmFseXNpc1Jlc3VsdC5hbmFseXNpc1xuICAgIH0pKTtcbiAgICBzZXRDdXJyZW50U3RlcCgncmVjb21tZW5kYXRpb25zJyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3RhcnRPdmVyID0gKCkgPT4ge1xuICAgIHNldFJvb20oe30pO1xuICAgIHNldEN1cnJlbnRTdGVwKCd1cGxvYWQnKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdG8tYmx1ZS01MFwiPlxuICAgICAgPEhlYWRlciAvPlxuXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgICAgey8qIFByb2dyZXNzIEluZGljYXRvciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgIHsgc3RlcDogJ3VwbG9hZCcsIGxhYmVsOiAnVXBsb2FkIFBob3RvJywgaWNvbjogJ/Cfk7gnIH0sXG4gICAgICAgICAgICAgIHsgc3RlcDogJ2NvbmZpZ3VyZScsIGxhYmVsOiAnQ29uZmlndXJlIFJvb20nLCBpY29uOiAn4pqZ77iPJyB9LFxuICAgICAgICAgICAgICB7IHN0ZXA6ICdhbmFseXplJywgbGFiZWw6ICdBSSBBbmFseXNpcycsIGljb246ICfwn6SWJyB9LFxuICAgICAgICAgICAgICB7IHN0ZXA6ICdyZWNvbW1lbmRhdGlvbnMnLCBsYWJlbDogJ1JlY29tbWVuZGF0aW9ucycsIGljb246ICfinKgnIH1cbiAgICAgICAgICAgIF0ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17aXRlbS5zdGVwfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTEyIGgtMTIgcm91bmRlZC1mdWxsIHRleHQtbGdcbiAgICAgICAgICAgICAgICAgICR7Y3VycmVudFN0ZXAgPT09IGl0ZW0uc3RlcFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICA6IGluZGV4IDwgWyd1cGxvYWQnLCAnY29uZmlndXJlJywgJ2FuYWx5emUnLCAncmVjb21tZW5kYXRpb25zJ10uaW5kZXhPZihjdXJyZW50U3RlcClcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMjAwIHRleHQtZ3JheS01MDAnXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYH0+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5pY29ufVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgbWwtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtXG4gICAgICAgICAgICAgICAgICAke2N1cnJlbnRTdGVwID09PSBpdGVtLnN0ZXAgPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC1ncmF5LTUwMCd9XG4gICAgICAgICAgICAgICAgYH0+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAge2luZGV4IDwgMyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgICB3LTggaC0wLjUgbXgtNFxuICAgICAgICAgICAgICAgICAgICAke2luZGV4IDwgWyd1cGxvYWQnLCAnY29uZmlndXJlJywgJ2FuYWx5emUnLCAncmVjb21tZW5kYXRpb25zJ10uaW5kZXhPZihjdXJyZW50U3RlcClcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0yMDAnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGB9IC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN0ZXAgQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ3VwbG9hZCcgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIFRyYW5zZm9ybSBZb3VyIFNwYWNlIHdpdGggQUlcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIG1iLThcIj5cbiAgICAgICAgICAgICAgICBVcGxvYWQgYSBwaG90byBvZiB5b3VyIHJvb20gYW5kIGdldCBwZXJzb25hbGl6ZWQgZnVybml0dXJlIHJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxJbWFnZVVwbG9hZCBvblVwbG9hZD17aGFuZGxlSW1hZ2VVcGxvYWR9IGlzTG9hZGluZz17aXNMb2FkaW5nfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ2NvbmZpZ3VyZScgJiYgcm9vbS5pbWFnZVVybCAmJiAoXG4gICAgICAgICAgICA8Um9vbUNvbmZpZ3VyYXRpb25cbiAgICAgICAgICAgICAgaW1hZ2VVcmw9e3Jvb20uaW1hZ2VVcmx9XG4gICAgICAgICAgICAgIG9uQ29uZmlndXJhdGlvbj17aGFuZGxlQ29uZmlndXJhdGlvbn1cbiAgICAgICAgICAgICAgb25CYWNrPXsoKSA9PiBzZXRDdXJyZW50U3RlcCgndXBsb2FkJyl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09ICdhbmFseXplJyAmJiByb29tLnR5cGUgJiYgcm9vbS5idWRnZXQgJiYgcm9vbS5zdHlsZSAmJiAoXG4gICAgICAgICAgICA8Um9vbUFuYWx5c2lzXG4gICAgICAgICAgICAgIHJvb209e3Jvb20gYXMgUm9vbX1cbiAgICAgICAgICAgICAgb25BbmFseXNpc0NvbXBsZXRlPXtoYW5kbGVBbmFseXNpc0NvbXBsZXRlfVxuICAgICAgICAgICAgICBvbkJhY2s9eygpID0+IHNldEN1cnJlbnRTdGVwKCdjb25maWd1cmUnKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ3JlY29tbWVuZGF0aW9ucycgJiYgcm9vbS5hbmFseXNpc1Jlc3VsdCAmJiAoXG4gICAgICAgICAgICA8UmVjb21tZW5kYXRpb25EaXNwbGF5XG4gICAgICAgICAgICAgIHJvb209e3Jvb20gYXMgUm9vbX1cbiAgICAgICAgICAgICAgb25TdGFydE92ZXI9e2hhbmRsZVN0YXJ0T3Zlcn1cbiAgICAgICAgICAgICAgb25CYWNrPXsoKSA9PiBzZXRDdXJyZW50U3RlcCgnYW5hbHl6ZScpfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cblxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiSW1hZ2VVcGxvYWQiLCJSb29tQ29uZmlndXJhdGlvbiIsIlJvb21BbmFseXNpcyIsIlJlY29tbWVuZGF0aW9uRGlzcGxheSIsIkhlYWRlciIsIkZvb3RlciIsIkhvbWUiLCJjdXJyZW50U3RlcCIsInNldEN1cnJlbnRTdGVwIiwicm9vbSIsInNldFJvb20iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJoYW5kbGVJbWFnZVVwbG9hZCIsInVwbG9hZERhdGEiLCJwcmV2IiwiaWQiLCJpbWFnZVVybCIsInVybCIsIm5hbWUiLCJoYW5kbGVDb25maWd1cmF0aW9uIiwiY29uZmlnIiwidHlwZSIsInJvb21UeXBlIiwiYnVkZ2V0Iiwic3R5bGUiLCJoYW5kbGVBbmFseXNpc0NvbXBsZXRlIiwiYW5hbHlzaXNSZXN1bHQiLCJhbmFseXNpcyIsImhhbmRsZVN0YXJ0T3ZlciIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJzdGVwIiwibGFiZWwiLCJpY29uIiwibWFwIiwiaXRlbSIsImluZGV4IiwiaW5kZXhPZiIsInNwYW4iLCJoMSIsInAiLCJvblVwbG9hZCIsIm9uQ29uZmlndXJhdGlvbiIsIm9uQmFjayIsIm9uQW5hbHlzaXNDb21wbGV0ZSIsIm9uU3RhcnRPdmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Home,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Home,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Home,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Home,Linkedin,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold\",\n                                            children: \"Interior AI\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6 max-w-md\",\n                                    children: \"Transform your living space with AI-powered interior design recommendations. Get personalized furniture suggestions that match your style and budget.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            \"aria-label\": \"Twitter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            \"aria-label\": \"LinkedIn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                            \"aria-label\": \"GitHub\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Home_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"How it Works\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"API\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Company\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Careers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"\\xa9 2024 Interior AI. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                    children: \"Cookie Policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Interior AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                    children: \"How it Works\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                    children: \"Examples\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2\",\n                                children: \"How it Works\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2\",\n                                children: \"Examples\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full text-left text-gray-600 hover:text-blue-600 transition-colors font-medium py-2\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium mt-2\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUpload: () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(ssr)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ ImageUpload auto */ \n\n\n\nfunction ImageUpload({ onUpload, isLoading }) {\n    const [preview, setPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageUpload.useCallback[onDrop]\": async (acceptedFiles)=>{\n            const file = acceptedFiles[0];\n            if (!file) return;\n            // Reset states\n            setError(null);\n            setUploadProgress(0);\n            // Validate file\n            if (!file.type.startsWith('image/')) {\n                setError('Please upload an image file');\n                return;\n            }\n            if (file.size > 10 * 1024 * 1024) {\n                setError('Image must be smaller than 10MB');\n                return;\n            }\n            // Create preview\n            const previewUrl = URL.createObjectURL(file);\n            setPreview(previewUrl);\n            try {\n                // Simulate upload progress\n                const progressInterval = setInterval({\n                    \"ImageUpload.useCallback[onDrop].progressInterval\": ()=>{\n                        setUploadProgress({\n                            \"ImageUpload.useCallback[onDrop].progressInterval\": (prev)=>{\n                                if (prev >= 90) {\n                                    clearInterval(progressInterval);\n                                    return 90;\n                                }\n                                return prev + 10;\n                            }\n                        }[\"ImageUpload.useCallback[onDrop].progressInterval\"]);\n                    }\n                }[\"ImageUpload.useCallback[onDrop].progressInterval\"], 100);\n                // Upload file\n                const formData = new FormData();\n                formData.append('image', file);\n                const response = await fetch('/api/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                clearInterval(progressInterval);\n                setUploadProgress(100);\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Upload failed');\n                }\n                const result = await response.json();\n                if (result.success) {\n                    onUpload(result.data);\n                } else {\n                    throw new Error(result.error || 'Upload failed');\n                }\n            } catch (err) {\n                setError(err instanceof Error ? err.message : 'Upload failed');\n                setPreview(null);\n                setUploadProgress(0);\n            }\n        }\n    }[\"ImageUpload.useCallback[onDrop]\"], [\n        onUpload\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: {\n            'image/*': [\n                '.jpeg',\n                '.jpg',\n                '.png',\n                '.webp'\n            ]\n        },\n        maxFiles: 1,\n        disabled: isLoading\n    });\n    const clearPreview = ()=>{\n        setPreview(null);\n        setError(null);\n        setUploadProgress(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-2xl mx-auto\",\n        children: [\n            !preview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n            ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'}\n            ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}\n          `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: isDragActive ? 'Drop your image here' : 'Upload a room photo'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Drag and drop an image, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Supports JPEG, PNG, WebP up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                disabled: isLoading,\n                                children: \"Choose File\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: preview,\n                                alt: \"Room preview\",\n                                className: \"w-full h-64 object-cover rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearPreview,\n                                className: \"absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    uploadProgress > 0 && uploadProgress < 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Uploading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${uploadProgress}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this),\n                    uploadProgress === 100 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-green-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-600 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Upload complete!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800 font-medium\",\n                                children: \"Upload Error\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-700 mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearPreview,\n                        className: \"mt-2 text-red-600 hover:text-red-800 text-sm font-medium\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCF8 Tips for best results:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-sm text-blue-800 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Take photos in good lighting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Include the entire room in the frame\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Avoid cluttered or messy spaces\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Make sure furniture and walls are clearly visible\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RecommendationDisplay.tsx":
/*!**************************************************!*\
  !*** ./src/components/RecommendationDisplay.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationDisplay: () => (/* binding */ RecommendationDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ExternalLink,Filter,Grid,Heart,List,RefreshCw,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ RecommendationDisplay auto */ \n\n\nfunction RecommendationDisplay({ room, onStartOver, onBack }) {\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [wishlist, setWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [summary, setSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecommendationDisplay.useEffect\": ()=>{\n            generateRecommendations();\n        }\n    }[\"RecommendationDisplay.useEffect\"], []);\n    const generateRecommendations = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/recommendations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    roomAnalysis: room.analysisResult,\n                    roomType: room.type,\n                    budget: room.budget,\n                    style: room.style\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to generate recommendations');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setRecommendations(result.data.recommendations);\n                setSummary(result.data.summary);\n            } else {\n                throw new Error(result.error || 'Failed to generate recommendations');\n            }\n        } catch (error) {\n            console.error('Recommendations error:', error);\n            // Mock recommendations for demo\n            const mockRecommendations = [\n                {\n                    id: 'sofa-1',\n                    name: 'Modern 3-Seat Sofa',\n                    category: 'seating',\n                    description: 'Comfortable modern sofa with clean lines',\n                    price: 899,\n                    currency: 'USD',\n                    imageUrl: '/api/placeholder/400/300',\n                    productUrl: '#',\n                    retailer: 'ikea',\n                    dimensions: {\n                        width: 228,\n                        height: 83,\n                        depth: 95\n                    },\n                    style: [\n                        'modern',\n                        'contemporary'\n                    ],\n                    placement: {\n                        suggestedPosition: {\n                            x: 200,\n                            y: 300\n                        },\n                        zone: 'seating-area'\n                    },\n                    compatibility: {\n                        roomType: [\n                            room.type\n                        ],\n                        existingFurniture: [],\n                        styleMatch: 0.9\n                    }\n                },\n                {\n                    id: 'table-1',\n                    name: 'Glass Coffee Table',\n                    category: 'tables',\n                    description: 'Sleek glass coffee table with metal legs',\n                    price: 299,\n                    currency: 'USD',\n                    imageUrl: '/api/placeholder/400/300',\n                    productUrl: '#',\n                    retailer: 'amazon',\n                    dimensions: {\n                        width: 120,\n                        height: 45,\n                        depth: 60\n                    },\n                    style: [\n                        'modern',\n                        'contemporary'\n                    ],\n                    placement: {\n                        suggestedPosition: {\n                            x: 280,\n                            y: 420\n                        },\n                        zone: 'seating-area'\n                    },\n                    compatibility: {\n                        roomType: [\n                            room.type\n                        ],\n                        existingFurniture: [],\n                        styleMatch: 0.85\n                    }\n                }\n            ];\n            setRecommendations(mockRecommendations);\n            setSummary({\n                totalItems: 2,\n                totalCost: 1198,\n                budget: room.budget,\n                budgetUtilization: 1198 / room.budget * 100,\n                remainingBudget: room.budget - 1198\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleWishlist = (itemId)=>{\n        const newWishlist = new Set(wishlist);\n        if (newWishlist.has(itemId)) {\n            newWishlist.delete(itemId);\n        } else {\n            newWishlist.add(itemId);\n        }\n        setWishlist(newWishlist);\n    };\n    const categories = [\n        'all',\n        ...Array.from(new Set(recommendations.map((r)=>r.category)))\n    ];\n    const filteredRecommendations = selectedCategory === 'all' ? recommendations : recommendations.filter((r)=>r.category === selectedCategory);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-8 h-8 text-blue-600 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Generating Recommendations\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Finding the perfect furniture for your space...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: \"Your Personalized Recommendations\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Based on your \",\n                            room.type?.replace('_', ' '),\n                            \" in \",\n                            room.style,\n                            \" style with a $\",\n                            room.budget,\n                            \" budget\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-900\",\n                                    children: summary.totalItems\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Items\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-900\",\n                                    children: [\n                                        \"$\",\n                                        summary.totalCost\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Total Cost\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-900\",\n                                    children: [\n                                        Math.round(summary.budgetUtilization),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Budget Used\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-900\",\n                                    children: [\n                                        \"$\",\n                                        summary.remainingBudget\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: \"Remaining\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCategory,\n                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                className: \"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: category,\n                                        children: category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)\n                                    }, category, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('grid'),\n                                className: `p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('list'),\n                                className: `p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}\n      `,\n                children: filteredRecommendations.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n              bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow\n              ${viewMode === 'list' ? 'flex' : ''}\n            `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${viewMode === 'list' ? 'w-48 flex-shrink-0' : 'aspect-square'} bg-gray-100`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.imageUrl,\n                                    alt: item.name,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-gray-900 line-clamp-2\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleWishlist(item.id),\n                                                className: `p-1 rounded-full ${wishlist.has(item.id) ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: `w-5 h-5 ${wishlist.has(item.id) ? 'fill-current' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: [\n                                                    \"$\",\n                                                    item.price\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 capitalize\",\n                                                children: item.retailer\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-3\",\n                                        children: item.style.slice(0, 2).map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full\",\n                                                children: style\n                                            }, style, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open(item.productUrl, '_blank'),\n                                                className: \"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, item.id, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onStartOver,\n                        className: \"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ExternalLink_Filter_Grid_Heart_List_RefreshCw_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Start New Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9SZWNvbW1lbmRhdGlvbkRpc3BsYXkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQytEO0FBU3BHLFNBQVNVLHNCQUFzQixFQUFFQyxJQUFJLEVBQUVDLFdBQVcsRUFBRUMsTUFBTSxFQUE4QjtJQUM3RixNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdmLCtDQUFRQSxDQUE0QixFQUFFO0lBQ3BGLE1BQU0sQ0FBQ2dCLFdBQVdDLGFBQWEsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2tCLFVBQVVDLFlBQVksR0FBR25CLCtDQUFRQSxDQUFrQjtJQUMxRCxNQUFNLENBQUNvQixrQkFBa0JDLG9CQUFvQixHQUFHckIsK0NBQVFBLENBQVM7SUFDakUsTUFBTSxDQUFDc0IsVUFBVUMsWUFBWSxHQUFHdkIsK0NBQVFBLENBQWMsSUFBSXdCO0lBQzFELE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHMUIsK0NBQVFBLENBQU07SUFFNUNDLGdEQUFTQTsyQ0FBQztZQUNSMEI7UUFDRjswQ0FBRyxFQUFFO0lBRUwsTUFBTUEsMEJBQTBCO1FBQzlCVixhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU1XLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25EQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLGNBQWN4QixLQUFLeUIsY0FBYztvQkFDakNDLFVBQVUxQixLQUFLMkIsSUFBSTtvQkFDbkJDLFFBQVE1QixLQUFLNEIsTUFBTTtvQkFDbkJDLE9BQU83QixLQUFLNkIsS0FBSztnQkFDbkI7WUFDRjtZQUVBLElBQUksQ0FBQ1osU0FBU2EsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxTQUFTLE1BQU1mLFNBQVNnQixJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEI5QixtQkFBbUI0QixPQUFPRyxJQUFJLENBQUNoQyxlQUFlO2dCQUM5Q1ksV0FBV2lCLE9BQU9HLElBQUksQ0FBQ3JCLE9BQU87WUFDaEMsT0FBTztnQkFDTCxNQUFNLElBQUlpQixNQUFNQyxPQUFPSSxLQUFLLElBQUk7WUFDbEM7UUFFRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEMsZ0NBQWdDO1lBQ2hDLE1BQU1FLHNCQUFpRDtnQkFDckQ7b0JBQ0VDLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLGFBQWE7b0JBQ2JDLE9BQU87b0JBQ1BDLFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFlBQVk7b0JBQ1pDLFVBQVU7b0JBQ1ZDLFlBQVk7d0JBQUVDLE9BQU87d0JBQUtDLFFBQVE7d0JBQUlDLE9BQU87b0JBQUc7b0JBQ2hEdEIsT0FBTzt3QkFBQzt3QkFBVTtxQkFBZTtvQkFDakN1QixXQUFXO3dCQUNUQyxtQkFBbUI7NEJBQUVDLEdBQUc7NEJBQUtDLEdBQUc7d0JBQUk7d0JBQ3BDQyxNQUFNO29CQUNSO29CQUNBQyxlQUFlO3dCQUNiL0IsVUFBVTs0QkFBQzFCLEtBQUsyQixJQUFJO3lCQUFFO3dCQUN0QitCLG1CQUFtQixFQUFFO3dCQUNyQkMsWUFBWTtvQkFDZDtnQkFDRjtnQkFDQTtvQkFDRXBCLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLGFBQWE7b0JBQ2JDLE9BQU87b0JBQ1BDLFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFlBQVk7b0JBQ1pDLFVBQVU7b0JBQ1ZDLFlBQVk7d0JBQUVDLE9BQU87d0JBQUtDLFFBQVE7d0JBQUlDLE9BQU87b0JBQUc7b0JBQ2hEdEIsT0FBTzt3QkFBQzt3QkFBVTtxQkFBZTtvQkFDakN1QixXQUFXO3dCQUNUQyxtQkFBbUI7NEJBQUVDLEdBQUc7NEJBQUtDLEdBQUc7d0JBQUk7d0JBQ3BDQyxNQUFNO29CQUNSO29CQUNBQyxlQUFlO3dCQUNiL0IsVUFBVTs0QkFBQzFCLEtBQUsyQixJQUFJO3lCQUFFO3dCQUN0QitCLG1CQUFtQixFQUFFO3dCQUNyQkMsWUFBWTtvQkFDZDtnQkFDRjthQUNEO1lBQ0R2RCxtQkFBbUJrQztZQUNuQnZCLFdBQVc7Z0JBQ1Q2QyxZQUFZO2dCQUNaQyxXQUFXO2dCQUNYakMsUUFBUTVCLEtBQUs0QixNQUFNO2dCQUNuQmtDLG1CQUFtQixPQUFROUQsS0FBSzRCLE1BQU0sR0FBSztnQkFDM0NtQyxpQkFBaUIvRCxLQUFLNEIsTUFBTSxHQUFJO1lBQ2xDO1FBQ0YsU0FBVTtZQUNSdEIsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNMEQsaUJBQWlCLENBQUNDO1FBQ3RCLE1BQU1DLGNBQWMsSUFBSXJELElBQUlGO1FBQzVCLElBQUl1RCxZQUFZQyxHQUFHLENBQUNGLFNBQVM7WUFDM0JDLFlBQVlFLE1BQU0sQ0FBQ0g7UUFDckIsT0FBTztZQUNMQyxZQUFZRyxHQUFHLENBQUNKO1FBQ2xCO1FBQ0FyRCxZQUFZc0Q7SUFDZDtJQUVBLE1BQU1JLGFBQWE7UUFBQztXQUFVQyxNQUFNQyxJQUFJLENBQUMsSUFBSTNELElBQUlWLGdCQUFnQnNFLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWpDLFFBQVE7S0FBSTtJQUN4RixNQUFNa0MsMEJBQTBCbEUscUJBQXFCLFFBQ2pETixrQkFDQUEsZ0JBQWdCeUUsTUFBTSxDQUFDRixDQUFBQSxJQUFLQSxFQUFFakMsUUFBUSxLQUFLaEM7SUFFL0MsSUFBSUosV0FBVztRQUNiLHFCQUNFLDhEQUFDd0U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDdEYsZ0pBQVNBO3dCQUFDc0YsV0FBVTs7Ozs7O2tDQUNyQiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQTJDOzs7Ozs7a0NBR3pELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTXJDO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNHO3dCQUFHSCxXQUFVO2tDQUF3Qzs7Ozs7O2tDQUd0RCw4REFBQ0U7d0JBQUVGLFdBQVU7OzRCQUFnQjs0QkFDWjlFLEtBQUsyQixJQUFJLEVBQUV1RCxRQUFRLEtBQUs7NEJBQUs7NEJBQUtsRixLQUFLNkIsS0FBSzs0QkFBQzs0QkFBZ0I3QixLQUFLNEIsTUFBTTs0QkFBQzs7Ozs7Ozs7Ozs7OztZQUszRmQseUJBQ0MsOERBQUMrRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNBO29DQUFJQyxXQUFVOzhDQUFvQ2hFLFFBQVE4QyxVQUFVOzs7Ozs7OENBQ3JFLDhEQUFDaUI7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7c0NBRXpDLDhEQUFDRDs7OENBQ0MsOERBQUNBO29DQUFJQyxXQUFVOzt3Q0FBbUM7d0NBQUVoRSxRQUFRK0MsU0FBUzs7Ozs7Ozs4Q0FDckUsOERBQUNnQjtvQ0FBSUMsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FFekMsOERBQUNEOzs4Q0FDQyw4REFBQ0E7b0NBQUlDLFdBQVU7O3dDQUFvQ0ssS0FBS0MsS0FBSyxDQUFDdEUsUUFBUWdELGlCQUFpQjt3Q0FBRTs7Ozs7Ozs4Q0FDekYsOERBQUNlO29DQUFJQyxXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUV6Qyw4REFBQ0Q7OzhDQUNDLDhEQUFDQTtvQ0FBSUMsV0FBVTs7d0NBQW1DO3dDQUFFaEUsUUFBUWlELGVBQWU7Ozs7Ozs7OENBQzNFLDhEQUFDYztvQ0FBSUMsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU8vQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNsRixnSkFBTUE7Z0NBQUNrRixXQUFVOzs7Ozs7MENBQ2xCLDhEQUFDTztnQ0FDQ0MsT0FBTzdFO2dDQUNQOEUsVUFBVSxDQUFDQyxJQUFNOUUsb0JBQW9COEUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dDQUNuRFIsV0FBVTswQ0FFVFIsV0FBV0csR0FBRyxDQUFDaEMsQ0FBQUEseUJBQ2QsOERBQUNpRDt3Q0FBc0JKLE9BQU83QztrREFDM0JBLGFBQWEsUUFBUSxtQkFBbUJBLFNBQVNrRCxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLbkQsU0FBU29ELEtBQUssQ0FBQzt1Q0FEaEZwRDs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRbkIsOERBQUNvQzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNnQjtnQ0FDQ0MsU0FBUyxJQUFNdkYsWUFBWTtnQ0FDM0JzRSxXQUFXLENBQUMsZUFBZSxFQUFFdkUsYUFBYSxTQUFTLDhCQUE4QixxQ0FBcUM7MENBRXRILDRFQUFDVixnSkFBSUE7b0NBQUNpRixXQUFVOzs7Ozs7Ozs7OzswQ0FFbEIsOERBQUNnQjtnQ0FDQ0MsU0FBUyxJQUFNdkYsWUFBWTtnQ0FDM0JzRSxXQUFXLENBQUMsZUFBZSxFQUFFdkUsYUFBYSxTQUFTLDhCQUE4QixxQ0FBcUM7MENBRXRILDRFQUFDVCxnSkFBSUE7b0NBQUNnRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNdEIsOERBQUNEO2dCQUFJQyxXQUFXLENBQUM7UUFDZixFQUFFdkUsYUFBYSxTQUNYLHlEQUNBLFlBQ0g7TUFDSCxDQUFDOzBCQUNFb0Usd0JBQXdCRixHQUFHLENBQUMsQ0FBQ3VCLHFCQUM1Qiw4REFBQ25CO3dCQUVDQyxXQUFXLENBQUM7O2NBRVYsRUFBRXZFLGFBQWEsU0FBUyxTQUFTLEdBQUc7WUFDdEMsQ0FBQzs7MENBR0QsOERBQUNzRTtnQ0FBSUMsV0FBVyxHQUFHdkUsYUFBYSxTQUFTLHVCQUF1QixnQkFBZ0IsWUFBWSxDQUFDOzBDQUMzRiw0RUFBQzBGO29DQUNDQyxLQUFLRixLQUFLbkQsUUFBUTtvQ0FDbEJzRCxLQUFLSCxLQUFLeEQsSUFBSTtvQ0FDZHNDLFdBQVU7Ozs7Ozs7Ozs7OzBDQUtkLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUdELFdBQVU7MERBQTRDa0IsS0FBS3hELElBQUk7Ozs7OzswREFDbkUsOERBQUNzRDtnREFDQ0MsU0FBUyxJQUFNL0IsZUFBZWdDLEtBQUt6RCxFQUFFO2dEQUNyQ3VDLFdBQVcsQ0FBQyxpQkFBaUIsRUFDM0JuRSxTQUFTd0QsR0FBRyxDQUFDNkIsS0FBS3pELEVBQUUsSUFBSSxpQkFBaUIsb0NBQ3pDOzBEQUVGLDRFQUFDOUMsZ0pBQUtBO29EQUFDcUYsV0FBVyxDQUFDLFFBQVEsRUFBRW5FLFNBQVN3RCxHQUFHLENBQUM2QixLQUFLekQsRUFBRSxJQUFJLGlCQUFpQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztrREFJOUUsOERBQUN5Qzt3Q0FBRUYsV0FBVTtrREFBMkNrQixLQUFLdEQsV0FBVzs7Ozs7O2tEQUV4RSw4REFBQ21DO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3NCO2dEQUFLdEIsV0FBVTs7b0RBQW1DO29EQUFFa0IsS0FBS3JELEtBQUs7Ozs7Ozs7MERBQy9ELDhEQUFDeUQ7Z0RBQUt0QixXQUFVOzBEQUFvQ2tCLEtBQUtqRCxRQUFROzs7Ozs7Ozs7Ozs7a0RBR25FLDhEQUFDOEI7d0NBQUlDLFdBQVU7a0RBQ1prQixLQUFLbkUsS0FBSyxDQUFDZ0UsS0FBSyxDQUFDLEdBQUcsR0FBR3BCLEdBQUcsQ0FBQyxDQUFDNUMsc0JBQzNCLDhEQUFDdUU7Z0RBRUN0QixXQUFVOzBEQUVUakQ7K0NBSElBOzs7Ozs7Ozs7O2tEQVFYLDhEQUFDZ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZ0I7Z0RBQ0NDLFNBQVMsSUFBTU0sT0FBT0MsSUFBSSxDQUFDTixLQUFLbEQsVUFBVSxFQUFFO2dEQUM1Q2dDLFdBQVU7O2tFQUVWLDhEQUFDcEYsZ0pBQVlBO3dEQUFDb0YsV0FBVTs7Ozs7O2tFQUN4Qiw4REFBQ3NCO2tFQUFLOzs7Ozs7Ozs7Ozs7MERBRVIsOERBQUNOO2dEQUFPaEIsV0FBVTswREFDaEIsNEVBQUNuRixnSkFBWUE7b0RBQUNtRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBeER6QmtCLEtBQUt6RCxFQUFFOzs7Ozs7Ozs7OzBCQWlFbEIsOERBQUNzQztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNnQjt3QkFDQ0MsU0FBUzdGO3dCQUNUNEUsV0FBVTs7MENBRVYsOERBQUN2RixnSkFBU0E7Z0NBQUN1RixXQUFVOzs7Ozs7MENBQ3JCLDhEQUFDc0I7MENBQUs7Ozs7Ozs7Ozs7OztrQ0FHUiw4REFBQ047d0JBQ0NDLFNBQVM5Rjt3QkFDVDZFLFdBQVU7OzBDQUVWLDhEQUFDdEYsZ0pBQVNBO2dDQUFDc0YsV0FBVTs7Ozs7OzBDQUNyQiw4REFBQ3NCOzBDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLaEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvTXkgYmlnIGlkZWEvaW50ZXJpb3ItZGVzaWduLWFpL3NyYy9jb21wb25lbnRzL1JlY29tbWVuZGF0aW9uRGlzcGxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQXJyb3dMZWZ0LCBSZWZyZXNoQ3csIEhlYXJ0LCBFeHRlcm5hbExpbmssIFNob3BwaW5nQ2FydCwgRmlsdGVyLCBHcmlkLCBMaXN0IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IFJvb20sIEZ1cm5pdHVyZVJlY29tbWVuZGF0aW9uIH0gZnJvbSAnQC90eXBlcyc7XG5cbmludGVyZmFjZSBSZWNvbW1lbmRhdGlvbkRpc3BsYXlQcm9wcyB7XG4gIHJvb206IFJvb207XG4gIG9uU3RhcnRPdmVyOiAoKSA9PiB2b2lkO1xuICBvbkJhY2s6ICgpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWNvbW1lbmRhdGlvbkRpc3BsYXkoeyByb29tLCBvblN0YXJ0T3Zlciwgb25CYWNrIH06IFJlY29tbWVuZGF0aW9uRGlzcGxheVByb3BzKSB7XG4gIGNvbnN0IFtyZWNvbW1lbmRhdGlvbnMsIHNldFJlY29tbWVuZGF0aW9uc10gPSB1c2VTdGF0ZTxGdXJuaXR1cmVSZWNvbW1lbmRhdGlvbltdPihbXSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3ZpZXdNb2RlLCBzZXRWaWV3TW9kZV0gPSB1c2VTdGF0ZTwnZ3JpZCcgfCAnbGlzdCc+KCdncmlkJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpO1xuICBjb25zdCBbd2lzaGxpc3QsIHNldFdpc2hsaXN0XSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbc3VtbWFyeSwgc2V0U3VtbWFyeV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGdlbmVyYXRlUmVjb21tZW5kYXRpb25zID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9yZWNvbW1lbmRhdGlvbnMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHJvb21BbmFseXNpczogcm9vbS5hbmFseXNpc1Jlc3VsdCxcbiAgICAgICAgICByb29tVHlwZTogcm9vbS50eXBlLFxuICAgICAgICAgIGJ1ZGdldDogcm9vbS5idWRnZXQsXG4gICAgICAgICAgc3R5bGU6IHJvb20uc3R5bGUsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcmVjb21tZW5kYXRpb25zJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIHNldFJlY29tbWVuZGF0aW9ucyhyZXN1bHQuZGF0YS5yZWNvbW1lbmRhdGlvbnMpO1xuICAgICAgICBzZXRTdW1tYXJ5KHJlc3VsdC5kYXRhLnN1bW1hcnkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3VsdC5lcnJvciB8fCAnRmFpbGVkIHRvIGdlbmVyYXRlIHJlY29tbWVuZGF0aW9ucycpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1JlY29tbWVuZGF0aW9ucyBlcnJvcjonLCBlcnJvcik7XG4gICAgICAvLyBNb2NrIHJlY29tbWVuZGF0aW9ucyBmb3IgZGVtb1xuICAgICAgY29uc3QgbW9ja1JlY29tbWVuZGF0aW9uczogRnVybml0dXJlUmVjb21tZW5kYXRpb25bXSA9IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnc29mYS0xJyxcbiAgICAgICAgICBuYW1lOiAnTW9kZXJuIDMtU2VhdCBTb2ZhJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ3NlYXRpbmcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQ29tZm9ydGFibGUgbW9kZXJuIHNvZmEgd2l0aCBjbGVhbiBsaW5lcycsXG4gICAgICAgICAgcHJpY2U6IDg5OSxcbiAgICAgICAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgICAgICAgaW1hZ2VVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC8zMDAnLFxuICAgICAgICAgIHByb2R1Y3RVcmw6ICcjJyxcbiAgICAgICAgICByZXRhaWxlcjogJ2lrZWEnLFxuICAgICAgICAgIGRpbWVuc2lvbnM6IHsgd2lkdGg6IDIyOCwgaGVpZ2h0OiA4MywgZGVwdGg6IDk1IH0sXG4gICAgICAgICAgc3R5bGU6IFsnbW9kZXJuJywgJ2NvbnRlbXBvcmFyeSddLFxuICAgICAgICAgIHBsYWNlbWVudDoge1xuICAgICAgICAgICAgc3VnZ2VzdGVkUG9zaXRpb246IHsgeDogMjAwLCB5OiAzMDAgfSxcbiAgICAgICAgICAgIHpvbmU6ICdzZWF0aW5nLWFyZWEnXG4gICAgICAgICAgfSxcbiAgICAgICAgICBjb21wYXRpYmlsaXR5OiB7XG4gICAgICAgICAgICByb29tVHlwZTogW3Jvb20udHlwZSFdLFxuICAgICAgICAgICAgZXhpc3RpbmdGdXJuaXR1cmU6IFtdLFxuICAgICAgICAgICAgc3R5bGVNYXRjaDogMC45XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICd0YWJsZS0xJyxcbiAgICAgICAgICBuYW1lOiAnR2xhc3MgQ29mZmVlIFRhYmxlJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ3RhYmxlcycsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdTbGVlayBnbGFzcyBjb2ZmZWUgdGFibGUgd2l0aCBtZXRhbCBsZWdzJyxcbiAgICAgICAgICBwcmljZTogMjk5LFxuICAgICAgICAgIGN1cnJlbmN5OiAnVVNEJyxcbiAgICAgICAgICBpbWFnZVVybDogJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzMwMCcsXG4gICAgICAgICAgcHJvZHVjdFVybDogJyMnLFxuICAgICAgICAgIHJldGFpbGVyOiAnYW1hem9uJyxcbiAgICAgICAgICBkaW1lbnNpb25zOiB7IHdpZHRoOiAxMjAsIGhlaWdodDogNDUsIGRlcHRoOiA2MCB9LFxuICAgICAgICAgIHN0eWxlOiBbJ21vZGVybicsICdjb250ZW1wb3JhcnknXSxcbiAgICAgICAgICBwbGFjZW1lbnQ6IHtcbiAgICAgICAgICAgIHN1Z2dlc3RlZFBvc2l0aW9uOiB7IHg6IDI4MCwgeTogNDIwIH0sXG4gICAgICAgICAgICB6b25lOiAnc2VhdGluZy1hcmVhJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgY29tcGF0aWJpbGl0eToge1xuICAgICAgICAgICAgcm9vbVR5cGU6IFtyb29tLnR5cGUhXSxcbiAgICAgICAgICAgIGV4aXN0aW5nRnVybml0dXJlOiBbXSxcbiAgICAgICAgICAgIHN0eWxlTWF0Y2g6IDAuODVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIF07XG4gICAgICBzZXRSZWNvbW1lbmRhdGlvbnMobW9ja1JlY29tbWVuZGF0aW9ucyk7XG4gICAgICBzZXRTdW1tYXJ5KHtcbiAgICAgICAgdG90YWxJdGVtczogMixcbiAgICAgICAgdG90YWxDb3N0OiAxMTk4LFxuICAgICAgICBidWRnZXQ6IHJvb20uYnVkZ2V0LFxuICAgICAgICBidWRnZXRVdGlsaXphdGlvbjogKDExOTggLyByb29tLmJ1ZGdldCEpICogMTAwLFxuICAgICAgICByZW1haW5pbmdCdWRnZXQ6IHJvb20uYnVkZ2V0ISAtIDExOThcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB0b2dnbGVXaXNobGlzdCA9IChpdGVtSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5ld1dpc2hsaXN0ID0gbmV3IFNldCh3aXNobGlzdCk7XG4gICAgaWYgKG5ld1dpc2hsaXN0LmhhcyhpdGVtSWQpKSB7XG4gICAgICBuZXdXaXNobGlzdC5kZWxldGUoaXRlbUlkKTtcbiAgICB9IGVsc2Uge1xuICAgICAgbmV3V2lzaGxpc3QuYWRkKGl0ZW1JZCk7XG4gICAgfVxuICAgIHNldFdpc2hsaXN0KG5ld1dpc2hsaXN0KTtcbiAgfTtcblxuICBjb25zdCBjYXRlZ29yaWVzID0gWydhbGwnLCAuLi5BcnJheS5mcm9tKG5ldyBTZXQocmVjb21tZW5kYXRpb25zLm1hcChyID0+IHIuY2F0ZWdvcnkpKSldO1xuICBjb25zdCBmaWx0ZXJlZFJlY29tbWVuZGF0aW9ucyA9IHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdhbGwnIFxuICAgID8gcmVjb21tZW5kYXRpb25zIFxuICAgIDogcmVjb21tZW5kYXRpb25zLmZpbHRlcihyID0+IHIuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnkpO1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweS0xMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtYmx1ZS02MDAgYW5pbWF0ZS1zcGluIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgIEdlbmVyYXRpbmcgUmVjb21tZW5kYXRpb25zXG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICBGaW5kaW5nIHRoZSBwZXJmZWN0IGZ1cm5pdHVyZSBmb3IgeW91ciBzcGFjZS4uLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICBZb3VyIFBlcnNvbmFsaXplZCBSZWNvbW1lbmRhdGlvbnNcbiAgICAgICAgPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIEJhc2VkIG9uIHlvdXIge3Jvb20udHlwZT8ucmVwbGFjZSgnXycsICcgJyl9IGluIHtyb29tLnN0eWxlfSBzdHlsZSB3aXRoIGEgJHtyb29tLmJ1ZGdldH0gYnVkZ2V0XG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU3VtbWFyeSAqL31cbiAgICAgIHtzdW1tYXJ5ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS05MDBcIj57c3VtbWFyeS50b3RhbEl0ZW1zfTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPkl0ZW1zPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS05MDBcIj4ke3N1bW1hcnkudG90YWxDb3N0fTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlRvdGFsIENvc3Q8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTkwMFwiPntNYXRoLnJvdW5kKHN1bW1hcnkuYnVkZ2V0VXRpbGl6YXRpb24pfSU8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5CdWRnZXQgVXNlZDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtOTAwXCI+JHtzdW1tYXJ5LnJlbWFpbmluZ0J1ZGdldH08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5SZW1haW5pbmc8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBDb250cm9scyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgc206aXRlbXMtY2VudGVyIHNwYWNlLXktNCBzbTpzcGFjZS15LTBcIj5cbiAgICAgICAgey8qIENhdGVnb3J5IEZpbHRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQ2F0ZWdvcnl9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkQ2F0ZWdvcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXNtIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2NhdGVnb3JpZXMubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NhdGVnb3J5fSB2YWx1ZT17Y2F0ZWdvcnl9PlxuICAgICAgICAgICAgICAgIHtjYXRlZ29yeSA9PT0gJ2FsbCcgPyAnQWxsIENhdGVnb3JpZXMnIDogY2F0ZWdvcnkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBjYXRlZ29yeS5zbGljZSgxKX1cbiAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFZpZXcgTW9kZSBUb2dnbGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoJ2dyaWQnKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWxnICR7dmlld01vZGUgPT09ICdncmlkJyA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNjAwJyA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEdyaWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoJ2xpc3QnKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWxnICR7dmlld01vZGUgPT09ICdsaXN0JyA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNjAwJyA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAnfWB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPExpc3QgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBSZWNvbW1lbmRhdGlvbnMgR3JpZC9MaXN0ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BcbiAgICAgICAgJHt2aWV3TW9kZSA9PT0gJ2dyaWQnIFxuICAgICAgICAgID8gJ2dyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTYnIFxuICAgICAgICAgIDogJ3NwYWNlLXktNCdcbiAgICAgICAgfVxuICAgICAgYH0+XG4gICAgICAgIHtmaWx0ZXJlZFJlY29tbWVuZGF0aW9ucy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3dcbiAgICAgICAgICAgICAgJHt2aWV3TW9kZSA9PT0gJ2xpc3QnID8gJ2ZsZXgnIDogJyd9XG4gICAgICAgICAgICBgfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiBJbWFnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHt2aWV3TW9kZSA9PT0gJ2xpc3QnID8gJ3ctNDggZmxleC1zaHJpbmstMCcgOiAnYXNwZWN0LXNxdWFyZSd9IGJnLWdyYXktMTAwYH0+XG4gICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICBzcmM9e2l0ZW0uaW1hZ2VVcmx9XG4gICAgICAgICAgICAgICAgYWx0PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgZmxleC0xXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbGluZS1jbGFtcC0yXCI+e2l0ZW0ubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZVdpc2hsaXN0KGl0ZW0uaWQpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0xIHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICB3aXNobGlzdC5oYXMoaXRlbS5pZCkgPyAndGV4dC1yZWQtNTAwJyA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcmVkLTUwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxIZWFydCBjbGFzc05hbWU9e2B3LTUgaC01ICR7d2lzaGxpc3QuaGFzKGl0ZW0uaWQpID8gJ2ZpbGwtY3VycmVudCcgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gbWItMyBsaW5lLWNsYW1wLTJcIj57aXRlbS5kZXNjcmlwdGlvbn08L3A+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItM1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+JHtpdGVtLnByaWNlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgY2FwaXRhbGl6ZVwiPntpdGVtLnJldGFpbGVyfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItM1wiPlxuICAgICAgICAgICAgICAgIHtpdGVtLnN0eWxlLnNsaWNlKDAsIDIpLm1hcCgoc3R5bGUpID0+IChcbiAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgIGtleT17c3R5bGV9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIHRleHQteHMgcm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3N0eWxlfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oaXRlbS5wcm9kdWN0VXJsLCAnX2JsYW5rJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtIGZvbnQtbWVkaXVtIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPlZpZXcgUHJvZHVjdDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgPFNob3BwaW5nQ2FydCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e29uQmFja31cbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTgwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgID5cbiAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgIDxzcGFuPkJhY2s8L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtvblN0YXJ0T3Zlcn1cbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNiBweS0yIGJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JlZW4tNzAwIHRyYW5zaXRpb24tY29sb3JzIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPHNwYW4+U3RhcnQgTmV3IFJvb208L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJBcnJvd0xlZnQiLCJSZWZyZXNoQ3ciLCJIZWFydCIsIkV4dGVybmFsTGluayIsIlNob3BwaW5nQ2FydCIsIkZpbHRlciIsIkdyaWQiLCJMaXN0IiwiUmVjb21tZW5kYXRpb25EaXNwbGF5Iiwicm9vbSIsIm9uU3RhcnRPdmVyIiwib25CYWNrIiwicmVjb21tZW5kYXRpb25zIiwic2V0UmVjb21tZW5kYXRpb25zIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwidmlld01vZGUiLCJzZXRWaWV3TW9kZSIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5Iiwid2lzaGxpc3QiLCJzZXRXaXNobGlzdCIsIlNldCIsInN1bW1hcnkiLCJzZXRTdW1tYXJ5IiwiZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwicm9vbUFuYWx5c2lzIiwiYW5hbHlzaXNSZXN1bHQiLCJyb29tVHlwZSIsInR5cGUiLCJidWRnZXQiLCJzdHlsZSIsIm9rIiwiRXJyb3IiLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJtb2NrUmVjb21tZW5kYXRpb25zIiwiaWQiLCJuYW1lIiwiY2F0ZWdvcnkiLCJkZXNjcmlwdGlvbiIsInByaWNlIiwiY3VycmVuY3kiLCJpbWFnZVVybCIsInByb2R1Y3RVcmwiLCJyZXRhaWxlciIsImRpbWVuc2lvbnMiLCJ3aWR0aCIsImhlaWdodCIsImRlcHRoIiwicGxhY2VtZW50Iiwic3VnZ2VzdGVkUG9zaXRpb24iLCJ4IiwieSIsInpvbmUiLCJjb21wYXRpYmlsaXR5IiwiZXhpc3RpbmdGdXJuaXR1cmUiLCJzdHlsZU1hdGNoIiwidG90YWxJdGVtcyIsInRvdGFsQ29zdCIsImJ1ZGdldFV0aWxpemF0aW9uIiwicmVtYWluaW5nQnVkZ2V0IiwidG9nZ2xlV2lzaGxpc3QiLCJpdGVtSWQiLCJuZXdXaXNobGlzdCIsImhhcyIsImRlbGV0ZSIsImFkZCIsImNhdGVnb3JpZXMiLCJBcnJheSIsImZyb20iLCJtYXAiLCJyIiwiZmlsdGVyZWRSZWNvbW1lbmRhdGlvbnMiLCJmaWx0ZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiLCJoMiIsInJlcGxhY2UiLCJNYXRoIiwicm91bmQiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9wdGlvbiIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJidXR0b24iLCJvbkNsaWNrIiwiaXRlbSIsImltZyIsInNyYyIsImFsdCIsInNwYW4iLCJ3aW5kb3ciLCJvcGVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RecommendationDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RoomAnalysis.tsx":
/*!*****************************************!*\
  !*** ./src/components/RoomAnalysis.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoomAnalysis: () => (/* binding */ RoomAnalysis)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/panels-top-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,CheckCircle,Layout,Loader2,Palette!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ RoomAnalysis auto */ \n\n\nfunction RoomAnalysis({ room, onAnalysisComplete, onBack }) {\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisStep, setAnalysisStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('detecting');\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoomAnalysis.useEffect\": ()=>{\n            startAnalysis();\n        }\n    }[\"RoomAnalysis.useEffect\"], []);\n    const startAnalysis = async ()=>{\n        setIsAnalyzing(true);\n        setAnalysisStep('detecting');\n        setProgress(0);\n        try {\n            // Simulate analysis progress\n            const progressInterval = setInterval(()=>{\n                setProgress((prev)=>{\n                    if (prev >= 90) {\n                        clearInterval(progressInterval);\n                        return 90;\n                    }\n                    return prev + 5;\n                });\n            }, 200);\n            // Call analysis API\n            const response = await fetch('/api/analyze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    imageId: room.id,\n                    imagePath: room.imageUrl\n                })\n            });\n            clearInterval(progressInterval);\n            setProgress(100);\n            if (!response.ok) {\n                throw new Error('Analysis failed');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setAnalysisResult(result.data);\n                setAnalysisStep('complete');\n            } else {\n                throw new Error(result.error || 'Analysis failed');\n            }\n        } catch (error) {\n            console.error('Analysis error:', error);\n            // For demo purposes, use mock data if API fails\n            const mockResult = {\n                analysis: {\n                    layout: {\n                        doors: [\n                            {\n                                id: '1',\n                                type: 'door',\n                                boundingBox: {\n                                    x: 50,\n                                    y: 200,\n                                    width: 80,\n                                    height: 200\n                                },\n                                confidence: 0.85\n                            }\n                        ],\n                        windows: [\n                            {\n                                id: '2',\n                                type: 'window',\n                                boundingBox: {\n                                    x: 300,\n                                    y: 50,\n                                    width: 150,\n                                    height: 100\n                                },\n                                confidence: 0.92\n                            }\n                        ],\n                        walls: [],\n                        floorArea: 2000\n                    },\n                    existingFurniture: [\n                        {\n                            id: '1',\n                            type: 'couch',\n                            category: 'seating',\n                            boundingBox: {\n                                x: 200,\n                                y: 300,\n                                width: 250,\n                                height: 120\n                            },\n                            confidence: 0.91\n                        },\n                        {\n                            id: '2',\n                            type: 'coffee table',\n                            category: 'tables',\n                            boundingBox: {\n                                x: 280,\n                                y: 420,\n                                width: 100,\n                                height: 60\n                            },\n                            confidence: 0.78\n                        }\n                    ],\n                    colorPalette: [\n                        '#F5F5F5',\n                        '#8B7355',\n                        '#2F4F4F',\n                        '#D2B48C',\n                        '#228B22'\n                    ],\n                    lightingSources: [\n                        {\n                            type: 'natural',\n                            position: {\n                                x: 375,\n                                y: 50\n                            },\n                            intensity: 0.8\n                        }\n                    ],\n                    spatialFlow: {\n                        entryPoints: [\n                            {\n                                x: 90,\n                                y: 300\n                            }\n                        ],\n                        trafficPaths: [],\n                        functionalZones: [\n                            {\n                                id: 'seating_area',\n                                type: 'relaxation',\n                                area: [],\n                                function: 'seating and entertainment'\n                            }\n                        ]\n                    }\n                },\n                predictedRoomType: room.type,\n                confidence: 0.85\n            };\n            setAnalysisResult(mockResult);\n            setAnalysisStep('complete');\n            setProgress(100);\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    const handleContinue = ()=>{\n        if (analysisResult) {\n            onAnalysisComplete(analysisResult);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: \"AI Room Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Our AI is analyzing your room to understand its layout and existing furniture\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Analysis Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: room.imageUrl,\n                                        alt: \"Room being analyzed\",\n                                        className: \"w-full h-64 object-cover rounded-lg border border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black bg-opacity-20 rounded-lg\",\n                                        children: analysisResult.analysis.existingFurniture.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20\",\n                                                style: {\n                                                    left: `${item.boundingBox.x / 640 * 100}%`,\n                                                    top: `${item.boundingBox.y / 480 * 100}%`,\n                                                    width: `${item.boundingBox.width / 640 * 100}%`,\n                                                    height: `${item.boundingBox.height / 480 * 100}%`\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded\",\n                                                    children: [\n                                                        item.type,\n                                                        \" (\",\n                                                        Math.round(item.confidence * 100),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, item.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: analysisStep === 'detecting' ? 'Detecting objects...' : 'Analyzing layout...'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    progress,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: `${progress}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Analysis Results\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-center space-x-3 p-3 rounded-lg ${analysisStep === 'detecting' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'}`,\n                                        children: [\n                                            analysisStep === 'detecting' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Object Detection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: analysisResult ? `Found ${analysisResult.analysis.existingFurniture.length} furniture items` : 'Identifying furniture and room elements'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-center space-x-3 p-3 rounded-lg ${analysisStep === 'analyzing' ? 'bg-blue-50 border border-blue-200' : analysisStep === 'complete' ? 'bg-gray-50' : 'bg-gray-100'}`,\n                                        children: [\n                                            analysisStep === 'analyzing' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this) : analysisStep === 'complete' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Layout Analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: analysisResult ? `Room type: ${analysisResult.predictedRoomType.replace('_', ' ')}` : 'Analyzing room layout and flow'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-center space-x-3 p-3 rounded-lg ${analysisStep === 'complete' ? 'bg-green-50 border border-green-200' : 'bg-gray-100'}`,\n                                        children: [\n                                            analysisStep === 'complete' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: \"Style & Color Analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: analysisResult ? 'Color palette and style preferences identified' : 'Extracting color palette and style elements'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-green-900 mb-2\",\n                                        children: \"✨ Analysis Complete!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm text-green-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Detected \",\n                                                    analysisResult.analysis.existingFurniture.length,\n                                                    \" furniture items\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Identified \",\n                                                    analysisResult.analysis.layout.doors.length,\n                                                    \" doors and \",\n                                                    analysisResult.analysis.layout.windows.length,\n                                                    \" windows\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Extracted \",\n                                                    analysisResult.analysis.colorPalette.length,\n                                                    \" dominant colors\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Room type confidence: \",\n                                                    Math.round(analysisResult.confidence * 100),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"Detected Color Palette\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: analysisResult.analysis.colorPalette.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full border border-gray-300\",\n                                                style: {\n                                                    backgroundColor: color\n                                                },\n                                                title: color\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleContinue,\n                        disabled: !analysisResult,\n                        className: `\n            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors\n            ${analysisResult ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n          `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Get Recommendations\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_CheckCircle_Layout_Loader2_Palette_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RoomAnalysis.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RoomConfiguration.tsx":
/*!**********************************************!*\
  !*** ./src/components/RoomConfiguration.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoomConfiguration: () => (/* binding */ RoomConfiguration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,DollarSign!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,DollarSign!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,DollarSign!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ RoomConfiguration auto */ \n\n\nconst ROOM_TYPES = [\n    {\n        value: 'living_room',\n        label: 'Living Room',\n        icon: '🛋️'\n    },\n    {\n        value: 'bedroom',\n        label: 'Bedroom',\n        icon: '🛏️'\n    },\n    {\n        value: 'kitchen',\n        label: 'Kitchen',\n        icon: '🍳'\n    },\n    {\n        value: 'bathroom',\n        label: 'Bathroom',\n        icon: '🚿'\n    },\n    {\n        value: 'dining_room',\n        label: 'Dining Room',\n        icon: '🍽️'\n    },\n    {\n        value: 'office',\n        label: 'Office',\n        icon: '💼'\n    },\n    {\n        value: 'nursery',\n        label: 'Nursery',\n        icon: '👶'\n    },\n    {\n        value: 'guest_room',\n        label: 'Guest Room',\n        icon: '🏠'\n    }\n];\nconst DESIGN_STYLES = [\n    {\n        value: 'modern',\n        label: 'Modern',\n        description: 'Clean lines, minimal clutter, neutral colors'\n    },\n    {\n        value: 'minimalist',\n        label: 'Minimalist',\n        description: 'Less is more, functional, simple'\n    },\n    {\n        value: 'boho',\n        label: 'Bohemian',\n        description: 'Eclectic, colorful, artistic'\n    },\n    {\n        value: 'industrial',\n        label: 'Industrial',\n        description: 'Raw materials, exposed elements'\n    },\n    {\n        value: 'scandinavian',\n        label: 'Scandinavian',\n        description: 'Light woods, cozy, functional'\n    },\n    {\n        value: 'traditional',\n        label: 'Traditional',\n        description: 'Classic, elegant, timeless'\n    },\n    {\n        value: 'contemporary',\n        label: 'Contemporary',\n        description: 'Current trends, sophisticated'\n    },\n    {\n        value: 'rustic',\n        label: 'Rustic',\n        description: 'Natural materials, warm, cozy'\n    }\n];\nconst BUDGET_RANGES = [\n    {\n        min: 500,\n        max: 1000,\n        label: '$500 - $1,000'\n    },\n    {\n        min: 1000,\n        max: 2500,\n        label: '$1,000 - $2,500'\n    },\n    {\n        min: 2500,\n        max: 5000,\n        label: '$2,500 - $5,000'\n    },\n    {\n        min: 5000,\n        max: 10000,\n        label: '$5,000 - $10,000'\n    },\n    {\n        min: 10000,\n        max: 25000,\n        label: '$10,000+'\n    }\n];\nfunction RoomConfiguration({ imageUrl, onConfiguration, onBack }) {\n    const [roomType, setRoomType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [style, setStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [budget, setBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customBudget, setCustomBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [useCustomBudget, setUseCustomBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = ()=>{\n        if (!roomType || !style || !budget) return;\n        onConfiguration({\n            roomType,\n            budget,\n            style\n        });\n    };\n    const isValid = roomType && style && budget;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                        children: \"Configure Your Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Help us understand your space and preferences for better recommendations\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Your Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imageUrl,\n                                alt: \"Room preview\",\n                                className: \"w-full h-64 object-cover rounded-lg border border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"What type of room is this?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: ROOM_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setRoomType(type.value),\n                                                className: `\n                    p-3 rounded-lg border-2 text-left transition-colors\n                    ${roomType === type.value ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 hover:border-gray-300'}\n                  `,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: type.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: type.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, type.value, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"What's your preferred style?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                        children: DESIGN_STYLES.map((styleOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setStyle(styleOption.value),\n                                                className: `\n                    w-full p-3 rounded-lg border-2 text-left transition-colors\n                    ${style === styleOption.value ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 hover:border-gray-300'}\n                  `,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: styleOption.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                        children: styleOption.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, styleOption.value, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"What's your budget?\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    !useCustomBudget ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            BUDGET_RANGES.map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setBudget(range.max),\n                                                    className: `\n                      w-full p-3 rounded-lg border-2 text-left transition-colors\n                      ${budget === range.max ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 hover:border-gray-300'}\n                    `,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: range.label\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, range.label, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setUseCustomBudget(true),\n                                                className: \"w-full p-3 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 text-gray-600 transition-colors\",\n                                                children: \"Enter custom amount\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: customBudget,\n                                                        onChange: (e)=>{\n                                                            setCustomBudget(e.target.value);\n                                                            setBudget(parseFloat(e.target.value) || 0);\n                                                        },\n                                                        placeholder: \"Enter your budget\",\n                                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setUseCustomBudget(false);\n                                                    setCustomBudget('');\n                                                    setBudget(null);\n                                                },\n                                                className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                                children: \"Choose from preset ranges\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between pt-6 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Back\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSubmit,\n                        disabled: !isValid,\n                        className: `\n            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors\n            ${isValid ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}\n          `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Analyze Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_DollarSign_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RoomConfiguration.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/prop-types","vendor-chunks/file-selector","vendor-chunks/react-dropzone","vendor-chunks/react-is","vendor-chunks/tslib","vendor-chunks/object-assign","vendor-chunks/attr-accept"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FMy%20big%20idea%2Finterior-design-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();