(()=>{var e={};e.id=974,e.ids=[974],e.modules={2015:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return x},getRouteRegex:function(){return d},parseParameter:function(){return s}});let i=a(46143),n=a(71437),r=a(53293),o=a(72887),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function s(e){let t=e.match(l);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let a=e.startsWith("...");return a&&(e=e.slice(3)),{key:e,repeat:a,optional:t}}function p(e,t,a){let i={},s=1,p=[];for(let d of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),o=d.match(l);if(e&&o&&o[2]){let{key:t,optional:a,repeat:n}=c(o[2]);i[t]={pos:s++,repeat:n,optional:a},p.push("/"+(0,r.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:n}=c(o[2]);i[e]={pos:s++,repeat:t,optional:n},a&&o[1]&&p.push("/"+(0,r.escapeStringRegexp)(o[1]));let l=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";a&&o[1]&&(l=l.substring(1)),p.push(l)}else p.push("/"+(0,r.escapeStringRegexp)(d));t&&o&&o[3]&&p.push((0,r.escapeStringRegexp)(o[3]))}return{parameterizedRoute:p.join(""),groups:i}}function d(e,t){let{includeSuffix:a=!1,includePrefix:i=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:r,groups:o}=p(e,a,i),l=r;return n||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:o}}function m(e){let t,{interceptionMarker:a,getSafeRouteKey:i,segment:n,routeKeys:o,keyPrefix:l,backreferenceDuplicateKeys:s}=e,{key:p,optional:d,repeat:m}=c(n),u=p.replace(/\W/g,"");l&&(u=""+l+u);let x=!1;(0===u.length||u.length>30)&&(x=!0),isNaN(parseInt(u.slice(0,1)))||(x=!0),x&&(u=i());let f=u in o;l?o[u]=""+l+p:o[u]=p;let v=a?(0,r.escapeStringRegexp)(a):"";return t=f&&s?"\\k<"+u+">":m?"(?<"+u+">.+?)":"(?<"+u+">[^/]+?)",d?"(?:/"+v+t+")?":"/"+v+t}function u(e,t,a,s,c){let p,d=(p=0,()=>{let e="",t=++p;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={},x=[];for(let p of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>p.startsWith(e)),o=p.match(l);if(e&&o&&o[2])x.push(m({getSafeRouteKey:d,interceptionMarker:o[1],segment:o[2],routeKeys:u,keyPrefix:t?i.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(o&&o[2]){s&&o[1]&&x.push("/"+(0,r.escapeStringRegexp)(o[1]));let e=m({getSafeRouteKey:d,segment:o[2],routeKeys:u,keyPrefix:t?i.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});s&&o[1]&&(e=e.substring(1)),x.push(e)}else x.push("/"+(0,r.escapeStringRegexp)(p));a&&o&&o[3]&&x.push((0,r.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:x.join(""),routeKeys:u}}function x(e,t){var a,i,n;let r=u(e,t.prefixRouteKeys,null!=(a=t.includeSuffix)&&a,null!=(i=t.includePrefix)&&i,null!=(n=t.backreferenceDuplicateKeys)&&n),o=r.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...d(e,t),namedRegex:"^"+o+"$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:a}=p(e,!1,!1),{catchAll:i=!0}=t;if("/"===a)return{namedRegex:"^/"+(i?".*":"")+"$"};let{namedParameterizedRoute:n}=u(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(i?"(?:(/.*)?)":"")+"$"}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6341:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return v},interpolateDynamicPath:function(){return x},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return u}});let i=a(79551),n=a(11959),r=a(12437),o=a(2015),l=a(78034),s=a(15526),c=a(72887),p=a(74722),d=a(46143),m=a(47912);function u(e,t,a){let n=(0,i.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let i=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),r=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(i||r||t.includes(e)||a&&Object.keys(a.groups).includes(e))&&delete n.query[e]}e.url=(0,i.format)(n)}function x(e,t,a){if(!a)return e;for(let i of Object.keys(a.groups)){let n,{optional:r,repeat:o}=a.groups[i],l=`[${o?"...":""}${i}]`;r&&(l=`[${l}]`);let s=t[i];n=Array.isArray(s)?s.map(e=>e&&encodeURIComponent(e)).join("/"):s?encodeURIComponent(s):"",e=e.replaceAll(l,n)}return e}function f(e,t,a,i){let n={};for(let r of Object.keys(t.groups)){let o=e[r];"string"==typeof o?o=(0,p.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(p.normalizeRscURL));let l=a[r],s=t.groups[r].optional;if((Array.isArray(l)?l.some(e=>Array.isArray(o)?o.some(t=>t.includes(e)):null==o?void 0:o.includes(e)):null==o?void 0:o.includes(l))||void 0===o&&!(s&&i))return{params:{},hasValidParams:!1};s&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${r}]]`))&&(o=void 0,delete e[r]),o&&"string"==typeof o&&t.groups[r].repeat&&(o=o.split("/")),o&&(n[r]=o)}return{params:n,hasValidParams:!0}}function v({page:e,i18n:t,basePath:a,rewrites:i,pageIsDynamic:p,trailingSlash:d,caseSensitive:v}){let g,h,b;return p&&(g=(0,o.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(h=(0,l.getRouteMatcher)(g))(e)),{handleRewrites:function(o,l){let m={},u=l.pathname,x=i=>{let c=(0,r.getPathMatch)(i.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!v});if(!l.pathname)return!1;let x=c(l.pathname);if((i.has||i.missing)&&x){let e=(0,s.matchHas)(o,l.query,i.has,i.missing);e?Object.assign(x,e):x=!1}if(x){let{parsedDestination:r,destQuery:o}=(0,s.prepareDestination)({appendParamsToQuery:!0,destination:i.destination,params:x,query:l.query});if(r.protocol)return!0;if(Object.assign(m,o,x),Object.assign(l.query,r.query),delete r.query,Object.assign(l,r),!(u=l.pathname))return!1;if(a&&(u=u.replace(RegExp(`^${a}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(u,t.locales);u=e.pathname,l.query.nextInternalLocale=e.detectedLocale||x.nextInternalLocale}if(u===e)return!0;if(p&&h){let e=h(u);if(e)return l.query={...l.query,...e},!0}}return!1};for(let e of i.beforeFiles||[])x(e);if(u!==e){let t=!1;for(let e of i.afterFiles||[])if(t=x(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(u||"");return t===(0,c.removeTrailingSlash)(e)||(null==h?void 0:h(t))})()){for(let e of i.fallback||[])if(t=x(e))break}}return m},defaultRouteRegex:g,dynamicRouteMatcher:h,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:a}=g,i=(0,l.getRouteMatcher)({re:{exec:e=>{let i=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(i)){let a=(0,m.normalizeNextQueryParam)(e);a&&(i[a]=t,delete i[e])}let n={};for(let e of Object.keys(a)){let r=a[e];if(!r)continue;let o=t[r],l=i[e];if(!o.optional&&!l)return null;n[o.pos]=l}return n}},groups:t})(e);return i||null},normalizeDynamicRouteParams:(e,t)=>g&&b?f(e,g,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>u(e,t,g),interpolateDynamicPath:(e,t)=>x(e,t,g)}}function g(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},8304:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return l},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return s},isMetadataPage:function(){return d},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return p}});let i=a(12958),n=a(74722),r=a(70554),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},l=["js","jsx","ts","tsx"],s=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,a){let n=(a?"":"?")+"$",r=`\\d?${a?"":"(-\\w{6})?"}`,l=[RegExp(`^[\\\\/]robots${s(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${s(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${s(["xml"],t)}${n}`),RegExp(`[\\\\/]${o.icon.filename}${r}${s(o.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${o.apple.filename}${r}${s(o.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${o.openGraph.filename}${r}${s(o.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${o.twitter.filename}${r}${s(o.twitter.extensions,t)}${n}`)],c=(0,i.normalizePathSep)(e);return l.some(e=>e.test(c))}function p(e){let t=e.replace(/\/route$/,"");return(0,r.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,r.isAppRouteRoute)(e)&&c(e,[],!1)}function m(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,r.isAppRouteRoute)(e)&&c(t,[],!1)}},8408:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12437:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let i=a(35362);function n(e,t){let a=[],n=(0,i.pathToRegexp)(e,a,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),r=(0,i.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,a);return(e,i)=>{if("string"!=typeof e)return!1;let n=r(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of a)"number"==typeof e.name&&delete n.params[e.name];return{...i,...n.params}}}},12958:(e,t)=>{"use strict";function a(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return a}})},15526:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{compileNonPath:function(){return p},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return m}});let i=a(35362),n=a(53293),r=a(76759),o=a(71437),l=a(88212);function s(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,a,i){void 0===a&&(a=[]),void 0===i&&(i=[]);let n={},r=a=>{let i,r=a.key;switch(a.type){case"header":r=r.toLowerCase(),i=e.headers[r];break;case"cookie":i="cookies"in e?e.cookies[a.key]:(0,l.getCookieParser)(e.headers)()[a.key];break;case"query":i=t[r];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};i=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!a.value&&i)return n[function(e){let t="";for(let a=0;a<e.length;a++){let i=e.charCodeAt(a);(i>64&&i<91||i>96&&i<123)&&(t+=e[a])}return t}(r)]=i,!0;if(i){let e=RegExp("^"+a.value+"$"),t=Array.isArray(i)?i.slice(-1)[0].match(e):i.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===a.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!a.every(e=>r(e))||i.some(e=>r(e)))&&n}function p(e,t){if(!e.includes(":"))return e;for(let a of Object.keys(t))e.includes(":"+a)&&(e=e.replace(RegExp(":"+a+"\\*","g"),":"+a+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+a+"\\?","g"),":"+a+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+a+"\\+","g"),":"+a+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+a+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+a));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,i.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let a of Object.keys({...e.params,...e.query}))a&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(a),"g"),"__ESC_COLON_"+a));let a=(0,r.parseUrl)(t),i=a.pathname;i&&(i=s(i));let o=a.href;o&&(o=s(o));let l=a.hostname;l&&(l=s(l));let c=a.hash;return c&&(c=s(c)),{...a,pathname:i,hostname:l,href:o,hash:c}}function m(e){let t,a,n=Object.assign({},e.query),r=d(e),{hostname:l,query:c}=r,m=r.pathname;r.hash&&(m=""+m+r.hash);let u=[],x=[];for(let e of((0,i.pathToRegexp)(m,x),x))u.push(e.name);if(l){let e=[];for(let t of((0,i.pathToRegexp)(l,e),e))u.push(t.name)}let f=(0,i.compile)(m,{validate:!1});for(let[a,n]of(l&&(t=(0,i.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(n)?c[a]=n.map(t=>p(s(t),e.params)):"string"==typeof n&&(c[a]=p(s(n),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>u.includes(e)))for(let t of v)t in c||(c[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let a=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(a){"(..)(..)"===a?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=a;break}}try{let[i,n]=(a=f(e.params)).split("#",2);t&&(r.hostname=t(e.params)),r.pathname=i,r.hash=(n?"#":"")+(n||""),delete r.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return r.query={...n,...r.query},{newUrl:a,destQuery:c,parsedDestination:r}}},18136:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx","default")},21376:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>eJ});var i=a(60687),n=a(43210),r=a(87955);function o(e,t,a,i){return new(a||(a=Promise))(function(n,r){function o(e){try{s(i.next(e))}catch(e){r(e)}}function l(e){try{s(i.throw(e))}catch(e){r(e)}}function s(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(o,l)}s((i=i.apply(e,t||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let l=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function s(e,t,a){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let a=t.split(".").pop().toLowerCase(),i=l.get(a);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,r="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&c(i,"path",r),void 0!==a&&Object.defineProperty(i,"handle",{value:a,writable:!1,configurable:!1,enumerable:!0}),c(i,"relativePath",r),i}function c(e,t,a){Object.defineProperty(e,t,{value:a,writable:!1,configurable:!1,enumerable:!0})}let p=[".DS_Store","Thumbs.db"];function d(e){return"object"==typeof e&&null!==e}function m(e){return e.filter(e=>-1===p.indexOf(e.name))}function u(e){if(null===e)return[];let t=[];for(let a=0;a<e.length;a++){let i=e[a];t.push(i)}return t}function x(e){if("function"!=typeof e.webkitGetAsEntry)return f(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?g(t):f(e,t)}function f(e,t){return o(this,void 0,void 0,function*(){var a;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,s(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return s(i,null!=(a=null==t?void 0:t.fullPath)?a:void 0)})}function v(e){return o(this,void 0,void 0,function*(){return e.isDirectory?g(e):function(e){return o(this,void 0,void 0,function*(){return new Promise((t,a)=>{e.file(a=>{t(s(a,e.fullPath))},e=>{a(e)})})})}(e)})}function g(e){let t=e.createReader();return new Promise((e,a)=>{let i=[];!function n(){t.readEntries(t=>o(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(v));i.push(e),n()}else try{let t=yield Promise.all(i);e(t)}catch(e){a(e)}}),e=>{a(e)})}()})}var h=a(27406);function b(e){return function(e){if(Array.isArray(e))return E(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||N(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function j(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?y(Object(a),!0).forEach(function(t){w(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):y(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function w(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(a=n.next()).done)&&(r.push(a.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return r}}(e,t)||N(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){if(e){if("string"==typeof e)return E(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return E(e,t)}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}var R="function"==typeof h?h:h.default,P=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),a=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(a)}},A=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},O=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},_={code:"too-many-files",message:"Too many files"};function S(e,t){var a="application/x-moz-file"===e.type||R(e,t);return[a,a?null:P(t)]}function C(e,t,a){if(z(e.size)){if(z(t)&&z(a)){if(e.size>a)return[!1,A(a)];if(e.size<t)return[!1,O(t)]}else if(z(t)&&e.size<t)return[!1,O(t)];else if(z(a)&&e.size>a)return[!1,A(a)]}return[!0,null]}function z(e){return null!=e}function D(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function T(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function M(e){e.preventDefault()}function $(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return function(e){for(var a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return t.some(function(t){return!D(e)&&t&&t.apply(void 0,[e].concat(i)),D(e)})}}function q(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function I(e){return/^.*\.[\w]+$/.test(e)}var F=["children"],U=["open"],L=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],H=["refKey","onChange","onClick"];function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(a=n.next()).done)&&(r.push(a.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return r}}(e,t)||B(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function B(e,t){if(e){if("string"==typeof e)return K(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return K(e,t)}}function K(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function G(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function X(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?G(Object(a),!0).forEach(function(t){V(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):G(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function V(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function Q(e,t){if(null==e)return{};var a,i,n=function(e,t){if(null==e)return{};var a,i,n={},r=Object.keys(e);for(i=0;i<r.length;i++)a=r[i],t.indexOf(a)>=0||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}var Y=(0,n.forwardRef)(function(e,t){var a=e.children,i=ee(Q(e,F)),r=i.open,o=Q(i,U);return(0,n.useImperativeHandle)(t,function(){return{open:r}},[r]),n.createElement(n.Fragment,null,a(X(X({},o),{},{open:r})))});Y.displayName="Dropzone";var J={disabled:!1,getFilesFromEvent:function(e){return o(this,void 0,void 0,function*(){var t;if(d(e)&&d(e.dataTransfer))return function(e,t){return o(this,void 0,void 0,function*(){if(e.items){let a=u(e.items).filter(e=>"file"===e.kind);return"drop"!==t?a:m(function e(t){return t.reduce((t,a)=>[...t,...Array.isArray(a)?e(a):[a]],[])}((yield Promise.all(a.map(x)))))}return m(u(e.files).map(e=>s(e)))})}(e.dataTransfer,e.type);if(d(t=e)&&d(t.target))return u(e.target.files).map(e=>s(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return o(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>s(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Y.defaultProps=J,Y.propTypes={children:r.func,accept:r.objectOf(r.arrayOf(r.string)),multiple:r.bool,preventDropOnDocument:r.bool,noClick:r.bool,noKeyboard:r.bool,noDrag:r.bool,noDragEventsBubbling:r.bool,minSize:r.number,maxSize:r.number,maxFiles:r.number,disabled:r.bool,getFilesFromEvent:r.func,onFileDialogCancel:r.func,onFileDialogOpen:r.func,useFsAccessApi:r.bool,autoFocus:r.bool,onDragEnter:r.func,onDragLeave:r.func,onDragOver:r.func,onDrop:r.func,onDropAccepted:r.func,onDropRejected:r.func,onError:r.func,validator:r.func};var Z={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ee(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=X(X({},J),e),a=t.accept,i=t.disabled,r=t.getFilesFromEvent,o=t.maxSize,l=t.minSize,s=t.multiple,c=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,m=t.onDragOver,u=t.onDrop,x=t.onDropAccepted,f=t.onDropRejected,v=t.onFileDialogCancel,g=t.onFileDialogOpen,h=t.useFsAccessApi,y=t.autoFocus,N=t.preventDropOnDocument,E=t.noClick,R=t.noKeyboard,P=t.noDrag,A=t.noDragEventsBubbling,O=t.onError,F=t.validator,U=(0,n.useMemo)(function(){return z(a)?Object.entries(a).reduce(function(e,t){var a=k(t,2),i=a[0],n=a[1];return[].concat(b(e),[i],b(n))},[]).filter(function(e){return q(e)||I(e)}).join(","):void 0},[a]),G=(0,n.useMemo)(function(){return z(a)?[{description:"Files",accept:Object.entries(a).filter(function(e){var t=k(e,2),a=t[0],i=t[1],n=!0;return q(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(I)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,t){var a=k(t,2),i=a[0],n=a[1];return j(j({},e),{},w({},i,n))},{})}]:a},[a]),Y=(0,n.useMemo)(function(){return"function"==typeof g?g:ea},[g]),ee=(0,n.useMemo)(function(){return"function"==typeof v?v:ea},[v]),ei=(0,n.useRef)(null),en=(0,n.useRef)(null),er=W((0,n.useReducer)(et,Z),2),eo=er[0],el=er[1],es=eo.isFocused,ec=eo.isFileDialogActive,ep=(0,n.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),ed=function(){!ep.current&&ec&&setTimeout(function(){en.current&&(en.current.files.length||(el({type:"closeDialog"}),ee()))},300)};(0,n.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[en,ec,ee,ep]);var em=(0,n.useRef)([]),eu=function(e){ei.current&&ei.current.contains(e.target)||(e.preventDefault(),em.current=[])};(0,n.useEffect)(function(){return N&&(document.addEventListener("dragover",M,!1),document.addEventListener("drop",eu,!1)),function(){N&&(document.removeEventListener("dragover",M),document.removeEventListener("drop",eu))}},[ei,N]),(0,n.useEffect)(function(){return!i&&y&&ei.current&&ei.current.focus(),function(){}},[ei,y,i]);var ex=(0,n.useCallback)(function(e){O?O(e):console.error(e)},[O]),ef=(0,n.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eA(e),em.current=[].concat(function(e){if(Array.isArray(e))return K(e)}(t=em.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||B(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),T(e)&&Promise.resolve(r(e)).then(function(t){if(!D(e)||A){var a,i,n,r,d,m,u,x,f=t.length,v=f>0&&(i=(a={files:t,accept:U,minSize:l,maxSize:o,multiple:s,maxFiles:c,validator:F}).files,n=a.accept,r=a.minSize,d=a.maxSize,m=a.multiple,u=a.maxFiles,x=a.validator,(!!m||!(i.length>1))&&(!m||!(u>=1)||!(i.length>u))&&i.every(function(e){var t=k(S(e,n),1)[0],a=k(C(e,r,d),1)[0],i=x?x(e):null;return t&&a&&!i}));el({isDragAccept:v,isDragReject:f>0&&!v,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return ex(e)})},[r,p,ex,A,U,l,o,s,c,F]),ev=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e);var t=T(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&m&&m(e),!1},[m,A]),eg=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e);var t=em.current.filter(function(e){return ei.current&&ei.current.contains(e)}),a=t.indexOf(e.target);-1!==a&&t.splice(a,1),em.current=t,!(t.length>0)&&(el({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),T(e)&&d&&d(e))},[ei,d,A]),eh=(0,n.useCallback)(function(e,t){var a=[],i=[];e.forEach(function(e){var t=W(S(e,U),2),n=t[0],r=t[1],s=W(C(e,l,o),2),c=s[0],p=s[1],d=F?F(e):null;if(n&&c&&!d)a.push(e);else{var m=[r,p];d&&(m=m.concat(d)),i.push({file:e,errors:m.filter(function(e){return e})})}}),(!s&&a.length>1||s&&c>=1&&a.length>c)&&(a.forEach(function(e){i.push({file:e,errors:[_]})}),a.splice(0)),el({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),u&&u(a,i,t),i.length>0&&f&&f(i,t),a.length>0&&x&&x(a,t)},[el,s,U,l,o,c,u,x,f,F]),eb=(0,n.useCallback)(function(e){e.preventDefault(),e.persist(),eA(e),em.current=[],T(e)&&Promise.resolve(r(e)).then(function(t){(!D(e)||A)&&eh(t,e)}).catch(function(e){return ex(e)}),el({type:"reset"})},[r,eh,ex,A]),ey=(0,n.useCallback)(function(){if(ep.current){el({type:"openDialog"}),Y(),window.showOpenFilePicker({multiple:s,types:G}).then(function(e){return r(e)}).then(function(e){eh(e,null),el({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(ee(e),el({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ep.current=!1,en.current?(en.current.value=null,en.current.click()):ex(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ex(e)});return}en.current&&(el({type:"openDialog"}),Y(),en.current.value=null,en.current.click())},[el,Y,ee,h,eh,ex,G,s]),ej=(0,n.useCallback)(function(e){ei.current&&ei.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ey())},[ei,ey]),ew=(0,n.useCallback)(function(){el({type:"focus"})},[]),ek=(0,n.useCallback)(function(){el({type:"blur"})},[]),eN=(0,n.useCallback)(function(){E||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ey,0):ey())},[E,ey]),eE=function(e){return i?null:e},eR=function(e){return R?null:eE(e)},eP=function(e){return P?null:eE(e)},eA=function(e){A&&e.stopPropagation()},eO=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.role,n=e.onKeyDown,r=e.onFocus,o=e.onBlur,l=e.onClick,s=e.onDragEnter,c=e.onDragOver,p=e.onDragLeave,d=e.onDrop,m=Q(e,L);return X(X(V({onKeyDown:eR($(n,ej)),onFocus:eR($(r,ew)),onBlur:eR($(o,ek)),onClick:eE($(l,eN)),onDragEnter:eP($(s,ef)),onDragOver:eP($(c,ev)),onDragLeave:eP($(p,eg)),onDrop:eP($(d,eb)),role:"string"==typeof a&&""!==a?a:"presentation"},void 0===t?"ref":t,ei),i||R?{}:{tabIndex:0}),m)}},[ei,ej,ew,ek,eN,ef,ev,eg,eb,R,P,i]),e_=(0,n.useCallback)(function(e){e.stopPropagation()},[]),eS=(0,n.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.onChange,i=e.onClick,n=Q(e,H);return X(X({},V({accept:U,multiple:s,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eE($(a,eb)),onClick:eE($(i,e_)),tabIndex:-1},void 0===t?"ref":t,en)),n)}},[en,a,s,eb,i]);return X(X({},eo),{},{isFocused:es&&!i,getRootProps:eO,getInputProps:eS,rootRef:ei,inputRef:en,open:eE(ey)})}function et(e,t){switch(t.type){case"focus":return X(X({},e),{},{isFocused:!0});case"blur":return X(X({},e),{},{isFocused:!1});case"openDialog":return X(X({},Z),{},{isFileDialogActive:!0});case"closeDialog":return X(X({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return X(X({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return X(X({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return X({},Z);default:return e}}function ea(){}let ei=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),en=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),er=e=>{let t=en(e);return t.charAt(0).toUpperCase()+t.slice(1)},eo=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),el=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var es={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ec=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:r="",children:o,iconNode:l,...s},c)=>(0,n.createElement)("svg",{ref:c,...es,width:t,height:t,stroke:e,strokeWidth:i?24*Number(a)/Number(t):a,className:eo("lucide",r),...!o&&!el(s)&&{"aria-hidden":"true"},...s},[...l.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(o)?o:[o]])),ep=(e,t)=>{let a=(0,n.forwardRef)(({className:a,...i},r)=>(0,n.createElement)(ec,{ref:r,iconNode:t,className:eo(`lucide-${ei(er(e))}`,`lucide-${e}`,a),...i}));return a.displayName=er(e),a},ed=ep("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),em=ep("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),eu=ep("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function ex({onUpload:e,isLoading:t}){let[a,r]=(0,n.useState)(null),[o,l]=(0,n.useState)(null),[s,c]=(0,n.useState)(0),{getRootProps:p,getInputProps:d,isDragActive:m}=ee({onDrop:(0,n.useCallback)(async t=>{let a=t[0];if(a){if(l(null),c(0),!a.type.startsWith("image/"))return void l("Please upload an image file");if(a.size>0xa00000)return void l("Image must be smaller than 10MB");r(URL.createObjectURL(a));try{let t=setInterval(()=>{c(e=>e>=90?(clearInterval(t),90):e+10)},100),i=new FormData;i.append("image",a);let n=await fetch("/api/upload",{method:"POST",body:i});if(clearInterval(t),c(100),!n.ok){let e=await n.json();throw Error(e.error||"Upload failed")}let r=await n.json();if(r.success)e(r.data);else throw Error(r.error||"Upload failed")}catch(e){l(e instanceof Error?e.message:"Upload failed"),r(null),c(0)}}},[e]),accept:{"image/*":[".jpeg",".jpg",".png",".webp"]},maxFiles:1,disabled:t}),u=()=>{r(null),l(null),c(0)};return(0,i.jsxs)("div",{className:"w-full max-w-2xl mx-auto",children:[a?(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("img",{src:a,alt:"Room preview",className:"w-full h-64 object-cover rounded-lg"}),(0,i.jsx)("button",{onClick:u,className:"absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-opacity",children:(0,i.jsx)(em,{className:"w-4 h-4 text-white"})})]}),s>0&&s<100&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Uploading..."}),(0,i.jsxs)("span",{className:"text-gray-600",children:[s,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${s}%`}})})]}),100===s&&!o&&(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,i.jsx)("div",{className:"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-2 h-2 bg-green-600 rounded-full"})}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Upload complete!"})]})]}):(0,i.jsxs)("div",{...p(),className:`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${m?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50"}
            ${t?"opacity-50 cursor-not-allowed":""}
          `,children:[(0,i.jsx)("input",{...d()}),(0,i.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(ed,{className:"w-8 h-8 text-blue-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:m?"Drop your image here":"Upload a room photo"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop an image, or click to browse"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Supports JPEG, PNG, WebP up to 10MB"})]}),(0,i.jsx)("button",{type:"button",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium",disabled:t,children:"Choose File"})]})]}),o&&(0,i.jsxs)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eu,{className:"w-5 h-5 text-red-600"}),(0,i.jsx)("span",{className:"text-red-800 font-medium",children:"Upload Error"})]}),(0,i.jsx)("p",{className:"text-red-700 mt-1",children:o}),(0,i.jsx)("button",{onClick:u,className:"mt-2 text-red-600 hover:text-red-800 text-sm font-medium",children:"Try again"})]}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCF8 Tips for best results:"}),(0,i.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,i.jsx)("li",{children:"• Take photos in good lighting"}),(0,i.jsx)("li",{children:"• Include the entire room in the frame"}),(0,i.jsx)("li",{children:"• Avoid cluttered or messy spaces"}),(0,i.jsx)("li",{children:"• Make sure furniture and walls are clearly visible"})]})]})]})}let ef=ep("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),ev=ep("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),eg=ep("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),eh=[{value:"living_room",label:"Living Room",icon:"\uD83D\uDECB️"},{value:"bedroom",label:"Bedroom",icon:"\uD83D\uDECF️"},{value:"kitchen",label:"Kitchen",icon:"\uD83C\uDF73"},{value:"bathroom",label:"Bathroom",icon:"\uD83D\uDEBF"},{value:"dining_room",label:"Dining Room",icon:"\uD83C\uDF7D️"},{value:"office",label:"Office",icon:"\uD83D\uDCBC"},{value:"nursery",label:"Nursery",icon:"\uD83D\uDC76"},{value:"guest_room",label:"Guest Room",icon:"\uD83C\uDFE0"}],eb=[{value:"modern",label:"Modern",description:"Clean lines, minimal clutter, neutral colors"},{value:"minimalist",label:"Minimalist",description:"Less is more, functional, simple"},{value:"boho",label:"Bohemian",description:"Eclectic, colorful, artistic"},{value:"industrial",label:"Industrial",description:"Raw materials, exposed elements"},{value:"scandinavian",label:"Scandinavian",description:"Light woods, cozy, functional"},{value:"traditional",label:"Traditional",description:"Classic, elegant, timeless"},{value:"contemporary",label:"Contemporary",description:"Current trends, sophisticated"},{value:"rustic",label:"Rustic",description:"Natural materials, warm, cozy"}],ey=[{min:500,max:1e3,label:"$500 - $1,000"},{min:1e3,max:2500,label:"$1,000 - $2,500"},{min:2500,max:5e3,label:"$2,500 - $5,000"},{min:5e3,max:1e4,label:"$5,000 - $10,000"},{min:1e4,max:25e3,label:"$10,000+"}];function ej({imageUrl:e,onConfiguration:t,onBack:a}){let[r,o]=(0,n.useState)(null),[l,s]=(0,n.useState)(null),[c,p]=(0,n.useState)(null),[d,m]=(0,n.useState)(""),[u,x]=(0,n.useState)(!1),f=r&&l&&c;return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Configure Your Room"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Help us understand your space and preferences for better recommendations"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Room"}),(0,i.jsx)("img",{src:e,alt:"Room preview",className:"w-full h-64 object-cover rounded-lg border border-gray-200"})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What type of room is this?"}),(0,i.jsx)("div",{className:"grid grid-cols-2 gap-3",children:eh.map(e=>(0,i.jsx)("button",{onClick:()=>o(e.value),className:`
                    p-3 rounded-lg border-2 text-left transition-colors
                    ${r===e.value?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300"}
                  `,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-lg",children:e.icon}),(0,i.jsx)("span",{className:"font-medium",children:e.label})]})},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's your preferred style?"}),(0,i.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:eb.map(e=>(0,i.jsxs)("button",{onClick:()=>s(e.value),className:`
                    w-full p-3 rounded-lg border-2 text-left transition-colors
                    ${l===e.value?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300"}
                  `,children:[(0,i.jsx)("div",{className:"font-medium",children:e.label}),(0,i.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:e.description})]},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's your budget?"}),u?(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(ef,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,i.jsx)("input",{type:"number",value:d,onChange:e=>{m(e.target.value),p(parseFloat(e.target.value)||0)},placeholder:"Enter your budget",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,i.jsx)("button",{onClick:()=>{x(!1),m(""),p(null)},className:"text-sm text-gray-600 hover:text-gray-800",children:"Choose from preset ranges"})]}):(0,i.jsxs)("div",{className:"space-y-2",children:[ey.map(e=>(0,i.jsx)("button",{onClick:()=>p(e.max),className:`
                      w-full p-3 rounded-lg border-2 text-left transition-colors
                      ${c===e.max?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300"}
                    `,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ef,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"font-medium",children:e.label})]})},e.label)),(0,i.jsx)("button",{onClick:()=>x(!0),className:"w-full p-3 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 text-gray-600 transition-colors",children:"Enter custom amount"})]})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,i.jsxs)("button",{onClick:a,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,i.jsx)(ev,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Back"})]}),(0,i.jsxs)("button",{onClick:()=>{r&&l&&c&&t({roomType:r,budget:c,style:l})},disabled:!f,className:`
            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors
            ${f?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}
          `,children:[(0,i.jsx)("span",{children:"Analyze Room"}),(0,i.jsx)(eg,{className:"w-4 h-4"})]})]})]})}let ew=ep("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),ek=ep("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),eN=ep("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]]),eE=ep("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);function eR({room:e,onAnalysisComplete:t,onBack:a}){let[r,o]=(0,n.useState)(!1),[l,s]=(0,n.useState)("detecting"),[c,p]=(0,n.useState)(null),[d,m]=(0,n.useState)(0);return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI Room Analysis"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Our AI is analyzing your room to understand its layout and existing furniture"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Progress"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("img",{src:e.imageUrl,alt:"Room being analyzed",className:"w-full h-64 object-cover rounded-lg border border-gray-200"}),c&&(0,i.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 rounded-lg",children:c.analysis.existingFurniture.map(e=>(0,i.jsx)("div",{className:"absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20",style:{left:`${e.boundingBox.x/640*100}%`,top:`${e.boundingBox.y/480*100}%`,width:`${e.boundingBox.width/640*100}%`,height:`${e.boundingBox.height/480*100}%`},children:(0,i.jsxs)("div",{className:"absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded",children:[e.type," (",Math.round(100*e.confidence),"%)"]})},e.id))})]}),r&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"detecting"===l?"Detecting objects...":"Analyzing layout..."}),(0,i.jsxs)("span",{className:"text-gray-600",children:[d,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${d}%`}})})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Results"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:`flex items-center space-x-3 p-3 rounded-lg ${"detecting"===l?"bg-blue-50 border border-blue-200":"bg-gray-50"}`,children:["detecting"===l?(0,i.jsx)(ew,{className:"w-5 h-5 text-blue-600 animate-spin"}):(0,i.jsx)(ek,{className:"w-5 h-5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900",children:"Object Detection"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:c?`Found ${c.analysis.existingFurniture.length} furniture items`:"Identifying furniture and room elements"})]})]}),(0,i.jsxs)("div",{className:`flex items-center space-x-3 p-3 rounded-lg ${"analyzing"===l?"bg-blue-50 border border-blue-200":"complete"===l?"bg-gray-50":"bg-gray-100"}`,children:["analyzing"===l?(0,i.jsx)(ew,{className:"w-5 h-5 text-blue-600 animate-spin"}):"complete"===l?(0,i.jsx)(ek,{className:"w-5 h-5 text-green-600"}):(0,i.jsx)(eN,{className:"w-5 h-5 text-gray-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900",children:"Layout Analysis"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:c?`Room type: ${c.predictedRoomType.replace("_"," ")}`:"Analyzing room layout and flow"})]})]}),(0,i.jsxs)("div",{className:`flex items-center space-x-3 p-3 rounded-lg ${"complete"===l?"bg-green-50 border border-green-200":"bg-gray-100"}`,children:["complete"===l?(0,i.jsx)(ek,{className:"w-5 h-5 text-green-600"}):(0,i.jsx)(eE,{className:"w-5 h-5 text-gray-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900",children:"Style & Color Analysis"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:c?"Color palette and style preferences identified":"Extracting color palette and style elements"})]})]})]}),c&&(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"✨ Analysis Complete!"}),(0,i.jsxs)("div",{className:"space-y-2 text-sm text-green-800",children:[(0,i.jsxs)("div",{children:["• Detected ",c.analysis.existingFurniture.length," furniture items"]}),(0,i.jsxs)("div",{children:["• Identified ",c.analysis.layout.doors.length," doors and ",c.analysis.layout.windows.length," windows"]}),(0,i.jsxs)("div",{children:["• Extracted ",c.analysis.colorPalette.length," dominant colors"]}),(0,i.jsxs)("div",{children:["• Room type confidence: ",Math.round(100*c.confidence),"%"]})]})]}),c&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Detected Color Palette"}),(0,i.jsx)("div",{className:"flex space-x-2",children:c.analysis.colorPalette.map((e,t)=>(0,i.jsx)("div",{className:"w-8 h-8 rounded-full border border-gray-300",style:{backgroundColor:e},title:e},t))})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,i.jsxs)("button",{onClick:a,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,i.jsx)(ev,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Back"})]}),(0,i.jsxs)("button",{onClick:()=>{c&&t(c)},disabled:!c,className:`
            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors
            ${c?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}
          `,children:[(0,i.jsx)("span",{children:"Get Recommendations"}),(0,i.jsx)(eg,{className:"w-4 h-4"})]})]})]})}let eP=ep("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),eA=ep("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),eO=ep("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),e_=ep("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),eS=ep("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),eC=ep("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ez=ep("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),eD=ep("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),eT=ep("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),eM=ep("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),e$=ep("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),eq=ep("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function eI({recommendations:e,budget:t,onItemClick:a}){let[r,o]=(0,n.useState)(e.map((e,t)=>({product:e,quantity:1,priority:t<3?"high":t<6?"medium":"low"}))),[l,s]=(0,n.useState)(!1),[c,p]=(0,n.useState)(null),d=r.reduce((e,t)=>e+t.product.price*t.quantity,0),m=d/t*100,u=t-d,x=(e,t)=>{t<1||o(a=>a.map(a=>a.product.id===e?{...a,quantity:t}:a))},f=e=>{o(t=>t.filter(t=>t.product.id!==e))},v=async e=>{try{await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"track-click",productId:e.id,retailer:e.retailer})}),window.open(e.affiliateUrl||e.productUrl,"_blank"),a&&a(e)}catch(t){console.error("Error tracking affiliate click:",t),window.open(e.affiliateUrl||e.productUrl,"_blank")}},g=async()=>{s(!0);try{let e=await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"export-csv",shoppingList:{id:`list_${Date.now()}`,name:`Shopping List - ${new Date().toLocaleDateString()}`,items:r.map(e=>({id:`item_${e.product.id}`,product:e.product,quantity:e.quantity,priority:e.priority,addedAt:new Date})),totalCost:d,budget:t,createdAt:new Date,updatedAt:new Date}})});if(e.ok){let t=await e.blob(),a=window.URL.createObjectURL(t),i=document.createElement("a");i.href=a,i.download=`shopping-list-${Date.now()}.csv`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i)}}catch(e){console.error("Error exporting CSV:",e)}finally{s(!1)}},h=async()=>{try{let e=await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generate-share-link",shoppingList:{id:`list_${Date.now()}`,name:`Shopping List - ${new Date().toLocaleDateString()}`,items:r.map(e=>({id:`item_${e.product.id}`,product:e.product,quantity:e.quantity,priority:e.priority,addedAt:new Date})),totalCost:d,budget:t,createdAt:new Date,updatedAt:new Date}})});if(e.ok){let t=await e.json();p(t.data.shareableLink),await navigator.clipboard.writeText(t.data.shareableLink),alert("Share link copied to clipboard!")}}catch(e){console.error("Error generating share link:",e)}},b=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eO,{className:"w-6 h-6 text-blue-600"}),(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Shopping List"})]}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsxs)("button",{onClick:g,disabled:l,className:"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[(0,i.jsx)(eD,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:l?"Exporting...":"Export CSV"})]}),(0,i.jsxs)("button",{onClick:h,className:"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,i.jsx)(eT,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Share"})]})]})]}),(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:r.length}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Items"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["$",d.toFixed(2)]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Total Cost"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:`text-2xl font-bold ${m>100?"text-red-600":"text-gray-900"}`,children:[m.toFixed(1),"%"]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Budget Used"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:`text-2xl font-bold ${u<0?"text-red-600":"text-green-600"}`,children:["$",Math.abs(u).toFixed(2)]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:u<0?"Over Budget":"Remaining"})]})]}),(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${m>100?"bg-red-500":m>80?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(m,100)}%`}})})})]}),m>100&&(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eu,{className:"w-5 h-5 text-red-600"}),(0,i.jsx)("span",{className:"text-red-800 font-medium",children:"Budget Exceeded"})]}),(0,i.jsxs)("p",{className:"text-red-700 mt-1",children:["Your shopping list is $",(d-t).toFixed(2)," over budget. Consider removing some items or choosing alternatives."]})]}),(0,i.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,i.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("img",{src:e.product.imageUrl,alt:e.product.name,className:"w-20 h-20 object-cover rounded-lg"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-900",children:e.product.name}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:e.product.description}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,i.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["$",e.product.price]}),(0,i.jsx)("span",{className:"text-sm text-gray-500 capitalize",children:e.product.retailer}),(0,i.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${b(e.priority)}`,children:e.priority})]})]}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)("button",{onClick:()=>f(e.product.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:(0,i.jsx)(eM,{className:"w-4 h-4"})})})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Quantity:"}),(0,i.jsx)("button",{onClick:()=>x(e.product.id,e.quantity-1),className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,i.jsx)(e$,{className:"w-3 h-3"})}),(0,i.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,i.jsx)("button",{onClick:()=>x(e.product.id,e.quantity+1),className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,i.jsx)(eq,{className:"w-3 h-3"})})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("span",{className:"font-semibold text-gray-900",children:["Total: $",(e.product.price*e.quantity).toFixed(2)]}),(0,i.jsxs)("button",{onClick:()=>v(e.product),className:"flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,i.jsx)(ez,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Buy Now"})]})]})]})]})]})},e.product.id))}),0===r.length&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(eO,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Your shopping list is empty"})]})]})}function eF({room:e,onStartOver:t,onBack:a}){let[r,o]=(0,n.useState)([]),[l,s]=(0,n.useState)(!0),[c,p]=(0,n.useState)("grid"),[d,m]=(0,n.useState)("all"),[u,x]=(0,n.useState)(new Set),[f,v]=(0,n.useState)(null),[g,h]=(0,n.useState)(!1),b=e=>{let t=new Set(u);t.has(e)?t.delete(e):t.add(e),x(t)},y=["all",...Array.from(new Set(r.map(e=>e.category)))],j="all"===d?r:r.filter(e=>e.category===d);return l?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(eP,{className:"w-8 h-8 text-blue-600 animate-spin mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Generating Recommendations"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Finding the perfect furniture for your space..."})]})}):(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Your Personalized Recommendations"}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Based on your ",e.type?.replace("_"," ")," in ",e.style," style with a $",e.budget," budget"]})]}),f&&(0,i.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:f.totalItems}),(0,i.jsx)("div",{className:"text-sm text-blue-700",children:"Items"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:["$",f.totalCost]}),(0,i.jsx)("div",{className:"text-sm text-blue-700",children:"Total Cost"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[Math.round(f.budgetUtilization),"%"]}),(0,i.jsx)("div",{className:"text-sm text-blue-700",children:"Budget Used"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:["$",f.remainingBudget]}),(0,i.jsx)("div",{className:"text-sm text-blue-700",children:"Remaining"})]})]})}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eA,{className:"w-4 h-4 text-gray-500"}),(0,i.jsx)("select",{value:d,onChange:e=>m(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:y.map(e=>(0,i.jsx)("option",{value:e,children:"all"===e?"All Categories":e.charAt(0).toUpperCase()+e.slice(1)},e))})]}),(0,i.jsxs)("button",{onClick:()=>h(!g),className:`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${g?"bg-green-100 text-green-700 border border-green-300":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[(0,i.jsx)(eO,{className:"w-4 h-4"}),(0,i.jsxs)("span",{children:[g?"Hide":"Show"," Shopping List"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("button",{onClick:()=>p("grid"),className:`p-2 rounded-lg ${"grid"===c?"bg-blue-100 text-blue-600":"text-gray-500 hover:text-gray-700"}`,children:(0,i.jsx)(e_,{className:"w-4 h-4"})}),(0,i.jsx)("button",{onClick:()=>p("list"),className:`p-2 rounded-lg ${"list"===c?"bg-blue-100 text-blue-600":"text-gray-500 hover:text-gray-700"}`,children:(0,i.jsx)(eS,{className:"w-4 h-4"})})]})]}),g&&(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(eI,{recommendations:j,budget:e.budget,onItemClick:e=>{console.log("Shopping list item clicked:",e)}})}),(0,i.jsx)("div",{className:`
        ${"grid"===c?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4"}
      `,children:j.map(e=>(0,i.jsxs)("div",{className:`
              bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow
              ${"list"===c?"flex":""}
            `,children:[(0,i.jsx)("div",{className:`${"list"===c?"w-48 flex-shrink-0":"aspect-square"} bg-gray-100`,children:(0,i.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-full object-cover"})}),(0,i.jsxs)("div",{className:"p-4 flex-1",children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-900 line-clamp-2",children:e.name}),(0,i.jsx)("button",{onClick:()=>b(e.id),className:`p-1 rounded-full ${u.has(e.id)?"text-red-500":"text-gray-400 hover:text-red-500"}`,children:(0,i.jsx)(eC,{className:`w-5 h-5 ${u.has(e.id)?"fill-current":""}`})})]}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.description}),(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:["$",e.price]}),(0,i.jsx)("span",{className:"text-sm text-gray-500 capitalize",children:e.retailer})]}),(0,i.jsx)("div",{className:"flex items-center space-x-2 mb-3",children:e.style.slice(0,2).map(e=>(0,i.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsxs)("button",{onClick:()=>window.open(e.productUrl,"_blank"),className:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1",children:[(0,i.jsx)(ez,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"View Product"})]}),(0,i.jsx)("button",{className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,i.jsx)(eO,{className:"w-4 h-4 text-gray-600"})})]})]})]},e.id))}),(0,i.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,i.jsxs)("button",{onClick:a,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,i.jsx)(ev,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Back"})]}),(0,i.jsxs)("button",{onClick:t,className:"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,i.jsx)(eP,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Start New Room"})]})]})]})}let eU=ep("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),eL=ep("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),eH=ep("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),eW=ep("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),eB=ep("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function eK(){let[e,t]=(0,n.useState)(null),[a,r]=(0,n.useState)(!1),o=async()=>{r(!0),setTimeout(()=>{t({id:"demo-user",name:"Demo User",email:"<EMAIL>",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face"}),r(!1)},1e3)};return e?(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[e.avatar?(0,i.jsx)("img",{src:e.avatar,alt:e.name,className:"w-8 h-8 rounded-full"}):(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,i.jsx)(eH,{className:"w-4 h-4 text-white"})}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 hidden sm:block",children:e.name})]}),(0,i.jsxs)("button",{onClick:()=>{t(null)},className:"flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,i.jsx)(eW,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"hidden sm:block",children:"Sign Out"})]})]}):(0,i.jsxs)("button",{onClick:o,disabled:a,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50",children:[(0,i.jsx)(eB,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:a?"Signing In...":"Sign In"})]})}function eG(){let[e,t]=(0,n.useState)(!1);return(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)(eU,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Interior AI"})]}),(0,i.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"How it Works"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"Examples"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"Pricing"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"About"})]}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:(0,i.jsx)(eK,{})}),(0,i.jsx)("button",{className:"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>t(!e),children:e?(0,i.jsx)(em,{className:"w-6 h-6 text-gray-600"}):(0,i.jsx)(eL,{className:"w-6 h-6 text-gray-600"})})]}),e&&(0,i.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,i.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"How it Works"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"Examples"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"Pricing"}),(0,i.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"About"}),(0,i.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)(eK,{})})})]})})]})})}let eX=ep("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),eV=ep("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),eQ=ep("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);function eY(){return(0,i.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)(eU,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"text-xl font-bold",children:"Interior AI"})]}),(0,i.jsx)("p",{className:"text-gray-400 mb-6 max-w-md",children:"Transform your living space with AI-powered interior design recommendations. Get personalized furniture suggestions that match your style and budget."}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Twitter",children:(0,i.jsx)(eX,{className:"w-5 h-5"})}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"LinkedIn",children:(0,i.jsx)(eV,{className:"w-5 h-5"})}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"GitHub",children:(0,i.jsx)(eQ,{className:"w-5 h-5"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Product"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"How it Works"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Features"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Pricing"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"API"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"About"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Blog"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Careers"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})})]})]})]}),(0,i.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,i.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Interior AI. All rights reserved."}),(0,i.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Privacy Policy"}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Terms of Service"}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Cookie Policy"})]})]})]})})}function eJ(){let[e,t]=(0,n.useState)("upload"),[a,r]=(0,n.useState)({}),[o,l]=(0,n.useState)(!1);return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,i.jsx)(eG,{}),(0,i.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)("div",{className:"flex items-center justify-center space-x-4",children:[{step:"upload",label:"Upload Photo",icon:"\uD83D\uDCF8"},{step:"configure",label:"Configure Room",icon:"⚙️"},{step:"analyze",label:"AI Analysis",icon:"\uD83E\uDD16"},{step:"recommendations",label:"Recommendations",icon:"✨"}].map((t,a)=>(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:`
                  flex items-center justify-center w-12 h-12 rounded-full text-lg
                  ${e===t.step?"bg-blue-600 text-white":a<["upload","configure","analyze","recommendations"].indexOf(e)?"bg-green-500 text-white":"bg-gray-200 text-gray-500"}
                `,children:t.icon}),(0,i.jsx)("span",{className:`
                  ml-2 text-sm font-medium
                  ${e===t.step?"text-blue-600":"text-gray-500"}
                `,children:t.label}),a<3&&(0,i.jsx)("div",{className:`
                    w-8 h-0.5 mx-4
                    ${a<["upload","configure","analyze","recommendations"].indexOf(e)?"bg-green-500":"bg-gray-200"}
                  `})]},t.step))})}),(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:["upload"===e&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Transform Your Space with AI"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Upload a photo of your room and get personalized furniture recommendations"}),(0,i.jsx)(ex,{onUpload:e=>{r(t=>({...t,id:e.id,imageUrl:e.url,name:`Room ${e.id}`})),t("configure")},isLoading:o})]}),"configure"===e&&a.imageUrl&&(0,i.jsx)(ej,{imageUrl:a.imageUrl,onConfiguration:e=>{r(t=>({...t,type:e.roomType,budget:e.budget,style:e.style})),t("analyze")},onBack:()=>t("upload")}),"analyze"===e&&a.type&&a.budget&&a.style&&(0,i.jsx)(eR,{room:a,onAnalysisComplete:e=>{r(t=>({...t,analysisResult:e.analysis})),t("recommendations")},onBack:()=>t("configure")}),"recommendations"===e&&a.analysisResult&&(0,i.jsx)(eF,{room:a,onStartOver:()=>{r({}),t("upload")},onBack:()=>t("analyze")})]})]}),(0,i.jsx)(eY,{})]})}},23736:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),a(44827);let i=a(42785);function n(e,t,a){void 0===a&&(a=!0);let n=new URL("http://n"),r=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:o,searchParams:l,search:s,hash:c,href:p,origin:d}=new URL(e,r);if(d!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:a?(0,i.searchParamsToUrlQuery)(l):void 0,search:s,hash:c,href:p.slice(d.length)}}},26415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,a){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},r=t.split(i),o=(a||{}).decode||e,l=0;l<r.length;l++){var s=r[l],c=s.indexOf("=");if(!(c<0)){var p=s.substr(0,c).trim(),d=s.substr(++c,s.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==n[p]&&(n[p]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return n},t.serialize=function(e,t,i){var r=i||{},o=r.encode||a;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var l=o(t);if(l&&!n.test(l))throw TypeError("argument val is invalid");var s=e+"="+l;if(null!=r.maxAge){var c=r.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(c)}if(r.domain){if(!n.test(r.domain))throw TypeError("option domain is invalid");s+="; Domain="+r.domain}if(r.path){if(!n.test(r.path))throw TypeError("option path is invalid");s+="; Path="+r.path}if(r.expires){if("function"!=typeof r.expires.toUTCString)throw TypeError("option expires is invalid");s+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(s+="; HttpOnly"),r.secure&&(s+="; Secure"),r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return s};var e=decodeURIComponent,a=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},27406:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var a=Array.isArray(t)?t:t.split(",");if(0===a.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),r=n.replace(/\/.*$/,"");return a.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?r===t.replace(/\/.*$/,""):n===t})}return!0}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30660:(e,t)=>{"use strict";function a(e){let t=5381;for(let a=0;a<e.length;a++)t=(t<<5)+t+e.charCodeAt(a)|0;return t>>>0}function i(e){return a(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{djb2Hash:function(){return a},hexHash:function(){return i}})},31658:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return x},normalizeMetadataRoute:function(){return u}});let i=a(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(a(78671)),r=a(6341),o=a(2015),l=a(30660),s=a(74722),c=a(12958),p=a(35499);function d(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let a="";return t.split("/").some(e=>(0,p.isGroupSegment)(e)||(0,p.isParallelRouteSegment)(e))&&(a=(0,l.djb2Hash)(t).toString(36).slice(0,6)),a}function m(e,t,a){let i=(0,s.normalizeAppPath)(e),l=(0,o.getNamedRouteRegex)(i,{prefixRouteKeys:!1}),p=(0,r.interpolateDynamicPath)(i,t,l),{name:m,ext:u}=n.default.parse(a),x=d(n.default.posix.join(e,m)),f=x?`-${x}`:"";return(0,c.normalizePathSep)(n.default.join(p,`${m}${f}${u}`))}function u(e){if(!(0,i.isMetadataPage)(e))return e;let t=e,a="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":a=d(e),!t.endsWith("/route")){let{dir:e,name:i,ext:r}=n.default.parse(t);t=n.default.posix.join(e,`${i}${a?`-${a}`:""}${r}`,"route")}return t}function x(e,t){let a=e.endsWith("/route"),i=a?e.slice(0,-6):e,n=i.endsWith("/sitemap")?".xml":"";return(t?`${i}/[__metadata_id__]`:`${i}${n}`)+(a?"/route":"")}},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},35362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var a=function(e){for(var t=[],a=0;a<e.length;){var i=e[a];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:a,value:e[a++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:a++,value:e[a++]});continue}if("{"===i){t.push({type:"OPEN",index:a,value:e[a++]});continue}if("}"===i){t.push({type:"CLOSE",index:a,value:e[a++]});continue}if(":"===i){for(var n="",r=a+1;r<e.length;){var o=e.charCodeAt(r);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){n+=e[r++];continue}break}if(!n)throw TypeError("Missing parameter name at "+a);t.push({type:"NAME",index:a,value:n}),a=r;continue}if("("===i){var l=1,s="",r=a+1;if("?"===e[r])throw TypeError('Pattern cannot start with "?" at '+r);for(;r<e.length;){if("\\"===e[r]){s+=e[r++]+e[r++];continue}if(")"===e[r]){if(0==--l){r++;break}}else if("("===e[r]&&(l++,"?"!==e[r+1]))throw TypeError("Capturing groups are not allowed at "+r);s+=e[r++]}if(l)throw TypeError("Unbalanced pattern at "+a);if(!s)throw TypeError("Missing pattern at "+a);t.push({type:"PATTERN",index:a,value:s}),a=r;continue}t.push({type:"CHAR",index:a,value:e[a++]})}return t.push({type:"END",index:a,value:""}),t}(e),i=t.prefixes,r=void 0===i?"./":i,o="[^"+n(t.delimiter||"/#?")+"]+?",l=[],s=0,c=0,p="",d=function(e){if(c<a.length&&a[c].type===e)return a[c++].value},m=function(e){var t=d(e);if(void 0!==t)return t;var i=a[c];throw TypeError("Unexpected "+i.type+" at "+i.index+", expected "+e)},u=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<a.length;){var x=d("CHAR"),f=d("NAME"),v=d("PATTERN");if(f||v){var g=x||"";-1===r.indexOf(g)&&(p+=g,g=""),p&&(l.push(p),p=""),l.push({name:f||s++,prefix:g,suffix:"",pattern:v||o,modifier:d("MODIFIER")||""});continue}var h=x||d("ESCAPED_CHAR");if(h){p+=h;continue}if(p&&(l.push(p),p=""),d("OPEN")){var g=u(),b=d("NAME")||"",y=d("PATTERN")||"",j=u();m("CLOSE"),l.push({name:b||(y?s++:""),pattern:b&&!y?o:y,prefix:g,suffix:j,modifier:d("MODIFIER")||""});continue}m("END")}return l}function a(e,t){void 0===t&&(t={});var a=r(t),i=t.encode,n=void 0===i?function(e){return e}:i,o=t.validate,l=void 0===o||o,s=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",a)});return function(t){for(var a="",i=0;i<e.length;i++){var r=e[i];if("string"==typeof r){a+=r;continue}var o=t?t[r.name]:void 0,c="?"===r.modifier||"*"===r.modifier,p="*"===r.modifier||"+"===r.modifier;if(Array.isArray(o)){if(!p)throw TypeError('Expected "'+r.name+'" to not repeat, but got an array');if(0===o.length){if(c)continue;throw TypeError('Expected "'+r.name+'" to not be empty')}for(var d=0;d<o.length;d++){var m=n(o[d],r);if(l&&!s[i].test(m))throw TypeError('Expected all "'+r.name+'" to match "'+r.pattern+'", but got "'+m+'"');a+=r.prefix+m+r.suffix}continue}if("string"==typeof o||"number"==typeof o){var m=n(String(o),r);if(l&&!s[i].test(m))throw TypeError('Expected "'+r.name+'" to match "'+r.pattern+'", but got "'+m+'"');a+=r.prefix+m+r.suffix;continue}if(!c){var u=p?"an array":"a string";throw TypeError('Expected "'+r.name+'" to be '+u)}}return a}}function i(e,t,a){void 0===a&&(a={});var i=a.decode,n=void 0===i?function(e){return e}:i;return function(a){var i=e.exec(a);if(!i)return!1;for(var r=i[0],o=i.index,l=Object.create(null),s=1;s<i.length;s++)!function(e){if(void 0!==i[e]){var a=t[e-1];"*"===a.modifier||"+"===a.modifier?l[a.name]=i[e].split(a.prefix+a.suffix).map(function(e){return n(e,a)}):l[a.name]=n(i[e],a)}}(s);return{path:r,index:o,params:l}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function r(e){return e&&e.sensitive?"":"i"}function o(e,t,a){void 0===a&&(a={});for(var i=a.strict,o=void 0!==i&&i,l=a.start,s=a.end,c=a.encode,p=void 0===c?function(e){return e}:c,d="["+n(a.endsWith||"")+"]|$",m="["+n(a.delimiter||"/#?")+"]",u=void 0===l||l?"^":"",x=0;x<e.length;x++){var f=e[x];if("string"==typeof f)u+=n(p(f));else{var v=n(p(f.prefix)),g=n(p(f.suffix));if(f.pattern)if(t&&t.push(f),v||g)if("+"===f.modifier||"*"===f.modifier){var h="*"===f.modifier?"?":"";u+="(?:"+v+"((?:"+f.pattern+")(?:"+g+v+"(?:"+f.pattern+"))*)"+g+")"+h}else u+="(?:"+v+"("+f.pattern+")"+g+")"+f.modifier;else u+="("+f.pattern+")"+f.modifier;else u+="(?:"+v+g+")"+f.modifier}}if(void 0===s||s)o||(u+=m+"?"),u+=a.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],y="string"==typeof b?m.indexOf(b[b.length-1])>-1:void 0===b;o||(u+="(?:"+m+"(?="+d+"))?"),y||(u+="(?="+m+"|"+d+")")}return new RegExp(u,r(a))}function l(t,a,i){if(t instanceof RegExp){if(!a)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var s=0;s<n.length;s++)a.push({name:s,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,a,i).source}).join("|")+")",r(i)):o(e(t,i),a,i)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,i){return a(e(t,i),i)},t.tokensToFunction=a,t.match=function(e,t){var a=[];return i(l(e,a,t),a,t)},t.regexpToFunction=i,t.tokensToRegexp=o,t.pathToRegexp=l})(),e.exports=t})()},42785:(e,t)=>{"use strict";function a(e){let t={};for(let[a,i]of e.entries()){let e=t[a];void 0===e?t[a]=i:Array.isArray(e)?e.push(i):t[a]=[e,i]}return t}function i(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[a,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(a,i(e));else t.set(a,i(n));return t}function r(e){for(var t=arguments.length,a=Array(t>1?t-1:0),i=1;i<t;i++)a[i-1]=arguments[i];for(let t of a){for(let a of t.keys())e.delete(a);for(let[a,i]of t.entries())e.append(a,i)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{assign:function(){return r},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return n}})},44827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DecodeError:function(){return x},MiddlewareNotFoundError:function(){return h},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return v},SP:function(){return m},ST:function(){return u},WEB_VITALS:function(){return a},execOnce:function(){return i},getDisplayName:function(){return s},getLocationOrigin:function(){return o},getURL:function(){return l},isAbsoluteUrl:function(){return r},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return p},stringifyError:function(){return b}});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function i(e){let t,a=!1;return function(){for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];return a||(a=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,r=e=>n.test(e);function o(){let{protocol:e,hostname:t,port:a}=window.location;return e+"//"+t+(a?":"+a:"")}function l(){let{href:e}=window.location,t=o();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function p(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let a=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let i=await e.getInitialProps(t);if(a&&c(a))return i;if(!i)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+i+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i}let m="undefined"!=typeof performance,u=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class x extends Error{}class f extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class h extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},47753:(e,t,a)=>{Promise.resolve().then(a.bind(a,21204))},49694:()=>{},53293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let a=/[|\\{}()[\]^$+*?.-]/,i=/[|\\{}()[\]^$+*?.-]/g;function n(e){return a.test(e)?e.replace(i,"\\$&"):e}},54422:()=>{},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var i=a(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70554:(e,t)=>{"use strict";function a(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return a}})},71437:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return r}});let i=a(74722),n=["(..)(..)","(.)","(..)","(...)"];function r(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function o(e){let t,a,r;for(let i of e.split("/"))if(a=n.find(e=>i.startsWith(e))){[t,r]=e.split(a,2);break}if(!t||!a||!r)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,i.normalizeAppPath)(t),a){case"(.)":r="/"===t?"/"+r:t+"/"+r;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});r=t.split("/").slice(0,-1).concat(r).join("/");break;case"(...)":r="/"+r;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});r=o.slice(0,-2).concat(r).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:r}}},74722:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{normalizeAppPath:function(){return r},normalizeRscURL:function(){return o}});let i=a(85531),n=a(35499);function r(e){return(0,i.ensureLeadingSlash)(e.split("/").reduce((e,t,a,i)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&a===i.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},76759:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return r}});let i=a(42785),n=a(23736);function r(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,i.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},78034:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let i=a(44827);function n(e){let{re:t,groups:a}=e;return e=>{let n=t.exec(e);if(!n)return!1;let r=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new i.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(a)){let a=n[t.pos];void 0!==a&&(t.repeat?o[e]=a.split("/").map(e=>r(e)):o[e]=r(a))}return o}}},79551:e=>{"use strict";e.exports=require("url")},84031:(e,t,a)=>{"use strict";var i=a(34452);function n(){}function r(){}r.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,r,o){if(o!==i){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:n};return a.PropTypes=a,a}},85531:(e,t)=>{"use strict";function a(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return a}})},87955:(e,t,a)=>{e.exports=a(84031)()},88212:(e,t,a)=>{"use strict";function i(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:i}=a(26415);return i(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return i}})},89609:(e,t,a)=>{Promise.resolve().then(a.bind(a,21376))},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,metadata:()=>s});var i=a(37413),n=a(2202),r=a.n(n),o=a(64988),l=a.n(o);a(61135);let s={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:`${r().variable} ${l().variable} antialiased`,children:e})})}},96402:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>m,tree:()=>c});var i=a(65239),n=a(48088),r=a(88170),o=a.n(r),l=a(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);a.d(t,s);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,21204)),"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[243,169],()=>a(96402));module.exports=i})();