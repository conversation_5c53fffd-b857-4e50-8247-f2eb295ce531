[{"/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/__tests__/components/Header.test.tsx": "1", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/__tests__/lib/recommendation-engine.test.ts": "2", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/analyze/route.ts": "3", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/auth/[...nextauth]/route.ts": "4", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/health/route.ts": "5", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/products/route.ts": "6", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/recommendations/route.ts": "7", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/shopping/route.ts": "8", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/upload/route.ts": "9", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx": "10", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx": "11", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/AuthButton.tsx": "12", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx": "13", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx": "14", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx": "15", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx": "16", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx": "17", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx": "18", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ShoppingList.tsx": "19", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/ai-models.ts": "20", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/database.ts": "21", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/product-database.ts": "22", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/recommendation-engine.ts": "23", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/shopping-service.ts": "24", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/style-matcher.ts": "25", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/types/index.ts": "26", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/utils/api-helpers.ts": "27", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/utils/image-processing.ts": "28", "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/types/next-auth.d.ts": "29"}, {"size": 873, "mtime": 1750794337175, "results": "30", "hashOfConfig": "31"}, {"size": 1809, "mtime": 1750794216316, "results": "32", "hashOfConfig": "31"}, {"size": 8283, "mtime": 1750703894240, "results": "33", "hashOfConfig": "31"}, {"size": 1269, "mtime": 1750794509528, "results": "34", "hashOfConfig": "31"}, {"size": 2104, "mtime": 1750703943185, "results": "35", "hashOfConfig": "31"}, {"size": 7306, "mtime": 1750704735168, "results": "36", "hashOfConfig": "31"}, {"size": 7976, "mtime": 1750703927986, "results": "37", "hashOfConfig": "31"}, {"size": 6790, "mtime": 1750704818581, "results": "38", "hashOfConfig": "31"}, {"size": 3715, "mtime": 1750703855425, "results": "39", "hashOfConfig": "31"}, {"size": 689, "mtime": 1750703175376, "results": "40", "hashOfConfig": "31"}, {"size": 4851, "mtime": 1750704051019, "results": "41", "hashOfConfig": "31"}, {"size": 2208, "mtime": 1750794093500, "results": "42", "hashOfConfig": "31"}, {"size": 4374, "mtime": 1750704091116, "results": "43", "hashOfConfig": "31"}, {"size": 3505, "mtime": 1750794132677, "results": "44", "hashOfConfig": "31"}, {"size": 6861, "mtime": 1750704120209, "results": "45", "hashOfConfig": "31"}, {"size": 12383, "mtime": 1750704924735, "results": "46", "hashOfConfig": "31"}, {"size": 11406, "mtime": 1750704238352, "results": "47", "hashOfConfig": "31"}, {"size": 9064, "mtime": 1750704156357, "results": "48", "hashOfConfig": "31"}, {"size": 12769, "mtime": 1750704866349, "results": "49", "hashOfConfig": "31"}, {"size": 7989, "mtime": 1750703738095, "results": "50", "hashOfConfig": "31"}, {"size": 7900, "mtime": 1750794662990, "results": "51", "hashOfConfig": "31"}, {"size": 12027, "mtime": 1750704520833, "results": "52", "hashOfConfig": "31"}, {"size": 18227, "mtime": 1750794024830, "results": "53", "hashOfConfig": "31"}, {"size": 9954, "mtime": 1750704785424, "results": "54", "hashOfConfig": "31"}, {"size": 12073, "mtime": 1750704575710, "results": "55", "hashOfConfig": "31"}, {"size": 3397, "mtime": 1750703702026, "results": "56", "hashOfConfig": "31"}, {"size": 5205, "mtime": 1750703968337, "results": "57", "hashOfConfig": "31"}, {"size": 8864, "mtime": 1750703829028, "results": "58", "hashOfConfig": "31"}, {"size": 209, "mtime": 1750794483520, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1x5fo3s", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/__tests__/components/Header.test.tsx", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/__tests__/lib/recommendation-engine.test.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/analyze/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/health/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/products/route.ts", ["147", "148", "149", "150"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/recommendations/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/shopping/route.ts", ["151", "152", "153", "154", "155", "156"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/api/upload/route.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/app/page.tsx", ["157", "158", "159"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/AuthButton.tsx", ["160"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Footer.tsx", ["161"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/Header.tsx", ["162", "163"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ImageUpload.tsx", ["164", "165", "166"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RecommendationDisplay.tsx", ["167", "168", "169"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomAnalysis.tsx", ["170", "171", "172", "173", "174", "175"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/RoomConfiguration.tsx", ["176", "177", "178"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/components/ShoppingList.tsx", ["179", "180", "181", "182", "183"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/ai-models.ts", ["184", "185", "186", "187"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/database.ts", ["188", "189", "190", "191", "192", "193"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/product-database.ts", ["194"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/recommendation-engine.ts", ["195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/shopping-service.ts", ["207", "208", "209"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/lib/style-matcher.ts", ["210"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/types/index.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/utils/api-helpers.ts", ["211", "212", "213"], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/utils/image-processing.ts", [], [], "/Users/<USER>/Documents/augment-projects/My big idea/interior-design-ai/src/types/next-auth.d.ts", ["214"], [], {"ruleId": "215", "severity": 1, "message": "216", "line": 3, "column": 23, "nodeType": null, "messageId": "217", "endLine": 3, "endColumn": 35}, {"ruleId": "218", "severity": 1, "message": "219", "line": 159, "column": 39, "nodeType": "220", "messageId": "221", "endLine": 159, "endColumn": 42, "suggestions": "222"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 166, "column": 41, "nodeType": "220", "messageId": "221", "endLine": 166, "endColumn": 44, "suggestions": "223"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 189, "column": 42, "nodeType": "220", "messageId": "221", "endLine": 189, "endColumn": 45, "suggestions": "224"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 32, "column": 47, "nodeType": "220", "messageId": "221", "endLine": 32, "endColumn": 50, "suggestions": "225"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 66, "column": 48, "nodeType": "220", "messageId": "221", "endLine": 66, "endColumn": 51, "suggestions": "226"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 81, "column": 44, "nodeType": "220", "messageId": "221", "endLine": 81, "endColumn": 47, "suggestions": "227"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 98, "column": 38, "nodeType": "220", "messageId": "221", "endLine": 98, "endColumn": 41, "suggestions": "228"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 115, "column": 46, "nodeType": "220", "messageId": "221", "endLine": 115, "endColumn": 49, "suggestions": "229"}, {"ruleId": "215", "severity": 1, "message": "230", "line": 167, "column": 37, "nodeType": null, "messageId": "217", "endLine": 167, "endColumn": 49}, {"ruleId": "215", "severity": 1, "message": "231", "line": 15, "column": 21, "nodeType": null, "messageId": "217", "endLine": 15, "endColumn": 33}, {"ruleId": "218", "severity": 1, "message": "219", "line": 17, "column": 42, "nodeType": "220", "messageId": "221", "endLine": 17, "endColumn": 45, "suggestions": "232"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 41, "column": 51, "nodeType": "220", "messageId": "221", "endLine": 41, "endColumn": 54, "suggestions": "233"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 44, "column": 13, "nodeType": "236", "endLine": 48, "endColumn": 15}, {"ruleId": "215", "severity": 1, "message": "237", "line": 3, "column": 16, "nodeType": null, "messageId": "217", "endLine": 3, "endColumn": 20}, {"ruleId": "215", "severity": 1, "message": "238", "line": 4, "column": 25, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 29}, {"ruleId": "215", "severity": 1, "message": "237", "line": 4, "column": 31, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 35}, {"ruleId": "215", "severity": 1, "message": "239", "line": 5, "column": 27, "nodeType": null, "messageId": "217", "endLine": 5, "endColumn": 36}, {"ruleId": "218", "severity": 1, "message": "219", "line": 8, "column": 26, "nodeType": "220", "messageId": "221", "endLine": 8, "endColumn": 29, "suggestions": "240"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 145, "column": 13, "nodeType": "236", "endLine": 149, "endColumn": 15}, {"ruleId": "218", "severity": 1, "message": "219", "line": 20, "column": 42, "nodeType": "220", "messageId": "221", "endLine": 20, "endColumn": 45, "suggestions": "241"}, {"ruleId": "242", "severity": 1, "message": "243", "line": 25, "column": 6, "nodeType": "244", "endLine": 25, "endColumn": 8, "suggestions": "245"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 268, "column": 15, "nodeType": "236", "endLine": 272, "endColumn": 17}, {"ruleId": "215", "severity": 1, "message": "246", "line": 4, "column": 55, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 58}, {"ruleId": "218", "severity": 1, "message": "219", "line": 9, "column": 40, "nodeType": "220", "messageId": "221", "endLine": 9, "endColumn": 43, "suggestions": "247"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 16, "column": 56, "nodeType": "220", "messageId": "221", "endLine": 16, "endColumn": 59, "suggestions": "248"}, {"ruleId": "242", "severity": 1, "message": "249", "line": 21, "column": 6, "nodeType": "244", "endLine": 21, "endColumn": 8, "suggestions": "250"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 129, "column": 13, "nodeType": "236", "endLine": 133, "endColumn": 15}, {"ruleId": "218", "severity": 1, "message": "219", "line": 139, "column": 71, "nodeType": "220", "messageId": "221", "endLine": 139, "endColumn": 74, "suggestions": "251"}, {"ruleId": "234", "severity": 1, "message": "235", "line": 82, "column": 11, "nodeType": "236", "endLine": 86, "endColumn": 13}, {"ruleId": "252", "severity": 1, "message": "253", "line": 121, "column": 19, "nodeType": "254", "messageId": "255", "suggestions": "256"}, {"ruleId": "252", "severity": 1, "message": "253", "line": 148, "column": 19, "nodeType": "254", "messageId": "255", "suggestions": "257"}, {"ruleId": "215", "severity": 1, "message": "258", "line": 12, "column": 3, "nodeType": null, "messageId": "217", "endLine": 12, "endColumn": 13}, {"ruleId": "215", "severity": 1, "message": "259", "line": 13, "column": 3, "nodeType": null, "messageId": "217", "endLine": 13, "endColumn": 15}, {"ruleId": "215", "severity": 1, "message": "260", "line": 39, "column": 10, "nodeType": null, "messageId": "217", "endLine": 39, "endColumn": 19}, {"ruleId": "215", "severity": 1, "message": "261", "line": 59, "column": 9, "nodeType": null, "messageId": "217", "endLine": 59, "endColumn": 23}, {"ruleId": "234", "severity": 1, "message": "235", "line": 274, "column": 15, "nodeType": "236", "endLine": 278, "endColumn": 17}, {"ruleId": "215", "severity": 1, "message": "262", "line": 4, "column": 55, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 60}, {"ruleId": "218", "severity": 1, "message": "219", "line": 145, "column": 52, "nodeType": "220", "messageId": "221", "endLine": 145, "endColumn": 55, "suggestions": "263"}, {"ruleId": "215", "severity": 1, "message": "264", "line": 201, "column": 37, "nodeType": null, "messageId": "217", "endLine": 201, "endColumn": 49}, {"ruleId": "215", "severity": 1, "message": "265", "line": 206, "column": 35, "nodeType": null, "messageId": "217", "endLine": 206, "endColumn": 44}, {"ruleId": "215", "severity": 1, "message": "266", "line": 4, "column": 10, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 14}, {"ruleId": "215", "severity": 1, "message": "267", "line": 4, "column": 16, "nodeType": null, "messageId": "217", "endLine": 4, "endColumn": 39}, {"ruleId": "218", "severity": 1, "message": "219", "line": 56, "column": 67, "nodeType": "220", "messageId": "221", "endLine": 56, "endColumn": 70, "suggestions": "268"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 134, "column": 18, "nodeType": "220", "messageId": "221", "endLine": 134, "endColumn": 21, "suggestions": "269"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 169, "column": 19, "nodeType": "220", "messageId": "221", "endLine": 169, "endColumn": 22, "suggestions": "270"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 311, "column": 17, "nodeType": "220", "messageId": "221", "endLine": 311, "endColumn": 20, "suggestions": "271"}, {"ruleId": "215", "severity": 1, "message": "272", "line": 3, "column": 67, "nodeType": null, "messageId": "217", "endLine": 3, "endColumn": 75}, {"ruleId": "215", "severity": 1, "message": "262", "line": 9, "column": 3, "nodeType": null, "messageId": "217", "endLine": 9, "endColumn": 8}, {"ruleId": "215", "severity": 1, "message": "273", "line": 13, "column": 10, "nodeType": null, "messageId": "217", "endLine": 13, "endColumn": 25}, {"ruleId": "218", "severity": 1, "message": "219", "line": 238, "column": 20, "nodeType": "220", "messageId": "221", "endLine": 238, "endColumn": 23, "suggestions": "274"}, {"ruleId": "215", "severity": 1, "message": "275", "line": 311, "column": 11, "nodeType": null, "messageId": "217", "endLine": 311, "endColumn": 24}, {"ruleId": "218", "severity": 1, "message": "219", "line": 366, "column": 22, "nodeType": "220", "messageId": "221", "endLine": 366, "endColumn": 25, "suggestions": "276"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 370, "column": 6, "nodeType": "220", "messageId": "221", "endLine": 370, "endColumn": 9, "suggestions": "277"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 371, "column": 21, "nodeType": "220", "messageId": "221", "endLine": 371, "endColumn": 24, "suggestions": "278"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 429, "column": 15, "nodeType": "220", "messageId": "221", "endLine": 429, "endColumn": 18, "suggestions": "279"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 430, "column": 15, "nodeType": "220", "messageId": "221", "endLine": 430, "endColumn": 18, "suggestions": "280"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 460, "column": 11, "nodeType": "220", "messageId": "221", "endLine": 460, "endColumn": 14, "suggestions": "281"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 485, "column": 36, "nodeType": "220", "messageId": "221", "endLine": 485, "endColumn": 39, "suggestions": "282"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 485, "column": 70, "nodeType": "220", "messageId": "221", "endLine": 485, "endColumn": 73, "suggestions": "283"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 173, "column": 55, "nodeType": "220", "messageId": "221", "endLine": 173, "endColumn": 58, "suggestions": "284"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 174, "column": 20, "nodeType": "220", "messageId": "221", "endLine": 174, "endColumn": 23, "suggestions": "285"}, {"ruleId": "215", "severity": 1, "message": "286", "line": 240, "column": 33, "nodeType": null, "messageId": "217", "endLine": 240, "endColumn": 42}, {"ruleId": "218", "severity": 1, "message": "219", "line": 91, "column": 60, "nodeType": "220", "messageId": "221", "endLine": 91, "endColumn": 63, "suggestions": "287"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 72, "column": 24, "nodeType": "220", "messageId": "221", "endLine": 72, "endColumn": 27, "suggestions": "288"}, {"ruleId": "215", "severity": 1, "message": "289", "line": 197, "column": 3, "nodeType": null, "messageId": "217", "endLine": 197, "endColumn": 14}, {"ruleId": "215", "severity": 1, "message": "290", "line": 204, "column": 9, "nodeType": null, "messageId": "217", "endLine": 204, "endColumn": 20}, {"ruleId": "215", "severity": 1, "message": "291", "line": 1, "column": 8, "nodeType": null, "messageId": "217", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'NextResponse' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["292", "293"], ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], ["306", "307"], "'searchParams' is defined but never used.", "'setIsLoading' is assigned a value but never used.", ["308", "309"], ["310", "311"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'Mail' is defined but never used.", "'Info' is defined but never used.", "'ImageIcon' is defined but never used.", ["312", "313"], ["314", "315"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'generateRecommendations'. Either include it or remove the dependency array.", "ArrayExpression", ["316"], "'Eye' is defined but never used.", ["317", "318"], ["319", "320"], "React Hook useEffect has a missing dependency: 'startAnalysis'. Either include it or remove the dependency array.", ["321"], ["322", "323"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["324", "325", "326", "327"], ["328", "329", "330", "331"], "'DollarSign' is defined but never used.", "'TrendingDown' is defined but never used.", "'shareLink' is assigned a value but never used.", "'updatePriority' is assigned a value but never used.", "'Point' is defined but never used.", ["332", "333"], "'imageElement' is defined but never used.", "'furniture' is defined but never used.", "'Room' is defined but never used.", "'FurnitureRecommendation' is defined but never used.", ["334", "335"], ["336", "337"], ["338", "339"], ["340", "341"], "'RoomType' is defined but never used.", "'DatabaseService' is defined but never used.", ["342", "343"], "'neutralColors' is assigned a value but never used.", ["344", "345"], ["346", "347"], ["348", "349"], ["350", "351"], ["352", "353"], ["354", "355"], ["356", "357"], ["358", "359"], ["360", "361"], ["362", "363"], "'productId' is defined but never used.", ["364", "365"], ["366", "367"], "'maxRequests' is assigned a value but never used.", "'windowStart' is assigned a value but never used.", "'NextAuth' is defined but never used.", {"messageId": "368", "fix": "369", "desc": "370"}, {"messageId": "371", "fix": "372", "desc": "373"}, {"messageId": "368", "fix": "374", "desc": "370"}, {"messageId": "371", "fix": "375", "desc": "373"}, {"messageId": "368", "fix": "376", "desc": "370"}, {"messageId": "371", "fix": "377", "desc": "373"}, {"messageId": "368", "fix": "378", "desc": "370"}, {"messageId": "371", "fix": "379", "desc": "373"}, {"messageId": "368", "fix": "380", "desc": "370"}, {"messageId": "371", "fix": "381", "desc": "373"}, {"messageId": "368", "fix": "382", "desc": "370"}, {"messageId": "371", "fix": "383", "desc": "373"}, {"messageId": "368", "fix": "384", "desc": "370"}, {"messageId": "371", "fix": "385", "desc": "373"}, {"messageId": "368", "fix": "386", "desc": "370"}, {"messageId": "371", "fix": "387", "desc": "373"}, {"messageId": "368", "fix": "388", "desc": "370"}, {"messageId": "371", "fix": "389", "desc": "373"}, {"messageId": "368", "fix": "390", "desc": "370"}, {"messageId": "371", "fix": "391", "desc": "373"}, {"messageId": "368", "fix": "392", "desc": "370"}, {"messageId": "371", "fix": "393", "desc": "373"}, {"messageId": "368", "fix": "394", "desc": "370"}, {"messageId": "371", "fix": "395", "desc": "373"}, {"desc": "396", "fix": "397"}, {"messageId": "368", "fix": "398", "desc": "370"}, {"messageId": "371", "fix": "399", "desc": "373"}, {"messageId": "368", "fix": "400", "desc": "370"}, {"messageId": "371", "fix": "401", "desc": "373"}, {"desc": "402", "fix": "403"}, {"messageId": "368", "fix": "404", "desc": "370"}, {"messageId": "371", "fix": "405", "desc": "373"}, {"messageId": "406", "data": "407", "fix": "408", "desc": "409"}, {"messageId": "406", "data": "410", "fix": "411", "desc": "412"}, {"messageId": "406", "data": "413", "fix": "414", "desc": "415"}, {"messageId": "406", "data": "416", "fix": "417", "desc": "418"}, {"messageId": "406", "data": "419", "fix": "420", "desc": "409"}, {"messageId": "406", "data": "421", "fix": "422", "desc": "412"}, {"messageId": "406", "data": "423", "fix": "424", "desc": "415"}, {"messageId": "406", "data": "425", "fix": "426", "desc": "418"}, {"messageId": "368", "fix": "427", "desc": "370"}, {"messageId": "371", "fix": "428", "desc": "373"}, {"messageId": "368", "fix": "429", "desc": "370"}, {"messageId": "371", "fix": "430", "desc": "373"}, {"messageId": "368", "fix": "431", "desc": "370"}, {"messageId": "371", "fix": "432", "desc": "373"}, {"messageId": "368", "fix": "433", "desc": "370"}, {"messageId": "371", "fix": "434", "desc": "373"}, {"messageId": "368", "fix": "435", "desc": "370"}, {"messageId": "371", "fix": "436", "desc": "373"}, {"messageId": "368", "fix": "437", "desc": "370"}, {"messageId": "371", "fix": "438", "desc": "373"}, {"messageId": "368", "fix": "439", "desc": "370"}, {"messageId": "371", "fix": "440", "desc": "373"}, {"messageId": "368", "fix": "441", "desc": "370"}, {"messageId": "371", "fix": "442", "desc": "373"}, {"messageId": "368", "fix": "443", "desc": "370"}, {"messageId": "371", "fix": "444", "desc": "373"}, {"messageId": "368", "fix": "445", "desc": "370"}, {"messageId": "371", "fix": "446", "desc": "373"}, {"messageId": "368", "fix": "447", "desc": "370"}, {"messageId": "371", "fix": "448", "desc": "373"}, {"messageId": "368", "fix": "449", "desc": "370"}, {"messageId": "371", "fix": "450", "desc": "373"}, {"messageId": "368", "fix": "451", "desc": "370"}, {"messageId": "371", "fix": "452", "desc": "373"}, {"messageId": "368", "fix": "453", "desc": "370"}, {"messageId": "371", "fix": "454", "desc": "373"}, {"messageId": "368", "fix": "455", "desc": "370"}, {"messageId": "371", "fix": "456", "desc": "373"}, {"messageId": "368", "fix": "457", "desc": "370"}, {"messageId": "371", "fix": "458", "desc": "373"}, {"messageId": "368", "fix": "459", "desc": "370"}, {"messageId": "371", "fix": "460", "desc": "373"}, {"messageId": "368", "fix": "461", "desc": "370"}, {"messageId": "371", "fix": "462", "desc": "373"}, "suggestUnknown", {"range": "463", "text": "464"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "465", "text": "466"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "467", "text": "464"}, {"range": "468", "text": "466"}, {"range": "469", "text": "464"}, {"range": "470", "text": "466"}, {"range": "471", "text": "464"}, {"range": "472", "text": "466"}, {"range": "473", "text": "464"}, {"range": "474", "text": "466"}, {"range": "475", "text": "464"}, {"range": "476", "text": "466"}, {"range": "477", "text": "464"}, {"range": "478", "text": "466"}, {"range": "479", "text": "464"}, {"range": "480", "text": "466"}, {"range": "481", "text": "464"}, {"range": "482", "text": "466"}, {"range": "483", "text": "464"}, {"range": "484", "text": "466"}, {"range": "485", "text": "464"}, {"range": "486", "text": "466"}, {"range": "487", "text": "464"}, {"range": "488", "text": "466"}, "Update the dependencies array to be: [generateRecommendations]", {"range": "489", "text": "490"}, {"range": "491", "text": "464"}, {"range": "492", "text": "466"}, {"range": "493", "text": "464"}, {"range": "494", "text": "466"}, "Update the dependencies array to be: [startAnalysis]", {"range": "495", "text": "496"}, {"range": "497", "text": "464"}, {"range": "498", "text": "466"}, "replaceWithAlt", {"alt": "499"}, {"range": "500", "text": "501"}, "Replace with `&apos;`.", {"alt": "502"}, {"range": "503", "text": "504"}, "Replace with `&lsquo;`.", {"alt": "505"}, {"range": "506", "text": "507"}, "Replace with `&#39;`.", {"alt": "508"}, {"range": "509", "text": "510"}, "Replace with `&rsquo;`.", {"alt": "499"}, {"range": "511", "text": "512"}, {"alt": "502"}, {"range": "513", "text": "514"}, {"alt": "505"}, {"range": "515", "text": "516"}, {"alt": "508"}, {"range": "517", "text": "518"}, {"range": "519", "text": "464"}, {"range": "520", "text": "466"}, {"range": "521", "text": "464"}, {"range": "522", "text": "466"}, {"range": "523", "text": "464"}, {"range": "524", "text": "466"}, {"range": "525", "text": "464"}, {"range": "526", "text": "466"}, {"range": "527", "text": "464"}, {"range": "528", "text": "466"}, {"range": "529", "text": "464"}, {"range": "530", "text": "466"}, {"range": "531", "text": "464"}, {"range": "532", "text": "466"}, {"range": "533", "text": "464"}, {"range": "534", "text": "466"}, {"range": "535", "text": "464"}, {"range": "536", "text": "466"}, {"range": "537", "text": "464"}, {"range": "538", "text": "466"}, {"range": "539", "text": "464"}, {"range": "540", "text": "466"}, {"range": "541", "text": "464"}, {"range": "542", "text": "466"}, {"range": "543", "text": "464"}, {"range": "544", "text": "466"}, {"range": "545", "text": "464"}, {"range": "546", "text": "466"}, {"range": "547", "text": "464"}, {"range": "548", "text": "466"}, {"range": "549", "text": "464"}, {"range": "550", "text": "466"}, {"range": "551", "text": "464"}, {"range": "552", "text": "466"}, {"range": "553", "text": "464"}, {"range": "554", "text": "466"}, [4772, 4775], "unknown", [4772, 4775], "never", [4954, 4957], [4954, 4957], [5405, 5408], [5405, 5408], [1050, 1053], [1050, 1053], [1910, 1913], [1910, 1913], [2292, 2295], [2292, 2295], [2739, 2742], [2739, 2742], [3200, 3203], [3200, 3203], [754, 757], [754, 757], [1280, 1283], [1280, 1283], [238, 241], [238, 241], [871, 874], [871, 874], [1007, 1009], "[generateRecommendations]", [274, 277], [274, 277], [618, 621], [618, 621], [724, 726], "[startAnalysis]", [4556, 4559], [4556, 4559], "&apos;", [4630, 4686], "\n              What&apos;s your preferred style?\n            ", "&lsquo;", [4630, 4686], "\n              What&lsquo;s your preferred style?\n            ", "&#39;", [4630, 4686], "\n              What&#39;s your preferred style?\n            ", "&rsquo;", [4630, 4686], "\n              What&rsquo;s your preferred style?\n            ", [5697, 5744], "\n              What&apos;s your budget?\n            ", [5697, 5744], "\n              What&lsquo;s your budget?\n            ", [5697, 5744], "\n              What&#39;s your budget?\n            ", [5697, 5744], "\n              What&rsquo;s your budget?\n            ", [5241, 5244], [5241, 5244], [1301, 1304], [1301, 1304], [2973, 2976], [2973, 2976], [3951, 3954], [3951, 3954], [7036, 7039], [7036, 7039], [8091, 8094], [8091, 8094], [13063, 13066], [13063, 13066], [13142, 13145], [13142, 13145], [13170, 13173], [13170, 13173], [15195, 15198], [15195, 15198], [15216, 15219], [15216, 15219], [16282, 16285], [16282, 16285], [16947, 16950], [16947, 16950], [16981, 16984], [16981, 16984], [4491, 4494], [4491, 4494], [4523, 4526], [4523, 4526], [3709, 3712], [3709, 3712], [1664, 1667], [1664, 1667]]