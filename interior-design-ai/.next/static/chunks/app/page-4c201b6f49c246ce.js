(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1007:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>J});var a=t(5155),l=t(2115),r=t(6710),i=t(9869),n=t(4416),c=t(5339);function o(e){let{onUpload:s,isLoading:t}=e,[o,d]=(0,l.useState)(null),[x,m]=(0,l.useState)(null),[h,u]=(0,l.useState)(0),g=(0,l.useCallback)(async e=>{let t=e[0];if(t){if(m(null),u(0),!t.type.startsWith("image/"))return void m("Please upload an image file");if(t.size>0xa00000)return void m("Image must be smaller than 10MB");d(URL.createObjectURL(t));try{let e=setInterval(()=>{u(s=>s>=90?(clearInterval(e),90):s+10)},100),a=new FormData;a.append("image",t);let l=await fetch("/api/upload",{method:"POST",body:a});if(clearInterval(e),u(100),!l.ok){let e=await l.json();throw Error(e.error||"Upload failed")}let r=await l.json();if(r.success)s(r.data);else throw Error(r.error||"Upload failed")}catch(e){m(e instanceof Error?e.message:"Upload failed"),d(null),u(0)}}},[s]),{getRootProps:p,getInputProps:b,isDragActive:y}=(0,r.VB)({onDrop:g,accept:{"image/*":[".jpeg",".jpg",".png",".webp"]},maxFiles:1,disabled:t}),j=()=>{d(null),m(null),u(0)};return(0,a.jsxs)("div",{className:"w-full max-w-2xl mx-auto",children:[o?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:o,alt:"Room preview",className:"w-full h-64 object-cover rounded-lg"}),(0,a.jsx)("button",{onClick:j,className:"absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-opacity",children:(0,a.jsx)(n.A,{className:"w-4 h-4 text-white"})})]}),h>0&&h<100&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Uploading..."}),(0,a.jsxs)("span",{className:"text-gray-600",children:[h,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})})]}),100===h&&!x&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-600",children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-green-600 rounded-full"})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Upload complete!"})]})]}):(0,a.jsxs)("div",{...p(),className:"\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n            ".concat(y?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-blue-400 hover:bg-gray-50","\n            ").concat(t?"opacity-50 cursor-not-allowed":"","\n          "),children:[(0,a.jsx)("input",{...b()}),(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:y?"Drop your image here":"Upload a room photo"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Drag and drop an image, or click to browse"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supports JPEG, PNG, WebP up to 10MB"})]}),(0,a.jsx)("button",{type:"button",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium",disabled:t,children:"Choose File"})]})]}),x&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-800 font-medium",children:"Upload Error"})]}),(0,a.jsx)("p",{className:"text-red-700 mt-1",children:x}),(0,a.jsx)("button",{onClick:j,className:"mt-2 text-red-600 hover:text-red-800 text-sm font-medium",children:"Try again"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCF8 Tips for best results:"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Take photos in good lighting"}),(0,a.jsx)("li",{children:"• Include the entire room in the frame"}),(0,a.jsx)("li",{children:"• Avoid cluttered or messy spaces"}),(0,a.jsx)("li",{children:"• Make sure furniture and walls are clearly visible"})]})]})]})}var d=t(5868),x=t(7550),m=t(2138);let h=[{value:"living_room",label:"Living Room",icon:"\uD83D\uDECB️"},{value:"bedroom",label:"Bedroom",icon:"\uD83D\uDECF️"},{value:"kitchen",label:"Kitchen",icon:"\uD83C\uDF73"},{value:"bathroom",label:"Bathroom",icon:"\uD83D\uDEBF"},{value:"dining_room",label:"Dining Room",icon:"\uD83C\uDF7D️"},{value:"office",label:"Office",icon:"\uD83D\uDCBC"},{value:"nursery",label:"Nursery",icon:"\uD83D\uDC76"},{value:"guest_room",label:"Guest Room",icon:"\uD83C\uDFE0"}],u=[{value:"modern",label:"Modern",description:"Clean lines, minimal clutter, neutral colors"},{value:"minimalist",label:"Minimalist",description:"Less is more, functional, simple"},{value:"boho",label:"Bohemian",description:"Eclectic, colorful, artistic"},{value:"industrial",label:"Industrial",description:"Raw materials, exposed elements"},{value:"scandinavian",label:"Scandinavian",description:"Light woods, cozy, functional"},{value:"traditional",label:"Traditional",description:"Classic, elegant, timeless"},{value:"contemporary",label:"Contemporary",description:"Current trends, sophisticated"},{value:"rustic",label:"Rustic",description:"Natural materials, warm, cozy"}],g=[{min:500,max:1e3,label:"$500 - $1,000"},{min:1e3,max:2500,label:"$1,000 - $2,500"},{min:2500,max:5e3,label:"$2,500 - $5,000"},{min:5e3,max:1e4,label:"$5,000 - $10,000"},{min:1e4,max:25e3,label:"$10,000+"}];function p(e){let{imageUrl:s,onConfiguration:t,onBack:r}=e,[i,n]=(0,l.useState)(null),[c,o]=(0,l.useState)(null),[p,b]=(0,l.useState)(null),[y,j]=(0,l.useState)(""),[f,N]=(0,l.useState)(!1),v=i&&c&&p;return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Configure Your Room"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Help us understand your space and preferences for better recommendations"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Room"}),(0,a.jsx)("img",{src:s,alt:"Room preview",className:"w-full h-64 object-cover rounded-lg border border-gray-200"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What type of room is this?"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:h.map(e=>(0,a.jsx)("button",{onClick:()=>n(e.value),className:"\n                    p-3 rounded-lg border-2 text-left transition-colors\n                    ".concat(i===e.value?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300","\n                  "),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:e.icon}),(0,a.jsx)("span",{className:"font-medium",children:e.label})]})},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's your preferred style?"}),(0,a.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,a.jsxs)("button",{onClick:()=>o(e.value),className:"\n                    w-full p-3 rounded-lg border-2 text-left transition-colors\n                    ".concat(c===e.value?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300","\n                  "),children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:e.description})]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"What's your budget?"}),f?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)("input",{type:"number",value:y,onChange:e=>{j(e.target.value),b(parseFloat(e.target.value)||0)},placeholder:"Enter your budget",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsx)("button",{onClick:()=>{N(!1),j(""),b(null)},className:"text-sm text-gray-600 hover:text-gray-800",children:"Choose from preset ranges"})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[g.map(e=>(0,a.jsx)("button",{onClick:()=>b(e.max),className:"\n                      w-full p-3 rounded-lg border-2 text-left transition-colors\n                      ".concat(p===e.max?"border-blue-500 bg-blue-50 text-blue-900":"border-gray-200 hover:border-gray-300","\n                    "),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"font-medium",children:e.label})]})},e.label)),(0,a.jsx)("button",{onClick:()=>N(!0),className:"w-full p-3 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 text-gray-600 transition-colors",children:"Enter custom amount"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,a.jsxs)("button",{onClick:r,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back"})]}),(0,a.jsxs)("button",{onClick:()=>{i&&c&&p&&t({roomType:i,budget:p,style:c})},disabled:!v,className:"\n            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors\n            ".concat(v?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed","\n          "),children:[(0,a.jsx)("span",{children:"Analyze Room"}),(0,a.jsx)(m.A,{className:"w-4 h-4"})]})]})]})}var b=t(1154),y=t(646),j=t(2417),f=t(3127);function N(e){let{room:s,onAnalysisComplete:t,onBack:r}=e,[i,n]=(0,l.useState)(!1),[c,o]=(0,l.useState)("detecting"),[d,h]=(0,l.useState)(null),[u,g]=(0,l.useState)(0);(0,l.useEffect)(()=>{p()},[]);let p=async()=>{n(!0),o("detecting"),g(0);try{let e=setInterval(()=>{g(s=>s>=90?(clearInterval(e),90):s+5)},200),t=await fetch("/api/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({imageId:s.id,imagePath:s.imageUrl})});if(clearInterval(e),g(100),!t.ok)throw Error("Analysis failed");let a=await t.json();if(a.success)h(a.data),o("complete");else throw Error(a.error||"Analysis failed")}catch(e){console.error("Analysis error:",e),h({analysis:{layout:{doors:[{id:"1",type:"door",boundingBox:{x:50,y:200,width:80,height:200},confidence:.85}],windows:[{id:"2",type:"window",boundingBox:{x:300,y:50,width:150,height:100},confidence:.92}],walls:[],floorArea:2e3},existingFurniture:[{id:"1",type:"couch",category:"seating",boundingBox:{x:200,y:300,width:250,height:120},confidence:.91},{id:"2",type:"coffee table",category:"tables",boundingBox:{x:280,y:420,width:100,height:60},confidence:.78}],colorPalette:["#F5F5F5","#8B7355","#2F4F4F","#D2B48C","#228B22"],lightingSources:[{type:"natural",position:{x:375,y:50},intensity:.8}],spatialFlow:{entryPoints:[{x:90,y:300}],trafficPaths:[],functionalZones:[{id:"seating_area",type:"relaxation",area:[],function:"seating and entertainment"}]}},predictedRoomType:s.type,confidence:.85}),o("complete"),g(100)}finally{n(!1)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI Room Analysis"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Our AI is analyzing your room to understand its layout and existing furniture"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Progress"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:s.imageUrl,alt:"Room being analyzed",className:"w-full h-64 object-cover rounded-lg border border-gray-200"}),d&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 rounded-lg",children:d.analysis.existingFurniture.map(e=>(0,a.jsx)("div",{className:"absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20",style:{left:"".concat(e.boundingBox.x/640*100,"%"),top:"".concat(e.boundingBox.y/480*100,"%"),width:"".concat(e.boundingBox.width/640*100,"%"),height:"".concat(e.boundingBox.height/480*100,"%")},children:(0,a.jsxs)("div",{className:"absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded",children:[e.type," (",Math.round(100*e.confidence),"%)"]})},e.id))})]}),i&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"detecting"===c?"Detecting objects...":"Analyzing layout..."}),(0,a.jsxs)("span",{className:"text-gray-600",children:[u,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(u,"%")}})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Analysis Results"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg ".concat("detecting"===c?"bg-blue-50 border border-blue-200":"bg-gray-50"),children:["detecting"===c?(0,a.jsx)(b.A,{className:"w-5 h-5 text-blue-600 animate-spin"}):(0,a.jsx)(y.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Object Detection"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:d?"Found ".concat(d.analysis.existingFurniture.length," furniture items"):"Identifying furniture and room elements"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg ".concat("analyzing"===c?"bg-blue-50 border border-blue-200":"complete"===c?"bg-gray-50":"bg-gray-100"),children:["analyzing"===c?(0,a.jsx)(b.A,{className:"w-5 h-5 text-blue-600 animate-spin"}):"complete"===c?(0,a.jsx)(y.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(j.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Layout Analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:d?"Room type: ".concat(d.predictedRoomType.replace("_"," ")):"Analyzing room layout and flow"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg ".concat("complete"===c?"bg-green-50 border border-green-200":"bg-gray-100"),children:["complete"===c?(0,a.jsx)(y.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(f.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Style & Color Analysis"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:d?"Color palette and style preferences identified":"Extracting color palette and style elements"})]})]})]}),d&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"✨ Analysis Complete!"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-green-800",children:[(0,a.jsxs)("div",{children:["• Detected ",d.analysis.existingFurniture.length," furniture items"]}),(0,a.jsxs)("div",{children:["• Identified ",d.analysis.layout.doors.length," doors and ",d.analysis.layout.windows.length," windows"]}),(0,a.jsxs)("div",{children:["• Extracted ",d.analysis.colorPalette.length," dominant colors"]}),(0,a.jsxs)("div",{children:["• Room type confidence: ",Math.round(100*d.confidence),"%"]})]})]}),d&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Detected Color Palette"}),(0,a.jsx)("div",{className:"flex space-x-2",children:d.analysis.colorPalette.map((e,s)=>(0,a.jsx)("div",{className:"w-8 h-8 rounded-full border border-gray-300",style:{backgroundColor:e},title:e},s))})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,a.jsxs)("button",{onClick:r,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back"})]}),(0,a.jsxs)("button",{onClick:()=>{d&&t(d)},disabled:!d,className:"\n            flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors\n            ".concat(d?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed","\n          "),children:[(0,a.jsx)("span",{children:"Get Recommendations"}),(0,a.jsx)(m.A,{className:"w-4 h-4"})]})]})]})}var v=t(3904),w=t(6932),A=t(7809),k=t(4653),C=t(5968),S=t(1976),U=t(3786),R=t(1788),I=t(6516),B=t(2525),P=t(7712),T=t(4616);function D(e){let{recommendations:s,budget:t,onItemClick:r}=e,[i,n]=(0,l.useState)(s.map((e,s)=>({product:e,quantity:1,priority:s<3?"high":s<6?"medium":"low"}))),[o,d]=(0,l.useState)(!1),[x,m]=(0,l.useState)(null),h=i.reduce((e,s)=>e+s.product.price*s.quantity,0),u=h/t*100,g=t-h,p=(e,s)=>{s<1||n(t=>t.map(t=>t.product.id===e?{...t,quantity:s}:t))},b=e=>{n(s=>s.filter(s=>s.product.id!==e))},y=async e=>{try{await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"track-click",productId:e.id,retailer:e.retailer})}),window.open(e.affiliateUrl||e.productUrl,"_blank"),r&&r(e)}catch(s){console.error("Error tracking affiliate click:",s),window.open(e.affiliateUrl||e.productUrl,"_blank")}},j=async()=>{d(!0);try{let e=await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"export-csv",shoppingList:{id:"list_".concat(Date.now()),name:"Shopping List - ".concat(new Date().toLocaleDateString()),items:i.map(e=>({id:"item_".concat(e.product.id),product:e.product,quantity:e.quantity,priority:e.priority,addedAt:new Date})),totalCost:h,budget:t,createdAt:new Date,updatedAt:new Date}})});if(e.ok){let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="shopping-list-".concat(Date.now(),".csv"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}}catch(e){console.error("Error exporting CSV:",e)}finally{d(!1)}},f=async()=>{try{let e=await fetch("/api/shopping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generate-share-link",shoppingList:{id:"list_".concat(Date.now()),name:"Shopping List - ".concat(new Date().toLocaleDateString()),items:i.map(e=>({id:"item_".concat(e.product.id),product:e.product,quantity:e.quantity,priority:e.priority,addedAt:new Date})),totalCost:h,budget:t,createdAt:new Date,updatedAt:new Date}})});if(e.ok){let s=await e.json();m(s.data.shareableLink),await navigator.clipboard.writeText(s.data.shareableLink),alert("Share link copied to clipboard!")}}catch(e){console.error("Error generating share link:",e)}},N=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.A,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Shopping List"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:j,disabled:o,className:"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[(0,a.jsx)(R.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:o?"Exporting...":"Export CSV"})]}),(0,a.jsxs)("button",{onClick:f,className:"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,a.jsx)(I.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Share"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:i.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Items"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["$",h.toFixed(2)]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Cost"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(u>100?"text-red-600":"text-gray-900"),children:[u.toFixed(1),"%"]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Budget Used"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(g<0?"text-red-600":"text-green-600"),children:["$",Math.abs(g).toFixed(2)]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:g<0?"Over Budget":"Remaining"})]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(u>100?"bg-red-500":u>80?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(u,100),"%")}})})})]}),u>100&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-800 font-medium",children:"Budget Exceeded"})]}),(0,a.jsxs)("p",{className:"text-red-700 mt-1",children:["Your shopping list is $",(h-t).toFixed(2)," over budget. Consider removing some items or choosing alternatives."]})]}),(0,a.jsx)("div",{className:"space-y-4",children:i.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("img",{src:e.product.imageUrl,alt:e.product.name,className:"w-20 h-20 object-cover rounded-lg"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:e.product.name}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:e.product.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["$",e.product.price]}),(0,a.jsx)("span",{className:"text-sm text-gray-500 capitalize",children:e.product.retailer}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(N(e.priority)),children:e.priority})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("button",{onClick:()=>b(e.product.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(B.A,{className:"w-4 h-4"})})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Quantity:"}),(0,a.jsx)("button",{onClick:()=>p(e.product.id,e.quantity-1),className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,a.jsx)(P.A,{className:"w-3 h-3"})}),(0,a.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>p(e.product.id,e.quantity+1),className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,a.jsx)(T.A,{className:"w-3 h-3"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:["Total: $",(e.product.price*e.quantity).toFixed(2)]}),(0,a.jsxs)("button",{onClick:()=>y(e.product),className:"flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,a.jsx)(U.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Buy Now"})]})]})]})]})]})},e.product.id))}),0===i.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(A.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Your shopping list is empty"})]})]})}function E(e){var s;let{room:t,onStartOver:r,onBack:i}=e,[n,c]=(0,l.useState)([]),[o,d]=(0,l.useState)(!0),[m,h]=(0,l.useState)("grid"),[u,g]=(0,l.useState)("all"),[p,b]=(0,l.useState)(new Set),[y,j]=(0,l.useState)(null),[f,N]=(0,l.useState)(!1);(0,l.useEffect)(()=>{R()},[]);let R=async()=>{d(!0);try{let e=await fetch("/api/recommendations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({roomAnalysis:t.analysisResult,roomType:t.type,budget:t.budget,style:t.style})});if(!e.ok)throw Error("Failed to generate recommendations");let s=await e.json();if(s.success)c(s.data.recommendations),j(s.data.summary);else throw Error(s.error||"Failed to generate recommendations")}catch(e){console.error("Recommendations error:",e),c([{id:"sofa-1",name:"Modern 3-Seat Sofa",category:"seating",description:"Comfortable modern sofa with clean lines",price:899,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"#",retailer:"ikea",dimensions:{width:228,height:83,depth:95},style:["modern","contemporary"],placement:{suggestedPosition:{x:200,y:300},zone:"seating-area"},compatibility:{roomType:[t.type],existingFurniture:[],styleMatch:.9}},{id:"table-1",name:"Glass Coffee Table",category:"tables",description:"Sleek glass coffee table with metal legs",price:299,currency:"USD",imageUrl:"/api/placeholder/400/300",productUrl:"#",retailer:"amazon",dimensions:{width:120,height:45,depth:60},style:["modern","contemporary"],placement:{suggestedPosition:{x:280,y:420},zone:"seating-area"},compatibility:{roomType:[t.type],existingFurniture:[],styleMatch:.85}}]),j({totalItems:2,totalCost:1198,budget:t.budget,budgetUtilization:1198/t.budget*100,remainingBudget:t.budget-1198})}finally{d(!1)}},I=e=>{let s=new Set(p);s.has(e)?s.delete(e):s.add(e),b(s)},B=["all",...Array.from(new Set(n.map(e=>e.category)))],P="all"===u?n:n.filter(e=>e.category===u);return o?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(v.A,{className:"w-8 h-8 text-blue-600 animate-spin mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Generating Recommendations"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Finding the perfect furniture for your space..."})]})}):(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Your Personalized Recommendations"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Based on your ",null==(s=t.type)?void 0:s.replace("_"," ")," in ",t.style," style with a $",t.budget," budget"]})]}),y&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:y.totalItems}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Items"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:["$",y.totalCost]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Total Cost"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[Math.round(y.budgetUtilization),"%"]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Budget Used"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:["$",y.remainingBudget]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Remaining"})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("select",{value:u,onChange:e=>g(e.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:B.map(e=>(0,a.jsx)("option",{value:e,children:"all"===e?"All Categories":e.charAt(0).toUpperCase()+e.slice(1)},e))})]}),(0,a.jsxs)("button",{onClick:()=>N(!f),className:"flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ".concat(f?"bg-green-100 text-green-700 border border-green-300":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[f?"Hide":"Show"," Shopping List"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>h("grid"),className:"p-2 rounded-lg ".concat("grid"===m?"bg-blue-100 text-blue-600":"text-gray-500 hover:text-gray-700"),children:(0,a.jsx)(k.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>h("list"),className:"p-2 rounded-lg ".concat("list"===m?"bg-blue-100 text-blue-600":"text-gray-500 hover:text-gray-700"),children:(0,a.jsx)(C.A,{className:"w-4 h-4"})})]})]}),f&&(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(D,{recommendations:P,budget:t.budget,onItemClick:e=>{console.log("Shopping list item clicked:",e)}})}),(0,a.jsx)("div",{className:"\n        ".concat("grid"===m?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4","\n      "),children:P.map(e=>(0,a.jsxs)("div",{className:"\n              bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow\n              ".concat("list"===m?"flex":"","\n            "),children:[(0,a.jsx)("div",{className:"".concat("list"===m?"w-48 flex-shrink-0":"aspect-square"," bg-gray-100"),children:(0,a.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-full object-cover"})}),(0,a.jsxs)("div",{className:"p-4 flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 line-clamp-2",children:e.name}),(0,a.jsx)("button",{onClick:()=>I(e.id),className:"p-1 rounded-full ".concat(p.has(e.id)?"text-red-500":"text-gray-400 hover:text-red-500"),children:(0,a.jsx)(S.A,{className:"w-5 h-5 ".concat(p.has(e.id)?"fill-current":"")})})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("span",{className:"text-2xl font-bold text-gray-900",children:["$",e.price]}),(0,a.jsx)("span",{className:"text-sm text-gray-500 capitalize",children:e.retailer})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mb-3",children:e.style.slice(0,2).map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full",children:e},e))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>window.open(e.productUrl,"_blank"),className:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1",children:[(0,a.jsx)(U.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Product"})]}),(0,a.jsx)("button",{className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsx)(A.A,{className:"w-4 h-4 text-gray-600"})})]})]})]},e.id))}),(0,a.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,a.jsxs)("button",{onClick:i,className:"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back"})]}),(0,a.jsxs)("button",{onClick:r,className:"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Start New Room"})]})]})]})}var F=t(7340),z=t(4783),O=t(3388),L=t(4835),_=t(306);function $(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!1),i=async()=>{r(!0),setTimeout(()=>{s({id:"demo-user",name:"Demo User",email:"<EMAIL>",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face"}),r(!1)},1e3)};return e?(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.avatar?(0,a.jsx)("img",{src:e.avatar,alt:e.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(O.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 hidden sm:block",children:e.name})]}),(0,a.jsxs)("button",{onClick:()=>{s(null)},className:"flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(L.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:block",children:"Sign Out"})]})]}):(0,a.jsxs)("button",{onClick:i,disabled:t,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50",children:[(0,a.jsx)(_.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:t?"Signing In...":"Sign In"})]})}function M(){let[e,s]=(0,l.useState)(!1);return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(F.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Interior AI"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"How it Works"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"Examples"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"Pricing"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium",children:"About"})]}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:(0,a.jsx)($,{})}),(0,a.jsx)("button",{className:"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>s(!e),children:e?(0,a.jsx)(n.A,{className:"w-6 h-6 text-gray-600"}):(0,a.jsx)(z.A,{className:"w-6 h-6 text-gray-600"})})]}),e&&(0,a.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"How it Works"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"Examples"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"Pricing"}),(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600 transition-colors font-medium py-2",children:"About"}),(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)($,{})})})]})})]})})}var q=t(8175),G=t(2894),W=t(9099);function H(){return(0,a.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(F.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold",children:"Interior AI"})]}),(0,a.jsx)("p",{className:"text-gray-400 mb-6 max-w-md",children:"Transform your living space with AI-powered interior design recommendations. Get personalized furniture suggestions that match your style and budget."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Twitter",children:(0,a.jsx)(q.A,{className:"w-5 h-5"})}),(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"LinkedIn",children:(0,a.jsx)(G.A,{className:"w-5 h-5"})}),(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"GitHub",children:(0,a.jsx)(W.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Product"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"How it Works"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Features"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Pricing"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"API"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"About"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Blog"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Careers"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Interior AI. All rights reserved."}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Privacy Policy"}),(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Terms of Service"}),(0,a.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Cookie Policy"})]})]})]})})}function J(){let[e,s]=(0,l.useState)("upload"),[t,r]=(0,l.useState)({}),[i,n]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,a.jsx)(M,{}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4",children:[{step:"upload",label:"Upload Photo",icon:"\uD83D\uDCF8"},{step:"configure",label:"Configure Room",icon:"⚙️"},{step:"analyze",label:"AI Analysis",icon:"\uD83E\uDD16"},{step:"recommendations",label:"Recommendations",icon:"✨"}].map((s,t)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"\n                  flex items-center justify-center w-12 h-12 rounded-full text-lg\n                  ".concat(e===s.step?"bg-blue-600 text-white":t<["upload","configure","analyze","recommendations"].indexOf(e)?"bg-green-500 text-white":"bg-gray-200 text-gray-500","\n                "),children:s.icon}),(0,a.jsx)("span",{className:"\n                  ml-2 text-sm font-medium\n                  ".concat(e===s.step?"text-blue-600":"text-gray-500","\n                "),children:s.label}),t<3&&(0,a.jsx)("div",{className:"\n                    w-8 h-0.5 mx-4\n                    ".concat(t<["upload","configure","analyze","recommendations"].indexOf(e)?"bg-green-500":"bg-gray-200","\n                  ")})]},s.step))})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:["upload"===e&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Transform Your Space with AI"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Upload a photo of your room and get personalized furniture recommendations"}),(0,a.jsx)(o,{onUpload:e=>{r(s=>({...s,id:e.id,imageUrl:e.url,name:"Room ".concat(e.id)})),s("configure")},isLoading:i})]}),"configure"===e&&t.imageUrl&&(0,a.jsx)(p,{imageUrl:t.imageUrl,onConfiguration:e=>{r(s=>({...s,type:e.roomType,budget:e.budget,style:e.style})),s("analyze")},onBack:()=>s("upload")}),"analyze"===e&&t.type&&t.budget&&t.style&&(0,a.jsx)(N,{room:t,onAnalysisComplete:e=>{r(s=>({...s,analysisResult:e.analysis})),s("recommendations")},onBack:()=>s("configure")}),"recommendations"===e&&t.analysisResult&&(0,a.jsx)(E,{room:t,onStartOver:()=>{r({}),s("upload")},onBack:()=>s("analyze")})]})]}),(0,a.jsx)(H,{})]})}},7959:(e,s,t)=>{Promise.resolve().then(t.bind(t,1007))}},e=>{var s=s=>e(e.s=s);e.O(0,[961,441,684,358],()=>s(7959)),_N_E=e.O()}]);