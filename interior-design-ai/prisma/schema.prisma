// Interior Design AI Database Schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  rooms         Room[]
  shoppingLists ShoppingList[]
  wishlistItems WishlistItem[]
  preferences   UserPreferences?

  @@map("users")
}

model UserPreferences {
  id     String @id @default(cuid())
  userId String @unique

  // Style preferences
  preferredStyles String[] // Array of DesignStyle
  budgetMin       Int?
  budgetMax       Int?
  favoriteRetailers String[] // Array of retailer names
  colorPreferences  String[] // Array of hex colors

  // Notification preferences
  priceAlerts      Boolean @default(true)
  newProducts      Boolean @default(false)
  styleUpdates     Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

// Room Management
model Room {
  id          String    @id @default(cuid())
  name        String
  type        String    // RoomType enum
  budget      Float?
  style       String?   // DesignStyle enum
  imageUrl    String?
  thumbnailUrl String?

  // Analysis results (stored as JSON)
  analysisResult Json?

  // Metadata
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userId            String?
  user              User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  recommendations   Recommendation[]
  shoppingLists     ShoppingList[]

  @@map("rooms")
}

// Product Catalog
model Product {
  id          String @id @default(cuid())
  externalId  String? @unique // Original product ID from retailer
  name        String
  description String?
  category    String // FurnitureCategory enum
  subcategory String?

  // Pricing
  price       Float
  currency    String @default("USD")
  originalPrice Float?

  // Product details
  imageUrl    String
  productUrl  String
  retailer    String // amazon, ikea, etc.
  sku         String?
  brand       String?

  // Review and rating data
  rating      Float?
  reviewCount Int?

  // Dimensions (in cm)
  width       Float?
  height      Float?
  depth       Float?

  // Style and attributes
  styles      String[] // Array of DesignStyle
  colors      String[] // Array of hex colors
  materials   String[] // Array of materials
  tags        String[] // Array of tags

  // Availability and status
  isAvailable Boolean @default(true)
  stock       Int?

  // SEO and search
  searchKeywords String?

  // Metadata
  scrapedAt   DateTime? // When this product was last scraped
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  recommendations Recommendation[]
  wishlistItems   WishlistItem[]
  shoppingListItems ShoppingListItem[]
  priceHistory    PriceHistory[]

  @@map("products")
  @@index([category])
  @@index([retailer])
  @@index([isAvailable])
}

// Recommendations
model Recommendation {
  id       String @id @default(cuid())
  roomId   String
  productId String

  // Recommendation scoring
  score           Float
  styleMatch      Float
  budgetMatch     Float
  spaceMatch      Float

  // Placement suggestions (stored as JSON)
  placement       Json?

  // Priority and ranking
  priority        String @default("medium") // high, medium, low
  rank            Int

  // Metadata
  createdAt       DateTime @default(now())

  // Relationships
  room            Room    @relation(fields: [roomId], references: [id], onDelete: Cascade)
  product         Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("recommendations")
  @@unique([roomId, productId])
  @@index([roomId])
  @@index([priority])
}

// Shopping Lists
model ShoppingList {
  id          String @id @default(cuid())
  name        String
  totalCost   Float  @default(0)
  budget      Float?

  // Status
  status      String @default("active") // active, completed, archived
  isPublic    Boolean @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userId      String?
  roomId      String?
  user        User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  room        Room?   @relation(fields: [roomId], references: [id], onDelete: SetNull)
  items       ShoppingListItem[]

  @@map("shopping_lists")
}

model ShoppingListItem {
  id              String @id @default(cuid())
  shoppingListId  String
  productId       String

  quantity        Int    @default(1)
  priority        String @default("medium") // high, medium, low
  notes           String?

  // Pricing at time of addition
  priceAtAdd      Float

  // Status
  isPurchased     Boolean @default(false)
  purchasedAt     DateTime?

  // Metadata
  addedAt         DateTime @default(now())

  // Relationships
  shoppingList    ShoppingList @relation(fields: [shoppingListId], references: [id], onDelete: Cascade)
  product         Product      @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("shopping_list_items")
  @@unique([shoppingListId, productId])
}

// Wishlist
model WishlistItem {
  id        String @id @default(cuid())
  userId    String
  productId String

  notes     String?
  addedAt   DateTime @default(now())

  // Relationships
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("wishlist_items")
  @@unique([userId, productId])
}

// Price Tracking
model PriceHistory {
  id        String   @id @default(cuid())
  productId String
  price     Float
  currency  String   @default("USD")
  source    String   // retailer or scraper source
  recordedAt DateTime @default(now())

  // Relationships
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("price_history")
  @@index([productId])
  @@index([recordedAt])
}

model PriceAlert {
  id          String  @id @default(cuid())
  userId      String
  productId   String
  targetPrice Float
  isActive    Boolean @default(true)

  // Notification settings
  emailNotify Boolean @default(true)

  createdAt   DateTime @default(now())
  triggeredAt DateTime?

  @@map("price_alerts")
  @@index([userId])
  @@index([isActive])
}

// Analytics and Tracking
model AnalyticsEvent {
  id        String   @id @default(cuid())
  eventType String   // page_view, product_click, affiliate_click, etc.
  eventData Json?    // Additional event data

  // User context
  userId    String?
  sessionId String?
  userAgent String?
  ipAddress String?

  // Product context
  productId String?
  roomId    String?

  timestamp DateTime @default(now())

  @@map("analytics_events")
  @@index([eventType])
  @@index([timestamp])
  @@index([userId])
}
