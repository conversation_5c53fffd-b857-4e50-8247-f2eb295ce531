{"name": "interior-design-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "prisma migrate dev", "db:seed": "npx tsx prisma/seed.ts", "db:studio": "prisma studio", "db:generate": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.10.1", "@supabase/supabase-js": "^2.50.0", "@tensorflow/tfjs": "^4.22.0", "@types/multer": "^1.4.13", "axios": "^1.10.0", "cheerio": "^1.1.0", "lucide-react": "^0.522.0", "next": "15.3.4", "next-auth": "^4.24.11", "playwright": "^1.53.1", "prisma": "^6.10.1", "proxy-agent": "^6.5.0", "puppeteer": "^24.10.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "sharp": "^0.34.2", "user-agents": "^1.1.580"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "tailwindcss": "^4", "typescript": "^5"}}