# Interior Design AI

An AI-powered interior design website that analyzes room photos and provides personalized furniture recommendations with shopping integration.

## 🚀 Features

- **AI Room Analysis**: Upload room photos for intelligent layout and style analysis
- **Smart Recommendations**: Get personalized furniture suggestions based on your style and budget
- **Shopping Integration**: Direct links to Amazon and IKEA with affiliate tracking
- **Style Matching**: Advanced algorithms to match furniture with your preferred design style
- **Budget Management**: Recommendations that respect your budget constraints
- **Interactive Shopping Lists**: Create, manage, and share shopping lists
- **Real-time Price Tracking**: Monitor price changes and get alerts

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (Prisma Cloud)
- **AI/ML**: TensorFlow.js, Hugging Face models
- **Authentication**: NextAuth.js
- **Deployment**: Vercel
- **Testing**: Jest, React Testing Library

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- PostgreSQL database (or use Prisma Cloud)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd interior-design-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Update `.env` with your configuration:
   ```env
   DATABASE_URL="your-postgresql-connection-string"
   NEXTAUTH_SECRET="your-nextauth-secret"
   AMAZON_AFFILIATE_TAG="your-amazon-affiliate-tag"
   IKEA_PARTNER_ID="your-ikea-partner-id"
   ```

4. **Set up the database**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3001`

## 🧪 Testing

Run the test suite:
```bash
npm test
```

## 🎯 Free AI Models Used

1. **COCO-SSD (TensorFlow.js)** - Object detection for furniture identification
2. **DeepLab v3** - Room segmentation for layout analysis
3. **MobileNet** - Image classification for room type detection
4. **Sentence Transformers (Hugging Face)** - Style matching and similarity
5. **OpenCV.js** - Edge detection and color analysis

## 📦 Deployment

### Vercel (Recommended)

1. **Connect your repository to Vercel**
2. **Set environment variables in Vercel dashboard**
3. **Deploy automatically on push to main branch**

---

**Built with ❤️ using free AI models and modern web technologies**
