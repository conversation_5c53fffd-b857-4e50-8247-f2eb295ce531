// Simple test script to verify scraping functionality
const { ScraperManager } = require('./src/lib/scrapers/scraper-manager');

async function testScraping() {
  console.log('🚀 Starting scraping test...');
  
  const scraperManager = new ScraperManager();
  
  try {
    // Test IKEA scraping
    console.log('\n📦 Testing IKEA scraper...');
    const ikeaJob = await scraperManager.startScrapingJob('ikea', 'seating', 5);
    console.log('IKEA job started:', ikeaJob);
    
    // Wait a bit for the job to process
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Check job status
    const jobStatus = await scraperManager.getJobStatus(ikeaJob.id);
    console.log('IKEA job status:', jobStatus);
    
    // Test getting products from database
    console.log('\n🗄️ Testing database products...');
    const products = await scraperManager.getProductsFromDatabase('seating', 10);
    console.log(`Found ${products.length} products in database`);
    
    if (products.length > 0) {
      console.log('Sample product:', {
        name: products[0].name,
        price: products[0].price,
        retailer: products[0].retailer,
        category: products[0].category
      });
    }
    
    // Test search functionality
    console.log('\n🔍 Testing search functionality...');
    const searchResults = await scraperManager.searchProducts('sofa', ['ikea']);
    console.log(`Search found ${searchResults.length} products for "sofa"`);
    
    if (searchResults.length > 0) {
      console.log('Sample search result:', {
        name: searchResults[0].name,
        price: searchResults[0].price,
        retailer: searchResults[0].retailer
      });
    }
    
  } catch (error) {
    console.error('❌ Scraping test failed:', error);
  } finally {
    await scraperManager.cleanup();
    console.log('\n✅ Scraping test completed');
  }
}

// Run the test
testScraping().catch(console.error);
