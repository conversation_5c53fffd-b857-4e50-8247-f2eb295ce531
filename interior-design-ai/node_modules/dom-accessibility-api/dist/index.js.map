{"version": 3, "file": "index.js", "names": [], "sources": ["../sources/index.ts"], "sourcesContent": ["export { computeAccessibleDescription } from \"./accessible-description\";\nexport { computeAccessibleName } from \"./accessible-name\";\nexport { default as getRole } from \"./getRole\";\nexport * from \"./is-inaccessible\";\n"], "mappings": ";;;;;;;;;AAAA;AAAwE;AACxE;AAA0D;AAC1D;AAA+C;AAC/C;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAkC"}