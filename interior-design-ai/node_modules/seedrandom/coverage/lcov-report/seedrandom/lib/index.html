<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for seedrandom/lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">All files</a> seedrandom/lib
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.45% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>343/371</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">69.39% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>102/147</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">89.66% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>52/58</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">93.93% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>294/313</span>
      </div>
    </div>
    <p class="quiet">
      Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
    </p>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="alea.js"><a href="alea.js.html">alea.js</a></td>
	<td data-value="91.94" class="pic high"><div class="chart"><div class="cover-fill" style="width: 91%;"></div><div class="cover-empty" style="width:9%;"></div></div></td>
	<td data-value="91.94" class="pct high">91.94%</td>
	<td data-value="62" class="abs high">57/62</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="24" class="abs medium">16/24</td>
	<td data-value="90.91" class="pct high">90.91%</td>
	<td data-value="11" class="abs high">10/11</td>
	<td data-value="94.34" class="pct high">94.34%</td>
	<td data-value="53" class="abs high">50/53</td>
	</tr>

<tr>
	<td class="file high" data-value="tychei.js"><a href="tychei.js.html">tychei.js</a></td>
	<td data-value="92.98" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.98" class="pct high">92.98%</td>
	<td data-value="57" class="abs high">53/57</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="20" class="abs medium">14/20</td>
	<td data-value="88.89" class="pct high">88.89%</td>
	<td data-value="9" class="abs high">8/9</td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="48" class="abs high">45/48</td>
	</tr>

<tr>
	<td class="file high" data-value="xor128.js"><a href="xor128.js.html">xor128.js</a></td>
	<td data-value="91.84" class="pic high"><div class="chart"><div class="cover-fill" style="width: 91%;"></div><div class="cover-empty" style="width:9%;"></div></div></td>
	<td data-value="91.84" class="pct high">91.84%</td>
	<td data-value="49" class="abs high">45/49</td>
	<td data-value="70" class="pct medium">70%</td>
	<td data-value="20" class="abs medium">14/20</td>
	<td data-value="88.89" class="pct high">88.89%</td>
	<td data-value="9" class="abs high">8/9</td>
	<td data-value="93.02" class="pct high">93.02%</td>
	<td data-value="43" class="abs high">40/43</td>
	</tr>

<tr>
	<td class="file high" data-value="xor4096.js"><a href="xor4096.js.html">xor4096.js</a></td>
	<td data-value="92.68" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.68" class="pct high">92.68%</td>
	<td data-value="82" class="abs high">76/82</td>
	<td data-value="65.71" class="pct medium">65.71%</td>
	<td data-value="35" class="abs medium">23/35</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="94.52" class="pct high">94.52%</td>
	<td data-value="73" class="abs high">69/73</td>
	</tr>

<tr>
	<td class="file high" data-value="xorshift7.js"><a href="xorshift7.js.html">xorshift7.js</a></td>
	<td data-value="92.42" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.42" class="pct high">92.42%</td>
	<td data-value="66" class="abs high">61/66</td>
	<td data-value="73.08" class="pct medium">73.08%</td>
	<td data-value="26" class="abs medium">19/26</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="94" class="pct high">94%</td>
	<td data-value="50" class="abs high">47/50</td>
	</tr>

<tr>
	<td class="file high" data-value="xorwow.js"><a href="xorwow.js.html">xorwow.js</a></td>
	<td data-value="92.73" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.73" class="pct high">92.73%</td>
	<td data-value="55" class="abs high">51/55</td>
	<td data-value="72.73" class="pct medium">72.73%</td>
	<td data-value="22" class="abs medium">16/22</td>
	<td data-value="88.89" class="pct high">88.89%</td>
	<td data-value="9" class="abs high">8/9</td>
	<td data-value="93.48" class="pct high">93.48%</td>
	<td data-value="46" class="abs high">43/46</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Tue Sep 17 2019 06:35:34 GMT-0400 (Eastern Daylight Time)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
<script src="../../block-navigation.js"></script>
</body>
</html>
