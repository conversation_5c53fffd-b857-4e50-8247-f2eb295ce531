<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      All files
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">93.21% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>439/471</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">73.2% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>142/194</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">90.41% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>66/73</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">94.44% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>374/396</span>
      </div>
    </div>
    <p class="quiet">
      Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
    </p>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="seedrandom"><a href="seedrandom/index.html">seedrandom</a></td>
	<td data-value="96" class="pic high"><div class="chart"><div class="cover-fill" style="width: 96%;"></div><div class="cover-empty" style="width:4%;"></div></div></td>
	<td data-value="96" class="pct high">96%</td>
	<td data-value="100" class="abs high">96/100</td>
	<td data-value="85.11" class="pct high">85.11%</td>
	<td data-value="47" class="abs high">40/47</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="96.39" class="pct high">96.39%</td>
	<td data-value="83" class="abs high">80/83</td>
	</tr>

<tr>
	<td class="file high" data-value="seedrandom/lib"><a href="seedrandom/lib/index.html">seedrandom/lib</a></td>
	<td data-value="92.45" class="pic high"><div class="chart"><div class="cover-fill" style="width: 92%;"></div><div class="cover-empty" style="width:8%;"></div></div></td>
	<td data-value="92.45" class="pct high">92.45%</td>
	<td data-value="371" class="abs high">343/371</td>
	<td data-value="69.39" class="pct medium">69.39%</td>
	<td data-value="147" class="abs medium">102/147</td>
	<td data-value="89.66" class="pct high">89.66%</td>
	<td data-value="58" class="abs high">52/58</td>
	<td data-value="93.93" class="pct high">93.93%</td>
	<td data-value="313" class="abs high">294/313</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Tue Sep 17 2019 06:35:34 GMT-0400 (Eastern Daylight Time)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
<script src="block-navigation.js"></script>
</body>
</html>
