{"/home/<USER>/git/seedrandom/seedrandom.js": {"path": "/home/<USER>/git/seedrandom/seedrandom.js", "statementMap": {"0": {"start": {"line": 25, "column": 0}, "end": {"line": 253, "column": 2}}, "1": {"start": {"line": 30, "column": 12}, "end": {"line": 30, "column": 15}}, "2": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 14}}, "3": {"start": {"line": 32, "column": 13}, "end": {"line": 32, "column": 15}}, "4": {"start": {"line": 33, "column": 14}, "end": {"line": 33, "column": 22}}, "5": {"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 40}}, "6": {"start": {"line": 35, "column": 19}, "end": {"line": 35, "column": 38}}, "7": {"start": {"line": 36, "column": 15}, "end": {"line": 36, "column": 31}}, "8": {"start": {"line": 37, "column": 11}, "end": {"line": 37, "column": 20}}, "9": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 14}}, "10": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 68}}, "11": {"start": {"line": 49, "column": 18}, "end": {"line": 51, "column": 48}}, "12": {"start": {"line": 54, "column": 13}, "end": {"line": 54, "column": 26}}, "13": {"start": {"line": 58, "column": 13}, "end": {"line": 73, "column": 3}}, "14": {"start": {"line": 59, "column": 12}, "end": {"line": 59, "column": 26}}, "15": {"start": {"line": 60, "column": 12}, "end": {"line": 60, "column": 22}}, "16": {"start": {"line": 61, "column": 12}, "end": {"line": 61, "column": 13}}, "17": {"start": {"line": 62, "column": 4}, "end": {"line": 66, "column": 5}}, "18": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 26}}, "19": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 17}}, "20": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 20}}, "21": {"start": {"line": 67, "column": 4}, "end": {"line": 71, "column": 5}}, "22": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 13}}, "23": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 13}}, "24": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 15}}, "25": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 23}}, "26": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 51}}, "27": {"start": {"line": 75, "column": 28}, "end": {"line": 75, "column": 49}}, "28": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 61}}, "29": {"start": {"line": 76, "column": 28}, "end": {"line": 76, "column": 59}}, "30": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 21}}, "31": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 33}}, "32": {"start": {"line": 83, "column": 2}, "end": {"line": 103, "column": 17}}, "33": {"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 9}}, "34": {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 45}}, "35": {"start": {"line": 87, "column": 25}, "end": {"line": 87, "column": 43}}, "36": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 60}}, "37": {"start": {"line": 89, "column": 36}, "end": {"line": 89, "column": 58}}, "38": {"start": {"line": 94, "column": 8}, "end": {"line": 98, "column": 25}}, "39": {"start": {"line": 94, "column": 28}, "end": {"line": 94, "column": 49}}, "40": {"start": {"line": 94, "column": 50}, "end": {"line": 94, "column": 62}}, "41": {"start": {"line": 98, "column": 13}, "end": {"line": 98, "column": 25}}, "42": {"start": {"line": 117, "column": 18}, "end": {"line": 117, "column": 28}}, "43": {"start": {"line": 118, "column": 11}, "end": {"line": 118, "column": 15}}, "44": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 22}}, "45": {"start": {"line": 118, "column": 28}, "end": {"line": 118, "column": 43}}, "46": {"start": {"line": 118, "column": 49}, "end": {"line": 118, "column": 58}}, "47": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 36}}, "48": {"start": {"line": 121, "column": 17}, "end": {"line": 121, "column": 34}}, "49": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 3}}, "50": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 15}}, "51": {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}, "52": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 60}}, "53": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 13}}, "54": {"start": {"line": 133, "column": 2}, "end": {"line": 146, "column": 12}}, "55": {"start": {"line": 135, "column": 15}, "end": {"line": 135, "column": 16}}, "56": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 16}}, "57": {"start": {"line": 136, "column": 22}, "end": {"line": 136, "column": 26}}, "58": {"start": {"line": 136, "column": 32}, "end": {"line": 136, "column": 36}}, "59": {"start": {"line": 137, "column": 4}, "end": {"line": 140, "column": 5}}, "60": {"start": {"line": 138, "column": 6}, "end": {"line": 138, "column": 32}}, "61": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 78}}, "62": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 13}}, "63": {"start": {"line": 141, "column": 14}, "end": {"line": 141, "column": 23}}, "64": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 13}}, "65": {"start": {"line": 154, "column": 2}, "end": {"line": 154, "column": 12}}, "66": {"start": {"line": 155, "column": 2}, "end": {"line": 155, "column": 12}}, "67": {"start": {"line": 156, "column": 2}, "end": {"line": 156, "column": 20}}, "68": {"start": {"line": 157, "column": 2}, "end": {"line": 157, "column": 11}}, "69": {"start": {"line": 165, "column": 15}, "end": {"line": 165, "column": 17}}, "70": {"start": {"line": 165, "column": 26}, "end": {"line": 165, "column": 36}}, "71": {"start": {"line": 166, "column": 2}, "end": {"line": 170, "column": 3}}, "72": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "73": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 70}}, "74": {"start": {"line": 168, "column": 12}, "end": {"line": 168, "column": 55}}, "75": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 71}}, "76": {"start": {"line": 180, "column": 19}, "end": {"line": 180, "column": 28}}, "77": {"start": {"line": 180, "column": 41}, "end": {"line": 180, "column": 42}}, "78": {"start": {"line": 181, "column": 2}, "end": {"line": 184, "column": 3}}, "79": {"start": {"line": 182, "column": 4}, "end": {"line": 183, "column": 74}}, "80": {"start": {"line": 185, "column": 2}, "end": {"line": 185, "column": 23}}, "81": {"start": {"line": 194, "column": 2}, "end": {"line": 208, "column": 3}}, "82": {"start": {"line": 196, "column": 4}, "end": {"line": 202, "column": 5}}, "83": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 23}}, "84": {"start": {"line": 200, "column": 6}, "end": {"line": 200, "column": 34}}, "85": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 62}}, "86": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 25}}, "87": {"start": {"line": 205, "column": 18}, "end": {"line": 205, "column": 34}}, "88": {"start": {"line": 206, "column": 18}, "end": {"line": 206, "column": 44}}, "89": {"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 71}}, "90": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 41}}, "91": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 28}}, "92": {"start": {"line": 232, "column": 0}, "end": {"line": 243, "column": 1}}, "93": {"start": {"line": 233, "column": 2}, "end": {"line": 233, "column": 30}}, "94": {"start": {"line": 235, "column": 2}, "end": {"line": 237, "column": 17}}, "95": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 35}}, "96": {"start": {"line": 238, "column": 7}, "end": {"line": 243, "column": 1}}, "97": {"start": {"line": 239, "column": 2}, "end": {"line": 239, "column": 44}}, "98": {"start": {"line": 239, "column": 22}, "end": {"line": 239, "column": 40}}, "99": {"start": {"line": 242, "column": 2}, "end": {"line": 242, "column": 38}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 25, "column": 1}, "end": {"line": 25, "column": 2}}, "loc": {"start": {"line": 25, "column": 31}, "end": {"line": 247, "column": 1}}, "line": 25}, "1": {"name": "seedrandom", "decl": {"start": {"line": 44, "column": 9}, "end": {"line": 44, "column": 19}}, "loc": {"start": {"line": 44, "column": 45}, "end": {"line": 104, "column": 1}}, "line": 44}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 58, "column": 13}, "end": {"line": 58, "column": 14}}, "loc": {"start": {"line": 58, "column": 24}, "end": {"line": 73, "column": 3}}, "line": 58}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 16}}, "loc": {"start": {"line": 75, "column": 26}, "end": {"line": 75, "column": 51}}, "line": 75}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 76, "column": 15}, "end": {"line": 76, "column": 16}}, "loc": {"start": {"line": 76, "column": 26}, "end": {"line": 76, "column": 61}}, "line": 76}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 7}}, "loc": {"start": {"line": 84, "column": 48}, "end": {"line": 99, "column": 7}}, "line": 84}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 23}, "end": {"line": 89, "column": 24}}, "loc": {"start": {"line": 89, "column": 34}, "end": {"line": 89, "column": 60}}, "line": 89}, "7": {"name": "ARC4", "decl": {"start": {"line": 116, "column": 9}, "end": {"line": 116, "column": 13}}, "loc": {"start": {"line": 116, "column": 19}, "end": {"line": 147, "column": 1}}, "line": 116}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 133, "column": 10}, "end": {"line": 133, "column": 11}}, "loc": {"start": {"line": 133, "column": 26}, "end": {"line": 146, "column": 3}}, "line": 133}, "9": {"name": "copy", "decl": {"start": {"line": 153, "column": 9}, "end": {"line": 153, "column": 13}}, "loc": {"start": {"line": 153, "column": 20}, "end": {"line": 158, "column": 1}}, "line": 153}, "10": {"name": "flatten", "decl": {"start": {"line": 164, "column": 9}, "end": {"line": 164, "column": 16}}, "loc": {"start": {"line": 164, "column": 29}, "end": {"line": 172, "column": 1}}, "line": 164}, "11": {"name": "mixkey", "decl": {"start": {"line": 179, "column": 9}, "end": {"line": 179, "column": 15}}, "loc": {"start": {"line": 179, "column": 27}, "end": {"line": 186, "column": 1}}, "line": 179}, "12": {"name": "autoseed", "decl": {"start": {"line": 193, "column": 9}, "end": {"line": 193, "column": 17}}, "loc": {"start": {"line": 193, "column": 20}, "end": {"line": 209, "column": 1}}, "line": 193}, "13": {"name": "tostring", "decl": {"start": {"line": 215, "column": 9}, "end": {"line": 215, "column": 17}}, "loc": {"start": {"line": 215, "column": 21}, "end": {"line": 217, "column": 1}}, "line": 215}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 239, "column": 9}, "end": {"line": 239, "column": 10}}, "loc": {"start": {"line": 239, "column": 20}, "end": {"line": 239, "column": 42}}, "line": 239}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 46, "column": 32}, "end": {"line": 46, "column": 49}}, {"start": {"line": 46, "column": 53}, "end": {"line": 46, "column": 66}}], "line": 46}, "1": {"loc": {"start": {"line": 46, "column": 53}, "end": {"line": 46, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 53}, "end": {"line": 46, "column": 60}}, {"start": {"line": 46, "column": 64}, "end": {"line": 46, "column": 66}}], "line": 46}, "2": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 51, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": 44}}, {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 38}}], "line": 50}, "3": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 51, "column": 21}, "end": {"line": 51, "column": 31}}, {"start": {"line": 51, "column": 34}, "end": {"line": 51, "column": 38}}], "line": 51}, "4": {"loc": {"start": {"line": 83, "column": 10}, "end": {"line": 99, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 10}, "end": {"line": 83, "column": 22}}, {"start": {"line": 83, "column": 26}, "end": {"line": 83, "column": 34}}, {"start": {"line": 84, "column": 6}, "end": {"line": 99, "column": 7}}], "line": 83}, "5": {"loc": {"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 9}}, "type": "if", "locations": [{"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 9}}, {"start": {"line": 85, "column": 8}, "end": {"line": 90, "column": 9}}], "line": 85}, "6": {"loc": {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 45}}, "type": "if", "locations": [{"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 45}}, {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 45}}], "line": 87}, "7": {"loc": {"start": {"line": 94, "column": 8}, "end": {"line": 98, "column": 25}}, "type": "if", "locations": [{"start": {"line": 94, "column": 8}, "end": {"line": 98, "column": 25}}, {"start": {"line": 94, "column": 8}, "end": {"line": 98, "column": 25}}], "line": 94}, "8": {"loc": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 24}, "end": {"line": 102, "column": 38}}, {"start": {"line": 102, "column": 42}, "end": {"line": 102, "column": 54}}], "line": 102}, "9": {"loc": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 36}}, "type": "if", "locations": [{"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 36}}, {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 36}}], "line": 121}, "10": {"loc": {"start": {"line": 166, "column": 2}, "end": {"line": 170, "column": 3}}, "type": "if", "locations": [{"start": {"line": 166, "column": 2}, "end": {"line": 170, "column": 3}}, {"start": {"line": 166, "column": 2}, "end": {"line": 170, "column": 3}}], "line": 166}, "11": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 11}}, {"start": {"line": 166, "column": 15}, "end": {"line": 166, "column": 30}}], "line": 166}, "12": {"loc": {"start": {"line": 171, "column": 10}, "end": {"line": 171, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 171, "column": 26}, "end": {"line": 171, "column": 32}}, {"start": {"line": 171, "column": 35}, "end": {"line": 171, "column": 69}}], "line": 171}, "13": {"loc": {"start": {"line": 171, "column": 35}, "end": {"line": 171, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 171, "column": 53}, "end": {"line": 171, "column": 56}}, {"start": {"line": 171, "column": 59}, "end": {"line": 171, "column": 69}}], "line": 171}, "14": {"loc": {"start": {"line": 196, "column": 4}, "end": {"line": 202, "column": 5}}, "type": "if", "locations": [{"start": {"line": 196, "column": 4}, "end": {"line": 202, "column": 5}}, {"start": {"line": 196, "column": 4}, "end": {"line": 202, "column": 5}}], "line": 196}, "15": {"loc": {"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 18}}, {"start": {"line": 196, "column": 23}, "end": {"line": 196, "column": 51}}], "line": 196}, "16": {"loc": {"start": {"line": 201, "column": 7}, "end": {"line": 201, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 201, "column": 7}, "end": {"line": 201, "column": 20}}, {"start": {"line": 201, "column": 24}, "end": {"line": 201, "column": 39}}], "line": 201}, "17": {"loc": {"start": {"line": 206, "column": 18}, "end": {"line": 206, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 206, "column": 18}, "end": {"line": 206, "column": 25}}, {"start": {"line": 206, "column": 29}, "end": {"line": 206, "column": 44}}], "line": 206}, "18": {"loc": {"start": {"line": 232, "column": 0}, "end": {"line": 243, "column": 1}}, "type": "if", "locations": [{"start": {"line": 232, "column": 0}, "end": {"line": 243, "column": 1}}, {"start": {"line": 232, "column": 0}, "end": {"line": 243, "column": 1}}], "line": 232}, "19": {"loc": {"start": {"line": 232, "column": 4}, "end": {"line": 232, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 232, "column": 4}, "end": {"line": 232, "column": 31}}, {"start": {"line": 232, "column": 35}, "end": {"line": 232, "column": 49}}], "line": 232}, "20": {"loc": {"start": {"line": 238, "column": 7}, "end": {"line": 243, "column": 1}}, "type": "if", "locations": [{"start": {"line": 238, "column": 7}, "end": {"line": 243, "column": 1}}, {"start": {"line": 238, "column": 7}, "end": {"line": 243, "column": 1}}], "line": 238}, "21": {"loc": {"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 11}, "end": {"line": 238, "column": 40}}, {"start": {"line": 238, "column": 44}, "end": {"line": 238, "column": 54}}], "line": 238}, "22": {"loc": {"start": {"line": 250, "column": 2}, "end": {"line": 250, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 250, "column": 34}, "end": {"line": 250, "column": 38}}, {"start": {"line": 250, "column": 41}, "end": {"line": 250, "column": 45}}], "line": 250}}, "s": {"0": 6, "1": 6, "2": 6, "3": 6, "4": 6, "5": 6, "6": 6, "7": 6, "8": 6, "9": 23, "10": 23, "11": 23, "12": 23, "13": 23, "14": 3694, "15": 3694, "16": 3694, "17": 3694, "18": 3917, "19": 3917, "20": 3917, "21": 3694, "22": 9249, "23": 9249, "24": 9249, "25": 3694, "26": 23, "27": 1025, "28": 23, "29": 4100001, "30": 23, "31": 23, "32": 23, "33": 20, "34": 4, "35": 2, "36": 4, "37": 2, "38": 20, "39": 1, "40": 1, "41": 19, "42": 23, "43": 23, "44": 23, "45": 23, "46": 23, "47": 23, "48": 1, "49": 23, "50": 5888, "51": 23, "52": 5888, "53": 5888, "54": 23, "55": 4108660, "56": 4108660, "57": 4108660, "58": 4108660, "59": 4108660, "60": 16436073, "61": 16436073, "62": 4108660, "63": 4108660, "64": 4108660, "65": 4, "66": 4, "67": 4, "68": 4, "69": 32, "70": 32, "71": 32, "72": 4, "73": 9, "74": 9, "75": 32, "76": 52, "77": 52, "78": 52, "79": 7735, "80": 52, "81": 4, "82": 4, "83": 3, "84": 1, "85": 1, "86": 3, "87": 1, "88": 1, "89": 1, "90": 81, "91": 6, "92": 6, "93": 6, "94": 6, "95": 6, "96": 0, "97": 0, "98": 0, "99": 0}, "f": {"0": 6, "1": 23, "2": 3694, "3": 1025, "4": 4100001, "5": 20, "6": 2, "7": 23, "8": 4108660, "9": 4, "10": 32, "11": 52, "12": 4, "13": 81, "14": 0}, "b": {"0": [1, 22], "1": [22, 11], "2": [2, 21], "3": [4, 17], "4": [23, 21, 20], "5": [4, 16], "6": [2, 2], "7": [1, 19], "8": [4, 19], "9": [1, 22], "10": [4, 28], "11": [32, 32], "12": [3, 29], "13": [23, 6], "14": [3, 1], "15": [4, 3], "16": [1, 1], "17": [1, 0], "18": [6, 0], "19": [6, 6], "20": [0, 0], "21": [0, 0], "22": [0, 6]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "51713684804e6a2df630b16f82a817311168866e", "contentHash": "8249b27b9573baed43d58f17ab1b4f20f9b703bbe06145f517fc6c05c6b6cfa4"}, "/home/<USER>/git/seedrandom/lib/xor128.js": {"path": "/home/<USER>/git/seedrandom/lib/xor128.js", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 79, "column": 2}}, "1": {"start": {"line": 7, "column": 11}, "end": {"line": 7, "column": 15}}, "2": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 29}}, "3": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 11}}, "4": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 11}}, "5": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 11}}, "6": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 11}}, "7": {"start": {"line": 15, "column": 2}, "end": {"line": 21, "column": 4}}, "8": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 31}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 16}}, "10": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 16}}, "11": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 16}}, "12": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 49}}, "13": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "14": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 16}}, "15": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 20}}, "16": {"start": {"line": 32, "column": 2}, "end": {"line": 35, "column": 3}}, "17": {"start": {"line": 32, "column": 15}, "end": {"line": 32, "column": 16}}, "18": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 38}}, "19": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 14}}, "20": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 12}}, "21": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 12}}, "22": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 12}}, "23": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 12}}, "24": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 11}}, "25": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 27}}, "26": {"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 32}}, "27": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 67}}, "28": {"start": {"line": 49, "column": 26}, "end": {"line": 49, "column": 65}}, "29": {"start": {"line": 50, "column": 2}, "end": {"line": 57, "column": 4}}, "30": {"start": {"line": 51, "column": 4}, "end": {"line": 55, "column": 27}}, "31": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 32}}, "32": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 47}}, "33": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 42}}, "34": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 18}}, "35": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 23}}, "36": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 20}}, "37": {"start": {"line": 60, "column": 2}, "end": {"line": 63, "column": 3}}, "38": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 51}}, "39": {"start": {"line": 61, "column": 35}, "end": {"line": 61, "column": 51}}, "40": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 52}}, "41": {"start": {"line": 62, "column": 30}, "end": {"line": 62, "column": 50}}, "42": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 14}}, "43": {"start": {"line": 67, "column": 0}, "end": {"line": 73, "column": 1}}, "44": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 24}}, "45": {"start": {"line": 69, "column": 7}, "end": {"line": 73, "column": 1}}, "46": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 38}}, "47": {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 34}}, "48": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 1}, "end": {"line": 4, "column": 2}}, "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 75, "column": 1}}, "line": 4}, "1": {"name": "XorGen", "decl": {"start": {"line": 6, "column": 9}, "end": {"line": 6, "column": 15}}, "loc": {"start": {"line": 6, "column": 22}, "end": {"line": 36, "column": 1}}, "line": 6}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 13}}, "loc": {"start": {"line": 15, "column": 23}, "end": {"line": 21, "column": 3}}, "line": 15}, "3": {"name": "copy", "decl": {"start": {"line": 38, "column": 9}, "end": {"line": 38, "column": 13}}, "loc": {"start": {"line": 38, "column": 20}, "end": {"line": 44, "column": 1}}, "line": 38}, "4": {"name": "impl", "decl": {"start": {"line": 46, "column": 9}, "end": {"line": 46, "column": 13}}, "loc": {"start": {"line": 46, "column": 26}, "end": {"line": 65, "column": 1}}, "line": 46}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 14}}, "loc": {"start": {"line": 49, "column": 24}, "end": {"line": 49, "column": 67}}, "line": 49}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 17}}, "loc": {"start": {"line": 50, "column": 27}, "end": {"line": 57, "column": 3}}, "line": 50}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 18}}, "loc": {"start": {"line": 62, "column": 28}, "end": {"line": 62, "column": 52}}, "line": 62}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 70, "column": 9}, "end": {"line": 70, "column": 10}}, "loc": {"start": {"line": 70, "column": 20}, "end": {"line": 70, "column": 36}}, "line": 70}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}], "line": 23}, "1": {"loc": {"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 14}, "end": {"line": 48, "column": 18}}, {"start": {"line": 48, "column": 22}, "end": {"line": 48, "column": 32}}], "line": 48}, "2": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 60, "column": 2}, "end": {"line": 63, "column": 3}}, {"start": {"line": 60, "column": 2}, "end": {"line": 63, "column": 3}}], "line": 60}, "3": {"loc": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 51}}, "type": "if", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 51}}, {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 51}}], "line": 61}, "4": {"loc": {"start": {"line": 67, "column": 0}, "end": {"line": 73, "column": 1}}, "type": "if", "locations": [{"start": {"line": 67, "column": 0}, "end": {"line": 73, "column": 1}}, {"start": {"line": 67, "column": 0}, "end": {"line": 73, "column": 1}}], "line": 67}, "5": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 10}}, {"start": {"line": 67, "column": 14}, "end": {"line": 67, "column": 28}}], "line": 67}, "6": {"loc": {"start": {"line": 69, "column": 7}, "end": {"line": 73, "column": 1}}, "type": "if", "locations": [{"start": {"line": 69, "column": 7}, "end": {"line": 73, "column": 1}}, {"start": {"line": 69, "column": 7}, "end": {"line": 73, "column": 1}}], "line": 69}, "7": {"loc": {"start": {"line": 69, "column": 11}, "end": {"line": 69, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 11}, "end": {"line": 69, "column": 17}}, {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": 31}}], "line": 69}, "8": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 29}}, {"start": {"line": 77, "column": 33}, "end": {"line": 77, "column": 39}}], "line": 77}, "9": {"loc": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 31}}, {"start": {"line": 78, "column": 35}, "end": {"line": 78, "column": 41}}], "line": 78}}, "s": {"0": 1, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 3, "7": 3, "8": 4105327, "9": 4105327, "10": 4105327, "11": 4105327, "12": 4105327, "13": 3, "14": 2, "15": 1, "16": 3, "17": 3, "18": 198, "19": 198, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 3, "26": 3, "27": 3, "28": 4102054, "29": 3, "30": 1025, "31": 1025, "32": 1025, "33": 1025, "34": 1025, "35": 3, "36": 3, "37": 3, "38": 2, "39": 1, "40": 2, "41": 1, "42": 3, "43": 1, "44": 1, "45": 0, "46": 0, "47": 0, "48": 0}, "f": {"0": 1, "1": 3, "2": 4105327, "3": 2, "4": 3, "5": 4102054, "6": 1025, "7": 1, "8": 0}, "b": {"0": [2, 1], "1": [3, 2], "2": [2, 1], "3": [1, 1], "4": [1, 0], "5": [1, 1], "6": [0, 0], "7": [0, 0], "8": [1, 1], "9": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "42c805c53b8a22e4d141f90ddecc8031ba21211b", "contentHash": "2bd5b1ea82454991d6453cdd05e909475478ffed8cf6dd0ce02556ae981256d8"}, "/home/<USER>/git/seedrandom/lib/xorwow.js": {"path": "/home/<USER>/git/seedrandom/lib/xorwow.js", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 84, "column": 2}}, "1": {"start": {"line": 7, "column": 11}, "end": {"line": 7, "column": 15}}, "2": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 29}}, "3": {"start": {"line": 10, "column": 2}, "end": {"line": 15, "column": 4}}, "4": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 32}}, "5": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 16}}, "6": {"start": {"line": 12, "column": 17}, "end": {"line": 12, "column": 29}}, "7": {"start": {"line": 12, "column": 30}, "end": {"line": 12, "column": 42}}, "8": {"start": {"line": 12, "column": 43}, "end": {"line": 12, "column": 55}}, "9": {"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 58}}, "10": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 11}}, "11": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 11}}, "12": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 11}}, "13": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 11}}, "14": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 11}}, "15": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "16": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 16}}, "17": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 20}}, "18": {"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 3}}, "19": {"start": {"line": 32, "column": 15}, "end": {"line": 32, "column": 16}}, "20": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 38}}, "21": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "22": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 37}}, "23": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 14}}, "24": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 12}}, "25": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 12}}, "26": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 12}}, "27": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 12}}, "28": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 12}}, "29": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 12}}, "30": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 11}}, "31": {"start": {"line": 52, "column": 11}, "end": {"line": 52, "column": 27}}, "32": {"start": {"line": 53, "column": 14}, "end": {"line": 53, "column": 32}}, "33": {"start": {"line": 54, "column": 13}, "end": {"line": 54, "column": 67}}, "34": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 65}}, "35": {"start": {"line": 55, "column": 2}, "end": {"line": 62, "column": 4}}, "36": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 27}}, "37": {"start": {"line": 57, "column": 16}, "end": {"line": 57, "column": 32}}, "38": {"start": {"line": 58, "column": 16}, "end": {"line": 58, "column": 47}}, "39": {"start": {"line": 59, "column": 19}, "end": {"line": 59, "column": 42}}, "40": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 18}}, "41": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 23}}, "42": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 20}}, "43": {"start": {"line": 65, "column": 2}, "end": {"line": 68, "column": 3}}, "44": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 51}}, "45": {"start": {"line": 66, "column": 35}, "end": {"line": 66, "column": 51}}, "46": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 52}}, "47": {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": 50}}, "48": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 14}}, "49": {"start": {"line": 72, "column": 0}, "end": {"line": 78, "column": 1}}, "50": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 24}}, "51": {"start": {"line": 74, "column": 7}, "end": {"line": 78, "column": 1}}, "52": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 38}}, "53": {"start": {"line": 75, "column": 22}, "end": {"line": 75, "column": 34}}, "54": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 1}, "end": {"line": 4, "column": 2}}, "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 80, "column": 1}}, "line": 4}, "1": {"name": "XorGen", "decl": {"start": {"line": 6, "column": 9}, "end": {"line": 6, "column": 15}}, "loc": {"start": {"line": 6, "column": 22}, "end": {"line": 39, "column": 1}}, "line": 6}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 12}, "end": {"line": 10, "column": 13}}, "loc": {"start": {"line": 10, "column": 23}, "end": {"line": 15, "column": 3}}, "line": 10}, "3": {"name": "copy", "decl": {"start": {"line": 41, "column": 9}, "end": {"line": 41, "column": 13}}, "loc": {"start": {"line": 41, "column": 20}, "end": {"line": 49, "column": 1}}, "line": 41}, "4": {"name": "impl", "decl": {"start": {"line": 51, "column": 9}, "end": {"line": 51, "column": 13}}, "loc": {"start": {"line": 51, "column": 26}, "end": {"line": 70, "column": 1}}, "line": 51}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 54, "column": 13}, "end": {"line": 54, "column": 14}}, "loc": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 67}}, "line": 54}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 55, "column": 16}, "end": {"line": 55, "column": 17}}, "loc": {"start": {"line": 55, "column": 27}, "end": {"line": 62, "column": 3}}, "line": 55}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 67, "column": 17}, "end": {"line": 67, "column": 18}}, "loc": {"start": {"line": 67, "column": 28}, "end": {"line": 67, "column": 52}}, "line": 67}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 75, "column": 9}, "end": {"line": 75, "column": 10}}, "loc": {"start": {"line": 75, "column": 20}, "end": {"line": 75, "column": 36}}, "line": 75}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}, {"start": {"line": 23, "column": 2}, "end": {"line": 29, "column": 3}}], "line": 23}, "1": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}], "line": 34}, "2": {"loc": {"start": {"line": 53, "column": 14}, "end": {"line": 53, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 14}, "end": {"line": 53, "column": 18}}, {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 32}}], "line": 53}, "3": {"loc": {"start": {"line": 65, "column": 2}, "end": {"line": 68, "column": 3}}, "type": "if", "locations": [{"start": {"line": 65, "column": 2}, "end": {"line": 68, "column": 3}}, {"start": {"line": 65, "column": 2}, "end": {"line": 68, "column": 3}}], "line": 65}, "4": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 51}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 51}}, {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 51}}], "line": 66}, "5": {"loc": {"start": {"line": 72, "column": 0}, "end": {"line": 78, "column": 1}}, "type": "if", "locations": [{"start": {"line": 72, "column": 0}, "end": {"line": 78, "column": 1}}, {"start": {"line": 72, "column": 0}, "end": {"line": 78, "column": 1}}], "line": 72}, "6": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 10}}, {"start": {"line": 72, "column": 14}, "end": {"line": 72, "column": 28}}], "line": 72}, "7": {"loc": {"start": {"line": 74, "column": 7}, "end": {"line": 78, "column": 1}}, "type": "if", "locations": [{"start": {"line": 74, "column": 7}, "end": {"line": 78, "column": 1}}, {"start": {"line": 74, "column": 7}, "end": {"line": 78, "column": 1}}], "line": 74}, "8": {"loc": {"start": {"line": 74, "column": 11}, "end": {"line": 74, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 74, "column": 11}, "end": {"line": 74, "column": 17}}, {"start": {"line": 74, "column": 21}, "end": {"line": 74, "column": 31}}], "line": 74}, "9": {"loc": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 29}}, {"start": {"line": 82, "column": 33}, "end": {"line": 82, "column": 39}}], "line": 82}, "10": {"loc": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 31}}, {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 41}}], "line": 83}}, "s": {"0": 1, "1": 3, "2": 3, "3": 3, "4": 4105327, "5": 4105327, "6": 4105327, "7": 4105327, "8": 4105327, "9": 4105327, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 2, "17": 1, "18": 3, "19": 3, "20": 198, "21": 198, "22": 3, "23": 198, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 3, "32": 3, "33": 3, "34": 4102054, "35": 3, "36": 1025, "37": 1025, "38": 1025, "39": 1025, "40": 1025, "41": 3, "42": 3, "43": 3, "44": 2, "45": 1, "46": 2, "47": 1, "48": 3, "49": 1, "50": 1, "51": 0, "52": 0, "53": 0, "54": 0}, "f": {"0": 1, "1": 3, "2": 4105327, "3": 2, "4": 3, "5": 4102054, "6": 1025, "7": 1, "8": 0}, "b": {"0": [2, 1], "1": [3, 195], "2": [3, 2], "3": [2, 1], "4": [1, 1], "5": [1, 0], "6": [1, 1], "7": [0, 0], "8": [0, 0], "9": [1, 1], "10": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "279728a54e5ce9face1abbf1b1093f69bd5e2cef", "contentHash": "870bba719eee59f28e0160645190fa7eda491db1be3c64600c523d0b510d2000"}, "/home/<USER>/git/seedrandom/lib/xorshift7.js": {"path": "/home/<USER>/git/seedrandom/lib/xorshift7.js", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 96, "column": 2}}, "1": {"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 15}}, "2": {"start": {"line": 12, "column": 2}, "end": {"line": 23, "column": 4}}, "3": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 16}}, "4": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 26}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 13}}, "6": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 29}}, "7": {"start": {"line": 15, "column": 30}, "end": {"line": 15, "column": 48}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 23}}, "9": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 44}}, "10": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 23}}, "11": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 43}}, "12": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 23}}, "13": {"start": {"line": 18, "column": 24}, "end": {"line": 18, "column": 42}}, "14": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 23}}, "15": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 42}}, "16": {"start": {"line": 19, "column": 43}, "end": {"line": 19, "column": 61}}, "17": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 13}}, "18": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 23}}, "19": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 13}}, "20": {"start": {"line": 26, "column": 18}, "end": {"line": 26, "column": 20}}, "21": {"start": {"line": 28, "column": 4}, "end": {"line": 38, "column": 5}}, "22": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 22}}, "23": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 23}}, "24": {"start": {"line": 34, "column": 6}, "end": {"line": 37, "column": 7}}, "25": {"start": {"line": 35, "column": 8}, "end": {"line": 36, "column": 56}}, "26": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 35}}, "27": {"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 35}}, "28": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 42}}, "29": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}, "30": {"start": {"line": 42, "column": 16}, "end": {"line": 42, "column": 30}}, "31": {"start": {"line": 42, "column": 36}, "end": {"line": 42, "column": 45}}, "32": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 13}}, "33": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 13}}, "34": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "35": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 16}}, "36": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 17}}, "37": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 20}}, "38": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 12}}, "39": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 11}}, "40": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 39}}, "41": {"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 39}}, "42": {"start": {"line": 64, "column": 11}, "end": {"line": 64, "column": 27}}, "43": {"start": {"line": 65, "column": 14}, "end": {"line": 65, "column": 32}}, "44": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 67}}, "45": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 65}}, "46": {"start": {"line": 67, "column": 2}, "end": {"line": 74, "column": 4}}, "47": {"start": {"line": 68, "column": 4}, "end": {"line": 72, "column": 27}}, "48": {"start": {"line": 69, "column": 16}, "end": {"line": 69, "column": 32}}, "49": {"start": {"line": 70, "column": 16}, "end": {"line": 70, "column": 47}}, "50": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 42}}, "51": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 18}}, "52": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 23}}, "53": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 20}}, "54": {"start": {"line": 77, "column": 2}, "end": {"line": 80, "column": 3}}, "55": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 33}}, "56": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 33}}, "57": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 52}}, "58": {"start": {"line": 79, "column": 30}, "end": {"line": 79, "column": 50}}, "59": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 14}}, "60": {"start": {"line": 84, "column": 0}, "end": {"line": 90, "column": 1}}, "61": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 24}}, "62": {"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 1}}, "63": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 38}}, "64": {"start": {"line": 87, "column": 22}, "end": {"line": 87, "column": 34}}, "65": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 1}, "end": {"line": 6, "column": 2}}, "loc": {"start": {"line": 6, "column": 34}, "end": {"line": 92, "column": 1}}, "line": 6}, "1": {"name": "XorGen", "decl": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 15}}, "loc": {"start": {"line": 8, "column": 22}, "end": {"line": 54, "column": 1}}, "line": 8}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 13}}, "loc": {"start": {"line": 12, "column": 23}, "end": {"line": 23, "column": 3}}, "line": 12}, "3": {"name": "init", "decl": {"start": {"line": 25, "column": 11}, "end": {"line": 25, "column": 15}}, "loc": {"start": {"line": 25, "column": 26}, "end": {"line": 51, "column": 3}}, "line": 25}, "4": {"name": "copy", "decl": {"start": {"line": 56, "column": 9}, "end": {"line": 56, "column": 13}}, "loc": {"start": {"line": 56, "column": 20}, "end": {"line": 60, "column": 1}}, "line": 56}, "5": {"name": "impl", "decl": {"start": {"line": 62, "column": 9}, "end": {"line": 62, "column": 13}}, "loc": {"start": {"line": 62, "column": 26}, "end": {"line": 82, "column": 1}}, "line": 62}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 14}}, "loc": {"start": {"line": 66, "column": 24}, "end": {"line": 66, "column": 67}}, "line": 66}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": 17}}, "loc": {"start": {"line": 67, "column": 27}, "end": {"line": 74, "column": 3}}, "line": 67}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 79, "column": 17}, "end": {"line": 79, "column": 18}}, "loc": {"start": {"line": 79, "column": 28}, "end": {"line": 79, "column": 52}}, "line": 79}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 87, "column": 9}, "end": {"line": 87, "column": 10}}, "loc": {"start": {"line": 87, "column": 20}, "end": {"line": 87, "column": 36}}, "line": 87}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 38, "column": 5}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 38, "column": 5}}, {"start": {"line": 28, "column": 4}, "end": {"line": 38, "column": 5}}], "line": 28}, "1": {"loc": {"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 21}}, {"start": {"line": 41, "column": 25}, "end": {"line": 41, "column": 35}}], "line": 41}, "2": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}, {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 45}}], "line": 42}, "3": {"loc": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 39}}, "type": "if", "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 39}}, {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 39}}], "line": 63}, "4": {"loc": {"start": {"line": 65, "column": 14}, "end": {"line": 65, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 14}, "end": {"line": 65, "column": 18}}, {"start": {"line": 65, "column": 22}, "end": {"line": 65, "column": 32}}], "line": 65}, "5": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 80, "column": 3}}, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 80, "column": 3}}, {"start": {"line": 77, "column": 2}, "end": {"line": 80, "column": 3}}], "line": 77}, "6": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 33}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 33}}, {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 33}}], "line": 78}, "7": {"loc": {"start": {"line": 84, "column": 0}, "end": {"line": 90, "column": 1}}, "type": "if", "locations": [{"start": {"line": 84, "column": 0}, "end": {"line": 90, "column": 1}}, {"start": {"line": 84, "column": 0}, "end": {"line": 90, "column": 1}}], "line": 84}, "8": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 10}}, {"start": {"line": 84, "column": 14}, "end": {"line": 84, "column": 28}}], "line": 84}, "9": {"loc": {"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 1}}, "type": "if", "locations": [{"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 1}}, {"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 1}}], "line": 86}, "10": {"loc": {"start": {"line": 86, "column": 11}, "end": {"line": 86, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 11}, "end": {"line": 86, "column": 17}}, {"start": {"line": 86, "column": 21}, "end": {"line": 86, "column": 31}}], "line": 86}, "11": {"loc": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 29}}, {"start": {"line": 94, "column": 33}, "end": {"line": 94, "column": 39}}], "line": 94}, "12": {"loc": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 31}}, {"start": {"line": 95, "column": 35}, "end": {"line": 95, "column": 41}}], "line": 95}}, "s": {"0": 1, "1": 3, "2": 3, "3": 4105897, "4": 4105897, "5": 4105897, "6": 4105897, "7": 4105897, "8": 4105897, "9": 4105897, "10": 4105897, "11": 4105897, "12": 4105897, "13": 4105897, "14": 4105897, "15": 4105897, "16": 4105897, "17": 4105897, "18": 4105897, "19": 4105897, "20": 3, "21": 3, "22": 2, "23": 1, "24": 1, "25": 6, "26": 3, "27": 16, "28": 3, "29": 3, "30": 2, "31": 1, "32": 3, "33": 3, "34": 3, "35": 768, "36": 3, "37": 2, "38": 2, "39": 2, "40": 3, "41": 0, "42": 3, "43": 3, "44": 3, "45": 4102054, "46": 3, "47": 1025, "48": 1025, "49": 1025, "50": 1025, "51": 1025, "52": 3, "53": 3, "54": 3, "55": 2, "56": 1, "57": 2, "58": 1, "59": 3, "60": 1, "61": 1, "62": 0, "63": 0, "64": 0, "65": 0}, "f": {"0": 1, "1": 3, "2": 4105897, "3": 3, "4": 2, "5": 3, "6": 4102054, "7": 1025, "8": 1, "9": 0}, "b": {"0": [2, 1], "1": [19, 17], "2": [2, 1], "3": [0, 3], "4": [3, 2], "5": [2, 1], "6": [1, 1], "7": [1, 0], "8": [1, 1], "9": [0, 0], "10": [0, 0], "11": [1, 1], "12": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "131ac72069e0ca2a2df6aae06f4b2d31b91187b5", "contentHash": "4aba94be2ce998b353804abdbf2962ed421abbc77501066348025b6fa45b856d"}, "/home/<USER>/git/seedrandom/lib/xor4096.js": {"path": "/home/<USER>/git/seedrandom/lib/xor4096.js", "statementMap": {"0": {"start": {"line": 26, "column": 0}, "end": {"line": 146, "column": 2}}, "1": {"start": {"line": 29, "column": 11}, "end": {"line": 29, "column": 15}}, "2": {"start": {"line": 32, "column": 2}, "end": {"line": 49, "column": 4}}, "3": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 16}}, "4": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 16}}, "5": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 26}}, "6": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 36}}, "7": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 26}}, "8": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 31}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 17}}, "10": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 17}}, "11": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 18}}, "12": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 18}}, "13": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 21}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 13}}, "15": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 38}}, "16": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 29}}, "17": {"start": {"line": 52, "column": 39}, "end": {"line": 52, "column": 42}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 62, "column": 5}}, "19": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 15}}, "20": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 18}}, "21": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 25}}, "22": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 12}}, "23": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 43}}, "24": {"start": {"line": 64, "column": 4}, "end": {"line": 78, "column": 5}}, "25": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 61}}, "26": {"start": {"line": 66, "column": 16}, "end": {"line": 66, "column": 61}}, "27": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}, "28": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 25}}, "29": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 19}}, "30": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 20}}, "31": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 18}}, "32": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 20}}, "33": {"start": {"line": 73, "column": 6}, "end": {"line": 77, "column": 7}}, "34": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 33}}, "35": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 36}}, "36": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 33}}, "37": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "38": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 47}}, "39": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 12}}, "40": {"start": {"line": 87, "column": 4}, "end": {"line": 95, "column": 5}}, "41": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 28}}, "42": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 33}}, "43": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 19}}, "44": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 19}}, "45": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 20}}, "46": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 20}}, "47": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 19}}, "48": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 13}}, "49": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 13}}, "50": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 13}}, "51": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 17}}, "52": {"start": {"line": 106, "column": 2}, "end": {"line": 106, "column": 12}}, "53": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 12}}, "54": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 20}}, "55": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 11}}, "56": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 39}}, "57": {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 39}}, "58": {"start": {"line": 114, "column": 11}, "end": {"line": 114, "column": 27}}, "59": {"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 32}}, "60": {"start": {"line": 116, "column": 13}, "end": {"line": 116, "column": 67}}, "61": {"start": {"line": 116, "column": 26}, "end": {"line": 116, "column": 65}}, "62": {"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 4}}, "63": {"start": {"line": 118, "column": 4}, "end": {"line": 122, "column": 27}}, "64": {"start": {"line": 119, "column": 16}, "end": {"line": 119, "column": 32}}, "65": {"start": {"line": 120, "column": 16}, "end": {"line": 120, "column": 47}}, "66": {"start": {"line": 121, "column": 19}, "end": {"line": 121, "column": 42}}, "67": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 18}}, "68": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 23}}, "69": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 20}}, "70": {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}, "71": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 33}}, "72": {"start": {"line": 128, "column": 17}, "end": {"line": 128, "column": 33}}, "73": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 52}}, "74": {"start": {"line": 129, "column": 30}, "end": {"line": 129, "column": 50}}, "75": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 14}}, "76": {"start": {"line": 134, "column": 0}, "end": {"line": 140, "column": 1}}, "77": {"start": {"line": 135, "column": 2}, "end": {"line": 135, "column": 24}}, "78": {"start": {"line": 136, "column": 7}, "end": {"line": 140, "column": 1}}, "79": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 38}}, "80": {"start": {"line": 137, "column": 22}, "end": {"line": 137, "column": 34}}, "81": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 1}, "end": {"line": 26, "column": 2}}, "loc": {"start": {"line": 26, "column": 34}, "end": {"line": 142, "column": 1}}, "line": 26}, "1": {"name": "XorGen", "decl": {"start": {"line": 28, "column": 9}, "end": {"line": 28, "column": 15}}, "loc": {"start": {"line": 28, "column": 22}, "end": {"line": 103, "column": 1}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 12}, "end": {"line": 32, "column": 13}}, "loc": {"start": {"line": 32, "column": 23}, "end": {"line": 49, "column": 3}}, "line": 32}, "3": {"name": "init", "decl": {"start": {"line": 51, "column": 11}, "end": {"line": 51, "column": 15}}, "loc": {"start": {"line": 51, "column": 26}, "end": {"line": 100, "column": 3}}, "line": 51}, "4": {"name": "copy", "decl": {"start": {"line": 105, "column": 9}, "end": {"line": 105, "column": 13}}, "loc": {"start": {"line": 105, "column": 20}, "end": {"line": 110, "column": 1}}, "line": 105}, "5": {"name": "impl", "decl": {"start": {"line": 112, "column": 9}, "end": {"line": 112, "column": 13}}, "loc": {"start": {"line": 112, "column": 26}, "end": {"line": 132, "column": 1}}, "line": 112}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 116, "column": 13}, "end": {"line": 116, "column": 14}}, "loc": {"start": {"line": 116, "column": 24}, "end": {"line": 116, "column": 67}}, "line": 116}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 117, "column": 16}, "end": {"line": 117, "column": 17}}, "loc": {"start": {"line": 117, "column": 27}, "end": {"line": 124, "column": 3}}, "line": 117}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 18}}, "loc": {"start": {"line": 129, "column": 28}, "end": {"line": 129, "column": 52}}, "line": 129}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 137, "column": 9}, "end": {"line": 137, "column": 10}}, "loc": {"start": {"line": 137, "column": 20}, "end": {"line": 137, "column": 36}}, "line": 137}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 62, "column": 5}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 62, "column": 5}}, {"start": {"line": 53, "column": 4}, "end": {"line": 62, "column": 5}}], "line": 53}, "1": {"loc": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 61}}, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 61}}, {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 61}}], "line": 66}, "2": {"loc": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}, "type": "if", "locations": [{"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}, {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}], "line": 68}, "3": {"loc": {"start": {"line": 73, "column": 6}, "end": {"line": 77, "column": 7}}, "type": "if", "locations": [{"start": {"line": 73, "column": 6}, "end": {"line": 77, "column": 7}}, {"start": {"line": 73, "column": 6}, "end": {"line": 77, "column": 7}}], "line": 73}, "4": {"loc": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 76, "column": 23}, "end": {"line": 76, "column": 28}}, {"start": {"line": 76, "column": 31}, "end": {"line": 76, "column": 32}}], "line": 76}, "5": {"loc": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}], "line": 80}, "6": {"loc": {"start": {"line": 81, "column": 9}, "end": {"line": 81, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 9}, "end": {"line": 81, "column": 13}}, {"start": {"line": 81, "column": 17}, "end": {"line": 81, "column": 28}}, {"start": {"line": 81, "column": 32}, "end": {"line": 81, "column": 33}}], "line": 81}, "7": {"loc": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 39}}, "type": "if", "locations": [{"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 39}}, {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 39}}], "line": 113}, "8": {"loc": {"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 18}}, {"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 32}}], "line": 115}, "9": {"loc": {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}, "type": "if", "locations": [{"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}, {"start": {"line": 127, "column": 2}, "end": {"line": 130, "column": 3}}], "line": 127}, "10": {"loc": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 33}}, "type": "if", "locations": [{"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 33}}, {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": 33}}], "line": 128}, "11": {"loc": {"start": {"line": 134, "column": 0}, "end": {"line": 140, "column": 1}}, "type": "if", "locations": [{"start": {"line": 134, "column": 0}, "end": {"line": 140, "column": 1}}, {"start": {"line": 134, "column": 0}, "end": {"line": 140, "column": 1}}], "line": 134}, "12": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 10}}, {"start": {"line": 134, "column": 14}, "end": {"line": 134, "column": 28}}], "line": 134}, "13": {"loc": {"start": {"line": 136, "column": 7}, "end": {"line": 140, "column": 1}}, "type": "if", "locations": [{"start": {"line": 136, "column": 7}, "end": {"line": 140, "column": 1}}, {"start": {"line": 136, "column": 7}, "end": {"line": 140, "column": 1}}], "line": 136}, "14": {"loc": {"start": {"line": 136, "column": 11}, "end": {"line": 136, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 136, "column": 11}, "end": {"line": 136, "column": 17}}, {"start": {"line": 136, "column": 21}, "end": {"line": 136, "column": 31}}], "line": 136}, "15": {"loc": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 29}}, {"start": {"line": 144, "column": 33}, "end": {"line": 144, "column": 39}}], "line": 144}, "16": {"loc": {"start": {"line": 145, "column": 2}, "end": {"line": 145, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 2}, "end": {"line": 145, "column": 31}}, {"start": {"line": 145, "column": 35}, "end": {"line": 145, "column": 41}}], "line": 145}}, "s": {"0": 1, "1": 3, "2": 3, "3": 4105129, "4": 4105129, "5": 4105129, "6": 4105129, "7": 4105129, "8": 4105129, "9": 4105129, "10": 4105129, "11": 4105129, "12": 4105129, "13": 4105129, "14": 4105129, "15": 4105129, "16": 3, "17": 3, "18": 3, "19": 2, "20": 2, "21": 1, "22": 1, "23": 1, "24": 3, "25": 480, "26": 160, "27": 480, "28": 3, "29": 480, "30": 480, "31": 480, "32": 480, "33": 480, "34": 384, "35": 384, "36": 384, "37": 3, "38": 0, "39": 3, "40": 3, "41": 1536, "42": 1536, "43": 1536, "44": 1536, "45": 1536, "46": 1536, "47": 1536, "48": 3, "49": 3, "50": 3, "51": 3, "52": 2, "53": 2, "54": 2, "55": 2, "56": 3, "57": 0, "58": 3, "59": 3, "60": 3, "61": 4102054, "62": 3, "63": 1025, "64": 1025, "65": 1025, "66": 1025, "67": 1025, "68": 3, "69": 3, "70": 3, "71": 2, "72": 1, "73": 2, "74": 1, "75": 3, "76": 1, "77": 1, "78": 0, "79": 0, "80": 0, "81": 0}, "f": {"0": 1, "1": 3, "2": 4105129, "3": 3, "4": 2, "5": 3, "6": 4102054, "7": 1025, "8": 1, "9": 0}, "b": {"0": [2, 1], "1": [160, 320], "2": [3, 477], "3": [384, 96], "4": [0, 384], "5": [0, 3], "6": [0, 0, 0], "7": [0, 3], "8": [3, 2], "9": [2, 1], "10": [1, 1], "11": [1, 0], "12": [1, 1], "13": [0, 0], "14": [0, 0], "15": [1, 1], "16": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "5de096409f62b3565b1ce6d3136eb46f59ce3abe", "contentHash": "2e41775c8739abfedd52237d6d32ba6c6ccbf9e10e6721c3a313ec1d67788df4"}, "/home/<USER>/git/seedrandom/lib/tychei.js": {"path": "/home/<USER>/git/seedrandom/lib/tychei.js", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 101, "column": 2}}, "1": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 15}}, "2": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 29}}, "3": {"start": {"line": 11, "column": 2}, "end": {"line": 21, "column": 4}}, "4": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 16}}, "5": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 26}}, "6": {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 36}}, "7": {"start": {"line": 12, "column": 42}, "end": {"line": 12, "column": 46}}, "8": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 34}}, "9": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 20}}, "10": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 34}}, "11": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 20}}, "12": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 42}}, "13": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 27}}, "14": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 38}}, "15": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 30}}, "16": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 11}}, "17": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 11}}, "18": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 24}}, "19": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 20}}, "20": {"start": {"line": 44, "column": 2}, "end": {"line": 51, "column": 3}}, "21": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 36}}, "22": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 20}}, "23": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 20}}, "24": {"start": {"line": 54, "column": 2}, "end": {"line": 57, "column": 3}}, "25": {"start": {"line": 54, "column": 15}, "end": {"line": 54, "column": 16}}, "26": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 38}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 14}}, "28": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 12}}, "29": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 12}}, "30": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 12}}, "31": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 12}}, "32": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 11}}, "33": {"start": {"line": 69, "column": 11}, "end": {"line": 69, "column": 27}}, "34": {"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 32}}, "35": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 67}}, "36": {"start": {"line": 71, "column": 26}, "end": {"line": 71, "column": 65}}, "37": {"start": {"line": 72, "column": 2}, "end": {"line": 79, "column": 4}}, "38": {"start": {"line": 73, "column": 4}, "end": {"line": 77, "column": 27}}, "39": {"start": {"line": 74, "column": 16}, "end": {"line": 74, "column": 32}}, "40": {"start": {"line": 75, "column": 16}, "end": {"line": 75, "column": 47}}, "41": {"start": {"line": 76, "column": 19}, "end": {"line": 76, "column": 42}}, "42": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 18}}, "43": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 23}}, "44": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 20}}, "45": {"start": {"line": 82, "column": 2}, "end": {"line": 85, "column": 3}}, "46": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}, "47": {"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 51}}, "48": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 52}}, "49": {"start": {"line": 84, "column": 30}, "end": {"line": 84, "column": 50}}, "50": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 14}}, "51": {"start": {"line": 89, "column": 0}, "end": {"line": 95, "column": 1}}, "52": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 24}}, "53": {"start": {"line": 91, "column": 7}, "end": {"line": 95, "column": 1}}, "54": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 38}}, "55": {"start": {"line": 92, "column": 22}, "end": {"line": 92, "column": 34}}, "56": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 1}, "end": {"line": 5, "column": 2}}, "loc": {"start": {"line": 5, "column": 34}, "end": {"line": 97, "column": 1}}, "line": 5}, "1": {"name": "XorGen", "decl": {"start": {"line": 7, "column": 9}, "end": {"line": 7, "column": 15}}, "loc": {"start": {"line": 7, "column": 22}, "end": {"line": 58, "column": 1}}, "line": 7}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 13}}, "loc": {"start": {"line": 11, "column": 23}, "end": {"line": 21, "column": 3}}, "line": 11}, "3": {"name": "copy", "decl": {"start": {"line": 60, "column": 9}, "end": {"line": 60, "column": 13}}, "loc": {"start": {"line": 60, "column": 20}, "end": {"line": 66, "column": 1}}, "line": 60}, "4": {"name": "impl", "decl": {"start": {"line": 68, "column": 9}, "end": {"line": 68, "column": 13}}, "loc": {"start": {"line": 68, "column": 26}, "end": {"line": 87, "column": 1}}, "line": 68}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 14}}, "loc": {"start": {"line": 71, "column": 24}, "end": {"line": 71, "column": 67}}, "line": 71}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 72, "column": 16}, "end": {"line": 72, "column": 17}}, "loc": {"start": {"line": 72, "column": 27}, "end": {"line": 79, "column": 3}}, "line": 72}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 84, "column": 17}, "end": {"line": 84, "column": 18}}, "loc": {"start": {"line": 84, "column": 28}, "end": {"line": 84, "column": 52}}, "line": 84}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 92, "column": 9}, "end": {"line": 92, "column": 10}}, "loc": {"start": {"line": 92, "column": 20}, "end": {"line": 92, "column": 36}}, "line": 92}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 51, "column": 3}}, "type": "if", "locations": [{"start": {"line": 44, "column": 2}, "end": {"line": 51, "column": 3}}, {"start": {"line": 44, "column": 2}, "end": {"line": 51, "column": 3}}], "line": 44}, "1": {"loc": {"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 14}, "end": {"line": 70, "column": 18}}, {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 32}}], "line": 70}, "2": {"loc": {"start": {"line": 82, "column": 2}, "end": {"line": 85, "column": 3}}, "type": "if", "locations": [{"start": {"line": 82, "column": 2}, "end": {"line": 85, "column": 3}}, {"start": {"line": 82, "column": 2}, "end": {"line": 85, "column": 3}}], "line": 82}, "3": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}, {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}], "line": 83}, "4": {"loc": {"start": {"line": 89, "column": 0}, "end": {"line": 95, "column": 1}}, "type": "if", "locations": [{"start": {"line": 89, "column": 0}, "end": {"line": 95, "column": 1}}, {"start": {"line": 89, "column": 0}, "end": {"line": 95, "column": 1}}], "line": 89}, "5": {"loc": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 10}}, {"start": {"line": 89, "column": 14}, "end": {"line": 89, "column": 28}}], "line": 89}, "6": {"loc": {"start": {"line": 91, "column": 7}, "end": {"line": 95, "column": 1}}, "type": "if", "locations": [{"start": {"line": 91, "column": 7}, "end": {"line": 95, "column": 1}}, {"start": {"line": 91, "column": 7}, "end": {"line": 95, "column": 1}}], "line": 91}, "7": {"loc": {"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 17}}, {"start": {"line": 91, "column": 21}, "end": {"line": 91, "column": 31}}], "line": 91}, "8": {"loc": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 29}}, {"start": {"line": 99, "column": 33}, "end": {"line": 99, "column": 39}}], "line": 99}, "9": {"loc": {"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 31}}, {"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 41}}], "line": 100}}, "s": {"0": 1, "1": 3, "2": 3, "3": 3, "4": 4105195, "5": 4105195, "6": 4105195, "7": 4105195, "8": 4105195, "9": 4105195, "10": 4105195, "11": 4105195, "12": 4105195, "13": 4105195, "14": 4105195, "15": 4105195, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 2, "22": 2, "23": 1, "24": 3, "25": 3, "26": 66, "27": 66, "28": 2, "29": 2, "30": 2, "31": 2, "32": 2, "33": 3, "34": 3, "35": 3, "36": 4102054, "37": 3, "38": 1025, "39": 1025, "40": 1025, "41": 1025, "42": 1025, "43": 3, "44": 3, "45": 3, "46": 2, "47": 1, "48": 2, "49": 1, "50": 3, "51": 1, "52": 1, "53": 0, "54": 0, "55": 0, "56": 0}, "f": {"0": 1, "1": 3, "2": 4105195, "3": 2, "4": 3, "5": 4102054, "6": 1025, "7": 1, "8": 0}, "b": {"0": [2, 1], "1": [3, 2], "2": [2, 1], "3": [1, 1], "4": [1, 0], "5": [1, 1], "6": [0, 0], "7": [0, 0], "8": [1, 1], "9": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "3875c0e0fa564e9ae68114bde64afc883b7c9ca0", "contentHash": "00c6380ad151ad19433a64fdc0b4051c1a7ee0af16dc43e971860cc64ce62b05"}, "/home/<USER>/git/seedrandom/lib/alea.js": {"path": "/home/<USER>/git/seedrandom/lib/alea.js", "statementMap": {"0": {"start": {"line": 28, "column": 0}, "end": {"line": 112, "column": 2}}, "1": {"start": {"line": 31, "column": 11}, "end": {"line": 31, "column": 15}}, "2": {"start": {"line": 31, "column": 24}, "end": {"line": 31, "column": 30}}, "3": {"start": {"line": 33, "column": 2}, "end": {"line": 38, "column": 4}}, "4": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 59}}, "5": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 18}}, "6": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 18}}, "7": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 38}}, "8": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 11}}, "9": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 20}}, "10": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 20}}, "11": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 20}}, "12": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 22}}, "13": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}, "14": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 30}}, "15": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 22}}, "16": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 32}}, "17": {"start": {"line": 48, "column": 19}, "end": {"line": 48, "column": 30}}, "18": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 22}}, "19": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 32}}, "20": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 30}}, "21": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 14}}, "22": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 12}}, "23": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 14}}, "24": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 14}}, "25": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 14}}, "26": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 11}}, "27": {"start": {"line": 63, "column": 11}, "end": {"line": 63, "column": 25}}, "28": {"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 32}}, "29": {"start": {"line": 65, "column": 13}, "end": {"line": 65, "column": 20}}, "30": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 67}}, "31": {"start": {"line": 66, "column": 28}, "end": {"line": 66, "column": 65}}, "32": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 4}}, "33": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 69}}, "34": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 20}}, "35": {"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 3}}, "36": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 51}}, "37": {"start": {"line": 72, "column": 35}, "end": {"line": 72, "column": 51}}, "38": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 52}}, "39": {"start": {"line": 73, "column": 30}, "end": {"line": 73, "column": 50}}, "40": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": 14}}, "41": {"start": {"line": 79, "column": 10}, "end": {"line": 79, "column": 20}}, "42": {"start": {"line": 81, "column": 13}, "end": {"line": 94, "column": 3}}, "43": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 24}}, "44": {"start": {"line": 83, "column": 4}, "end": {"line": 92, "column": 5}}, "45": {"start": {"line": 83, "column": 17}, "end": {"line": 83, "column": 18}}, "46": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 30}}, "47": {"start": {"line": 85, "column": 14}, "end": {"line": 85, "column": 37}}, "48": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 18}}, "49": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 13}}, "50": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 13}}, "51": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 18}}, "52": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 13}}, "53": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 27}}, "54": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 46}}, "55": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 14}}, "56": {"start": {"line": 100, "column": 0}, "end": {"line": 106, "column": 1}}, "57": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 24}}, "58": {"start": {"line": 102, "column": 7}, "end": {"line": 106, "column": 1}}, "59": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 38}}, "60": {"start": {"line": 103, "column": 22}, "end": {"line": 103, "column": 34}}, "61": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 1}, "end": {"line": 28, "column": 2}}, "loc": {"start": {"line": 28, "column": 34}, "end": {"line": 108, "column": 1}}, "line": 28}, "1": {"name": "Alea", "decl": {"start": {"line": 30, "column": 9}, "end": {"line": 30, "column": 13}}, "loc": {"start": {"line": 30, "column": 20}, "end": {"line": 52, "column": 1}}, "line": 30}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 13}}, "loc": {"start": {"line": 33, "column": 23}, "end": {"line": 38, "column": 3}}, "line": 33}, "3": {"name": "copy", "decl": {"start": {"line": 54, "column": 9}, "end": {"line": 54, "column": 13}}, "loc": {"start": {"line": 54, "column": 20}, "end": {"line": 60, "column": 1}}, "line": 54}, "4": {"name": "impl", "decl": {"start": {"line": 62, "column": 9}, "end": {"line": 62, "column": 13}}, "loc": {"start": {"line": 62, "column": 26}, "end": {"line": 76, "column": 1}}, "line": 62}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 66, "column": 15}, "end": {"line": 66, "column": 16}}, "loc": {"start": {"line": 66, "column": 26}, "end": {"line": 66, "column": 67}}, "line": 66}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": 17}}, "loc": {"start": {"line": 67, "column": 27}, "end": {"line": 69, "column": 3}}, "line": 67}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 18}}, "loc": {"start": {"line": 73, "column": 28}, "end": {"line": 73, "column": 52}}, "line": 73}, "8": {"name": "<PERSON><PERSON>", "decl": {"start": {"line": 78, "column": 9}, "end": {"line": 78, "column": 13}}, "loc": {"start": {"line": 78, "column": 16}, "end": {"line": 97, "column": 1}}, "line": 78}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 81, "column": 13}, "end": {"line": 81, "column": 14}}, "loc": {"start": {"line": 81, "column": 28}, "end": {"line": 94, "column": 3}}, "line": 81}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 103, "column": 9}, "end": {"line": 103, "column": 10}}, "loc": {"start": {"line": 103, "column": 20}, "end": {"line": 103, "column": 36}}, "line": 103}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}, {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 32}}], "line": 46}, "1": {"loc": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 32}}, "type": "if", "locations": [{"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 32}}, {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 32}}], "line": 48}, "2": {"loc": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 32}}, "type": "if", "locations": [{"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 32}}, {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 32}}], "line": 50}, "3": {"loc": {"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 18}}, {"start": {"line": 64, "column": 22}, "end": {"line": 64, "column": 32}}], "line": 64}, "4": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 3}}, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 3}}, {"start": {"line": 71, "column": 2}, "end": {"line": 74, "column": 3}}], "line": 71}, "5": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 51}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 51}}, {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 51}}], "line": 72}, "6": {"loc": {"start": {"line": 100, "column": 0}, "end": {"line": 106, "column": 1}}, "type": "if", "locations": [{"start": {"line": 100, "column": 0}, "end": {"line": 106, "column": 1}}, {"start": {"line": 100, "column": 0}, "end": {"line": 106, "column": 1}}], "line": 100}, "7": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 10}}, {"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 28}}], "line": 100}, "8": {"loc": {"start": {"line": 102, "column": 7}, "end": {"line": 106, "column": 1}}, "type": "if", "locations": [{"start": {"line": 102, "column": 7}, "end": {"line": 106, "column": 1}}, {"start": {"line": 102, "column": 7}, "end": {"line": 106, "column": 1}}], "line": 102}, "9": {"loc": {"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 11}, "end": {"line": 102, "column": 17}}, {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 31}}], "line": 102}, "10": {"loc": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 29}}, {"start": {"line": 110, "column": 33}, "end": {"line": 110, "column": 39}}], "line": 110}, "11": {"loc": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 31}}, {"start": {"line": 111, "column": 35}, "end": {"line": 111, "column": 41}}], "line": 111}}, "s": {"0": 1, "1": 3, "2": 3, "3": 3, "4": 4105129, "5": 4105129, "6": 4105129, "7": 4105129, "8": 3, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 1, "15": 3, "16": 3, "17": 3, "18": 3, "19": 3, "20": 0, "21": 3, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 3, "28": 3, "29": 3, "30": 3, "31": 1025, "32": 3, "33": 1025, "34": 3, "35": 3, "36": 2, "37": 1, "38": 2, "39": 1, "40": 3, "41": 3, "42": 3, "43": 18, "44": 18, "45": 18, "46": 33, "47": 33, "48": 33, "49": 33, "50": 33, "51": 33, "52": 33, "53": 33, "54": 18, "55": 3, "56": 1, "57": 1, "58": 0, "59": 0, "60": 0, "61": 0}, "f": {"0": 1, "1": 3, "2": 4105129, "3": 2, "4": 3, "5": 1025, "6": 1025, "7": 1, "8": 3, "9": 18, "10": 0}, "b": {"0": [1, 2], "1": [3, 0], "2": [0, 3], "3": [3, 2], "4": [2, 1], "5": [1, 1], "6": [1, 0], "7": [1, 1], "8": [0, 0], "9": [0, 0], "10": [1, 1], "11": [1, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "b70bcb8438fd8d93267e4355bb97ec0e15c51502", "contentHash": "026d994d4bc0afeb3f977bb09d4e6284476ab12f7a5be78bdde751602626af54"}}