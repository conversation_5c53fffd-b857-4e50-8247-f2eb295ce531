/**
 * @license
 * Copyright 2024 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import{util as e,kernel_impls as t,KernelBackend as n,DataStorage as a,engine as s,env as r,backend_util as o,buffer as i,Abs as l,Complex as d,Identity as c,Real as p,Cast as u,Add as h,BitwiseAnd as f,Ceil as m,Equal as g,Exp as k,Expm1 as I,Floor as b,FloorDiv as y,Greater as N,GreaterEqual as T,Less as x,LessEqual as S,Log as v,Maximum as F,Minimum as w,Multiply as M,Neg as A,NotEqual as D,Transpose as E,upcastType as z,Prod as W,tidy as R,reshape as P,broadcastTo as H,Rsqrt as C,TensorBuffer as $,Sigmoid as O,slice_util as V,Slice as _,Sqrt as G,SquaredDifference as B,StaticRegexReplace as L,Sub as q,registerBackend as U,Elu as Z,LeakyRelu as K,Prelu as j,Relu as Y,Relu6 as J,Reshape as Q,BatchMatMul as X,broadcast_util as ee,_FusedMatMul as te,Acos as ne,Acosh as ae,AddN as se,All as re,Any as oe,ArgMax as ie,ArgMin as le,Asin as de,Asinh as ce,Atan as pe,Atan2 as ue,Atanh as he,AvgPool as fe,AvgPool3D as me,AvgPool3DGrad as ge,AvgPoolGrad as ke,FusedBatchNorm as Ie,BatchToSpaceND as be,Bincount as ye,BroadcastArgs as Ne,ClipByValue as Te,ComplexAbs as xe,Imag as Se,Concat as ve,Conv2D as Fe,Conv2DBackpropFilter as we,Conv2DBackpropInput as Me,Conv3D as Ae,Conv3DBackpropFilterV2 as De,Conv3DBackpropInputV2 as Ee,Cos as ze,Cosh as We,CropAndResize as Re,Cumprod as Pe,Cumsum as He,DenseBincount as Ce,DepthToSpace as $e,DepthwiseConv2dNative as Oe,DepthwiseConv2dNativeBackpropFilter as Ve,DepthwiseConv2dNativeBackpropInput as _e,Diag as Ge,Dilation2D as Be,Dilation2DBackpropFilter as Le,Dilation2DBackpropInput as qe,Draw as Ue,Sum as Ze,Einsum as Ke,EluGrad as je,Erf as Ye,ExpandDims as Je,RealDiv as Qe,FFT as Xe,Fill as et,FlipLeftRight as tt,FusedConv2D as nt,FusedDepthwiseConv2D as at,GatherNd as st,GatherV2 as rt,IFFT as ot,IsFinite as it,IsInf as lt,IsNan as dt,LinSpace as ct,Log1p as pt,LogicalAnd as ut,LogicalNot as ht,LogicalOr as ft,LRN as mt,LRNGrad as gt,Max as kt,MaxPool as It,MaxPool3D as bt,MaxPool3DGrad as yt,MaxPoolGrad as Nt,MaxPoolWithArgmax as Tt,Mean as xt,Min as St,MirrorPad as vt,Mod as Ft,Softmax as wt,Multinomial as Mt,NonMaxSuppressionV3 as At,NonMaxSuppressionV4 as Dt,NonMaxSuppressionV5 as Et,OneHot as zt,ZerosLike as Wt,OnesLike as Rt,Pack as Pt,PadV2 as Ht,Pow as Ct,RaggedGather as $t,RaggedRange as Ot,RaggedTensorToTensor as Vt,Range as _t,Reciprocal as Gt,ResizeBilinear as Bt,ResizeBilinearGrad as Lt,ResizeNearestNeighbor as qt,ResizeNearestNeighborGrad as Ut,Reverse as Zt,RotateWithOffset as Kt,Round as jt,ScatterNd as Yt,SearchSorted as Jt,Select as Qt,Selu as Xt,Sign as en,Sin as tn,Sinh as nn,Softplus as an,SpaceToBatchND as sn,SparseFillEmptyRows as rn,SparseReshape as on,SparseSegmentMean as ln,SparseSegmentSum as dn,SparseToDense as cn,SplitV as pn,Square as un,Step as hn,StridedSlice as fn,StringNGrams as mn,StringSplit as gn,StringToHashBucketFast as kn,Tan as In,Tanh as bn,TensorScatterUpdate as yn,Tile as Nn,TopK as Tn,Transform as xn,Unique as Sn,Unpack as vn,UnsortedSegmentSum as Fn,registerKernel as wn}from"@tensorflow/tfjs-core";import*as Mn from"seedrandom";function An(t,n){Array.isArray(t)||(t=[t]),t.forEach((t=>{null!=t&&e.assert("complex64"!==t.dtype,(()=>`${n} does not support complex64 tensors in the CPU backend.`))}))}const Dn=t.whereImpl;class En extends n{nextDataId(){return En.nextDataId++}constructor(){super(),this.blockSize=48,this.firstUse=!0,this.data=new a(this,s())}write(e,t,n){this.firstUse&&(this.firstUse=!1,r().get("IS_NODE")&&o.warn("\n============================\nHi, looks like you are running TensorFlow.js in Node.js. To speed things up dramatically, install our node backend, visit https://github.com/tensorflow/tfjs-node for more details. \n============================"));const a={id:this.nextDataId()};return this.data.set(a,{values:e,dtype:n,refCount:1}),a}makeTensorInfo(t,n,a){let s;if("string"===n&&null!=a&&a.length>0&&e.isString(a[0])){const r=a.map((t=>e.encodeString(t)));s=this.write(r,t,n)}else s=this.write(a,t,n);return{dataId:s,shape:t,dtype:n}}refCount(e){if(this.data.has(e)){return this.data.get(e).refCount}return 0}incRef(e){this.data.get(e).refCount++}decRef(e){if(this.data.has(e)){this.data.get(e).refCount--}}move(e,t,n,a,s){this.data.set(e,{values:t,dtype:a,refCount:s})}numDataIds(){return this.data.numDataIds()}async read(e){return this.readSync(e)}readSync(t){const{dtype:n,complexTensorInfos:a}=this.data.get(t);if("complex64"===n){const e=this.readSync(a.real.dataId),t=this.readSync(a.imag.dataId);return o.mergeRealAndImagArrays(e,t)}return e.convertBackendValuesAndArrayBuffer(this.data.get(t).values,n)}bufferSync(t){const n=this.readSync(t.dataId);if("string"===t.dtype)try{const a=n.map((t=>e.decodeString(t)));return i(t.shape,t.dtype,a)}catch(e){throw new Error("Failed to decode encoded string bytes into utf-8")}return i(t.shape,t.dtype,n)}makeOutput(e,t,n){return s().makeTensorFromTensorInfo(this.makeTensorInfo(t,n,e),this)}disposeData(e,t=!1){if(this.data.has(e)){if(this.data.get(e).refCount--,!t&&this.data.get(e).refCount>0)return!1;const{complexTensorInfos:n}=this.data.get(e);null!=n&&(this.disposeData(n.real.dataId,!0),this.disposeData(n.imag.dataId,!0)),this.data.delete(e)}return!0}disposeIntermediateTensorInfo(e){this.disposeData(e.dataId)}async time(t){const n=e.now();t();return{kernelMs:e.now()-n}}memory(){return{unreliable:!0,reasons:["The reported memory is an upper bound. Due to automatic garbage collection, the true allocated memory may be less."]}}where(e){An([e],"where");const t=this.readSync(e.dataId);return Dn(e.shape,t)}dispose(){}floatPrecision(){return 32}epsilon(){return super.epsilon()}}function zn(e){const t=new Float32Array(e.length);for(let n=0;n<e.length;++n)t[n]=Math.abs(e[n]);return t}En.nextDataId=0;const Wn={kernelName:l,backendName:"cpu",kernelFunc:t=>{const{x:n}=t.inputs,a=t.backend;An(n,"abs");let s=new Float32Array(e.sizeFromShape(n.shape));return s=zn(a.data.get(n.dataId).values),a.makeOutput(s,n.shape,n.dtype)}};function Rn(t){return(n,a,s,r,i)=>{const l=o.assertAndGetBroadcastShape(n,a),d=l.length,c=e.computeStrides(l),p=e.sizeFromShape(l),u=e.getTypedArrayFromDType(i,p),h=n.length,f=a.length,m=e.computeStrides(n),g=e.computeStrides(a),k=o.getBroadcastDims(n,l),I=o.getBroadcastDims(a,l);if(k.length+I.length===0)for(let e=0;e<u.length;++e)u[e]=t(s[e%s.length],r[e%r.length]);else for(let n=0;n<u.length;++n){const a=e.indexToLoc(n,d,c),o=a.slice(-h);k.forEach((e=>o[e]=0));const i=e.locToIndex(o,h,m),l=a.slice(-f);I.forEach((e=>l[e]=0));const p=e.locToIndex(l,f,g);u[n]=t(s[i],r[p])}return[u,l]}}function Pn(e){const{inputs:t,backend:n}=e,{real:a,imag:s}=t,r=n.data.get(a.dataId).values,o=n.data.get(s.dataId).values,i=n.makeTensorInfo(a.shape,"complex64");return n.data.get(i.dataId).complexTensorInfos={real:n.makeTensorInfo(a.shape,"float32",r),imag:n.makeTensorInfo(s.shape,"float32",o)},i}const Hn={kernelName:d,backendName:"cpu",kernelFunc:Pn};function Cn(t,n,a="float32"){if("complex64"===a){return Pn({inputs:{real:Cn(t,n,"float32"),imag:Cn(t,n,"float32")},backend:t})}const s=e.makeZerosTypedArray(e.sizeFromShape(n),a);return t.makeTensorInfo(n,a,s)}function $n(e){const{inputs:t,backend:n}=e,{x:a}=t;return n.incRef(a.dataId),{dataId:a.dataId,shape:a.shape,dtype:a.dtype}}const On={kernelName:c,backendName:"cpu",kernelFunc:$n};function Vn(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.real,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const _n={kernelName:p,backendName:"cpu",kernelFunc:Vn};function Gn(t,n,a,s){if("int32"===s){return[n,"int32",Int32Array.from(t)]}if("bool"===s){const s=e.toTypedArray([0],a),[r,o]=Rn(((e,t)=>e!==t?1:0))(n,[],t,s,"bool");return[o,"bool",r]}throw new Error(`Error in Cast: failed to cast ${a} to ${s}`)}function Bn(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{dtype:o}=s;if("complex64"===o){if("complex64"===r.dtype)return $n({inputs:{x:r},backend:a});const e=Cn(a,r.shape,r.dtype),t=Bn({inputs:{x:r},backend:a,attrs:{dtype:"float32"}}),n=Pn({inputs:{real:t,imag:e},backend:a});return a.disposeIntermediateTensorInfo(e),a.disposeIntermediateTensorInfo(t),n}if("complex64"===r.dtype){const e=Vn({inputs:{input:r},backend:a}),t=Bn({inputs:{x:e},backend:a,attrs:{dtype:o}});return a.disposeIntermediateTensorInfo(e),t}if(!e.hasEncodingLoss(r.dtype,o)){const e=$n({inputs:{x:r},backend:a});return{dataId:e.dataId,shape:e.shape,dtype:o}}const i=a.data.get(r.dataId).values,[l,d,c]=Gn(i,r.shape,r.dtype,o);return a.makeTensorInfo(l,d,c)}const Ln={kernelName:u,backendName:"cpu",kernelFunc:Bn};function qn(e,t,n,a){return null==n?({inputs:n,backend:s})=>{const{a:r,b:i}=n,l=s;An([r,i],e);const d=l.data.get(r.dataId).values,c=l.data.get(i.dataId).values,p="string"===r.dtype?o.fromUint8ToStringArray(d):d,u="string"===r.dtype?o.fromUint8ToStringArray(c):c,h=a||r.dtype,[f,m]=t(r.shape,i.shape,p,u,h);return l.makeTensorInfo(m,h,f)}:({inputs:e,backend:s})=>{const{a:r,b:o}=e,i=s;if("complex64"===r.dtype||"complex64"===o.dtype){const e=Bn({inputs:{x:r},backend:i,attrs:{dtype:"complex64"}}),t=i.data.get(e.dataId),a=t.complexTensorInfos.real,s=t.complexTensorInfos.imag,l=i.data.get(a.dataId).values,d=i.data.get(s.dataId).values,c=Bn({inputs:{x:o},backend:i,attrs:{dtype:"complex64"}}),p=i.data.get(c.dataId),u=p.complexTensorInfos.real,h=p.complexTensorInfos.imag,f=i.data.get(u.dataId).values,m=i.data.get(h.dataId).values,[g,k,I]=n(r.shape,o.shape,l,d,f,m),b=i.makeTensorInfo(I,"float32",g),y=i.makeTensorInfo(I,"float32",k),N=Pn({inputs:{real:b,imag:y},backend:i});return i.disposeIntermediateTensorInfo(e),i.disposeIntermediateTensorInfo(c),i.disposeIntermediateTensorInfo(b),i.disposeIntermediateTensorInfo(y),N}{const e=i.data.get(r.dataId).values,n=i.data.get(o.dataId).values,s=a||r.dtype,[l,d]=t(r.shape,o.shape,e,n,s);return i.makeTensorInfo(d,s,l)}}}function Un(t){return(n,a,s,r,i,l)=>{const d=o.assertAndGetBroadcastShape(n,a),c=e.sizeFromShape(d),p=d.length,u=e.computeStrides(d),h=e.getTypedArrayFromDType("float32",c),f=e.getTypedArrayFromDType("float32",c),m=o.getBroadcastDims(n,d),g=o.getBroadcastDims(a,d),k=o.mergeRealAndImagArrays(s,r),I=o.mergeRealAndImagArrays(i,l),b=n.length,y=e.computeStrides(n),N=a.length,T=e.computeStrides(a);if(m.length+g.length===0)for(let e=0;e<h.length;e++){const n=e%k.length,a=e%I.length,s=t(k[2*n],k[2*n+1],I[2*a],I[2*a+1]);h[e]=s.real,f[e]=s.imag}else for(let n=0;n<h.length;n++){const a=e.indexToLoc(n,p,u),s=a.slice(-b);m.forEach((e=>s[e]=0));const r=e.locToIndex(s,b,y),o=a.slice(-N);g.forEach((e=>o[e]=0));const i=e.locToIndex(o,N,T),l=t(k[2*r],k[2*r+1],I[2*i],I[2*i+1]);h[n]=l.real,f[n]=l.imag}return[h,f,d]}}const Zn=Rn(((e,t)=>e+t)),Kn=qn(h,Zn,Un(((e,t,n,a)=>({real:e+n,imag:t+a})))),jn={kernelName:h,backendName:"cpu",kernelFunc:Kn};function Yn(t,n,a,s,r){const o=e.sizeFromShape(s),i=e.makeZerosTypedArray(r,a);for(let e=0;e<t.length;e++){const a=t[e];if(a<0)throw new Error("Input x must be non-negative!");a>=r||(i[a]+=o>0?n[e]:1)}return i}function Jn(e,t,n,a=!1){const s=e.shape[0],r=e.shape[1],o=i([s,n],t.dtype);for(let i=0;i<s;i++)for(let s=0;s<r;s++){const r=e.get(i,s);if(r<0)throw new Error("Input x must be non-negative!");r>=n||(a?o.set(1,i,r):t.size>0?o.set(o.get(i,r)+t.get(i,s),i,r):o.set(o.get(i,r)+1,i,r))}return o}const Qn=Rn(((e,t)=>e&t)),Xn={kernelName:f,backendName:"cpu",kernelFunc:qn(f,Qn)};function ea(t){return(n,a,s)=>{const r=e.getArrayFromDType(a,n.length);for(let e=0;e<n.length;++e)r[e]=t(n[e],s);return r}}function ta(e,t,n){return na(e,ea(t),n)}function na(e,t,n){return({inputs:a,attrs:s,backend:r})=>{const{x:i}=a;An(i,e);const l=r,d=l.data.get(i.dataId).values;let c;if("string"===i.dtype){if(!Array.isArray(d))throw new Error("String tensor's value was not an instance of Array");c=o.fromUint8ToStringArray(d)}else c=d;const p=n||i.dtype,u=t(c,p,s);return l.makeTensorInfo(i.shape,p,u)}}const aa=ea((e=>Math.ceil(e))),sa={kernelName:m,backendName:"cpu",kernelFunc:na(m,aa)};function ra(t,n,a,s){const r=e.getArrayFromDType(a,e.sizeFromShape(n));if(s&&"string"!==a){let n=0;t.forEach((t=>{const a=e.sizeFromShape(t.shape);r.set(t.vals,n),n+=a}))}else{let e=0;t.forEach((t=>{const s="string"===a?o.fromUint8ToStringArray(t.vals):t.vals;let i=0;for(let a=0;a<t.shape[0];++a){const o=a*n[1]+e;for(let e=0;e<t.shape[1];++e)r[o+e]=s[i++]}e+=t.shape[1]}))}return r}const oa=Rn(((e,t)=>e===t?1:0)),ia=qn(g,oa,null,"bool"),la={kernelName:g,backendName:"cpu",kernelFunc:ia},da=ea((e=>Math.exp(e))),ca=na(k,da,"float32"),pa={kernelName:k,backendName:"cpu",kernelFunc:ca},ua=ea((e=>Math.expm1(e))),ha={kernelName:I,backendName:"cpu",kernelFunc:na(I,ua)},fa=ea((e=>Math.floor(e))),ma={kernelName:b,backendName:"cpu",kernelFunc:na(b,fa)},ga=Rn(((e,t)=>Math.floor(e/t))),ka={kernelName:y,backendName:"cpu",kernelFunc:qn(y,ga,null,"int32")};function Ia(e,t,n,a,s,r,o,l,d){const c=i([a,r],n);for(let n=0;n<a;n++){const a=[];let i=0;for(let t=0;t<s;t++){const r=e[n*s+t];i+=r*o[t],a.push(r)}if(i<0||i>=d/r)throw new Error(`Invalid indices: ${a} does not index into ${l}`);for(let e=0;e<r;e++)c.values[n*r+e]=t.get(...t.indexToLoc(i*r+e))}return c}function ba(e,t,n){const a=i(n,e.dtype);for(let n=0;n<a.size;++n){const s=a.indexToLoc(n).slice(),r=s[0],o=s[2],i=t.locToIndex([r,o]);s[2]=t.values[i];const l=e.locToIndex(s);0<=l&&l<e.values.length&&(a.values[n]=e.values[l])}return a}const ya=Rn(((e,t)=>e>t?1:0)),Na={kernelName:N,backendName:"cpu",kernelFunc:qn(N,ya,null,"bool")},Ta=Rn(((e,t)=>e>=t?1:0)),xa={kernelName:T,backendName:"cpu",kernelFunc:qn(T,Ta,null,"bool")},Sa=Rn(((e,t)=>e<t?1:0)),va={kernelName:x,backendName:"cpu",kernelFunc:qn(x,Sa,null,"bool")},Fa=Rn(((e,t)=>e<=t?1:0)),wa={kernelName:S,backendName:"cpu",kernelFunc:qn(S,Fa,null,"bool")};function Ma(t,n,a){const s=(n-t)/(a-1),r=e.makeZerosTypedArray(a,"float32");r[0]=t;for(let e=1;e<r.length;e++)r[e]=r[e-1]+s;return r}const Aa=ea((e=>Math.log(e))),Da={kernelName:v,backendName:"cpu",kernelFunc:na(v,Aa)};function Ea(t,n,a,s){const r=e.getTypedArrayFromDType(s,e.sizeFromShape(a));for(let e=0;e<r.length;++e){const a=e*n;let s=t[a];for(let e=0;e<n;++e){const n=t[a+e];(Number.isNaN(n)||n>s)&&(s=n)}r[e]=s}return r}const za=Rn(((e,t)=>Math.max(e,t))),Wa={kernelName:F,backendName:"cpu",kernelFunc:qn(F,za)},Ra=Rn(((e,t)=>Math.min(e,t))),Pa={kernelName:w,backendName:"cpu",kernelFunc:qn(w,Ra)},Ha=Rn(((e,t)=>e*t)),Ca=Un(((e,t,n,a)=>({real:e*n-t*a,imag:e*a+t*n}))),$a=qn(M,Ha,Ca),Oa={kernelName:M,backendName:"cpu",kernelFunc:$a};function Va(t,n,a){const s=e.createScalarValue(-1,a);return Ha([],n,s,t,a)}const _a={kernelName:A,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{x:a}=t;An(a,"neg");const s=n.data.get(a.dataId).values,[r,o]=Va(s,a.shape,a.dtype);return n.makeTensorInfo(o,a.dtype,r)}},Ga=Rn(((e,t)=>e!==t?1:0)),Ba={kernelName:D,backendName:"cpu",kernelFunc:qn(D,Ga,null,"bool")};function La(t,n,a,s,r){const o=n.length,i=e.sizeFromShape(n),l=e.computeStrides(n),d=e.computeStrides(r),c=e.getTypedArrayFromDType(a,e.sizeFromShape(r));for(let n=0;n<i;++n){const a=e.indexToLoc(n,o,l),r=new Array(a.length);for(let e=0;e<r.length;e++)r[e]=a[s[e]];c[e.locToIndex(r,o,d)]=t[n]}return c}function qa(e){const{inputs:t,attrs:n,backend:a}=e,{x:s}=t,{perm:r}=n;An(s,"transpose");const o=s.shape.length,i=new Array(o);for(let e=0;e<i.length;e++)i[e]=s.shape[r[e]];const l=La(a.data.get(s.dataId).values,s.shape,s.dtype,r,i);return{dataId:a.write(l,i,s.dtype),shape:i,dtype:s.dtype}}const Ua={kernelName:E,backendName:"cpu",kernelFunc:qa};function Za(t,n,a,s){const[r,i]=o.computeOutAndReduceShapes(t,s),l=z(n,"int32"),d=e.makeZerosTypedArray(e.sizeFromShape(r),l),c=e.sizeFromShape(i);for(let e=0;e<d.length;++e){const t=e*c;let n=1;for(let e=0;e<c;++e)n*=a[t+e];d[e]=n}return{outVals:d,outShape:r,outDtype:l}}const Ka={kernelName:W,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;An(r,"prod");const d=r.shape.length,c=e.parseAxisParam(i,r.shape),p=o.getAxesPermutation(c,d);let u=c,h=r;const f=[];null!=p&&(h=qa({inputs:{x:r},backend:a,attrs:{perm:p}}),f.push(h),u=o.getInnerMostAxes(u.length,d));const m=a.data.get(h.dataId).values,{outVals:g,outShape:k,outDtype:I}=Za(h.shape,h.dtype,m,u);let b=k;return l&&(b=o.expandShapeToKeepDim(k,c)),f.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(b,I,g)}};function ja(e,t,n,a){const s=[];let r=0;const o=t.length-1+n.length,i=new Array(o).fill(null).map((()=>[0]));!function(e,t){for(let n=0;n<e.length;++n){const a=e[n],s=n===e.length-1?t:e[n+1].length;if(0===a.length)throw new Error("Ragged splits may not be empty");if(a[0]<0)throw new Error("Ragged splits must be non-negative");if(a[a.length-1]>s)throw new Error("Ragged splits must not point past values");for(let e=1;e<a.length;++e)if(a[e-1]>a[e])throw new Error("Ragged splits must be sorted in ascending order")}}(n,a);let l=1;for(let e=0;e<t.length-1;++e){l*=t[e];const n=t[e+1];for(let t=1;t<l+1;++t)i[e].push(t*n)}for(let a=0;a<e.length;++a){let o=e[a],l=e[a]+1;for(let e=0;e<n.length;++e){const a=n[e],s=e+t.length-1;if(s>=0){const e=i[s],t=e[e.length-1]-a[o];for(let e=o;e<l;++e)i[s].push(a[e+1]+t)}o=a[o],l=a[l]}l!==o&&(s.push([o,l]),r+=l-o)}return{outSplits:i,valueSlices:s,numValues:r}}function Ya(e,t){const n=e.slice(0,t);for(;n.length<t;)n.push(1);for(let a=t;a<e.length;a++)n[t-1]*=e[a];return n}function Ja(t,n,a,s,r){const o=n.slice();o[0]=r;const i=e.getArrayFromDType(a,e.sizeFromShape(o)),l=t.length;return function(e,t,n,a,s,r){const o=Ya(t,2)[1],i=Ya(r,2)[1];let l=0;for(const t of n)for(let n=t[0];n<t[1];++n){for(let t=0;t<a;++t)s[l*i+t]=e[n*o+t];++l}}(t,n,s,0===l?0:l/n[0],i,o),[i,o]}function Qa(t,n,a,s,r,o,i,l){if(0===t.length)throw new Error("paramsNestedSplits must be non empty");if(0===n[0].length)throw new Error("Split tensors must not be scalars");if(function(t,n,a){t.forEach(((t,s)=>{if(t<0||t>=a){const r=e.indexToLoc(s,n.length,e.computeStrides(n)).join(",");throw new Error(`indices[${r}] = ${t} is not in [0, ${a})`)}}))}(o,i,n[0][0]-1),0===s.length)throw new Error("params.rank must be nonzero");const d=s[0],{outSplits:c,valueSlices:p,numValues:u}=ja(o,i,t,d),h=function(t){const n=[];for(let a=0;a<t.length;++a){const s=t[a].length,r=e.getArrayFromDType("int32",s);n.push(r),t[a].forEach(((e,t)=>r[t]=e))}return n}(c),f=Ja(a,s,r,p,u);return[h,f[0],f[1]]}function Xa(t,n,a,s,r,o,i){if(n.length>1)throw new Error("starts must be a scalar or vector");if(r.length>1)throw new Error("limits must be a scalar or vector");if(i.length>1)throw new Error("deltas must be a scalar or vector");const l=0===n.length,d=0===r.length,c=0===i.length,p=[];l||p.push(n[0]),d||p.push(r[0]),c||p.push(i[0]);for(let e=1;e<p.length;++e)if(p[e]!==p[e-1])throw new Error("starts, limits, and deltas must have the same shape");const u=0===p.length?1:p[0],h=e.getArrayFromDType("int32",u+1);h[0]=0;for(let e=0;e<u;++e){const n=l?t[0]:t[e],a=d?s[0]:s[e],r=c?o[0]:o[e];if(0===r)throw new Error("Requires delta != 0");let i;if(r>0&&a<n||r<0&&a>n)i=0;else if(i=Math.ceil(Math.abs((a-n)/r)),i>2147483647)throw new Error("Requires ((limit - start) / delta) <= 2147483647");h[e+1]=h[e]+i}const f=h[u],m=e.getArrayFromDType(a,f);let g=0;for(let e=0;e<u;++e){const n=h[e+1]-h[e];let a=l?t[0]:t[e];const s=c?o[0]:o[e];for(let e=0;e<n;++e)m[g++]=a,a+=s}return[h,m]}var es=o.RowPartitionType;class ts{constructor(e,t,n,a,s,r,i,l,d,c){this.shape=e,this.shapeShape=t,this.values=n,this.valuesShape=a,this.valuesDType=s,this.defaultValue=r,this.defaultValueShape=i,this.rowPartitionValues=l,this.rowPartitionValuesShapes=d,this.rowPartitionTypes=o.getRowPartitionTypesHelper(c),this.raggedRank=o.getRaggedRank(this.rowPartitionTypes)}getRowPartitionTypeByDimension(e){return this.rowPartitionTypes[0]===es.FIRST_DIM_SIZE?this.rowPartitionTypes[e+1]:this.rowPartitionTypes[e]}getRowPartitionTensor(e){return this.rowPartitionTypes[0]===es.FIRST_DIM_SIZE?this.rowPartitionValues[e+1]:this.rowPartitionValues[e]}getMaxWidth(e){const t=this.getRowPartitionTensor(e-1);switch(this.getRowPartitionTypeByDimension(e-1)){case es.VALUE_ROWIDS:return ts.getMaxWidthValueRowID(t);case es.ROW_SPLITS:return ts.getMaxWidthRowSplit(t);default:throw new Error(`Cannot handle partition type ${es[this.getRowPartitionTypeByDimension(e-1)]}`)}}static getMaxWidthRowSplit(e){const t=e.length;if(0===t||1===t)return 0;let n=0;for(let a=0;a<t-1;++a){const t=e[a+1]-e[a];t>n&&(n=t)}return n}static getMaxWidthValueRowID(e){const t=e.length;if(0===t)return 0;let n=0,a=e[0],s=0;for(let r=1;r<t;++r){const t=e[r];t!==a&&(a=t,s=Math.max(r-n,s),n=r)}return Math.max(t-n,s)}tensorShapeFromTensor(e,t,n=!0){if(0===t.length){if(-1===e[0])return[];throw new Error("The only valid scalar shape tensor is the fully unknown shape specified as -1.")}return as(e,n)}calculateOutputSize(e){const t=this.valuesShape,n=this.defaultValueShape;o.validateDefaultValueShape(n,t);const a=this.tensorShapeFromTensor(this.shape,this.shapeShape),s=o.combineRaggedTensorToTensorShapes(this.raggedRank,a,t);s[0]<0&&(s[0]=e);for(let e=1;e<=this.raggedRank;++e)s[e]<0&&(s[e]=this.getMaxWidth(e));return s}calculateFirstParentOutputIndex(t,n,a){const s=Math.min(t,a),r=[];let o=0;for(let e=0;e<s;++e,o+=n)r.push(o);for(let e=s;e<t;++e)r.push(-1);return e.assert(r.length===t,(()=>"Final length of result must be equal to firstDimension.")),r}calculateOutputIndexRowSplit(e,t,n,a){const s=e.length,r=[];for(let o=0;o<s-1;++o){const s=e[o+1]-e[o];let i=Math.min(a,s),l=t[o];-1===l&&(i=0);for(let e=0;e<i;++e)r.push(l),l+=n;for(let e=0;e<s-i;++e)r.push(-1)}if(s>0&&r.length!==e[s-1])throw new Error("Invalid row split size.");return r}calculateOutputIndexValueRowID(e,t,n,a){const s=e.length,r=[];if(0===s)return[];let o=0,i=e[0];if(i>=t.length)throw new Error(`Got currentValueRowId=${i}, which is not less than ${t.length}`);let l=t[i];r.push(l);for(let d=1;d<s;++d){const s=e[d];if(s===i)l>=0&&(++o,o<a?l+=n:l=-1);else{if(o=0,i=s,s>=t.length)throw new Error(`Got nextValueRowId=${s} which is not less than ${t.length}`);l=t[s]}r.push(l)}if(r.length!==e.length)throw new Error("Invalid row ids.");return r}calculateOutputIndex(e,t,n,a){const s=this.getRowPartitionTensor(e),r=this.getRowPartitionTypeByDimension(e);switch(r){case es.VALUE_ROWIDS:return this.calculateOutputIndexValueRowID(s,t,n,a);case es.ROW_SPLITS:if(s.length-1>t.length)throw new Error(`Row partition size is greater than output size: ${s.length-1} > ${t.length}`);return this.calculateOutputIndexRowSplit(s,t,n,a);default:throw new Error(`Unsupported partition type: ${es[r]}`)}}getFirstDimensionSize(){const e=this.rowPartitionValues[0];if(0===this.rowPartitionTypes.length)throw new Error("No row_partition_types given.");const t=this.rowPartitionTypes[0];switch(t){case es.FIRST_DIM_SIZE:return e[0];case es.VALUE_ROWIDS:throw new Error("Cannot handle VALUE_ROWIDS in first dimension.");case es.ROW_SPLITS:return this.rowPartitionValuesShapes[0][0]-1;default:throw new Error(`Cannot handle type ${es[t]}`)}}compute(){if(this.rowPartitionValues[0].length<=0)throw new Error("Invalid first partition input. Tensor requires at least one element.");const t=this.getFirstDimensionSize(),n=this.calculateOutputSize(t),a=new Array(this.raggedRank+1);a[a.length-1]=1;for(let e=a.length-2;e>=0;--e)a[e]=a[e+1]*n[e+1];const s=as(n,!1),r=e.getArrayFromDType(this.valuesDType,e.sizeFromShape(s));if(a[0]*n[0]>0){let e=this.calculateFirstParentOutputIndex(t,a[0],n[0]);for(let t=1;t<=this.raggedRank;++t){e=this.calculateOutputIndex(t-1,e,a[t],n[t])}this.setOutput(this.raggedRank,e,r,s)}return[s,r]}setOutput(t,n,a,s){if(0===a.length)return;const r=this.values,o=a;let i=s.slice();i=i.slice(t+1);const l=e.sizeFromShape(i),d=n.length;let c=this.defaultValue;if(c.length!==l&&1!==c.length){const e=this.defaultValueShape;R((()=>{const t=P(c,e),n=H(t,i);c=n.dataSync()}))}let p=0,u=0,h=0;for(let e=0;e<=d;++e){let t=e<d?n[e]:-1;if(t!==h){if(u<h){const e=r.subarray(p*l);ns(o.subarray(u*l),e,(h-u)*l)}if(e>=d){const e=a.length;t=Math.floor(e/l)}if(t>h)if(1===this.defaultValue.length)o.subarray(h*l,t*l).fill(this.defaultValue[0]),h=t;else for(;t>h;){ns(o.slice(h*l),c,l),++h}t<0?(p=e+1,u=h):(p=e,u=h,h=u+1)}else++h}}}function ns(e,t,n){for(let a=0;a<n;a++)e[a]=t[a]}function as(e,t){const n=[];for(let a of e){if(a<0){if(!t)throw new Error(`Dimension ${a} must be >= 0`);if(a<-1)throw new Error(`Dimension ${a} must be >= -1`);a=-1}n.push(a)}return n}function ss(e,t,n,a,s,r,o,i,l,d){return new ts(e,t,n,a,s,r,o,i,l,d).compute()}function rs(t,n,a,s){if(t===n||t<n&&a<0||n<t&&a>1)return e.makeZerosTypedArray(0,s);const r=Math.abs(Math.ceil((n-t)/a)),o=e.makeZerosTypedArray(r,s);n<t&&1===a&&(a=-1),o[0]=t;for(let e=1;e<o.length;e++)o[e]=o[e-1]+a;return o}const os=ea((e=>1/Math.sqrt(e))),is={kernelName:C,backendName:"cpu",kernelFunc:na(C,os)};function ls(e,t,n,a,s,r,o,l,d,c){const p=[a/s,s],u=e.values,h=t.values;if(0===a)return i(n,t.dtype);const f=d instanceof $?d:i(p,t.dtype);"string"==typeof d||"number"==typeof d?f.values.fill(d):"boolean"==typeof d&&f.values.fill(+d);for(let e=0;e<r;e++){const r=[];let i=0;for(let t=0;t<o;t++){const n=u[e*o+t];r.push(n),i+=n*l[t]}if(i<0||i>=a/s)throw new Error(`Invalid indices: ${r} does not index into ${n}`);for(let n=0;n<s;n++)c?f.values[i*s+n]+=h[e*s+n]:f.values[i*s+n]=0===t.rank?h[0]:h[e*s+n]}return f}const ds=ea((e=>1/(1+Math.exp(-e)))),cs=ta(O,(e=>1/(1+Math.exp(-e)))),ps={kernelName:O,backendName:"cpu",kernelFunc:cs};function us(t,n,a,s,r){const l=V.isSliceContinous(s,n,a),d=e.sizeFromShape(a),c=e.computeStrides(s);if(l){const e=V.computeFlatOffset(n,c);return"string"===r?t.slice(e,e+d):t.subarray(e,e+d)}const p="string"===r?o.fromUint8ToStringArray(t):t,u=i(s,r,p),h=i(a,r);for(let e=0;e<h.size;++e){const t=h.indexToLoc(e),a=t.map(((e,t)=>e+n[t]));h.set(u.get(...a),...t)}return"string"===r?o.fromStringArrayToUint8(h.values):h.values}function hs(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{begin:r,size:o}=a;An(s,"slice");const[i,l]=V.parseSliceParams(s,r,o);V.assertParamsValid(s,i,l);const d=us(n.data.get(s.dataId).values,i,l,s.shape,s.dtype);return n.makeTensorInfo(l,s.dtype,d)}const fs={kernelName:_,backendName:"cpu",kernelFunc:hs};function ms(t,n,a,s,r,i,l){const d=n[0],c=i[0],p=new Array(c),u=new Array(d),h=n[1];if(0===c){if(0!==d)throw new Error(o.getSparseFillEmptyRowsIndicesDenseShapeMismatch(d));return[e.getArrayFromDType(a,0),[0,h],e.getArrayFromDType(r,0),p,u]}let f=!0,m=0;const g=new Array(c).fill(0);for(let e=0;e<d;++e){const n=t[e*h];if(n<0)throw new Error(o.getSparseFillEmptyRowsNegativeIndexErrorMessage(e,n));if(n>=c)throw new Error(o.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(e,n,c));++g[n],f=f&&n>=m,m=n}let k=!0;for(let e=0;e<c;++e){const t=0===g[e];p[e]=t,k=k&&!t,g[e]=Math.max(g[e],1),e>0&&(g[e]+=g[e-1])}if(k&&f){const e=t,n=s;for(let e=0;e<d;++e)u[e]=e;return[e,[d,h],n,p,u]}{const n=g[c-1],o=e.getArrayFromDType(a,n*h),i=e.getArrayFromDType(r,n),f=new Array(c).fill(0);for(let e=0;e<d;++e){const n=t[e*h],a=f[n],r=(0===n?0:g[n-1])+a;f[n]++;for(let n=0;n<h;++n)o[r*h+n]=t[e*h+n];i[r]=s[e],u[e]=r}for(let e=0;e<c;++e){if(0===f[e]){const t=0===e?0:g[e-1];o[t*h+0]=e;for(let e=1;e<h;++e)o[t*h+e]=0;i[t]=l}}return[o,[n,h],i,p,u]}}function gs(t,n,a,s,r){const i=e.sizeFromShape(s),l=n[0],d=r.length,c=[];let p=1,u=-1;for(let e=0;e<d;++e){const t=r[e];if(-1===t){if(-1!==u)throw new Error(o.getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(u,e));u=e,c.push(1)}else{if(t<0)throw new Error(o.getSparseReshapeNegativeOutputDimErrorMessage(e,t));p*=t,c.push(t)}}if(-1!==u){if(p<=0)throw new Error(o.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());const e=Math.trunc(i/p);if(p*e!==i)throw new Error(o.getSparseReshapeInputOutputMultipleErrorMessage(s,c));c[u]=e}if(e.sizeFromShape(c)!==i)throw new Error(o.getSparseReshapeInputOutputMismatchErrorMessage(s,c));const h=s.length,f=[];if(h>0){f[h-1]=1;for(let e=h-2;e>=0;--e)f[e]=f[e+1]*s[e+1]}const m=[];if(d>0){m[d-1]=1;for(let e=d-2;e>=0;--e)m[e]=m[e+1]*c[e+1]}const g=e.getArrayFromDType(a,l*d);for(let e=0;e<l;++e){let n=0;for(let a=0;a<h;++a)n+=t[e*h+a]*f[a];for(let t=0;t<d;++t)g[e*d+t]=Math.trunc(n/m[t]),n%=m[t]}return[g,[l,d],c]}function ks(t,n,a,s,r,i=!1,l=0){const d=s.length,c=[n[0],t.length/n[0]],p=c[1],u=d>0?r[d-1]+1:0;if(u<0)throw new Error(o.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());const h=n.slice();h[0]=u;const f=h.reduce(((e,t)=>e*t),1),m=e.getArrayFromDType(a,f);if(0===d)return u>0&&m.fill(l),[m,h];if(u<=0)throw new Error(o.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());let g=0,k=1,I=0,b=r[g];for(;;){let e=0;if(k<d){if(e=r[k],b===e){++k;continue}if(b>=e)throw new Error(o.getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage())}if(b<0||b>=u)throw new Error(o.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(b,u));b>I&&m.fill(l,I*p,b*p);for(let e=g;e<k;++e){const n=s[e];if(n<0||n>=c[0])throw new Error(o.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(e,s[e],c[0]));for(let e=0;e<p;e++)m[b*p+e]+=t[n*p+e]}if(i)for(let e=0;e<p;e++)m[b*p+e]/=k-g;if(g=k,++k,I=b+1,b=e,k>d)break}return I<u&&m.fill(l,I*p,u*p),[m,h]}const Is=ea((e=>Math.sqrt(e))),bs={kernelName:G,backendName:"cpu",kernelFunc:ta(G,(e=>Math.sqrt(e)))},ys=Rn(((e,t)=>{const n=e-t;return n*n})),Ns={kernelName:B,backendName:"cpu",kernelFunc:qn(B,ys)},Ts=ea(((e,t)=>{const{pattern:n,replaceGlobal:a,rewrite:s}=t;return e.replace(new RegExp(n,a?"g":""),s)})),xs={kernelName:L,backendName:"cpu",kernelFunc:na(L,Ts)};function Ss(e,t,n,a){const s=i(e,t.dtype);for(let e=0;e<s.size;e++){const r=s.indexToLoc(e),o=new Array(r.length);for(let e=0;e<o.length;e++)o[e]=r[e]*n[e]+a[e];s.set(t.get(...o),...r)}return s}class vs{constructor(t,n,a,s,r,o){this.separator=e.encodeString(t),this.nGramWidths=n,this.leftPad=e.encodeString(a),this.rightPad=e.encodeString(s),this.padWidth=r,this.preserveShort=o}getPadWidth(e){return Math.min(this.padWidth<0?e-1:this.padWidth,e-1)}getNumNGrams(e,t){const n=this.getPadWidth(t);return Math.max(0,e+2*n-t+1)}createNGrams(e,t,n,a,s,r){for(let o=0;o<s;++o){const i=this.getPadWidth(r),l=Math.max(0,i-o),d=Math.max(0,i-(s-(o+1))),c=r-(l+d),p=t+(l>0?0:o-i);let u=0;u+=l*this.leftPad.length;for(let t=0;t<c;++t)u+=e[p+t].length;u+=d*this.rightPad.length;u+=(l+d+c-1)*this.separator.length,n[a+o]=new Uint8Array(u);const h=n[a+o];let f=0;const m=e=>e.forEach((e=>h[f++]=e));for(let e=0;e<l;++e)m(this.leftPad),m(this.separator);for(let t=0;t<c-1;++t)m(e[p+t]),m(this.separator);if(c>0){m(e[p+c-1]);for(let e=0;e<d;++e)m(this.separator),m(this.rightPad)}else{for(let e=0;e<d-1;++e)m(this.rightPad),m(this.separator);m(this.rightPad)}}}compute(t,n){const a=t.length,s=n.length;if(s>0){let e=n[0];if(0!==e)throw new Error(`First split value must be 0, got ${e}`);for(let t=1;t<s;++t){let s=n[t]>=e;if(s=s&&n[t]<=a,!s)throw new Error(`Invalid split value ${n[t]}, must be in [${e}, ${a}]`);e=n[t]}if(e!==a)throw new Error(`Last split value must be data size. Expected ${a}, got ${e}`)}const r=s-1,o=e.getArrayFromDType("int32",s);if(0===a||0===s){const e=new Array(a);for(let e=0;e<=r;++e)o[e]=0;return[e,o]}o[0]=0;for(let e=1;e<=r;++e){const t=n[e]-n[e-1];let a=0;this.nGramWidths.forEach((e=>{a+=this.getNumNGrams(t,e)})),this.preserveShort&&t>0&&0===a&&(a=1),o[e]=o[e-1]+a}const i=new Array(o[r]);for(let e=0;e<r;++e){const a=n[e];let s=o[e];if(this.nGramWidths.forEach((r=>{const o=n[e+1]-n[e],l=this.getNumNGrams(o,r);this.createNGrams(t,a,i,s,l,r),s+=l})),this.preserveShort&&s===o[e]){const r=n[e+1]-n[e];if(0===r)continue;const o=r+2*this.padWidth,l=1;this.createNGrams(t,a,i,s,l,o)}}return[i,o]}}function Fs(e,t,n,a,s,r,o,i){return new vs(n,a,s,r,o,i).compute(e,t)}function ws(e,t,n,a){if(!e.length)return;if(0===t.length){for(let t=0;t<e.length;++t)a.push(e.subarray(t,t+1));return}if(1===t.length){const s=t[0];let r=e.indexOf(s);for(;-1!==r;){const t=e.subarray(0,r);n&&0===t.length||a.push(t),r=(e=e.subarray(r+1)).indexOf(s)}return void(n&&0===e.length||a.push(e))}let s=0;for(let r=0;r<e.length+1;r++)if(r===e.length||-1!==t.indexOf(e[r])){const t=e.subarray(s,r);n&&0===t.length||a.push(t),s=r+1}}function Ms(t,n,a){const s=t.length,r=[];let o=0,i=0;const l=new Array(s);for(let e=0;e<s;++e){const s=r.length;ws(t[e],n,a,r);const d=r.length-s;l[e]=d,o+=d,i=Math.max(i,d)}const d=e.getArrayFromDType("int32",2*o),c=new Array(o),p=[s,i];let u=0;for(let e=0;e<s;++e)for(let t=0;t<l[e];++t)d[2*u]=e,d[2*u+1]=t,c[u]=r[u],++u;return[d,c,p]}function As(t,n){const a=e.getArrayFromDType("int32",t.length);for(let s=0;s<t.length;++s)a[s]=e.fingerPrint64(t[s]).modulo(n).getLowBitsUnsigned();return a}const Ds=Rn(((e,t)=>e-t)),Es=qn(q,Ds,Un(((e,t,n,a)=>({real:e-n,imag:t-a})))),zs={kernelName:q,backendName:"cpu",kernelFunc:Es};function Ws(e,t){const n=new Array(e.rank);for(let a=0;a<n.length;a++)n[a]=e.shape[a]*t[a];const a=i(n,e.dtype);for(let t=0;t<a.values.length;++t){const n=a.indexToLoc(t),s=new Array(e.rank);for(let t=0;t<s.length;t++)s[t]=n[t]%e.shape[t];const r=e.locToIndex(s);a.values[t]=e.values[r]}return a}const Rs=(e,t)=>{const n=t.value-e.value;return 0===n?e.index-t.index:n};function Ps(t,n,a=0,s=t.length-1){for(;s>a;){if(s-a>600){const e=s-a+1,r=n-a+1,o=Math.log(e),i=.5*Math.exp(2*o/3),l=.5*Math.sqrt(o*i*(e-i)/e)*Math.sign(r-e/2);Ps(t,n,Math.max(a,Math.floor(n-r*i/e+l)),Math.min(s,Math.floor(n+(e-r)*i/e+l)))}const r=t[n];let o=a,i=s;for(e.swap(t,a,n),Rs(t[s],r)>0&&e.swap(t,a,s);o<i;){for(e.swap(t,o,i),o++,i--;Rs(t[o],r)<0;)o+=1;for(;Rs(t[i],r)>0;)i-=1}0===Rs(t[a],r)?e.swap(t,a,i):(i+=1,e.swap(t,i,s)),i<=n&&(a=i+1),n<=i&&(s=i-1)}}function Hs(t,n,a,s,r){const o=n[n.length-1],[l,d]=[t.length/o,o],c=e.getTypedArrayFromDType(a,l*s),p=e.getTypedArrayFromDType("int32",l*s);for(let e=0;e<l;e++){const n=e*d,a=t.subarray(n,n+d);let o=new Array(a.length);a.forEach(((e,t)=>o[t]={value:e,index:t})),s<o.length&&(Ps(o,s),o=o.slice(0,s)),r&&o.sort(Rs);const i=e*s,l=c.subarray(i,i+s),u=p.subarray(i,i+s);for(let e=0;e<s;e++)l[e]=o[e].value,u[e]=o[e].index}const u=n.slice();return u[u.length-1]=s,[i(u,a,c),i(u,"int32",p)]}function Cs(t,n,a,s){const r=e.parseAxisParam(n,a)[0],o=[1,a[0],1];for(let e=0;e<r;e++)o[0]*=a[e];o[1]=a[r];for(let e=r+1;e<a.length;e++)o[2]*=a[e];const i=new Map,l=new Int32Array(a[r]),d=new $(o,s,t),c=[],p=1===o[0]&&1===o[2];for(let e=0;e<a[r];e++){let n;if(p)n=t[e].toString();else{const t=[];for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)t.push(d.get(n,e,a));n=t.join(",")}const a=i.get(n);if(null!=a)l[e]=a;else{const t=i.size;i.set(n,t),l[e]=t,c.push(e)}}const u=o.slice();u[1]=i.size;const h=new $(u,s);c.forEach(((e,t)=>{for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)h.set(d.get(n,e,a),n,t,a)}));const f=a.slice();return f[r]=u[1],{outputValues:h.values,outputShape:f,indices:l}}var $s={__proto__:null,addImpl:Zn,bincountImpl:Yn,bincountReduceImpl:Jn,bitwiseAndImpl:Qn,castImpl:Gn,ceilImpl:aa,concatImpl:ra,equalImpl:oa,expImpl:da,expm1Impl:ua,floorDivImpl:ga,floorImpl:fa,gatherNdImpl:Ia,gatherV2Impl:ba,greaterEqualImpl:Ta,greaterImpl:ya,lessEqualImpl:Fa,lessImpl:Sa,linSpaceImpl:Ma,logImpl:Aa,maxImpl:Ea,maximumImpl:za,minimumImpl:Ra,multiplyImpl:Ha,negImpl:Va,notEqualImpl:Ga,prodImpl:Za,raggedGatherImpl:Qa,raggedRangeImpl:Xa,raggedTensorToTensorImpl:ss,rangeImpl:rs,rsqrtImpl:os,scatterImpl:ls,sigmoidImpl:ds,simpleAbsImpl:zn,sliceImpl:us,sparseFillEmptyRowsImpl:ms,sparseReshapeImpl:gs,sparseSegmentReductionImpl:ks,sqrtImpl:Is,squaredDifferenceImpl:ys,staticRegexReplaceImpl:Ts,stridedSliceImpl:Ss,stringNGramsImpl:Fs,stringSplitImpl:Ms,stringToHashBucketFastImpl:As,subImpl:Ds,tileImpl:Ws,topKImpl:Hs,transposeImpl:La,uniqueImpl:Cs};const Os="4.22.0";U("cpu",(()=>new En),1);const Vs=ta(Z,(e=>e>=0?e:Math.exp(e)-1)),_s={kernelName:Z,backendName:"cpu",kernelFunc:Vs};function Gs(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{alpha:o}=s;An([r],"leakyRelu");const i=e.sizeFromShape(r.shape),l=a.data.get(r.dataId).values,d=e.getTypedArrayFromDType("float32",i);for(let e=0;e<l.length;e++)d[e]=l[e]<0?o*l[e]:l[e];return a.makeTensorInfo(r.shape,"float32",d)}const Bs={kernelName:K,backendName:"cpu",kernelFunc:Gs},Ls=Rn(((e,t)=>e<0?t*e:e));function qs(e){const{inputs:t,backend:n}=e,{x:a,alpha:s}=t;An([a,s],"prelu");const r=n.data.get(a.dataId).values,o=n.data.get(s.dataId).values,[i,l]=Ls(a.shape,s.shape,r,o,"float32");return n.makeTensorInfo(l,"float32",i)}const Us={kernelName:j,backendName:"cpu",kernelFunc:qs},Zs=ta(Y,(e=>Math.max(0,e))),Ks={kernelName:Y,backendName:"cpu",kernelFunc:Zs},js=ta(J,(e=>Math.min(Math.max(0,e),6))),Ys={kernelName:J,backendName:"cpu",kernelFunc:js};function Js(e,t,n,a,s){if("linear"===n)return $n({inputs:{x:t},backend:e});if("relu"===n)return Zs({inputs:{x:t},backend:e});if("elu"===n)return Vs({inputs:{x:t},backend:e});if("relu6"===n)return js({inputs:{x:t},backend:e});if("prelu"===n)return qs({inputs:{x:t,alpha:a},backend:e});if("leakyrelu"===n)return Gs({inputs:{x:t},backend:e,attrs:{alpha:s}});if("sigmoid"===n)return cs({inputs:{x:t},backend:e});throw new Error(`Activation ${n} has not been implemented for the CPU backend.`)}function Qs(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{shape:o}=s,i=e.sizeFromShape(r.shape),l=e.inferFromImplicitShape(o,i),d=e.sizeFromShape(l);e.assert(i===d,(()=>`The new shape (${l}) has ${d} elements and the old shape (${r.shape}) has ${i} elements. The new shape and old shape must have the same number of elements.`)),a.incRef(r.dataId);const c=a.data.get(r.dataId);if(null!=c.complexTensorInfos){const e=c.complexTensorInfos.real,t=c.complexTensorInfos.imag;e.shape=l,t.shape=l}return{dataId:r.dataId,shape:l,dtype:r.dtype}}const Xs={kernelName:Q,backendName:"cpu",kernelFunc:Qs};function er(t){const{inputs:n,backend:a,attrs:s}=t,{a:r,b:o}=n,{transposeA:l,transposeB:d}=s;An([r,o],"matMul");const c=r.shape.length,p=o.shape.length,u=l?r.shape[c-2]:r.shape[c-1],h=d?o.shape[p-1]:o.shape[p-2],f=l?r.shape[c-1]:r.shape[c-2],m=d?o.shape[p-2]:o.shape[p-1],g=r.shape.slice(0,-2),k=o.shape.slice(0,-2),I=e.sizeFromShape(g),b=e.sizeFromShape(k),y=ee.assertAndGetBroadcastShape(r.shape.slice(0,-2),o.shape.slice(0,-2)).concat([f,m]);e.assert(u===h,(()=>`Error in matMul: inner shapes (${u}) and (${h}) of Tensors with shapes ${r.shape} and ${o.shape} and transposeA=${l} and transposeB=${d} must match.`));const N=d?[b,m,h]:[b,h,m],T=Qs({inputs:{x:r},backend:a,attrs:{shape:l?[I,u,f]:[I,f,u]}}),x=Qs({inputs:{x:o},backend:a,attrs:{shape:N}}),S=l?T.shape[1]:T.shape[2],v=l?T.shape[2]:T.shape[1],F=d?x.shape[1]:x.shape[2],w=Math.max(I,b),M=a.data.get(T.dataId).values,A=a.data.get(x.dataId).values,D=e.computeStrides(T.shape),E=e.computeStrides(x.shape),[z,W,R]=l?[D[0],1,D[1]]:[D[0],D[1],1],[P,H,C]=d?[1,E[1],E[0]]:[E[1],1,E[0]],$=v*F,O=i([w,v,F],T.dtype),V=O.values,_=a.blockSize;for(let e=0;e<w;e++){const t=e%I,n=e%b;for(let a=0;a<v;a+=_){const s=Math.min(a+_,v);for(let r=0;r<F;r+=_){const o=Math.min(r+_,F);for(let i=0;i<S;i+=_){const l=Math.min(i+_,S);for(let d=a;d<s;d++)for(let a=r;a<o;a++){let s=0;for(let e=i;e<l;e++){s+=M[t*z+d*W+e*R]*A[e*P+a*H+n*C]}V[e*$+(d*F+a)]+=s}}}}}return a.disposeIntermediateTensorInfo(T),a.disposeIntermediateTensorInfo(x),a.makeTensorInfo(y,O.dtype,O.values)}const tr={kernelName:X,backendName:"cpu",kernelFunc:er};const nr={kernelName:te,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{a:s,b:r,bias:o,preluActivationWeights:i}=t,{transposeA:l,transposeB:d,activation:c,leakyreluAlpha:p}=a;let u,h,f;const m=[];u=er({inputs:{a:s,b:r},attrs:{transposeA:l,transposeB:d},backend:n}),o&&(h=Kn({inputs:{a:u,b:o},backend:n}),m.push(u),u=h),c&&(f=Js(n,u,c,i,p),m.push(u),u=f);for(const e of m)n.disposeIntermediateTensorInfo(e);return u}},ar={kernelName:ne,backendName:"cpu",kernelFunc:ta(ne,(e=>Math.acos(e)))},sr={kernelName:ae,backendName:"cpu",kernelFunc:ta(ae,(e=>Math.acosh(e)))};const rr={kernelName:se,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,a=t;An(t,"addN");const s=a.map((e=>n.data.get(e.dataId).values)),r=i(a[0].shape,a[0].dtype),o=r.values;for(let e=0;e<a.length;e++){const t=s[e];for(let e=0;e<o.length;e++)o[e]+=t[e]}return n.makeTensorInfo(r.shape,r.dtype,r.values)}};const or={kernelName:re,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;An(r,"all");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=qa({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("all",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),g=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),k=a.data.get(u.dataId).values;for(let e=0;e<g.length;++e){const t=e*m;let n=k[t];for(let e=0;e<m;++e){const a=k[t+e];n=n&&a}g[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,g);if(l){const e=Qs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const ir={kernelName:oe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;An(r,"any");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=qa({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("any",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),g=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),k=a.data.get(u.dataId).values;for(let e=0;e<g.length;++e){const t=e*m;let n=k[t];for(let e=0;e<m;++e){const a=k[t+e];n=n||a}g[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,g);if(l){const e=Qs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const lr={kernelName:ie,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i}=s;An(r,"argMax");let l=e.parseAxisParam(i,r.shape);const d=o.getAxesPermutation(l,r.shape.length);let c=r;const p=[];null!=d&&(c=qa({inputs:{x:r},backend:a,attrs:{perm:d}}),p.push(c),l=o.getInnerMostAxes(l.length,c.shape.length)),l=[l[0]],o.assertAxesAreInnerMostDims("argMax",l,c.shape.length);const[u,h]=o.computeOutAndReduceShapes(c.shape,l),f=e.sizeFromShape(u),m=e.makeZerosTypedArray(f,"int32"),g=e.sizeFromShape(h),k=a.data.get(c.dataId).values;for(let e=0;e<m.length;++e){const t=e*g;let n=k[t],a=0;for(let e=0;e<g;++e){const s=k[t+e];s>n&&(n=s,a=e)}m[e]=a}return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(u,"int32",m)}};const dr={kernelName:le,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i}=s;An(r,"argMin");let l=e.parseAxisParam(i,r.shape);const d=o.getAxesPermutation(l,r.shape.length);let c=r;const p=[];null!=d&&(c=qa({inputs:{x:r},backend:a,attrs:{perm:d}}),p.push(c),l=o.getInnerMostAxes(l.length,c.shape.length)),l=[l[0]],o.assertAxesAreInnerMostDims("argMin",l,c.shape.length);const[u,h]=o.computeOutAndReduceShapes(c.shape,l),f=e.sizeFromShape(u),m=e.makeZerosTypedArray(f,"int32"),g=e.sizeFromShape(h),k=a.data.get(c.dataId).values;for(let e=0;e<m.length;++e){const t=e*g;let n=k[t],a=0;for(let e=0;e<g;++e){const s=k[t+e];s<n&&(n=s,a=e)}m[e]=a}return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(u,"int32",m)}},cr={kernelName:de,backendName:"cpu",kernelFunc:ta(de,(e=>Math.asin(e)))},pr={kernelName:ce,backendName:"cpu",kernelFunc:ta(ce,(e=>Math.asinh(e)))},ur={kernelName:pe,backendName:"cpu",kernelFunc:ta(pe,(e=>Math.atan(e)))},hr={kernelName:ue,backendName:"cpu",kernelFunc:qn(ue,Rn(((e,t)=>Math.atan2(e,t))))},fr={kernelName:he,backendName:"cpu",kernelFunc:ta(he,(e=>Math.atanh(e)))};function mr(e,t,n,a,s,r){const o=s.strideHeight,l=s.strideWidth,d=s.dilationHeight,c=s.dilationWidth,p=s.effectiveFilterHeight,u=s.effectiveFilterWidth,h=s.padInfo.top,f=s.padInfo.left,m="max"===r?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,g=i(s.outShape,n),k=g.values,I=s.outShape[1]*s.outShape[2]*s.outShape[3],b=s.outShape[2]*s.outShape[3],y=s.outShape[3];for(let t=0;t<s.batchSize;++t){const n=t*I,i=t*a[0];for(let t=0;t<s.inChannels;++t)for(let g=0;g<s.outHeight;++g){const I=g*o-h,N=Math.max(0,I),T=Math.min(s.inHeight,p+I),x=n+g*b;for(let n=0;n<s.outWidth;++n){const o=n*l-f,p=Math.max(0,o),h=Math.min(s.inWidth,u+o);let g=m,I=0,b=0;for(let n=N;n<T;n+=d){const s=i+n*a[1];for(let n=p;n<h;n+=c){const o=e[s+n*a[2]+t];"max"===r&&o>g?g=o:"avg"===r&&(I+=o,b++)}if(isNaN(g))break}k[x+n*y+t]="avg"===r?I/b:g}}}return g}function gr(e,t,n,a,s=!1,r=!1){const o=i(a.outShape,"int32"),l=a.strideHeight,d=a.strideWidth,c=a.dilationHeight,p=a.dilationWidth,u=a.effectiveFilterHeight,h=a.effectiveFilterWidth,f=a.padInfo.top,m=a.padInfo.left,g=i(t,n,e);for(let e=0;e<a.batchSize;++e)for(let t=0;t<a.inChannels;++t)for(let n=0;n<a.outHeight;++n){const i=n*l-f;let k=i;for(;k<0;)k+=c;const I=Math.min(a.inHeight,u+i);for(let l=0;l<a.outWidth;++l){const u=l*d-m;let f=u;for(;f<0;)f+=p;const b=Math.min(a.inWidth,h+u);let y=Number.NEGATIVE_INFINITY,N=-1;for(let n=k;n<I;n+=c){const o=n-i;for(let i=f;i<b;i+=p){const l=i-u,d=g.get(e,n,i,t);d>y&&(y=d,N=s?r?((e*a.inHeight+n)*a.inWidth+i)*a.inChannels+t:(n*a.inWidth+i)*a.inChannels+t:o*h+l)}}o.set(N,e,n,l,t)}}return o}function kr(e,t,n,a,s,r){const o=s.strideDepth,l=s.strideHeight,d=s.strideWidth,c=s.dilationDepth,p=s.dilationHeight,u=s.dilationWidth,h=s.effectiveFilterDepth,f=s.effectiveFilterHeight,m=s.effectiveFilterWidth,g=s.padInfo.front,k=s.padInfo.top,I=s.padInfo.left,b="max"===r?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,y=i(s.outShape,n),N=y.values,T=s.outShape[1]*s.outShape[2]*s.outShape[3]*s.outShape[4],x=s.outShape[2]*s.outShape[3]*s.outShape[4],S=s.outShape[3]*s.outShape[4],v=s.outShape[4];for(let t=0;t<s.batchSize;++t){const n=t*T,i=t*a[0];for(let t=0;t<s.inChannels;++t)for(let y=0;y<s.outDepth;++y){const T=y*o-g;let F=T;for(;F<0;)F+=c;const w=Math.min(s.inDepth,h+T),M=n+y*x;for(let n=0;n<s.outHeight;++n){const o=n*l-k;let h=o;for(;h<0;)h+=p;const g=Math.min(s.inHeight,f+o),y=M+n*S;for(let n=0;n<s.outWidth;++n){const o=n*d-I;let l=o;for(;l<0;)l+=u;const f=Math.min(s.inWidth,m+o),k=y+n*v;let T=b,x=0,S=0;for(let n=F;n<w;n+=c){const s=i+n*a[1];for(let n=h;n<g;n+=p){const o=s+n*a[2];for(let n=l;n<f;n+=u){const s=e[o+n*a[3]+t];if("max"===r&&s>T?T=s:"avg"===r&&(x+=s,S++),isNaN(T))break}if(isNaN(T))break}if(isNaN(T))break}N[k+t]="avg"===r?x/Math.max(S,1):T}}}}return y}const Ir={kernelName:fe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n;An(r,"avgPool");const{filterSize:i,strides:l,pad:d,dimRoundingMode:c}=s;e.assert(o.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in avgPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const p=o.computePool2DInfo(r.shape,i,l,1,d,c);let u;if(1===p.filterWidth&&1===p.filterHeight&&e.arraysEqual(p.inShape,p.outShape))u=$n({inputs:{x:r},backend:a});else{const t=a.data.get(r.dataId).values,n=e.computeStrides(r.shape),s=mr(t,r.shape,r.dtype,n,p,"avg");u=a.makeTensorInfo(p.outShape,r.dtype,s.values)}return u}};const br={kernelName:me,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{filterSize:i,strides:l,pad:d,dimRoundingMode:c,dataFormat:p}=s;An(r,"avgPool3d");const u=o.computePool3DInfo(r.shape,i,l,1,d,c,p),h=kr(a.data.get(r.dataId).values,r.shape,r.dtype,e.computeStrides(r.shape),u,"avg");return a.makeTensorInfo(h.shape,"float32",h.values)}};const yr={kernelName:ge,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,{filterSize:l,strides:d,pad:c,dimRoundingMode:p}=a;An([s,r],"avgPool3DGrad");const u=o.computePool3DInfo(r.shape,l,d,1,c,p),h=u.strideDepth,f=u.strideHeight,m=u.strideWidth,g=u.filterDepth,k=u.filterHeight,I=u.filterWidth,b=u.dilationDepth,y=u.dilationHeight,N=u.dilationWidth,T=u.effectiveFilterDepth,x=u.effectiveFilterHeight,S=u.effectiveFilterWidth,v=T-1-u.padInfo.front,F=S-1-u.padInfo.left,w=x-1-u.padInfo.top,M=i(r.shape,"float32"),A=1/(g*k*I),D=n.bufferSync(s);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inDepth;++n)for(let a=0;a<u.inHeight;++a)for(let s=0;s<u.inWidth;++s){const r=n-v,o=a-w,i=s-F;let l=0;for(let n=0;n<T;n+=b){const a=(r+n)/h;if(!(a<0||a>=u.outDepth||Math.floor(a)!==a))for(let n=0;n<x;n+=y){const s=(o+n)/f;if(!(s<0||s>=u.outHeight||Math.floor(s)!==s))for(let n=0;n<S;n+=N){const r=(i+n)/m;if(r<0||r>=u.outWidth||Math.floor(r)!==r)continue;l+=D.get(e,a,s,r,t)}}}M.set(l*A,e,n,a,s,t)}return n.makeTensorInfo(M.shape,M.dtype,M.values)}};const Nr={kernelName:ke,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,l=r;An([s,r],"avgPoolGrad");const{filterSize:d,strides:c,pad:p}=a,u=o.computePool2DInfo(l.shape,d,c,1,p),h=u.strideHeight,f=u.strideWidth,m=u.filterHeight,g=u.filterWidth,k=u.dilationHeight,I=u.dilationWidth,b=u.effectiveFilterHeight,y=u.effectiveFilterWidth,N=y-1-u.padInfo.left,T=b-1-u.padInfo.top,x=i(l.shape,"float32"),S=1/(m*g),v=n.data.get(s.dataId).values,F=i(s.shape,"float32",v);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inHeight;++n)for(let a=0;a<u.inWidth;++a){const s=n-T,r=a-N;let o=0;for(let n=0;n<b;n+=k){const a=(s+n)/h;if(!(a<0||a>=u.outHeight||Math.floor(a)!==a))for(let n=0;n<y;n+=I){const s=(r+n)/f;if(s<0||s>=u.outWidth||Math.floor(s)!==s)continue;o+=F.get(e,a,s,t)}}x.set(o*S,e,n,a,t)}return n.makeTensorInfo(x.shape,x.dtype,x.values)}};const Tr={kernelName:Ie,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,scale:o,offset:i,mean:l,variance:d}=n;e.assert(l.shape.length===d.shape.length,(()=>"Batch normalization gradient requires mean and variance to have equal ranks.")),e.assert(null==i||l.shape.length===i.shape.length,(()=>"Batch normalization gradient requires mean and offset to have equal ranks.")),e.assert(null==o||l.shape.length===o.shape.length,(()=>"Batch normalization gradient requires mean and scale to have equal ranks.")),An([r,l,d,o,i],"batchNorm");let{varianceEpsilon:c}=s;null==c&&(c=.001);const p=a.data.get(r.dataId).values,u=a.data.get(l.dataId).values,h=a.data.get(d.dataId).values,f=o?a.data.get(o.dataId).values:new Float32Array([1]),m=i?a.data.get(i.dataId).values:new Float32Array([0]),g=new Float32Array(p.length),k=m.length,I=f.length,b=h.length,y=u.length;let N=0,T=0,x=0,S=0;for(let e=0;e<p.length;++e)g[e]=m[N++]+(p[e]-u[T++])*f[x++]/Math.sqrt(h[S++]+c),N>=k&&(N=0),T>=y&&(T=0),x>=I&&(x=0),S>=b&&(S=0);return a.makeTensorInfo(r.shape,r.dtype,g)}};const xr={kernelName:be,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{blockShape:r,crops:i}=a;An([s],"batchToSpaceND");const l=r.reduce(((e,t)=>e*t)),d=o.getReshaped(s.shape,r,l),c=o.getPermuted(d.length,r.length),p=o.getReshapedPermuted(s.shape,r,l),u=o.getSliceBeginCoords(i,r.length),h=o.getSliceSize(p,i,r.length),f=Qs({inputs:{x:s},backend:n,attrs:{shape:d}}),m=qa({inputs:{x:f},backend:n,attrs:{perm:c}}),g=Qs({inputs:{x:m},backend:n,attrs:{shape:p}}),k=hs({inputs:{x:g},backend:n,attrs:{begin:u,size:h}});return n.disposeIntermediateTensorInfo(f),n.disposeIntermediateTensorInfo(m),n.disposeIntermediateTensorInfo(g),k}};const Sr={kernelName:ye,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o}=a,i=Yn(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,i)}};const vr={kernelName:Ne,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{s0:a,s1:s}=t,r=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=o.assertAndGetBroadcastShape(Array.from(r),Array.from(i));return n.makeTensorInfo([l.length],"int32",Int32Array.from(l))}},Fr={kernelName:Te,backendName:"cpu",kernelFunc:ta(Te,((e,t)=>{const n=t;return e>n.clipValueMax?n.clipValueMax:e<n.clipValueMin?n.clipValueMin:e}))},wr={kernelName:xe,backendName:"cpu",kernelFunc:t=>{const{x:n}=t.inputs,a=t.backend,s=new Float32Array(e.sizeFromShape(n.shape)),r=a.data.get(n.dataId),o=r.complexTensorInfos.real,i=r.complexTensorInfos.imag,l=a.data.get(o.dataId).values,d=a.data.get(i.dataId).values;for(let e=0;e<l.length;e++){const t=l[e],n=d[e];s[e]=Math.hypot(t,n)}return a.makeOutput(s,n.shape,"float32")}};function Mr(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.imag,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const Ar={kernelName:Se,backendName:"cpu",kernelFunc:Mr};function Dr(t){const{inputs:n,backend:a,attrs:s}=t,{axis:r}=s,i=e.parseAxisParam(r,n[0].shape)[0],l=n.map((e=>e.shape));o.assertParamsConsistent(l,i);let d=o.computeOutShape(n.map((e=>e.shape)),i);if(0===e.sizeFromShape(d))return a.makeTensorInfo(d,n[0].dtype,[]);const c=n.filter((t=>e.sizeFromShape(t.shape)>0));if(1===c.length)return $n({inputs:{x:c[0]},backend:a});if("complex64"===c[0].dtype){const e=c.map((e=>Vn({inputs:{input:e},backend:a}))),t=c.map((e=>Mr({inputs:{input:e},backend:a}))),n=Dr({inputs:e,backend:a,attrs:{axis:i}}),s=Dr({inputs:t,backend:a,attrs:{axis:i}}),r=Pn({inputs:{real:n,imag:s},backend:a});return e.forEach((e=>a.disposeIntermediateTensorInfo(e))),t.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(s),r}const p=c.map((t=>{const n=e.sizeFromShape(t.shape.slice(i));return Qs({inputs:{x:t},backend:a,attrs:{shape:[-1,n]}})})),u=p.map((e=>({vals:a.data.get(e.dataId).values,shape:e.shape})));d=o.computeOutShape(p.map((e=>e.shape)),1);const h=1===p[0].shape[0],f=ra(u,d,n[0].dtype,h),m=o.computeOutShape(c.map((e=>e.shape)),i),g=a.makeTensorInfo(m,n[0].dtype,f);return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),g}const Er={kernelName:ve,backendName:"cpu",kernelFunc:Dr};function zr(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}=s;An([r,i],"conv2d");const h=o.convertConv2DDataFormat(c),f=o.computeConv2DInfo(r.shape,i.shape,l,p,d,u,!1,h),m=f.filterHeight,g=f.filterWidth,k=f.dilationHeight,I=f.dilationWidth,b=f.padInfo.left,y=f.padInfo.top,N="channelsLast"===f.dataFormat,T=new $(f.outShape,r.dtype),x=e.computeStrides(r.shape),S=e.computeStrides(i.shape),v=x[0],F=N?x[1]:x[2],w=N?x[2]:1,M=N?1:x[1],A=T.strides[0],D=N?T.strides[1]:T.strides[2],E=N?T.strides[2]:1,z=N?1:T.strides[1],W=a.data.get(r.dataId).values,R=a.data.get(i.dataId).values,P=T.values;for(let e=0;e<f.batchSize;++e){const t=e*v,n=e*A;for(let e=0;e<f.outHeight;++e){const a=n+e*D,s=e*f.strideHeight-y;for(let e=0;e<m;++e){const n=s+e*k;if(n<0||n>=f.inHeight)continue;const r=e*S[0],o=t+n*F;for(let e=0;e<f.outWidth;++e){const t=a+e*E,n=e*f.strideWidth-b;for(let e=0;e<g;++e){const a=n+e*I;if(a<0||a>=f.inWidth)continue;const s=o+a*w;let i=r+e*S[1];for(let e=0;e<f.inChannels;++e){const n=W[s+e*M];for(let e=0;e<f.outChannels;++e)P[t+e*z]+=n*R[i+e];i+=f.outChannels}}}}}}return a.makeTensorInfo(T.shape,T.dtype,P)}const Wr={kernelName:Fe,backendName:"cpu",kernelFunc:zr};const Rr={kernelName:we,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,dy:r}=t,{strides:i,pad:l,dataFormat:d,dimRoundingMode:c,filterShape:p}=a;An([s,r],"conv2dBackpropFilter");const u=o.convertConv2DDataFormat(d),h=o.computeConv2DInfo(s.shape,p,i,1,l,c,!1,u),{strideHeight:f,strideWidth:m,filterHeight:g,filterWidth:k}=h,I="channelsLast"===h.dataFormat,b=new $(h.filterShape,"float32"),y=h.padInfo.left,N=h.padInfo.top,T=n.data.get(s.dataId).values,x=n.data.get(r.dataId).values,S=new $(s.shape,s.dtype,T),v=new $(r.shape,r.dtype,x);for(let e=0;e<g;++e){const t=Math.max(0,Math.ceil((N-e)/f)),n=Math.min(h.outHeight,(h.inHeight+N-e)/f);for(let a=0;a<k;++a){const s=Math.max(0,Math.ceil((y-a)/m)),r=Math.min(h.outWidth,(h.inWidth+y-a)/m);for(let o=0;o<h.inChannels;++o)for(let i=0;i<h.outChannels;++i){let l=0;for(let d=0;d<h.batchSize;++d)for(let c=t;c<n;++c){const t=e+c*f-N;for(let e=s;e<r;++e){const n=a+e*m-y;l+=I?S.get(d,t,n,o)*v.get(d,c,e,i):S.get(d,o,t,n)*v.get(d,i,c,e)}}b.set(l,e,a,o,i)}}}return n.makeTensorInfo(b.shape,b.dtype,b.values)}};const Pr={kernelName:Me,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{inputShape:l,strides:d,pad:c,dataFormat:p,dimRoundingMode:u}=s;An([r,i],"conv2dBackpropInput");const h=e.computeStrides(i.shape),f=e.computeStrides(r.shape);let m=o.convertConv2DDataFormat(p);const g=o.computeConv2DInfo(l,i.shape,d,1,c,u,!1,m),k=new $(g.inShape,"float32"),I=k.values,b=a.data.get(r.dataId).values,y=a.data.get(i.dataId).values,[N,T,x]=h,{batchSize:S,filterHeight:v,filterWidth:F,inChannels:w,inHeight:M,inWidth:A,outChannels:D,outHeight:E,outWidth:z,strideHeight:W,strideWidth:R}=g;m=g.dataFormat;const P=v-1-g.padInfo.top,H=F-1-g.padInfo.left,C="channelsLast"===m,O=k.strides[0],V=C?k.strides[1]:k.strides[2],_=C?k.strides[2]:1,G=C?1:k.strides[1],B=f[0],L=C?f[1]:f[2],q=C?f[2]:1,U=C?1:f[1];for(let e=0;e<S;++e)for(let t=0;t<w;++t)for(let n=0;n<M;++n){const a=n-P,s=Math.max(0,Math.ceil(a/W)),r=Math.min(E,(v+a)/W);for(let o=0;o<A;++o){const i=o-H,l=Math.max(0,Math.ceil(i/R)),d=Math.min(z,(F+i)/R);let c=0;for(let n=s;n<r;++n){const s=n*W-a;for(let a=l;a<d;++a){const r=B*e+L*n+q*a,o=N*(v-1-s)+T*(F-1-(a*R-i))+x*t;for(let e=0;e<D;++e){c+=b[r+U*e]*y[o+e]}}}I[O*e+V*n+_*o+G*t]=c}}return a.makeTensorInfo(k.shape,k.dtype,k.values)}};const Hr={kernelName:Ae,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dilations:c}=s;An([r,i],"conv3d");const p=o.computeConv3DInfo(r.shape,i.shape,l,c,d),{filterDepth:u,filterHeight:h,filterWidth:f,dilationDepth:m,dilationHeight:g,dilationWidth:k,padInfo:I}=p,b=I.front,y=I.left,N=I.top,T=new $(p.outShape,r.dtype),x=a.data.get(r.dataId).values,S=a.data.get(i.dataId).values,v=T.values,F=e.computeStrides(r.shape),w=e.computeStrides(i.shape);for(let e=0;e<p.batchSize;++e){const t=e*F[0],n=e*T.strides[0];for(let e=0;e<p.outDepth;++e){const a=n+e*T.strides[1],s=e*p.strideDepth-b;for(let e=0;e<u;++e){const n=s+e*m;if(n<0||n>=p.inDepth)continue;const r=e*w[0],o=t+n*F[1];for(let e=0;e<p.outHeight;++e){const t=a+e*T.strides[2],n=e*p.strideHeight-N;for(let e=0;e<h;++e){const a=n+e*g;if(a<0||a>=p.inHeight)continue;const s=r+e*w[1],i=o+a*F[2];for(let e=0;e<p.outWidth;++e){const n=t+e*p.outChannels,a=e*p.strideWidth-y;for(let e=0;e<f;++e){const t=a+e*k;if(t<0||t>=p.inWidth)continue;const r=s+e*w[2],o=i+t*p.inChannels;let l=r;for(let e=0;e<p.inChannels;++e){const t=x[o+e];for(let e=0;e<p.outChannels;++e)v[n+e]+=t*S[l+e];l+=p.outChannels}}}}}}}}return a.makeTensorInfo(T.shape,T.dtype,T.values)}};const Cr={kernelName:De,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,dy:i}=n,{strides:l,pad:d,filterShape:c}=s;An([r,i],"conv3dBackpropFilterV2");const p=e.computeStrides(r.shape),u=e.computeStrides(i.shape),h=o.computeConv3DInfo(r.shape,c,l,1,d),f=h.strideDepth,m=h.strideHeight,g=h.strideWidth,k=h.filterDepth,I=h.filterHeight,b=h.filterWidth,y=new $(h.filterShape,"float32"),N=y.values,[T,x,S,v]=y.strides,F=a.data.get(i.dataId).values,[w,M,A,D]=u,E=a.data.get(r.dataId).values,[z,W,R,P]=p,H=h.padInfo.front,C=h.padInfo.left,O=h.padInfo.top;for(let e=0;e<k;++e){const t=Math.max(0,Math.ceil((H-e)/f)),n=Math.min(h.outDepth,(h.inDepth+H-e)/f),a=e*T;for(let s=0;s<I;++s){const r=Math.max(0,Math.ceil((O-s)/m)),o=Math.min(h.outHeight,(h.inHeight+O-s)/m),i=s*x+a;for(let a=0;a<b;++a){const l=Math.max(0,Math.ceil((C-a)/g)),d=Math.min(h.outWidth,(h.inWidth+C-a)/g),c=a*S+i;for(let i=0;i<h.inChannels;++i){const p=i*v+c;for(let c=0;c<h.outChannels;++c){let u=0;for(let p=0;p<h.batchSize;++p){const h=p*z,k=p*w;for(let p=t;p<n;++p){const t=(e+p*f-H)*W+h,n=p*M+k;for(let e=r;e<o;++e){const r=(s+e*m-O)*R+t,o=e*A+n;for(let e=l;e<d;++e){const t=e*D+o;u+=E[(a+e*g-C)*P+r+i]*F[t+c]}}}}N[p+c]=u}}}}}return a.makeTensorInfo(y.shape,y.dtype,y.values)}};const $r={kernelName:Ee,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{pad:l,strides:d,inputShape:c}=s;An([r],"conv3dBackpropInputV2");const p=e.computeStrides(r.shape),u=e.computeStrides(i.shape),h=o.computeConv3DInfo(c,i.shape,d,1,l),f=new $(h.inShape,"float32"),m=f.values,[g,k,I,b]=f.strides,y=a.data.get(r.dataId).values,[N,T,x,S]=p,v=a.data.get(i.dataId).values,[F,w,M,A]=u,{batchSize:D,filterDepth:E,filterHeight:z,filterWidth:W,inChannels:R,inDepth:P,inHeight:H,inWidth:C,outChannels:O,outDepth:V,outHeight:_,outWidth:G,strideDepth:B,strideHeight:L,strideWidth:q}=h,U=E-1-h.padInfo.front,Z=z-1-h.padInfo.top,K=W-1-h.padInfo.left;for(let e=0;e<D;++e)for(let t=0;t<R;++t)for(let n=0;n<P;++n){const a=n-U,s=Math.max(0,Math.ceil(a/B)),r=Math.min(V,(E+a)/B);for(let o=0;o<H;++o){const i=o-Z,l=Math.max(0,Math.ceil(i/L)),d=Math.min(_,(z+i)/L);for(let c=0;c<C;++c){const p=c-K,u=Math.max(0,Math.ceil(p/q)),h=Math.min(G,(W+p)/q);let f=0;for(let n=s;n<r;++n){const s=n*B-a;for(let a=l;a<d;++a){const r=a*L-i;for(let o=u;o<h;++o){const i=N*e+T*n+x*a+S*o,l=F*(E-1-s)+w*(z-1-r)+M*(W-1-(o*q-p))+A*t;for(let e=0;e<O;++e){f+=y[i+e]*v[l+e]}}}}m[g*e+k*n+I*o+b*c+t]=f}}}return a.makeTensorInfo(f.shape,f.dtype,f.values)}},Or={kernelName:ze,backendName:"cpu",kernelFunc:ta(ze,(e=>Math.cos(e)))},Vr={kernelName:We,backendName:"cpu",kernelFunc:ta(We,(e=>Math.cosh(e)))};const _r={kernelName:Re,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{image:r,boxes:o,boxInd:l}=n,{cropSize:d,method:c,extrapolationValue:p}=s,[u,h,f,m]=r.shape,g=o.shape[0],[k,I]=d,b=i([g,k,I,m],"float32"),y=a.data.get(o.dataId).values,N=a.data.get(l.dataId).values,T=a.data.get(r.dataId).values,x=e.computeStrides(r.shape),S=e.computeStrides(b.shape);for(let e=0;e<g;e++){const t=4*e,n=y[t],a=y[t+1],s=y[t+2],r=y[t+3],o=N[e];if(o>=u)continue;const i=k>1?(s-n)*(h-1)/(k-1):0,l=I>1?(r-a)*(f-1)/(I-1):0;for(let t=0;t<k;t++){const d=k>1?n*(h-1)+t*i:.5*(n+s)*(h-1);if(d<0||d>h-1)for(let n=0;n<I;n++)for(let a=0;a<m;a++){const s=a+n*S[2]+t*S[1]+e*S[0];b.values[s]=p}else if("bilinear"===c){const n=Math.floor(d),s=Math.ceil(d),i=d-n;for(let d=0;d<I;d++){const c=I>1?a*(f-1)+d*l:.5*(a+r)*(f-1);if(c<0||c>f-1){for(let n=0;n<m;n++){const a=n+d*S[2]+t*S[1]+e*S[0];b.values[a]=p}continue}const u=Math.floor(c),h=Math.ceil(c),g=c-u;for(let a=0;a<m;a++){let r=a+u*x[2]+n*x[1]+o*x[0];const l=T[r];r=a+h*x[2]+n*x[1]+o*x[0];const c=T[r];r=a+u*x[2]+s*x[1]+o*x[0];const p=T[r];r=a+h*x[2]+s*x[1]+o*x[0];const f=l+(c-l)*g,m=p+(T[r]-p)*g;r=a+d*S[2]+t*S[1]+e*S[0],b.values[r]=f+(m-f)*i}}}else for(let n=0;n<I;++n){const s=I>1?a*(f-1)+n*l:.5*(a+r)*(f-1);if(s<0||s>f-1){for(let a=0;a<m;a++){const s=a+n*S[2]+t*S[1]+e*S[0];b.values[s]=p}continue}const i=Math.round(s),c=Math.round(d);for(let a=0;a<m;a++){const s=a+i*x[2]+c*x[1]+o*x[0],r=a+n*S[2]+t*S[1]+e*S[0];b.values[r]=T[s]}}}}return a.makeTensorInfo(b.shape,b.dtype,b.values)}};const Gr={kernelName:Pe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,exclusive:l,reverse:d}=s;An(r,"cumprod");const c=o.getAxesPermutation([i],r.shape.length);let p=r;null!=c&&(p=qa({inputs:{x:r},backend:a,attrs:{perm:c}}));const u=o.getInnerMostAxes(1,r.shape.length)[0];if(u!==p.shape.length-1)throw new Error(`backend.cumprod in CPU expects an inner-most axis=${p.shape.length-1} but got axis=${u}`);const h=z(p.dtype,"int32"),f=e.makeOnesTypedArray(e.sizeFromShape(p.shape),h),m=a.data.get(p.dataId).values,g=p.shape[p.shape.length-1],k=d?(e,t)=>e+g-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=g)for(let t=0;t<g;t++){const n=k(e,t);if(0===t)f[n]=l?1:m[n];else{const a=k(e,t-1);f[n]=l?m[a]*f[a]:m[n]*f[a]}}const I=a.makeTensorInfo(p.shape,h,f);if(null!=c){const e=qa({inputs:{x:I},backend:a,attrs:{perm:o.getUndoAxesPermutation(c)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(p),e}return I}};const Br={kernelName:He,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,exclusive:l,reverse:d}=s;An(r,"cumsum");const c=o.getAxesPermutation([i],r.shape.length);let p=r;null!=c&&(p=qa({inputs:{x:r},backend:a,attrs:{perm:c}}));const u=o.getInnerMostAxes(1,r.shape.length)[0];if(u!==p.shape.length-1)throw new Error(`backend.cumsum in CPU expects an inner-most axis=${p.shape.length-1} but got axis=${u}`);const h=z(p.dtype,"int32"),f=e.makeZerosTypedArray(e.sizeFromShape(p.shape),h),m=a.data.get(p.dataId).values,g=p.shape[p.shape.length-1],k=d?(e,t)=>e+g-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=g)for(let t=0;t<g;t++){const n=k(e,t);if(0===t)f[n]=l?0:m[n];else{const a=k(e,t-1);f[n]=l?m[a]+f[a]:m[n]+f[a]}}const I=a.makeTensorInfo(p.shape,h,f);if(null!=c){const e=qa({inputs:{x:I},backend:a,attrs:{perm:o.getUndoAxesPermutation(c)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(p),e}return I}};const Lr={kernelName:Ce,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o,binaryOutput:i}=a;if(1===s.shape.length){const e=Yn(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,e)}if(2===s.shape.length){const e=Jn(n.bufferSync(s),n.bufferSync(r),o,i);return n.makeTensorInfo(e.shape,r.dtype,e.values)}throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank${s.shape.length}.`)}};const qr={kernelName:$e,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{blockSize:o,dataFormat:i}=s;e.assert("NHWC"===i,(()=>`Only NHWC dataFormat supported on CPU for depthToSpace. Got ${i}`));const l=r.shape[0],d=r.shape[1],c=r.shape[2],p=r.shape[3],u=d*o,h=c*o,f=p/(o*o),m=a.data.get(r.dataId).values,g=new Float32Array(l*u*h*f);let k=0;for(let e=0;e<l;++e)for(let t=0;t<u;++t){const n=Math.floor(t/o),a=t%o;for(let t=0;t<h;++t){const s=Math.floor(t/o),r=(a*o+t%o)*f;for(let t=0;t<f;++t){const a=t+r+p*(s+c*(n+d*e));g[k++]=m[a]}}}return a.makeTensorInfo([l,u,h,f],r.dtype,g)}};function Ur(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dilations:c,dimRoundingMode:p}=s;An([r,i],"depthwiseConv2DNative");const u=e.computeStrides(r.shape),h=e.computeStrides(i.shape);let f=c;null==f&&(f=[1,1]),e.assert(o.eitherStridesOrDilationsAreOne(l,f),(()=>`Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${l} and dilations '${f}'`));const m=o.computeConv2DInfo(r.shape,i.shape,l,f,d,p,!0),{filterHeight:g,filterWidth:k,dilationHeight:I,dilationWidth:b,padInfo:y}=m,N=y.left,T=y.top,x=m.outChannels/m.inChannels,S=new $(m.outShape,r.dtype),v=a.data.get(r.dataId).values,F=a.data.get(i.dataId).values,w=S.values;for(let e=0;e<m.batchSize;++e){const t=e*u[0],n=e*S.strides[0];for(let e=0;e<m.outHeight;++e){const a=n+e*S.strides[1],s=e*m.strideHeight-T;for(let e=0;e<g;++e){const n=s+e*I;if(n<0||n>=m.inHeight)continue;const r=e*h[0],o=t+n*u[1];for(let e=0;e<m.outWidth;++e){const t=a+e*S.strides[2],n=e*m.strideWidth-N;for(let e=0;e<k;++e){const a=n+e*b;if(a<0||a>=m.inWidth)continue;const s=r+e*h[1],i=o+a*m.inChannels;let l=t,d=s;for(let e=0;e<m.inChannels;++e){const t=v[i+e];for(let e=0;e<x;++e)w[l+e]+=t*F[d+e];l+=x,d+=x}}}}}}return a.makeTensorInfo(S.shape,S.dtype,S.values)}const Zr={kernelName:Oe,backendName:"cpu",kernelFunc:Ur};const Kr={kernelName:Ve,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,dy:r}=t,{strides:i,dilations:l,pad:d,dimRoundingMode:c,filterShape:p}=a;An([s,r],"depthwiseConv2dNativeBackpropFilter");const u=o.computeConv2DInfo(s.shape,p,i,l,d,c,!0),{strideHeight:h,strideWidth:f,filterHeight:m,filterWidth:g}=u,k=new $(u.filterShape,"float32"),I=u.padInfo.left,b=u.padInfo.top,y=u.outChannels/u.inChannels,N=n.data.get(s.dataId).values,T=new $(s.shape,s.dtype,N),x=n.data.get(r.dataId).values,S=new $(r.shape,r.dtype,x);for(let e=0;e<m;++e){const t=Math.max(0,Math.ceil((b-e)/h)),n=Math.min(u.outHeight,(u.inHeight+b-e)/h);for(let a=0;a<g;++a){const s=Math.max(0,Math.ceil((I-a)/f)),r=Math.min(u.outWidth,(u.inWidth+I-a)/f);for(let o=0;o<u.outChannels;++o){const i=Math.trunc(o/y),l=o%y;let d=0;for(let l=0;l<u.batchSize;++l)for(let c=t;c<n;++c){const t=e+c*h-b;for(let e=s;e<r;++e){const n=a+e*f-I;d+=T.get(l,t,n,i)*S.get(l,c,e,o)}}k.set(d,e,a,i,l)}}}return n.makeTensorInfo(k.shape,k.dtype,k.values)}};const jr={kernelName:_e,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{strides:l,dilations:d,pad:c,dimRoundingMode:p,inputShape:u}=s;An([r,i],"depthwiseConv2DNativeBackpropInput");const h=e.computeStrides(r.shape),f=e.computeStrides(i.shape),m=o.computeConv2DInfo(u,i.shape,l,d,c,p,!0),g=new $(m.inShape,"float32"),k=g.values,[I,b,y]=g.strides,N=a.data.get(r.dataId).values,[T,x,S]=h,v=a.data.get(i.dataId).values,[F,w,M]=f,{batchSize:A,filterHeight:D,filterWidth:E,inChannels:z,inHeight:W,inWidth:R,outChannels:P,outHeight:H,outWidth:C,strideHeight:O,strideWidth:V}=m,_=D-1-m.padInfo.top,G=E-1-m.padInfo.left,B=P/z;for(let e=0;e<A;++e)for(let t=0;t<z;++t)for(let n=0;n<W;++n){const a=n-_,s=Math.max(0,Math.ceil(a/O)),r=Math.min(H,(D+a)/O);for(let o=0;o<R;++o){const i=o-G,l=Math.max(0,Math.ceil(i/V)),d=Math.min(C,(E+i)/V);let c=0;for(let n=s;n<r;++n){const s=n*O-a;for(let a=l;a<d;++a){const r=T*e+x*n+S*a,o=F*(D-1-s)+w*(E-1-(a*V-i))+M*t;for(let e=0;e<B;++e){c+=N[r+(t*B+e)]*v[o+e]}}}k[I*e+b*n+y*o+t]=c}}return a.makeTensorInfo(g.shape,g.dtype,g.values)}};const Yr={kernelName:Ge,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{x:s}=n,r=e.sizeFromShape(s.shape),o=a.data.get(s.dataId).values,l=i([r,r],s.dtype),d=l.values;for(let e=0;e<o.length;e++)d[e*r+e]=o[e];const c=[...s.shape,...s.shape];return a.makeTensorInfo(c,l.dtype,l.values)}},Jr={kernelName:Be,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r}=t,{strides:i,pad:l,dilations:d}=a,c=n,p=c.data.get(s.dataId).values,u=s.shape.length,h=c.data.get(r.dataId).values,f=r.shape.length,{batchSize:m,inHeight:g,inWidth:k,inChannels:I,outHeight:b,outWidth:y,padInfo:N,strideHeight:T,strideWidth:x,filterHeight:S,filterWidth:v,dilationHeight:F,dilationWidth:w,outShape:M}=o.computeDilation2DInfo(s.shape,r.shape,i,l,"NHWC",d),A=e.sizeFromShape(M),D=M.length,E=e.getArrayFromDType(s.dtype,A);for(let t=0;t<m;++t)for(let n=0;n<b;++n){const a=n*T-N.top;for(let o=0;o<y;++o){const i=o*x-N.left;for(let l=0;l<I;++l){let d=Number.MIN_SAFE_INTEGER;for(let n=0;n<S;++n){const o=a+n*F;if(o>=0&&o<g)for(let a=0;a<v;++a){const c=i+a*w;if(c>=0&&c<k){const i=e.locToIndex([t,o,c,l],u,e.computeStrides(s.shape)),m=e.locToIndex([n,a,l],f,e.computeStrides(r.shape)),g=p[i]+h[m];g>d&&(d=g)}}}E[e.locToIndex([t,n,o,l],D,e.computeStrides(M))]=d}}}return{dataId:c.write(e.toTypedArray(E,s.dtype),M,s.dtype),shape:M,dtype:s.dtype}}},Qr={kernelName:Le,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r,dy:i}=t,{strides:l,pad:d,dilations:c}=a,p=n,u=e.toNestedArray(s.shape,p.data.get(s.dataId).values),h=e.toNestedArray(r.shape,p.data.get(r.dataId).values),{batchSize:f,inHeight:m,inWidth:g,inChannels:k,outHeight:I,outWidth:b,padInfo:y,strideHeight:N,strideWidth:T,filterHeight:x,filterWidth:S,dilationHeight:v,dilationWidth:F,outShape:w}=o.computeDilation2DInfo(s.shape,r.shape,l,d,"NHWC",c);e.assert(i.rank===w.length,(()=>`Error in ${Le}, dy must have the same rank as output ${w.length}, but got ${i.rank}`));const M=e.toNestedArray(w,p.data.get(i.dataId).values),A=e.makeZerosNestedTypedArray(r.shape,r.dtype);for(let e=0;e<f;++e)for(let t=0;t<I;++t){const n=t*N-y.top;for(let a=0;a<b;++a){const s=a*T-y.left;for(let r=0;r<k;++r){let o=Number.MIN_SAFE_INTEGER,i=0,l=0;for(let t=0;t<x;++t){const a=n+t*v;if(a>=0&&a<m)for(let n=0;n<S;++n){const d=s+n*F;if(d>=0&&d<g){const s=u[e][a][d][r]+h[t][n][r];s>o&&(o=s,i=t,l=n)}}}A[i][l][r]+=M[e][t][a][r]}}}return{dataId:p.write(e.toTypedArray(A,s.dtype),r.shape,r.dtype),shape:r.shape,dtype:r.dtype}}},Xr={kernelName:qe,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r,dy:i}=t,{strides:l,pad:d,dilations:c}=a,p=n,u=e.toNestedArray(s.shape,p.data.get(s.dataId).values),h=e.toNestedArray(r.shape,p.data.get(r.dataId).values),{batchSize:f,inHeight:m,inWidth:g,inChannels:k,outHeight:I,outWidth:b,padInfo:y,strideHeight:N,strideWidth:T,filterHeight:x,filterWidth:S,dilationHeight:v,dilationWidth:F,outShape:w}=o.computeDilation2DInfo(s.shape,r.shape,l,d,"NHWC",c);e.assert(i.rank===w.length,(()=>`Error in ${qe}, dy must have the same rank as output ${w.length}, but got ${i.rank}`));const M=e.toNestedArray(w,p.data.get(i.dataId).values),A=e.makeZerosNestedTypedArray(s.shape,s.dtype);for(let e=0;e<f;++e)for(let t=0;t<I;++t){const n=t*N-y.top;for(let a=0;a<b;++a){const s=a*T-y.left;for(let r=0;r<k;++r){let o=Number.MIN_SAFE_INTEGER,i=n<0?0:n,l=s<0?0:s;for(let t=0;t<x;++t){const a=n+t*v;if(a>=0&&a<m)for(let n=0;n<S;++n){const d=s+n*F;if(d>=0&&d<g){const s=u[e][a][d][r]+h[t][n][r];s>o&&(o=s,i=a,l=d)}}}A[e][i][l][r]+=M[e][t][a][r]}}}return{dataId:p.write(e.toTypedArray(A,s.dtype),s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}};const eo={kernelName:Ue,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{image:s}=t,{canvas:r,options:o}=a,{contextOptions:i,imageOptions:l}=o||{},d=(null==l?void 0:l.alpha)||1,c=(null==i?void 0:i.contextType)||"2d";if("2d"!==c)throw new Error(`Context type ${i.contextType} is not supported by the CPU backend.`);const p=r.getContext(c,(null==i?void 0:i.contextAttributes)||{});if(null==p)throw new Error(`Could not get the context with ${c} type.`);const[u,h]=s.shape.slice(0,2),f=2===s.shape.length?1:s.shape[2],m=n.data.get(s.dataId).values,g="float32"===s.dtype?255:1,k=new Uint8ClampedArray(h*u*4);for(let e=0;e<u*h;++e){const t=[0,0,0,255*d];for(let n=0;n<f;n++){const a=m[e*f+n];if("float32"===s.dtype){if(a<0||a>1)throw new Error(`Tensor values for a float32 Tensor must be in the range [0 - 1] but encountered ${a}.`)}else if("int32"===s.dtype&&(a<0||a>255))throw new Error(`Tensor values for a int32 Tensor must be in the range [0 - 255] but encountered ${a}.`);1===f?(t[0]=a*g,t[1]=a*g,t[2]=a*g):t[n]=a*g}const n=4*e;k[n+0]=Math.round(t[0]),k[n+1]=Math.round(t[1]),k[n+2]=Math.round(t[2]),k[n+3]=Math.round(t[3])}r.width=h,r.height=u;const I=new ImageData(k,h,u);return p.putImageData(I,0,0),s}};function to(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;let d;An(r,"sum"),d="bool"===r.dtype?Bn({inputs:{x:r},backend:a,attrs:{dtype:"int32"}}):$n({inputs:{x:r},backend:a});const c=d.shape.length,p=e.parseAxisParam(i,d.shape),u=o.getAxesPermutation(p,c);let h=p,f=d;null!=u&&(f=qa({inputs:{x:d},backend:a,attrs:{perm:u}}),h=o.getInnerMostAxes(h.length,c)),o.assertAxesAreInnerMostDims("sum",h,f.shape.length);const[m,g]=o.computeOutAndReduceShapes(f.shape,h);let k=Cn(a,m,o.upcastType(f.dtype,"int32"));const I=e.sizeFromShape(g),b=a.data.get(k.dataId).values,y=a.data.get(f.dataId).values;for(let e=0;e<b.length;++e){const t=e*I;let n=0;for(let e=0;e<I;++e)n+=y[t+e];b[e]=n}if(l){const e=k;k=Qs({inputs:{x:k},backend:a,attrs:{shape:o.expandShapeToKeepDim(k.shape,p)}}),a.disposeIntermediateTensorInfo(e)}return a.disposeIntermediateTensorInfo(d),null!=u&&a.disposeIntermediateTensorInfo(f),k}const no={kernelName:Ze,backendName:"cpu",kernelFunc:to};const ao={kernelName:Ke,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{equation:r}=s,i=n,{allDims:l,summedDims:d,idDims:c}=o.decodeEinsumEquation(r,i.length);o.checkEinsumDimSizes(l.length,c,i);const{path:p,steps:u}=o.getEinsumComputePath(d,c),h=u.length;let f=null,m=l.length;const g=[];for(let t=0;t<h;++t){for(const n of u[t]){const{permutationIndices:t,expandDims:s}=o.getEinsumPermutation(m,c[n]);let r;o.isIdentityPermutation(t)?r=i[n]:(r=qa({inputs:{x:i[n]},backend:a,attrs:{perm:t}}),g.push(r));const l=r.shape.slice();for(let e=0;e<s.length;++e)l.splice(s[e],0,1);e.arraysEqual(r.shape,l)||(r=Qs({inputs:{x:r},backend:a,attrs:{shape:l}}),g.push(r)),null===f?f=r:(f=$a({inputs:{a:r,b:f},backend:a}),g.push(f))}t<h-1&&(p[t]>=0&&(f=to({inputs:{x:f},backend:a,attrs:{axis:p[t]-(l.length-m),keepDims:!1}}),g.push(f)),m--)}for(const e of g)e!==f&&a.disposeIntermediateTensorInfo(e);return f}};const so={kernelName:je,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{dy:s,y:r}=n;An([s,r],"eluGrad");const o=new Float32Array(e.sizeFromShape(r.shape)),i=a.data.get(r.dataId).values,l=a.data.get(s.dataId).values;for(let e=0;e<i.length;++e){const t=i[e];o[e]=t>=0?l[e]:l[e]*(t+1)}return a.makeTensorInfo(r.shape,"float32",o)}},ro=o.ERF_P,oo=o.ERF_A1,io=o.ERF_A2,lo=o.ERF_A3,co=o.ERF_A4,po=o.ERF_A5,uo=ta(Ye,(e=>{const t=Math.sign(e),n=Math.abs(e),a=1/(1+ro*n);return t*(1-((((po*a+co)*a+lo)*a+io)*a+oo)*a*Math.exp(-n*n))})),ho={kernelName:Ye,backendName:"cpu",kernelFunc:uo};function fo(t){const{inputs:n,backend:a,attrs:s}=t,{input:r}=n,{dim:o}=s,i=r.shape.length,l=r.shape.slice();let d=o;return o<0&&(e.assert(-(i+1)<=o,(()=>`Axis must be in the interval [${-(i+1)}, ${i}]`)),d=i+o+1),l.splice(d,0,1),Qs({inputs:{x:r},backend:a,attrs:{shape:l}})}const mo={kernelName:Je,backendName:"cpu",kernelFunc:fo},go=qn(Qe,Rn(((e,t)=>e/t))),ko={kernelName:Qe,backendName:"cpu",kernelFunc:go};function Io(t,n,a){const s=t.shape,r=s[0],i=s[1],l=a.data.get(t.dataId),d=l.complexTensorInfos.real,c=l.complexTensorInfos.imag,p=[r,i],u=e.sizeFromShape(p),h=e.getTypedArrayFromDType("float32",u),f=e.getTypedArrayFromDType("float32",u);for(let e=0;e<r;e++){const t=hs({inputs:{x:d},backend:a,attrs:{begin:[e,0],size:[1,i]}}),s=hs({inputs:{x:c},backend:a,attrs:{begin:[e,0],size:[1,i]}}),r=Pn({inputs:{real:t,imag:s},backend:a}),{real:l,imag:p}=bo(r,n,a),u=o.mergeRealAndImagArrays(l,p);for(let t=0;t<i;t++){const n=o.getComplexWithIndex(u,t);h[e*i+t]=n.real,f[e*i+t]=n.imag}a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(s),a.disposeIntermediateTensorInfo(r)}const m=a.makeTensorInfo(p,"float32",h),g=a.makeTensorInfo(p,"float32",f),k=Pn({inputs:{real:m,imag:g},backend:a});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),k}function bo(t,n,a){const s=e.sizeFromShape(t.shape),r=a.data.get(t.dataId),i=a.data.get(r.complexTensorInfos.real.dataId).values,l=a.data.get(r.complexTensorInfos.imag.dataId).values;if(0==((d=s)&d-1)){const r=yo(i,l,s,n,a),o=[t.shape[0],t.shape[1]];if(n){const t=a.makeTensorInfo(o,"float32",r.real),n=a.makeTensorInfo(o,"float32",r.imag),i=a.makeTensorInfo([],"float32",e.createScalarValue(s,"float32")),l=$n({inputs:{x:i},backend:a}),d=ko.kernelFunc({inputs:{a:t,b:i},backend:a}),c=ko.kernelFunc({inputs:{a:n,b:l},backend:a}),p=a.data.get(d.dataId).values,u=a.data.get(c.dataId).values;return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),a.disposeIntermediateTensorInfo(d),a.disposeIntermediateTensorInfo(c),{real:p,imag:u}}return r}{const e=function(e,t,n){const a=new Float32Array(2*t);for(let s=0;s<t;s++){let r=0,i=0;for(let a=0;a<t;a++){const l=o.exponent(s*a,t,n),d=o.getComplexWithIndex(e,a);r+=d.real*l.real-d.imag*l.imag,i+=d.real*l.imag+d.imag*l.real}n&&(r/=t,i/=t),o.assignToTypedArray(a,r,i,s)}return a}(o.mergeRealAndImagArrays(i,l),s,n);return o.splitRealAndImagArrays(e)}var d}function yo(e,t,n,a,s){if(1===n)return{real:e,imag:t};const r=o.mergeRealAndImagArrays(e,t),i=n/2,l=o.complexWithEvenIndex(r),d=l.real,c=l.imag,p=[d.length],u=s.makeTensorInfo(p,"float32",d),h=s.makeTensorInfo(p,"float32",c),f=Pn({inputs:{real:u,imag:h},backend:s}),m=o.complexWithOddIndex(r),g=m.real,k=m.imag,I=[g.length],b=s.makeTensorInfo(I,"float32",g),y=s.makeTensorInfo(I,"float32",k),N=Pn({inputs:{real:b,imag:y},backend:s}),T=yo(d,c,i,a,s),x=T.real,S=T.imag,v=[x.length],F=s.makeTensorInfo(v,"float32",x),w=s.makeTensorInfo(v,"float32",S),M=Pn({inputs:{real:F,imag:w},backend:s}),A=yo(g,k,i,a,s),D=A.real,E=A.imag,z=[D.length],W=s.makeTensorInfo(z,"float32",D),R=s.makeTensorInfo(z,"float32",E),P=Pn({inputs:{real:W,imag:R},backend:s}),H=o.exponents(n,a),C=[H.real.length],$=s.makeTensorInfo(C,"float32",H.real),O=s.makeTensorInfo(C,"float32",H.imag),V=Pn({inputs:{real:$,imag:O},backend:s}),_=$a({inputs:{a:V,b:P},backend:s}),G=Kn({inputs:{a:M,b:_},backend:s}),B=Es({inputs:{a:M,b:_},backend:s}),L=Vn({inputs:{input:G},backend:s}),q=Vn({inputs:{input:B},backend:s}),U=Mr({inputs:{input:G},backend:s}),Z=Mr({inputs:{input:B},backend:s}),K=Dr({inputs:[L,q],backend:s,attrs:{axis:0}}),j=Dr({inputs:[U,Z],backend:s,attrs:{axis:0}}),Y=s.data.get(K.dataId).values,J=s.data.get(j.dataId).values;return s.disposeIntermediateTensorInfo(u),s.disposeIntermediateTensorInfo(h),s.disposeIntermediateTensorInfo(f),s.disposeIntermediateTensorInfo(b),s.disposeIntermediateTensorInfo(y),s.disposeIntermediateTensorInfo(N),s.disposeIntermediateTensorInfo(F),s.disposeIntermediateTensorInfo(w),s.disposeIntermediateTensorInfo(M),s.disposeIntermediateTensorInfo(W),s.disposeIntermediateTensorInfo(R),s.disposeIntermediateTensorInfo(P),s.disposeIntermediateTensorInfo($),s.disposeIntermediateTensorInfo(O),s.disposeIntermediateTensorInfo(V),s.disposeIntermediateTensorInfo(_),s.disposeIntermediateTensorInfo(G),s.disposeIntermediateTensorInfo(B),s.disposeIntermediateTensorInfo(L),s.disposeIntermediateTensorInfo(U),s.disposeIntermediateTensorInfo(q),s.disposeIntermediateTensorInfo(Z),s.disposeIntermediateTensorInfo(K),s.disposeIntermediateTensorInfo(j),{real:Y,imag:J}}const No={kernelName:Xe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{input:s}=n,r=e.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Qs({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=Io(i,!1,a),d=Qs({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),d}};function To(t){const{backend:n,attrs:a}=t,{shape:s,value:r,dtype:o}=a,i=o||e.inferDtype(r),l=e.getArrayFromDType(i,e.sizeFromShape(s));return function(e,t,n){e.fill(t)}(l,r),n.makeTensorInfo(s,i,l)}const xo={kernelName:et,backendName:"cpu",kernelFunc:To};const So={kernelName:tt,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{image:s}=t,r=a,o=e.getTypedArrayFromDType(s.dtype,e.sizeFromShape(s.shape)),[i,l,d,c]=s.shape,p=r.data.get(s.dataId).values;for(let e=0;e<i;e++){const t=e*d*l*c;for(let e=0;e<l;e++){const n=e*(d*c);for(let e=0;e<d;e++){const a=e*c;for(let s=0;s<c;s++){const r=Math.round(d-e-1),i=t+n+a+s;let l=p[i];if(r>=0&&r<d){l=p[t+n+r*c+s]}o[i]=l}}}}return{dataId:r.write(o,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}};const vo={kernelName:nt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u,activation:h,leakyreluAlpha:f}=a;let m=zr({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}});if(o){const e=m;if("NCHW"===c&&1===o.shape.length&&1!==o.shape[0]){const e=Qs({inputs:{x:o},backend:n,attrs:{shape:[o.shape[0],1,1]}});m=Kn({inputs:{a:m,b:e},backend:n}),n.disposeIntermediateTensorInfo(e)}else m=Kn({inputs:{a:m,b:o},backend:n});n.disposeIntermediateTensorInfo(e)}if(h){const e=m;if("NCHW"===c&&"prelu"===h&&1===i.shape.length&&1!==i.shape[0]){const e=Qs({inputs:{x:i},backend:n,attrs:{shape:[i.shape[0],1,1]}});m=Js(n,m,h,e,f),n.disposeIntermediateTensorInfo(e)}else m=Js(n,m,h,i,f);n.disposeIntermediateTensorInfo(e)}return m}};const Fo={kernelName:at,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u,activation:h,leakyreluAlpha:f}=a;let m=Ur({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}});if(o){const e=m;m=Kn({inputs:{a:m,b:o},backend:n}),n.disposeIntermediateTensorInfo(e)}if(h){const e=m;m=Js(n,m,h,i,f),n.disposeIntermediateTensorInfo(e)}return m}};const wo={kernelName:st,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{params:s,indices:r}=n,i=e.sizeFromShape(s.shape),l=r.shape,d=l[l.length-1],[c,p,u,h]=o.prepareAndValidate(s,r);if(0===p)return a.makeTensorInfo(c,s.dtype,[]);const f=Ia(a.data.get(r.dataId).values,a.bufferSync(s),s.dtype,p,d,u,h,s.shape,i);return a.makeTensorInfo(c,s.dtype,f.values)}};const Mo={kernelName:rt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,indices:i}=n,{axis:l,batchDims:d}=s;An([r,i],"gatherV2");const c=e.parseAxisParam(l,r.shape)[0],p=a.data.get(i.dataId).values,u=r.shape[c];for(let t=0;t<p.length;++t){const n=p[t];e.assert(n<=u-1&&n>=0,(()=>`GatherV2: the index value ${n} is not in [0, ${u-1}]`))}let h=d;null==d&&(h=0);const f=e.sizeFromShape(i.shape),m=o.segment_util.collectGatherOpShapeInfo(r,i,c,h),g=Qs({inputs:{x:r},backend:a,attrs:{shape:[m.batchSize,m.outerSize,m.dimSize,m.sliceSize]}}),k=Qs({inputs:{x:i},backend:a,attrs:{shape:[m.batchSize,f/m.batchSize]}}),I=[m.batchSize,m.outerSize,f/m.batchSize,m.sliceSize],b=a.bufferSync(k),y=ba(a.bufferSync(g),b,I);return a.disposeIntermediateTensorInfo(g),a.disposeIntermediateTensorInfo(k),a.makeTensorInfo(m.outputShape,y.dtype,y.values)}};const Ao={kernelName:ot,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{input:s}=n,r=e.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Qs({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=Io(i,!0,a),d=Qs({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),d}},Do={kernelName:it,backendName:"cpu",kernelFunc:ta(it,(e=>Number.isFinite(e)?1:0),"bool")},Eo={kernelName:lt,backendName:"cpu",kernelFunc:ta(lt,(e=>Math.abs(e)===1/0?1:0),"bool")},zo={kernelName:dt,backendName:"cpu",kernelFunc:ta(dt,(e=>Number.isNaN(e)?1:0),"bool")};const Wo={kernelName:ct,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,num:r}=n,o=Ma(a,s,r);return t.makeTensorInfo([o.length],"float32",o)}},Ro={kernelName:pt,backendName:"cpu",kernelFunc:ta(pt,(e=>Math.log1p(e)))},Po={kernelName:ut,backendName:"cpu",kernelFunc:qn(ut,Rn(((e,t)=>e&&t)),null,"bool")},Ho={kernelName:ht,backendName:"cpu",kernelFunc:ta(ht,(e=>e?0:1),"bool")},Co={kernelName:ft,backendName:"cpu",kernelFunc:qn(ft,Rn(((e,t)=>e||t)),null,"bool")};const $o={kernelName:mt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{depthRadius:o,bias:i,alpha:l,beta:d}=s;An(r,"LRN");const c=r.shape[3],p=c-1,u=a.data.get(r.dataId).values,h=e.sizeFromShape(r.shape),f=new Float32Array(h);function m(e){const t=e%c;let n=e-t+Math.max(0,t-o);const a=e-t+Math.min(t+o,p);let s=0;for(;n<=a;n++){const e=u[n];s+=e*e}return s}for(let e=0;e<h;e++){const t=m(e),n=u[e]*Math.pow(i+l*t,-d);f[e]=n}return a.makeTensorInfo(r.shape,r.dtype,f)}};const Oo={kernelName:gt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,y:o,dy:i}=n,{depthRadius:l,bias:d,alpha:c,beta:p}=s;An(i,"LRNGrad");const u=e.sizeFromShape(i.shape),h=i.shape[3],f=a.data.get(i.dataId).values,m=a.data.get(r.dataId).values,g=a.data.get(o.dataId).values,k=new Float32Array(u),I=u;for(let e=0;e<I;e++){const t=e%h,n=e-t+Math.max(0,t-l),a=e-t+Math.min(h,t+l+1);let s=0;for(let e=n;e<a;e++)s+=Math.pow(m[e],2);s=c*s+d;for(let t=n;t<a;t++){let n=-2*c*p*m[t]*g[e]/s;e===t&&(n+=Math.pow(s,-p)),n*=f[e],k[t]+=n}}return a.makeTensorInfo(i.shape,r.dtype,k)}};function Vo(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{reductionIndices:i,keepDims:l}=s,d=a;let c=r.shape;const p=c.length,u=e.parseAxisParam(i,c);let h=u;const f=o.getAxesPermutation(h,p);let m=d.data.get(r.dataId).values;if(null!=f){const e=new Array(p);for(let t=0;t<e.length;t++)e[t]=c[f[t]];m=La(m,c,r.dtype,f,e),h=o.getInnerMostAxes(h.length,p),c=e}An(r,"max"),o.assertAxesAreInnerMostDims("max",h,p);const[g,k]=o.computeOutAndReduceShapes(c,h),I=Ea(m,e.sizeFromShape(k),g,r.dtype),b=d.write(I,g,r.dtype);let y=g;if(l){y=o.expandShapeToKeepDim(g,u)}return{dataId:b,shape:y,dtype:r.dtype}}const _o={kernelName:kt,backendName:"cpu",kernelFunc:Vo};const Go={kernelName:It,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n;An(r,"maxPool");const{filterSize:i,strides:l,pad:d,dimRoundingMode:c}=s;e.assert(o.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const p=o.computePool2DInfo(r.shape,i,l,1,d,c);let u;if(1===p.filterWidth&&1===p.filterHeight&&e.arraysEqual(p.inShape,p.outShape))u=$n({inputs:{x:r},backend:a});else{const t=a.data.get(r.dataId).values,n=e.computeStrides(r.shape),s=mr(t,r.shape,r.dtype,n,p,"max");u=a.makeTensorInfo(p.outShape,r.dtype,s.values)}return u}};const Bo={kernelName:bt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{filterSize:i,strides:l,pad:d,dimRoundingMode:c,dataFormat:p}=s;An(r,"maxPool3d");const u=o.computePool3DInfo(r.shape,i,l,1,d,c,p),h=kr(a.data.get(r.dataId).values,r.shape,r.dtype,e.computeStrides(r.shape),u,"max");return a.makeTensorInfo(h.shape,"float32",h.values)}};const Lo={kernelName:yt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,{filterSize:l,strides:d,pad:c,dimRoundingMode:p}=a;An([s,r],"maxPool3DGrad");const u=o.computePool3DInfo(r.shape,l,d,1,c,p),h=function(e,t){const n=i(t.outShape,"int32"),a=t.strideDepth,s=t.strideHeight,r=t.strideWidth,o=t.dilationDepth,l=t.dilationHeight,d=t.dilationWidth,c=t.effectiveFilterDepth,p=t.effectiveFilterHeight,u=t.effectiveFilterWidth,h=t.padInfo.front,f=t.padInfo.top,m=t.padInfo.left;for(let i=0;i<t.batchSize;++i)for(let g=0;g<t.inChannels;++g)for(let k=0;k<t.outDepth;++k){const I=k*a-h;let b=I;for(;b<0;)b+=o;const y=Math.min(t.inDepth,c+I);for(let a=0;a<t.outHeight;++a){const c=a*s-f;let h=c;for(;h<0;)h+=l;const N=Math.min(t.inHeight,p+c);for(let s=0;s<t.outWidth;++s){const f=s*r-m;let T=f;for(;T<0;)T+=d;const x=Math.min(t.inWidth,u+f);let S=Number.NEGATIVE_INFINITY,v=-1;for(let t=b;t<y;t+=o){const n=t-I;for(let a=h;a<N;a+=l){const s=a-c;for(let r=T;r<x;r+=d){const o=r-f,l=e.get(i,t,a,r,g);l>=S&&(S=l,v=n*p*u+s*p+o)}}}n.set(v,i,k,a,s,g)}}}return n}(n.bufferSync(r),u),f=u.strideDepth,m=u.strideHeight,g=u.strideWidth,k=u.dilationDepth,I=u.dilationHeight,b=u.dilationWidth,y=u.effectiveFilterDepth,N=u.effectiveFilterHeight,T=u.effectiveFilterWidth,x=y-1-u.padInfo.front,S=T-1-u.padInfo.left,v=N-1-u.padInfo.top,F=i(r.shape,"float32"),w=n.bufferSync(s);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inDepth;++n)for(let a=0;a<u.inHeight;++a)for(let s=0;s<u.inWidth;++s){const r=n-x,o=a-v,i=s-S;let l=0;for(let n=0;n<y;n+=k){const a=(r+n)/f;if(!(a<0||a>=u.outDepth||Math.floor(a)!==a))for(let s=0;s<N;s+=I){const r=(o+s)/m;if(!(r<0||r>=u.outHeight||Math.floor(r)!==r))for(let o=0;o<T;o+=b){const d=(i+o)/g;if(d<0||d>=u.outWidth||Math.floor(d)!==d)continue;const c=y*N*T-1-h.get(e,a,r,d,t)===n*N*T+s*T+o?1:0;if(0===c)continue;l+=w.get(e,a,r,d,t)*c}}}F.set(l,e,n,a,s,t)}return n.makeTensorInfo(F.shape,F.dtype,F.values)}};const qo={kernelName:Nt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r,output:l}=t,d=r;An([r,l],"maxPoolGrad");const{filterSize:c,strides:p,pad:u,dimRoundingMode:h}=a,f=o.computePool2DInfo(d.shape,c,p,1,u,h),m=n.data.get(d.dataId).values,g=i(f.outShape,d.dtype,gr(m,d.shape,d.dtype,f).values),k=f.strideHeight,I=f.strideWidth,b=f.dilationHeight,y=f.dilationWidth,N=f.effectiveFilterHeight,T=f.effectiveFilterWidth,x=T-1-f.padInfo.left,S=N-1-f.padInfo.top,v=i(d.shape,"float32"),F=n.data.get(s.dataId).values,w=i(s.shape,"float32",F);for(let e=0;e<f.batchSize;++e)for(let t=0;t<f.inChannels;++t)for(let n=0;n<f.inHeight;++n)for(let a=0;a<f.inWidth;++a){const s=n-S,r=a-x;let o=0;for(let n=0;n<N;n+=b){const a=(s+n)/k;if(!(a<0||a>=f.outHeight||Math.floor(a)!==a))for(let s=0;s<T;s+=y){const i=(r+s)/I;if(i<0||i>=f.outWidth||Math.floor(i)!==i)continue;const l=N*T-1-g.get(e,a,i,t)===n*T+s?1:0;if(0===l)continue;o+=w.get(e,a,i,t)*l}}v.set(o,e,n,a,t)}return n.makeTensorInfo(v.shape,v.dtype,v.values)}};const Uo={kernelName:Tt,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{x:s}=t,{filterSize:r,strides:i,pad:l,includeBatchInIndex:d}=n,c=a;An(s,"MaxPoolWithArgmax");const p=c.data.get(s.dataId).values,u=o.computePool2DInfo(s.shape,r,i,[1,1],l),[h,f]=function(t,n,a,s,r){const o=mr(t,0,a,e.computeStrides(n),r,"max"),i=gr(t,n,a,r,!0,s);return[o.values,i.values]}(p,s.shape,s.dtype,d,u),m=c.write(h,u.outShape,s.dtype),g=c.write(f,u.outShape,s.dtype);return[{dataId:m,shape:u.outShape,dtype:s.dtype},{dataId:g,shape:u.outShape,dtype:"int32"}]}};const Zo={kernelName:xt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s,d=e.parseAxisParam(i,r.shape),c=o.computeOutAndReduceShapes(r.shape,d)[1],p=e.sizeFromShape(c),u=[],h=a.makeTensorInfo([],"float32",new Float32Array([p]));u.push(h);const f=Bn({inputs:{x:r},backend:a,attrs:{dtype:"float32"}});u.push(f);const m=go({inputs:{a:f,b:h},backend:a});u.push(m);const g=to({inputs:{x:m},backend:a,attrs:{axis:i,keepDims:l}});return u.forEach((e=>a.disposeIntermediateTensorInfo(e))),g}};const Ko={kernelName:St,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;An(r,"min");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=qa({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("min",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),g=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),k=a.data.get(u.dataId).values;for(let e=0;e<g.length;++e){const t=e*m;let n=k[t];for(let e=0;e<m;++e){const a=k[t+e];(Number.isNaN(a)||a<n)&&(n=a)}g[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,g);if(l){const e=Qs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const jo={kernelName:vt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{paddings:o,mode:i}=s;An(r,"mirrorPad");const l=o.map(((e,t)=>e[0]+r.shape[t]+e[1])),d=o.map((e=>e[0])),c=o.map(((e,t)=>e[0]+r.shape[t])),p="reflect"===i?0:1,u=a.data.get(r.dataId).values,h=r.shape.length,f=e.computeStrides(r.shape),m=e.sizeFromShape(l),g=l.length,k=e.computeStrides(l),I=e.getTypedArrayFromDType(r.dtype,m);for(let t=0;t<m;t++){let n=e.indexToLoc(t,g,k);for(let e=0;e<g;e++)n[e]<d[e]?n[e]=2*d[e]-n[e]-p:n[e]>=c[e]&&(n[e]=2*(c[e]-1)-n[e]+p);n=n.map(((e,t)=>e-d[t]));const a=e.locToIndex(n,h,f);I[t]=u[a]}return{dataId:a.write(I,l,r.dtype),shape:l,dtype:r.dtype}}},Yo={kernelName:Ft,backendName:"cpu",kernelFunc:qn(Ft,Rn(((e,t)=>{const n=e%t;return e<0&&t<0||e>=0&&t>=0?n:(n+t)%t})))};function Jo(t){const{inputs:n,backend:a,attrs:s}=t,{logits:r}=n,{dim:i}=s,l=r.shape.length;let d=i;if(-1===d&&(d=l-1),d!==l-1)throw Error(`Softmax along a non-last dimension is not yet supported. Logits was rank ${l} and dim was ${d}`);const c=e.parseAxisParam([d],r.shape),p=Vo({inputs:{x:r},backend:a,attrs:{reductionIndices:c,keepDims:!1}}),u=o.expandShapeToKeepDim(p.shape,c),h=Qs({inputs:{x:p},backend:a,attrs:{shape:u}}),f=Es({inputs:{a:r,b:h},backend:a}),m=ca({inputs:{x:f},backend:a}),g=to({inputs:{x:m},backend:a,attrs:{axis:c,keepDims:!1}}),k=Qs({inputs:{x:g},backend:a,attrs:{shape:u}}),I=go({inputs:{a:m,b:k},backend:a});return a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(h),a.disposeIntermediateTensorInfo(f),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),a.disposeIntermediateTensorInfo(k),I}const Qo={kernelName:wt,backendName:"cpu",kernelFunc:Jo};const Xo={kernelName:Mt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{logits:r}=n,{numSamples:o,seed:i,normalized:l}=s;An(r,"multinomial");const d=l?r:Jo({inputs:{logits:r},backend:a,attrs:{dim:-1}}),c=d.shape[0],p=d.shape[1],u=a.data.get(d.dataId).values,h=[c,o],f=e.makeZerosTypedArray(e.sizeFromShape(h),"int32");for(let e=0;e<c;++e){const t=e*p,n=new Float32Array(p-1);n[0]=u[t];for(let e=1;e<n.length;++e)n[e]=n[e-1]+u[t+e];const a=Mn.alea(i.toString()),s=e*o;for(let e=0;e<o;++e){const t=a();f[s+e]=n.length;for(let a=0;a<n.length;a++)if(t<n[a]){f[s+e]=a;break}}}return l||a.disposeIntermediateTensorInfo(d),a.makeTensorInfo(h,"int32",f)}},ei=t.nonMaxSuppressionV3Impl;const ti={kernelName:At,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l}=a;An(s,"NonMaxSuppression");const d=n.data.get(s.dataId).values,c=n.data.get(r.dataId).values,{selectedIndices:p}=ei(d,c,o,i,l);return n.makeTensorInfo([p.length],"int32",new Int32Array(p))}},ni=t.nonMaxSuppressionV4Impl;const ai={kernelName:Dt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l,padToMaxOutputSize:d}=a;An(s,"NonMaxSuppressionPadded");const c=n.data.get(s.dataId).values,p=n.data.get(r.dataId).values,{selectedIndices:u,validOutputs:h}=ni(c,p,o,i,l,d);return[n.makeTensorInfo([u.length],"int32",new Int32Array(u)),n.makeTensorInfo([],"int32",new Int32Array([h]))]}},si=t.nonMaxSuppressionV5Impl;const ri={kernelName:Et,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l,softNmsSigma:d}=a;An(s,"NonMaxSuppressionWithScore");const c=n.data.get(s.dataId).values,p=n.data.get(r.dataId).values,u=o,h=i,f=l,m=d,{selectedIndices:g,selectedScores:k}=si(c,p,u,h,f,m);return[n.makeTensorInfo([g.length],"int32",new Int32Array(g)),n.makeTensorInfo([k.length],"float32",new Float32Array(k))]}};const oi={kernelName:zt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{indices:r}=n,{dtype:o,depth:i,onValue:l,offValue:d}=s;An(r,"oneHot");const c=e.sizeFromShape(r.shape),p=new Float32Array(c*i);p.fill(d);const u=a.data.get(r.dataId).values;for(let e=0;e<c;++e)u[e]>=0&&u[e]<i&&(p[e*i+u[e]]=l);return a.makeTensorInfo([...r.shape,i],o,p)}};function ii(e){const{inputs:t,backend:n}=e,{x:a}=t;if("string"===a.dtype)throw new Error("zerosLike is not supported for string tensors");if("complex64"===a.dtype){const e=Vn({inputs:{input:a},backend:n}),t=ii({inputs:{x:e},backend:n}),s=Mr({inputs:{input:a},backend:n}),r=ii({inputs:{x:s},backend:n}),o=Pn({inputs:{real:t,imag:r},backend:n});return n.disposeIntermediateTensorInfo(e),n.disposeIntermediateTensorInfo(t),n.disposeIntermediateTensorInfo(s),n.disposeIntermediateTensorInfo(r),o}return To({backend:n,attrs:{shape:a.shape,value:0,dtype:a.dtype}})}const li={kernelName:Wt,backendName:"cpu",kernelFunc:ii};const di={kernelName:Rt,backendName:"cpu",kernelFunc:function e(t){const{inputs:n,backend:a}=t,{x:s}=n;if("string"===s.dtype)throw new Error("onesLike is not supported for string tensors");if("complex64"===s.dtype){const t=Vn({inputs:{input:s},backend:a}),n=e({inputs:{x:t},backend:a}),r=Mr({inputs:{input:s},backend:a}),o=ii({inputs:{x:r},backend:a}),i=Pn({inputs:{real:n,imag:o},backend:a});return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(r),a.disposeIntermediateTensorInfo(o),i}return To({backend:a,attrs:{shape:s.shape,value:1,dtype:s.dtype}})}};function ci(t){const{inputs:n,backend:a,attrs:s}=t,{axis:r}=s;if(1===n.length)return fo({inputs:{input:n[0]},backend:a,attrs:{dim:r}});const o=n[0].shape,i=n[0].dtype;n.forEach((t=>{e.assertShapesMatch(o,t.shape,"All tensors passed to stack must have matching shapes"),e.assert(i===t.dtype,(()=>"All tensors passed to stack must have matching dtypes"))}));const l=[],d=Dr({inputs:n.map((e=>{const t=fo({inputs:{input:e},backend:a,attrs:{dim:r}});return l.push(t),t})),backend:a,attrs:{axis:r}});return l.forEach((e=>a.disposeIntermediateTensorInfo(e))),d}const pi={kernelName:Pt,backendName:"cpu",kernelFunc:ci};const ui={kernelName:Ht,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{paddings:o,constantValue:i}=s;An(r,"pad");const l=o.map(((e,t)=>e[0]+r.shape[t]+e[1])),d=o.map((e=>e[0])),c=a.data.get(r.dataId).values,p=e.sizeFromShape(r.shape),u=r.shape.length,h=e.computeStrides(r.shape),f=e.sizeFromShape(l),m=l.length,g=e.computeStrides(l),k=e.getTypedArrayFromDType(r.dtype,f);0!==i&&k.fill(i);for(let t=0;t<p;t++){const n=e.indexToLoc(t,u,h).map(((e,t)=>e+d[t]));k[e.locToIndex(n,m,g)]=c[t]}return{dataId:a.write(k,l,r.dtype),shape:l,dtype:r.dtype}}},hi={kernelName:Ct,backendName:"cpu",kernelFunc:qn(Ct,Rn(((e,t)=>Math.pow(e,t))))};const fi={kernelName:$t,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{paramsNestedSplits:s,paramsDenseValues:r,indices:o}=t,i=s.map((e=>n.data.get(e.dataId).values)),l=s.map((e=>e.shape)),d=n.data.get(r.dataId).values,c=n.data.get(o.dataId).values,[p,u,h]=Qa(i,l,d,r.shape,r.dtype,c,o.shape),f=p.map((e=>n.makeTensorInfo([e.length],"int32",e))),m=n.makeTensorInfo(h,r.dtype,u);return f.concat([m])}};const mi={kernelName:Ot,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{starts:a,limits:s,deltas:r}=t,o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[d,c]=Xa(o,a.shape,a.dtype,i,s.shape,l,r.shape);return[n.makeTensorInfo([d.length],"int32",d),n.makeTensorInfo([c.length],a.dtype,c)]}};const gi={kernelName:Vt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{shape:s,values:r,defaultValue:o,rowPartitionTensors:i}=t,{rowPartitionTypes:l}=a,d=n.data.get(s.dataId).values,c=n.data.get(r.dataId).values,p=n.data.get(o.dataId).values,u=i.map((e=>n.data.get(e.dataId).values)),h=i.map((e=>e.shape)),[f,m]=ss(d,s.shape,c,r.shape,r.dtype,p,o.shape,u,h,l);return n.makeTensorInfo(f,r.dtype,m)}};const ki={kernelName:_t,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,dtype:r,step:o}=n,i=rs(a,s,o,r);return t.makeTensorInfo([i.length],r,i)}},Ii={kernelName:Gt,backendName:"cpu",kernelFunc:ta(Gt,(e=>1/e))};const bi={kernelName:Bt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r}=n,{alignCorners:o,halfPixelCenters:i,size:l}=s;An(r,"resizeBilinear");const d=e.computeStrides(r.shape),[c,p]=l,[u,h,f,m]=r.shape,g=a.data.get(r.dataId).values,k=new Float32Array(e.sizeFromShape([u,c,p,m])),I=[o&&c>1?h-1:h,o&&p>1?f-1:f],b=[o&&c>1?c-1:c,o&&p>1?p-1:p];let y=0;const N=I[0]/b[0],T=I[1]/b[1];for(let e=0;e<u;e++)for(let t=0;t<c;t++){let n;n=i?N*(t+.5)-.5:N*t;const a=Math.max(0,Math.floor(n)),s=n-a,r=Math.min(h-1,Math.ceil(n)),o=e*d[0]+a*d[1],l=e*d[0]+r*d[1];for(let e=0;e<p;e++){let t;t=i?T*(e+.5)-.5:T*e;const n=Math.max(0,Math.floor(t)),a=t-n,r=Math.min(f-1,Math.ceil(t)),c=o+n*d[2],p=l+n*d[2],u=o+r*d[2],h=l+r*d[2];for(let e=0;e<m;e++){const t=g[c+e],n=g[p+e],r=t+(g[u+e]-t)*a,o=r+(n+(g[h+e]-n)*a-r)*s;k[y++]=o}}}return a.makeTensorInfo([u,c,p,m],"float32",k)}};const yi={kernelName:Lt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r,dy:o}=n,{alignCorners:i}=s;An([o,r],"resizeBilinearGrad");const l=e.computeStrides(r.shape),[d,c,p,u]=r.shape,[,h,f]=o.shape,m=new Float32Array(d*c*p*u),g=[i&&h>1?c-1:c,i&&f>1?p-1:p],k=[i&&h>1?h-1:h,i&&f>1?f-1:f],I=g[0]/k[0],b=g[1]/k[1],y=a.data.get(o.dataId).values;let N=0;for(let e=0;e<d;e++){const t=e*l[0];for(let e=0;e<h;e++){const n=e*I,a=Math.floor(n),s=Math.min(Math.ceil(n),c-1),r=t+a*l[1],o=t+s*l[1],i=n-a,d=1-i;for(let e=0;e<f;e++){const t=e*b,n=Math.floor(t),a=Math.min(Math.ceil(t),p-1),s=t-n,c=1-s,h=r+n*l[2],f=r+a*l[2],g=o+n*l[2],k=o+a*l[2],I=d*c,T=d*s,x=i*c,S=i*s;for(let e=0;e<u;e++){const t=y[N++];m[h+e]+=t*I,m[f+e]+=t*T,m[g+e]+=t*x,m[k+e]+=t*S}}}}return a.makeTensorInfo([d,p,c,u],"float32",m)}};const Ni={kernelName:qt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r}=n,{alignCorners:o,halfPixelCenters:i,size:l}=s;An(r,"resizeNearestNeighbor");const d=e.computeStrides(r.shape),[c,p]=l,[u,h,f,m]=r.shape,g=a.data.get(r.dataId).values,k=new Float32Array(u*c*p*m),I=[o&&c>1?h-1:h,o&&p>1?f-1:f],b=[o&&c>1?c-1:c,o&&p>1?p-1:p],y=I[0]/b[0],N=I[1]/b[1];let T=0;for(let e=0;e<u;e++){const t=e*d[0];for(let e=0;e<c;e++){const n=i?y*(e+.5):y*e;let a=Math.min(h-1,o?Math.round(n):Math.floor(n));i&&(a=Math.max(0,a));const s=t+a*d[1];for(let e=0;e<p;e++){const t=i?N*(e+.5):N*e;let n=Math.min(f-1,o?Math.round(t):Math.floor(t));i&&(n=Math.max(0,n));const a=s+n*d[2];for(let e=0;e<m;e++){const t=g[a+e];k[T++]=t}}}}return a.makeTensorInfo([u,c,p,m],r.dtype,k)}};const Ti={kernelName:Ut,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r,dy:o}=n,{alignCorners:i}=s;An([o,r],"resizeNearestNeighborGrad");const l=e.computeStrides(r.shape),d=e.computeStrides(o.shape),[c,p,u,h]=r.shape,[,f,m]=o.shape,g=new Float32Array(c*p*u*h),k=a.data.get(o.dataId).values,I=[i&&f>1?p-1:p,i&&m>1?u-1:u],b=[i&&f>1?f-1:f,i&&m>1?m-1:m],y=I[0]/b[0],N=I[1]/b[1],T=1/y,x=1/N,S=2*Math.ceil(T)+2,v=2*Math.ceil(x)+2;for(let e=0;e<c;e++){const t=e*l[0];for(let e=0;e<p;e++){const n=t+e*l[1],a=Math.floor(e*T),s=Math.floor(a-S/2);for(let a=0;a<u;a++){const r=n+a*l[2],o=Math.floor(a*x),c=Math.floor(o-v/2);for(let n=0;n<h;n++){let o=0;for(let r=0;r<S;r++){const l=r+s;if(l<0||l>=f)continue;const h=t+l*d[1],g=l*y;if(e===Math.min(p-1,i?Math.round(g):Math.floor(g)))for(let e=0;e<v;e++){const t=e+c;if(t<0||t>=m)continue;const s=h+t*d[2],r=t*N;a===Math.min(u-1,i?Math.round(r):Math.floor(r))&&(o+=k[s+n])}}g[r+n]=o}}}}return a.makeTensorInfo(r.shape,r.dtype,g)}};const xi={kernelName:Zt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{dims:o}=s;An(r,"reverse");const i=r.shape.length,l=e.parseAxisParam(o,r.shape);if(0===i)return $n({inputs:{x:r},backend:a});const d=new $(r.shape,r.dtype),c=a.bufferSync(r);for(let e=0;e<d.size;e++){const t=d.indexToLoc(e),n=t.slice();l.forEach((e=>n[e]=r.shape[e]-1-n[e])),d.set(c.get(...n),...t)}return a.makeTensorInfo(d.shape,d.dtype,d.values)}},Si={kernelName:Kt,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{image:s}=t,{radians:r,fillValue:i,center:l}=n,d=a,c=e.getTypedArrayFromDType(s.dtype,e.sizeFromShape(s.shape)),[p,u,h,f]=s.shape,[m,g]=o.getImageCenter(l,u,h),k=Math.sin(r),I=Math.cos(r),b=d.data.get(s.dataId).values;for(let e=0;e<p;e++){const t=e*h*u*f;for(let e=0;e<u;e++){const n=e*(h*f);for(let a=0;a<h;a++){const s=a*f;for(let r=0;r<f;r++){const o=[p,e,a,r],l=o[2],d=o[1];let y=(l-m)*I-(d-g)*k,N=(l-m)*k+(d-g)*I;y=Math.round(y+m),N=Math.round(N+g);let T=i;if("number"!=typeof i&&(T=3===r?255:i[r]),y>=0&&y<h&&N>=0&&N<u){T=b[t+N*(h*f)+y*f+r]}c[t+n+s+r]=T}}}}return{dataId:d.write(c,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}},vi={kernelName:jt,backendName:"cpu",kernelFunc:ta(jt,(e=>{const t=Math.floor(e);return e-t<.5?Math.floor(e):e-t>.5?Math.ceil(e):t%2==0?t:t+1}))};const Fi={kernelName:Yt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{indices:s,updates:r}=t,{shape:i}=a,{sliceRank:l,numUpdates:d,sliceSize:c,strides:p,outputSize:u}=o.calculateShapes(r,s,i),h=ls(n.bufferSync(s),n.bufferSync(r),i,u,c,d,l,p,0,!0);return n.makeTensorInfo(i,h.dtype,h.values)}};function wi(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<t?n=s+1:a=s;return a}function Mi(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<=t?n=s+1:a=s;return a}const Ai={kernelName:Jt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{sortedSequence:r,values:o}=n,{side:i}=s,l=function(t,n,a,s,r,o){const i=e.getArrayFromDType("int32",a*r);for(let e=0;e<a;++e){const a=t.slice(e*s,(e+1)*s),l=e*r;for(let e=0;e<r;++e)i[l+e]="left"===o?wi(a,n[e+l]):Mi(a,n[e+l])}return i}(a.data.get(r.dataId).values,a.data.get(o.dataId).values,r.shape[0],r.shape[1],o.shape[1],i);return a.makeTensorInfo(o.shape,"int32",l)}};const Di={kernelName:Qt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{condition:s,t:r,e:o}=n;An([s,r,o],"select");const i=s.shape.length,l=a.data.get(s.dataId).values,d=a.data.get(r.dataId).values,c=a.data.get(o.dataId).values,p=z(r.dtype,o.dtype),u=e.makeZerosTypedArray(e.sizeFromShape(r.shape),p);let h=0;const f=0===i||i>1||1===r.shape.length?1:e.sizeFromShape(r.shape.slice(1));for(let e=0;e<l.length;e++)for(let t=0;t<f;t++)1===l[e]?u[h++]=d[e]:u[h++]=c[e];return a.makeTensorInfo(r.shape,p,u)}},Ei=o.SELU_SCALEALPHA,zi=o.SELU_SCALE,Wi={kernelName:Xt,backendName:"cpu",kernelFunc:ta(Xt,(e=>e>=0?zi*e:Ei*(Math.exp(e)-1)))},Ri={kernelName:en,backendName:"cpu",kernelFunc:ta(en,(e=>e<0?-1:e>0?1:0))},Pi={kernelName:tn,backendName:"cpu",kernelFunc:ta(tn,(e=>Math.sin(e)))},Hi={kernelName:nn,backendName:"cpu",kernelFunc:ta(nn,(e=>Math.sinh(e)))},Ci=Math.log(1.1920928955078125e-7)+2,$i={kernelName:an,backendName:"cpu",kernelFunc:ta(an,(e=>{const t=e>-Ci,n=e<Ci,a=Math.exp(e);let s;return s=n?a:t?e:Math.log(1+a),s}))};const Oi={kernelName:sn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{blockShape:i,paddings:l}=s;An([r],"spaceToBatchND");const d=e.sizeFromShape(i),c=[[0,0]];c.push(...l);for(let e=1+i.length;e<r.shape.length;++e)c.push([0,0]);const p=ui.kernelFunc({inputs:{x:r},backend:a,attrs:{paddings:c,constantValue:0}}),u=o.getReshaped(p.shape,i,d,!1),h=o.getPermuted(u.length,i.length,!1),f=o.getReshapedPermuted(p.shape,i,d,!1),m=Qs({inputs:{x:p},backend:a,attrs:{shape:u}}),g=qa({inputs:{x:m},backend:a,attrs:{perm:h}}),k=Qs({inputs:{x:g},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(g),k}};const Vi={kernelName:rn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{indices:a,values:s,denseShape:r,defaultValue:o}=t;if(1!==r.shape.length)throw new Error(`Dense shape must be a vector, saw:\n        ${r.shape}`);if(2!==a.shape.length)throw new Error(`Indices must be a matrix, saw:\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Values must be a vector, saw:\n        ${s.shape}`);if(0!==o.shape.length)throw new Error(`Default value must be a scalar, saw:\n        ${o.shape}`);const i=n.data.get(a.dataId).values,l=n.data.get(s.dataId).values,d=n.data.get(r.dataId).values,c=n.data.get(o.dataId).values[0],[p,u,h,f,m]=ms(i,a.shape,a.dtype,l,s.dtype,d,c);return[n.makeTensorInfo(u,a.dtype,p),n.makeTensorInfo([u[0]],s.dtype,h),n.makeTensorInfo([f.length],"bool",new Uint8Array(f.map((e=>Number(e))))),n.makeTensorInfo([m.length],a.dtype,new Int32Array(m))]}};const _i={kernelName:on,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{inputIndices:a,inputShape:s,newShape:r}=t;if(2!==a.shape.length)throw new Error(`Input indices should be a matrix but received shape\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Input shape should be a vector but received shape\n        ${s.shape}`);if(1!==r.shape.length)throw new Error(`Target shape should be a vector but received shape ${r.shape}`);const o=Array.from(n.data.get(s.dataId).values),i=n.data.get(a.dataId).values,l=Array.from(n.data.get(r.dataId).values),[d,c,p]=gs(i,a.shape,a.dtype,o,l);return[n.makeTensorInfo(c,a.dtype,d),n.makeTensorInfo([p.length],r.dtype,new Int32Array(p))]}};const Gi={kernelName:ln,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n          ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n          ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[d,c]=ks(o,a.shape,a.dtype,i,l,!0);return n.makeTensorInfo(c,a.dtype,d)}};const Bi={kernelName:dn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n         ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n         ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[d,c]=ks(o,a.shape,a.dtype,i,l);return n.makeTensorInfo(c,a.dtype,d)}};const Li={kernelName:cn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{sparseIndices:r,sparseValues:i,defaultValue:l}=n,{outputShape:d}=s,{sliceRank:c,numUpdates:p,sliceSize:u,strides:h,outputSize:f}=o.calculateShapes(i,r,d),m=!1,g=a.bufferSync(r);let k;switch(i.dtype){case"bool":k=ls(g,a.bufferSync(i),d,f,u,p,c,h,Boolean(a.data.get(l.dataId).values[0]),m);break;case"float32":k=ls(g,a.bufferSync(i),d,f,u,p,c,h,a.data.get(l.dataId).values[0],m);break;case"int32":k=ls(g,a.bufferSync(i),d,f,u,p,c,h,a.data.get(l.dataId).values[0],m);break;case"string":k=ls(g,a.bufferSync(i),d,f,u,p,c,h,e.decodeString(a.data.get(l.dataId).values[0]),m);break;default:throw new Error(`Unsupported type ${i.dtype}`)}return a.makeTensorInfo(d,k.dtype,k.values)}};const qi={kernelName:pn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{numOrSizeSplits:i,axis:l}=s,d=e.parseAxisParam(l,r.shape)[0],c=o.prepareSplitSize(r,i,d),p=new Array(r.shape.length).fill(0),u=r.shape.slice();return c.map((e=>{const t=[...u];t[d]=e;const n=hs({inputs:{x:r},backend:a,attrs:{begin:p,size:t}});return p[d]+=e,n}))}},Ui={kernelName:un,backendName:"cpu",kernelFunc:({inputs:e,backend:t})=>{const{x:n}=e,a=t;An(n,"square");const s=a.data.get(n.dataId).values,r=new Float32Array(s.length);for(let e=0;e<s.length;++e){const t=s[e];r[e]=t*t}return{dataId:a.write(r,n.shape,n.dtype),shape:n.shape,dtype:n.dtype}}},Zi={kernelName:hn,backendName:"cpu",kernelFunc:ta(hn,((e,t)=>{const n=t;return isNaN(e)?NaN:e>0?1:n.alpha}))};const Ki={kernelName:fn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{begin:o,end:i,strides:l,beginMask:d,endMask:c,ellipsisMask:p,newAxisMask:u,shrinkAxisMask:h}=s;An(r,"stridedSlice");const{finalShapeSparse:f,finalShape:m,isIdentity:g,sliceDim0:k,isSimpleSlice:I,begin:b,end:y,strides:N}=V.sliceInfo(r.shape,o,i,l,d,c,p,u,h);let T;if(g)T=Qs({inputs:{x:r},backend:a,attrs:{shape:m}});else if(k||I){e.assert(r.shape.length>=1,(()=>`Input must have rank at least 1, got: ${r.shape.length}`));const t=V.computeOutShape(b,y,N),n=hs({inputs:{x:r},backend:a,attrs:{begin:b,size:t}});T=Qs({inputs:{x:n},backend:a,attrs:{shape:m}}),a.disposeIntermediateTensorInfo(n)}else{const e=Ss(f,a.bufferSync(r),N,b);T=a.makeTensorInfo(m,e.dtype,e.values)}return T}};const ji={kernelName:mn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{separator:s,nGramWidths:r,leftPad:o,rightPad:i,padWidth:l,preserveShortSequences:d}=a,{data:c,dataSplits:p}=t,u=n.data.get(c.dataId).values,h=n.data.get(p.dataId).values,[f,m]=Fs(u,h,s,r,o,i,l,d);return[n.makeTensorInfo([f.length],"string",f),n.makeTensorInfo(p.shape,"int32",m)]}};const Yi={kernelName:gn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{skipEmpty:s}=a,{input:r,delimiter:o}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(1!==r.shape.length)throw new Error(`Input must be a vector, got shape: ${r.shape}`);if(0!==o.shape.length)throw new Error(`Delimiter must be a scalar, got shape: ${o.shape}`);const i=n.data.get(r.dataId).values,l=n.data.get(o.dataId).values[0],[d,c,p]=Ms(i,l,s),u=c.length;return[n.makeTensorInfo([u,2],"int32",d),n.makeTensorInfo([u],"string",c),n.makeTensorInfo([2],"int32",new Int32Array(p))]}};const Ji={kernelName:kn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{numBuckets:s}=a,{input:r}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(s<=0)throw new Error("Number of buckets must be at least 1");const o=As(n.data.get(r.dataId).values,s);return n.makeTensorInfo(r.shape,"int32",o)}},Qi={kernelName:In,backendName:"cpu",kernelFunc:ta(In,(e=>Math.tan(e)))},Xi=ta(bn,(e=>Math.tanh(e)));const el={kernelName:xn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,attrs:a,backend:s}=t,{image:r,transforms:o}=n,{interpolation:i,fillMode:l,fillValue:d,outputShape:c}=a,[p,u,h,f]=r.shape,[m,g]=null!=c?c:[u,h],k=[p,m,g,f],I=e.computeStrides(r.shape),b=I[0],y=I[1],N=I[2],T=e.computeStrides(k),x=T[0],S=T[1],v=T[2],F=e.getTypedArrayFromDType(r.dtype,e.sizeFromShape(k));F.fill(d);const w=s.data.get(r.dataId).values,M=s.data.get(o.dataId).values;for(let e=0;e<p;++e){const t=1===o.shape[0]?M:M.subarray(8*e,8*e+8);for(let n=0;n<m;++n)for(let a=0;a<g;++a)for(let s=0;s<f;++s){let r;const o=t[6]*a+t[7]*n+1;if(0===o)continue;const c=(t[0]*a+t[1]*n+t[2])/o,p=(t[3]*a+t[4]*n+t[5])/o,f=tl(c,h,l),m=tl(p,u,l);switch(i){case"nearest":r=al(w,u,h,b,y,N,e,m,f,s,d);break;case"bilinear":r=sl(w,u,h,b,y,N,e,m,f,s,d);break;default:throw new Error(`Error in Transform: Expect 'nearest' or 'bilinear', but got ${i}`)}F[e*x+n*S+a*v+s]=r}return s.makeTensorInfo(k,r.dtype,F)}return{dataId:s.write(F,k,r.dtype),shape:r.shape,dtype:r.dtype}}};function tl(t,n,a){switch(a){case"reflect":return function(t,n){let a=t;if(a<0)if(n<=1)a=0;else{const e=2*n;a<e&&(a=e*Math.trunc(-a/e)+a),a=a<-n?a+e:-a-1}else if(a>n-1)if(n<=1)a=0;else{const e=2*n;a-=e*Math.trunc(a/e),a>=n&&(a=e-a-1)}return e.clamp(0,a,n-1)}(t,n);case"wrap":return function(t,n){let a=t;if(a<0)if(n<=1)a=0;else{const e=n-1;a+=n*(Math.trunc(-a/e)+1)}else if(a>n-1)if(n<=1)a=0;else{const e=n-1;a-=n*Math.trunc(a/e)}return e.clamp(0,a,n-1)}(t,n);case"nearest":return function(t,n){return e.clamp(0,t,n-1)}(t,n);default:return function(e,t){return e}(t)}}function nl(e,t,n,a,s,r,o,i,l,d,c){return 0<=i&&i<t&&0<=l&&l<n?e[o*a+i*s+l*r+d]:c}function al(e,t,n,a,s,r,o,i,l,d,c){return nl(e,t,n,a,s,r,o,Math.round(i),Math.round(l),d,c)}function sl(e,t,n,a,s,r,o,i,l,d,c){const p=Math.floor(i),u=Math.floor(l),h=p+1,f=u+1;return(h-i)*((f-l)*nl(e,t,n,a,s,r,o,p,u,d,c)+(l-u)*nl(e,t,n,a,s,r,o,p,f,d,c))+(i-p)*((f-l)*nl(e,t,n,a,s,r,o,h,u,d,c)+(l-u)*nl(e,t,n,a,s,r,o,h,f,d,c))}const rl=[nr,Wn,ar,sr,jn,rr,or,ir,lr,dr,cr,pr,ur,hr,fr,Ir,br,yr,Nr,tr,Tr,xr,Sr,Xn,vr,Ln,sa,Fr,Hn,wr,Er,Wr,Rr,Pr,Hr,Cr,$r,Or,Vr,_r,Gr,Br,Lr,qr,Zr,Kr,jr,Yr,Jr,Qr,Xr,eo,ao,_s,so,la,ho,pa,mo,ha,No,xo,So,ma,ka,vo,Fo,wo,Mo,Na,xa,On,Ao,Ar,Do,Eo,zo,Bs,va,wa,Wo,Da,Ro,Po,Ho,Co,$o,Oo,_o,Wa,Go,Bo,Lo,qo,Uo,Zo,Ko,Pa,jo,Yo,Xo,Oa,_a,ti,ai,ri,Ba,oi,di,pi,ui,hi,Us,Ka,fi,mi,gi,ki,_n,ko,Ii,Ks,Ys,Xs,bi,yi,Ni,Ti,xi,Si,vi,is,Fi,Ai,Di,Wi,ps,Ri,Pi,Hi,fs,Qo,$i,Oi,Vi,_i,Gi,Bi,Li,qi,bs,Ui,Ns,xs,Zi,Ki,ji,Yi,Ji,zs,no,Qi,{kernelName:bn,backendName:"cpu",kernelFunc:Xi},{kernelName:yn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{tensor:a,indices:s,updates:r}=t,{sliceRank:i,numUpdates:l,sliceSize:d,strides:c,outputSize:p}=o.calculateShapes(r,s,a.shape),u=n.bufferSync(s),h=n.bufferSync(r),f=n.bufferSync(a),m=ls(u,h,a.shape,p,d,l,i,c,f,!1);return n.makeTensorInfo(a.shape,m.dtype,m.values)}},{kernelName:Nn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{reps:r}=a;An(s,"tile");const o=Ws(n.bufferSync(s),r);return n.makeTensorInfo(o.shape,o.dtype,o.values)}},{kernelName:Tn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{k:r,sorted:o}=a;An(s,"topk");const i=n.data.get(s.dataId).values,[l,d]=Hs(i,s.shape,s.dtype,r,o);return[n.makeTensorInfo(l.shape,l.dtype,l.values),n.makeTensorInfo(d.shape,d.dtype,d.values)]}},el,Ua,{kernelName:Sn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,attrs:n,backend:a}=e,{axis:s}=n,{x:r}=t;An(r,"unique");const o=a.data.get(r.dataId).values,{outputValues:i,outputShape:l,indices:d}=Cs(o,s,r.shape,r.dtype);return[a.makeTensorInfo(l,r.dtype,i),a.makeTensorInfo([d.length],"int32",d)]}},{kernelName:vn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{value:s}=t;let{axis:r}=a;r<0&&(r+=s.shape.length);const o=s.shape.length,i=s.shape[r],l=new Array(o-1);let d=0;for(let e=0;e<o;e++)e!==r&&(l[d++]=s.shape[e]);const c=new Array(o).fill(0),p=s.shape.slice();p[r]=1;const u=new Array(i);for(let e=0;e<u.length;e++){c[r]=e;const t=hs({inputs:{x:s},backend:n,attrs:{begin:c,size:p}});u[e]=Qs({inputs:{x:t},backend:n,attrs:{shape:l}}),n.disposeIntermediateTensorInfo(t)}return u}},{kernelName:Fn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,segmentIds:o}=n,{numSegments:i}=s;An(r,"unsortedSegmentSum");const l=[],d=[],c=r.shape.length-o.shape.length;let p=o;for(let e=0;e<c;++e){const t=fo({inputs:{input:p},backend:a,attrs:{dim:e+1}});p=t,d.push(t)}for(let t=0;t<i;++t){const n=e.createScalarValue(t,"int32"),s=a.makeTensorInfo([],"int32",n),o=ia({inputs:{a:s,b:p},backend:a}),i=Bn({inputs:{x:o},backend:a,attrs:{dtype:"float32"}}),c=$a({inputs:{a:i,b:r},backend:a}),u=to({inputs:{x:c},backend:a,attrs:{axis:0,keepDims:!1}});l.push(u),d.push(s),d.push(o),d.push(i),d.push(c),d.push(u)}const u=ci({inputs:l,backend:a,attrs:{axis:0}});return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),u}},li];for(const e of rl)wn(e);export{En as MathBackendCPU,$s as shared,Os as version_cpu};
//# sourceMappingURL=tf-backend-cpu.fesm.min.js.map
