{"version": 3, "file": "tf-data.es2017.min.js", "sources": ["../../../../node_modules/seedrandom/lib/alea.js", "../../../../node_modules/seedrandom/lib/xor128.js", "../../../../node_modules/seedrandom/lib/xorwow.js", "../../../../node_modules/seedrandom/lib/xorshift7.js", "../../../../node_modules/seedrandom/lib/xor4096.js", "../../../../node_modules/seedrandom/lib/tychei.js", "../../../../node_modules/seedrandom/seedrandom.js", "../../../../node_modules/seedrandom/index.js", "../../../../tfjs-data/src/iterators/lazy_iterator.ts", "../../../../tfjs-data/src/util/deep_map.ts", "../../../../tfjs-data/src/util/deep_clone.ts", "../../../../tfjs-data/src/util/ring_buffer.ts", "../../../../tfjs-data/src/util/growing_ring_buffer.ts", "../../../../tfjs-data/src/dataset.ts", "../../../../tfjs-data/src/datasets/text_line_dataset.ts", "../../../../tfjs-data/src/datasets/csv_dataset.ts", "../../../../tfjs-data/src/iterators/microphone_iterator.ts", "../../../../tfjs-data/src/iterators/webcam_iterator.ts", "../../../../tfjs-data/src/datasource.ts", "../../../../tfjs-data/src/iterators/string_iterator.ts", "../../../../tfjs-data/src/iterators/byte_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/file_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/url_chunk_iterator.ts", "../../../../tfjs-data/src/util/source_util.ts", "../../../../tfjs-data/src/sources/file_data_source.ts", "../../../../tfjs-data/src/sources/url_data_source.ts", "../../../../tfjs-data/src/readers.ts", "../../../../tfjs-data/src/version.ts"], "sourcesContent": ["// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {Container} from '../types';\nimport {deepClone} from '../util/deep_clone';\nimport {deepMapAndAwaitAll, DeepMapAsyncResult, DeepMapResult, deepZip, zipToList} from '../util/deep_map';\nimport {GrowingRingBuffer} from '../util/growing_ring_buffer';\nimport {RingBuffer} from '../util/ring_buffer';\n\n/**\n * A nested structure of LazyIterators, used as the input to zip().\n */\nexport type IteratorContainer = Container<LazyIterator<tf.TensorContainer>>;\n\n// Here we implement a simple asynchronous iterator.\n// This lets us avoid using either third-party stream libraries or\n// recent TypeScript language support requiring polyfills.\n\n/**\n * Create a `LazyIterator` from an array of items.\n */\nexport function iteratorFromItems<T>(items: T[]): LazyIterator<T> {\n  return new ArrayIterator(items);\n}\n\n/**\n * Create a `LazyIterator` of incrementing integers.\n */\nexport function iteratorFromIncrementing(start: number): LazyIterator<number> {\n  let i = start;\n  return iteratorFromFunction(() => ({value: i++, done: false}));\n}\n\n/**\n * Create a `LazyIterator` from a function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * await iter.forEachAsync(e => console.log(e));\n * ```\n *\n * @param func A function that produces data on each call.\n */\nexport function iteratorFromFunction<T>(\n    func: () =>\n        IteratorResult<T>| Promise<IteratorResult<T>>): LazyIterator<T> {\n  return new FunctionCallIterator(func);\n}\n\n/**\n * Create a `LazyIterator` by concatenating underlying streams, which are\n * themselves provided as a stream.\n *\n * This can also be thought of as a \"stream flatten\" operation.\n *\n * @param baseIterators A stream of streams to be concatenated.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenated<T>(\n    baseIterators: LazyIterator<LazyIterator<T>>,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return new ChainedIterator(baseIterators, baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by concatenating streams produced by calling a\n * stream-generating function a given number of times.\n *\n * Since a `LazyIterator` is read-once, it cannot be repeated, but this\n * function can be used to achieve a similar effect:\n *\n *   LazyIterator.ofConcatenatedFunction(() => new MyIterator(), 6);\n *\n * @param iteratorFunc: A function that produces a new stream on each call.\n * @param count: The number of times to call the function.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenatedFunction<T>(\n    iteratorFunc: () => IteratorResult<LazyIterator<T>>, count: number,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return iteratorFromConcatenated(\n      iteratorFromFunction(iteratorFunc).take(count), baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by zipping together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nexport function iteratorFromZipped<O extends tf.TensorContainer>(\n    iterators: IteratorContainer,\n    mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL): LazyIterator<O> {\n  return new ZipIterator<O>(iterators, mismatchMode);\n}\n\n/**\n * An asynchronous iterator, providing lazy access to a potentially\n * unbounded stream of elements.\n *\n * Iterator can be obtained from a dataset:\n * `const iter = await dataset.iterator();`\n */\nexport abstract class LazyIterator<T> {\n  // This class implements AsyncIterator<T>, but we have not yet set the\n  // TypeScript --downlevelIteration flag to enable that.\n\n  abstract summary(): string;\n\n  /**\n   * Returns a `Promise` for the next element in the stream.\n   *\n   * When an item can be provided successfully, the return value is\n   * `{value:T, done:false}`.\n   *\n   * Calling next() on a closed stream returns `{value:null, done:true}`.\n   */\n  abstract next(): Promise<IteratorResult<T>>;\n\n  /**\n   * Collect all remaining elements of a bounded stream into an array.\n   * Obviously this will succeed only for small streams that fit in memory.\n   * Useful for testing.\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArray(): Promise<T[]> {\n    const result: T[] = [];\n    let x = await this.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await this.next();\n    }\n    return result;\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArrayForTest(): Promise<T[]> {\n    const stream = this.prefetch(100);\n    const result: T[] = [];\n    let x = await stream.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await stream.next();\n    }\n    return result;\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveFully(): Promise<void> {\n    let x = await this.next();\n    while (!x.done) {\n      x = await this.next();\n    }\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted, or a predicate fails.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveWhile(predicate: (r: T) => boolean): Promise<void> {\n    let x = await this.next();\n    let shouldContinue = predicate(x.value);\n    while ((!x.done) && shouldContinue) {\n      x = await this.next();\n      shouldContinue = predicate(x.value);\n    }\n  }\n\n  /**\n   * Handles errors thrown on this stream using a provided handler function.\n   *\n   * @param handler A function that handles any `Error` thrown during a `next()`\n   *   call and returns true if the stream should continue (dropping the failed\n   *   call) or false if the stream should quietly terminate.  If the handler\n   *   itself throws (or rethrows) an `Error`, that will be propagated.\n   *\n   * @returns A `LazyIterator` of elements passed through from upstream,\n   *   possibly filtering or terminating on upstream `next()` calls that\n   *   throw an `Error`.\n   */\n  handleErrors(handler: (error: Error) => boolean): LazyIterator<T> {\n    return new ErrorHandlingLazyIterator(this, handler);\n  }\n\n  // TODO(soergel): Implement reduce() etc.\n\n  /**\n   * Filters this stream according to `predicate`.\n   *\n   * @param predicate A function mapping a stream element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `LazyIterator` of elements for which the predicate was true.\n   */\n  filter(predicate: (value: T) => boolean): LazyIterator<T> {\n    return new FilterIterator(this, predicate);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  map<O>(transform: (value: T) => O): LazyIterator<O> {\n    return new MapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through an async 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a `Promise` for a\n   *   transformed stream element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  mapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform, forcing serial execution.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  serialMapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform).serial();\n  }\n\n  /**\n   * Maps this stream through a 1-to-many transform.\n   *\n   * @param transform A function mapping a stream element to an array of\n   *   transformed elements.\n   *\n   * @returns A `DataStream` of transformed elements.\n   */\n  flatmap<O>(transform: (value: T) => O[]): LazyIterator<O> {\n    return new FlatmapIterator(this, transform);\n  }\n\n  /**\n   * Apply a function to every element of the stream.\n   *\n   * @param f A function to apply to each stream element.\n   */\n  async forEachAsync(f: (value: T) => void): Promise<void> {\n    return this.map(f).resolveFully();\n  }\n\n  /**\n   * Apply a function to every element of the stream, forcing serial execution.\n   *\n   * @param f A function to apply to each stream element.  Should return 'true'\n   *   to indicate that the stream should continue, or 'false' to cause it to\n   *   terminate.\n   */\n  async serialForEach(f: (value: T) => Promise<boolean>): Promise<void> {\n    return this.serialMapAsync(f).resolveWhile(x => (x === true));\n  }\n\n  /**\n   * Groups elements into batches, represented as arrays of elements.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"Row-major\" means that the resulting batch is simply a collection of\n   * rows: `[row1, row2, row3, ...]`.  This is contrast to the column-major\n   * form, which is needed for vectorized computation.\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `LazyIterator` of batches of elements, represented as arrays\n   *   of the original element type.\n   */\n  rowMajorBatch(batchSize: number, smallLastBatch = true): LazyIterator<T[]> {\n    return new RowMajorBatchIterator(this, batchSize, smallLastBatch);\n  }\n\n  /**\n   * Groups elements into batches, represented in column-major form.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"column-major\" means that the resulting batch is a (potentially\n   * nested) structure representing the columns.  Each column entry, then,\n   * contains a collection of the values found in that column for a range of\n   * input elements.  This representation allows for vectorized computation, in\n   * contrast to the row-major form.\n   *\n   * The inputs should all have the same nested structure (i.e., of arrays and\n   * dicts).  The result is a single object with the same nested structure,\n   * where the leaves are arrays collecting the values of the inputs at that\n   * location (or, optionally, the result of a custom function applied to those\n   * arrays).\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @param zipFn: (optional) A function that expects an array of elements at a\n   *   single node of the object tree, and returns a `DeepMapResult`.  The\n   *   `DeepMapResult` either provides a result value for that node (i.e.,\n   *   representing the subtree), or indicates that the node should be processed\n   *   recursively.  The default zipFn recurses as far as possible and places\n   *   arrays at the leaves.\n   * @returns A `LazyIterator` of batches of elements, represented as an object\n   *   with collections at the leaves.\n   */\n  columnMajorBatch(\n      batchSize: number, smallLastBatch = true,\n      // tslint:disable-next-line:no-any\n      zipFn: (xs: any[]) => DeepMapResult = zipToList):\n      LazyIterator<tf.TensorContainer> {\n    // First collect the desired number of input elements as a row-major batch.\n    const rowBatches = this.rowMajorBatch(batchSize, smallLastBatch);\n    // Now 'rotate' or 'pivot' the data, collecting all values from each column\n    // in the batch (i.e., for each key within the elements) into an array.\n    return rowBatches.map(x => deepZip(x, zipFn));\n  }\n\n  /**\n   * Concatenate this `LazyIterator` with another.\n   *\n   * @param iterator A `LazyIterator` to be concatenated onto this one.\n   * @param baseErrorHandler An optional function that can intercept `Error`s\n   *   raised during a `next()` call on the base stream.  This function can\n   *   decide whether the error should be propagated, whether the error should\n   *   be ignored, or whether the base stream should be terminated.\n   * @returns A `LazyIterator`.\n   */\n  concatenate(\n      iterator: LazyIterator<T>,\n      baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n    return new ChainedIterator(\n        iteratorFromItems([this, iterator]), baseErrorHandler);\n  }\n\n  /**\n   * Limits this stream to return at most `count` items.\n   *\n   * @param count The maximum number of items to provide from the stream. If\n   * a negative or undefined value is given, the entire stream is returned\n   *   unaltered.\n   */\n  take(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new TakeIterator(this, count);\n  }\n\n  /**\n   * Skips the first `count` items in this stream.\n   *\n   * @param count The number of items to skip.  If a negative or undefined\n   * value is given, the entire stream is returned unaltered.\n   */\n  skip(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new SkipIterator(this, count);\n  }\n\n  /**\n   * Prefetch the first `bufferSize` items in this stream.\n   *\n   * Note this prefetches Promises, but makes no guarantees about when those\n   * Promises resolve.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   */\n  prefetch(bufferSize: number): LazyIterator<T> {\n    return new PrefetchIterator(this, bufferSize);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  /**\n   * Randomly shuffles the elements of this stream.\n   *\n   * @param bufferSize: An integer specifying the number of elements from\n   * this stream from which the new stream will sample.\n   * @param seed: (Optional.) An integer specifying the random seed that\n   * will be used to create the distribution.\n   */\n  shuffle(windowSize: number, seed?: string): LazyIterator<T> {\n    return new ShuffleIterator(this, windowSize, seed);\n  }\n\n  /**\n   * Force an iterator to execute serially: each next() call will await the\n   * prior one, so that they cannot execute concurrently.\n   */\n  serial(): LazyIterator<T> {\n    return new SerialIterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on LazyIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// Iterators that just extend LazyIterator directly\n// ============================================================================\n\nclass ArrayIterator<T> extends LazyIterator<T> {\n  private trav = 0;\n  constructor(protected items: T[]) {\n    super();\n  }\n\n  summary() {\n    return `Array of ${this.items.length} items`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.trav >= this.items.length) {\n      return {value: null, done: true};\n    }\n    const item = this.items[this.trav];\n    this.trav++;\n    return {value: deepClone(item), done: false};\n  }\n}\n\nclass FunctionCallIterator<T> extends LazyIterator<T> {\n  constructor(\n      protected nextFn: () => IteratorResult<T>| Promise<IteratorResult<T>>) {\n    super();\n  }\n\n  summary() {\n    return `Function call`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    try {\n      return this.nextFn();\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message =\n          `Error thrown while iterating through a dataset: ${e.message}`;\n      throw e;\n    }\n  }\n}\n\nclass SerialIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(protected upstream: LazyIterator<T>) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Serial`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    return this.upstream.next();\n  }\n}\n\nclass SkipIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  count = 0;\n\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Skip`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider tradeoffs of reading in parallel, eg.\n    // collecting next() promises in an Array and then waiting for\n    // Promise.all() of those. Benefit: pseudo-parallel execution.  Drawback:\n    // maybe delayed GC.\n    while (this.count++ < this.maxCount) {\n      const skipped = await this.upstream.next();\n      // short-circuit if upstream is already empty\n      if (skipped.done) {\n        return skipped;\n      }\n      tf.dispose(skipped.value as {});\n    }\n    return this.upstream.next();\n  }\n}\n\nclass TakeIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Take`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.count++ >= this.maxCount) {\n      return {value: null, done: true};\n    }\n    return this.upstream.next();\n  }\n}\n\n// Note this batch just groups items into row-wise element arrays.\n// Rotating these to a column-wise representation happens only at the dataset\n// level.\nclass RowMajorBatchIterator<T> extends LazyIterator<T[]> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T[]>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected batchSize: number,\n      protected enableSmallLastBatch = true) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> RowMajorBatch`;\n  }\n\n  async next(): Promise<IteratorResult<T[]>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T[]>> {\n    const batch: T[] = [];\n    while (batch.length < this.batchSize) {\n      const item = await this.upstream.next();\n      if (item.done) {\n        if (this.enableSmallLastBatch && batch.length > 0) {\n          return {value: batch, done: false};\n        }\n        return {value: null, done: true};\n      }\n      batch.push(item.value);\n    }\n    return {value: batch, done: false};\n  }\n}\n\nclass FilterIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected predicate: (value: T) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Filter`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      const item = await this.upstream.next();\n      if (item.done || this.predicate(item.value)) {\n        return item;\n      }\n      tf.dispose(item.value as {});\n    }\n  }\n}\n\nclass MapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Map`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\nclass ErrorHandlingLazyIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected handler: (error: Error) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> handleErrors`;\n  }\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      try {\n        return await this.upstream.next();\n      } catch (e) {\n        if (!this.handler(e)) {\n          return {value: null, done: true};\n        }\n        // If the handler returns true, loop and fetch the next upstream item.\n\n        // If the upstream iterator throws an endless stream of errors, and if\n        // the handler says to ignore them, then we loop forever here.  That is\n        // the correct behavior-- it's up to the handler to decide when to stop.\n      }\n    }\n  }\n}\n\nclass AsyncMapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => Promise<O>) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> AsyncMap`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = await this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\n// Iterators that maintain a queue of pending items\n// ============================================================================\n\n/**\n * A base class for transforming streams that operate by maintaining an\n * output queue of elements that are ready to return via next().  This is\n * commonly required when the transformation is 1-to-many:  A call to next()\n * may trigger a call to the underlying stream, which will produce many\n * mapped elements of this stream-- of which we need to return only one, so\n * we have to queue the rest.\n */\nexport abstract class OneToManyIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  protected outputQueue: RingBuffer<T>;\n\n  constructor() {\n    super();\n    this.outputQueue = new GrowingRingBuffer<T>();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  /**\n   * Read one or more chunks from upstream and process them, possibly\n   * reading or writing a carryover, and adding processed items to the\n   * output queue.  Note it's possible that no items are added to the queue\n   * on a given pump() call, even if the upstream stream is not closed\n   * (e.g., because items are filtered).\n   *\n   * @return `true` if any action was taken, i.e. fetching items from the\n   *   upstream source OR adding items to the output queue.  `false` if the\n   *   upstream source is exhausted AND nothing was added to the queue\n   * (i.e., any remaining carryover).\n   */\n  protected abstract pump(): Promise<boolean>;\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // Fetch so that the queue contains at least one item if possible.\n    // If the upstream source is exhausted, AND there are no items left in\n    // the output queue, then this stream is also exhausted.\n    while (this.outputQueue.length() === 0) {\n      // TODO(soergel): consider parallel reads.\n      if (!await this.pump()) {\n        return {value: null, done: true};\n      }\n    }\n    return {value: this.outputQueue.shift(), done: false};\n  }\n}\nclass FlatmapIterator<I, O> extends OneToManyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O[]) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Flatmap`;\n  }\n\n  async pump(): Promise<boolean> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return false;\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // that's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying any\n    // intermediate Tensors.  Here we are concerned only about the inputs.\n    const mappedArray = this.transform(item.value);\n    const outputTensors =\n        tf.tensor_util.getTensorsInContainer(mappedArray as {});\n    this.outputQueue.pushAll(mappedArray);\n\n    // TODO(soergel) faster intersection, and deduplicate outputTensors\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n\n    return true;\n  }\n}\n\n/**\n * Provides a `LazyIterator` that concatenates a stream of underlying\n * streams.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n */\nexport class ChainedIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>> = null;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private iterator: LazyIterator<T> = null;\n  private moreIterators: LazyIterator<LazyIterator<T>>;\n\n  constructor(\n      iterators: LazyIterator<LazyIterator<T>>,\n      private readonly baseErrorHandler?: (e: Error) => boolean) {\n    super();\n    this.moreIterators = iterators;\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of chained summaries';\n    return `${upstreamSummaries} -> Chained`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    this.lastRead = this.readFromChain(this.lastRead);\n    return this.lastRead;\n  }\n\n  private async readFromChain(lastRead: Promise<IteratorResult<T>>):\n      Promise<IteratorResult<T>> {\n    // Must await on the previous read since the previous read may have advanced\n    // the stream of streams, from which we need to read.\n    // This is unfortunate since we can't parallelize reads. Which means\n    // prefetching of chained streams is a no-op.\n    // One solution is to prefetch immediately upstream of this.\n    await lastRead;\n    if (this.iterator == null) {\n      const iteratorResult = await this.moreIterators.next();\n      if (iteratorResult.done) {\n        // No more streams to stream from.\n        return {value: null, done: true};\n      }\n      this.iterator = iteratorResult.value;\n      if (this.baseErrorHandler != null) {\n        this.iterator = this.iterator.handleErrors(this.baseErrorHandler);\n      }\n    }\n    const itemResult = await this.iterator.next();\n    if (itemResult.done) {\n      this.iterator = null;\n      return this.readFromChain(lastRead);\n    }\n    return itemResult;\n  }\n}\n\nexport enum ZipMismatchMode {\n  FAIL,      // require zipped streams to have the same length\n  SHORTEST,  // terminate zip when the first stream is exhausted\n  LONGEST    // use nulls for exhausted streams; use up the longest stream.\n}\n\n/**\n * Provides a `LazyIterator` that zips together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nclass ZipIterator<O extends tf.TensorContainer> extends LazyIterator<O> {\n  private count = 0;\n  private currentPromise: Promise<IteratorResult<O>> = null;\n\n  constructor(\n      protected readonly iterators: IteratorContainer,\n      protected readonly mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL) {\n    super();\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of zip summaries';\n    return `{${upstreamSummaries}} -> Zip`;\n  }\n\n  private async nextState(afterState: Promise<IteratorResult<O>>):\n      Promise<IteratorResult<O>> {\n    // This chaining ensures that the underlying next() are not even called\n    // before the previous ones have resolved.\n    await afterState;\n\n    // Collect underlying iterator \"done\" signals as a side effect in\n    // getNext()\n    let numIterators = 0;\n    let iteratorsDone = 0;\n\n    function getNext(container: IteratorContainer): DeepMapAsyncResult {\n      if (container instanceof LazyIterator) {\n        const result = container.next();\n        return {\n          value: result.then(x => {\n            numIterators++;\n            if (x.done) {\n              iteratorsDone++;\n            }\n            return x.value;\n          }),\n          recurse: false\n        };\n      } else {\n        return {value: null, recurse: true};\n      }\n    }\n\n    const mapped: O = await deepMapAndAwaitAll(this.iterators, getNext);\n\n    if (numIterators === iteratorsDone) {\n      // The streams have all ended.\n      return {value: null, done: true};\n    }\n    if (iteratorsDone > 0) {\n      switch (this.mismatchMode) {\n        case ZipMismatchMode.FAIL:\n          throw new Error(\n              'Zipped streams should have the same length. ' +\n              `Mismatched at element ${this.count}.`);\n        case ZipMismatchMode.SHORTEST:\n          return {value: null, done: true};\n        case ZipMismatchMode.LONGEST:\n        default:\n          // Continue.  The exhausted streams already produced value: null.\n      }\n    }\n\n    this.count++;\n    return {value: mapped, done: false};\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    this.currentPromise = this.nextState(this.currentPromise);\n    return this.currentPromise;\n  }\n}\n\n// Iterators that maintain a ring buffer of pending promises\n// ============================================================================\n\n/**\n * A stream that prefetches a given number of items from an upstream source,\n * returning them in FIFO order.\n *\n * Note this prefetches Promises, but makes no guarantees about when those\n * Promises resolve.\n */\nexport class PrefetchIterator<T> extends LazyIterator<T> {\n  protected buffer: RingBuffer<Promise<IteratorResult<T>>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected bufferSize: number) {\n    super();\n    this.buffer = new RingBuffer<Promise<IteratorResult<T>>>(bufferSize);\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Prefetch`;\n  }\n\n  /**\n   * Refill the prefetch buffer.  Returns only after the buffer is full, or\n   * the upstream source is exhausted.\n   */\n  protected refill() {\n    while (!this.buffer.isFull()) {\n      const v = this.upstream.next();\n      this.buffer.push(v);\n    }\n  }\n\n  next(): Promise<IteratorResult<T>> {\n    this.refill();\n    // This shift will never throw an error because the buffer is always\n    // full after a refill. If the stream is exhausted, the buffer will be\n    // full of Promises that will resolve to the end-of-stream signal.\n    return this.buffer.shift();\n  }\n}\n\n/**\n * A stream that performs a sliding-window random shuffle on an upstream\n * source. This is like a `PrefetchIterator` except that the items are\n * returned in randomized order.  Mixing naturally improves as the buffer\n * size increases.\n */\nexport class ShuffleIterator<T> extends PrefetchIterator<T> {\n  private readonly random: seedrandom.prng;\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private upstreamExhausted = false;\n\n  constructor(\n    protected override upstream: LazyIterator<T>, protected windowSize: number,\n      seed?: string) {\n    super(upstream, windowSize);\n    this.random = seedrandom.alea(seed || tf.util.now().toString());\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  override async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private randomInt(max: number) {\n    return Math.floor(this.random() * max);\n  }\n\n  protected chooseIndex(): number {\n    return this.randomInt(this.buffer.length());\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider performance\n    if (!this.upstreamExhausted) {\n      this.refill();\n    }\n    while (!this.buffer.isEmpty()) {\n      const chosenIndex = this.chooseIndex();\n      const result = await this.buffer.shuffleExcise(chosenIndex);\n      if (result.done) {\n        this.upstreamExhausted = true;\n      } else {\n        this.refill();\n        return result;\n      }\n    }\n    return {value: null, done: true};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\n\n// tslint:disable:no-any\n\n/**\n * A return value for a mapping function that can be applied via deepMap.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapResult = {\n  value: any,\n  recurse: boolean\n};\n\n/**\n * Apply a mapping function to a nested structure in a recursive manner.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapResult`.  The `DeepMapResult` either provides a\n *   replacement value for that node (i.e., replacing the subtree), or indicates\n *   that the node should be processed recursively.\n */\nexport function deepMap(input: any, mapFn: (x: any) => DeepMapResult): any|\n    any[] {\n  return deepMapInternal(input, mapFn);\n}\n\n/**\n * @param seen: A Map of known object mappings (i.e., memoized results of\n *   `mapFn()`)\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepMapInternal(\n    input: any, mapFn: (x: any) => DeepMapResult,\n    seen: Map<any, any> = new Map(), containedIn: Set<{}> = new Set()): any|\n    any[] {\n  if (input == null) {\n    return null;\n  }\n  if (typeof Blob === 'function' && input instanceof Blob) {\n    return input.slice();\n  }\n\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  if (seen.has(input)) {\n    return seen.get(input);\n  }\n  const result = mapFn(input);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep map function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    seen.set(input, result.value);\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const child = input[k];\n      const childResult = deepMapInternal(child, mapFn, seen, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    if (input.__proto__) {\n      mappedIterable.__proto__ = input.__proto__;\n    }\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// TODO(soergel, kangyizhang) Reconsider naming of deepZip() to avoid confusion\n// with zip()\n\n/**\n * Zip nested structures together in a recursive manner.\n *\n * This has the effect of transposing or pivoting data, e.g. converting it from\n * a row-major representation to a column-major representation.\n *\n * For example, `deepZip([{a: 1, b: 2}, {a: 3, b: 4}])` returns\n * `{a: [1, 3], b: [2, 4]}`.\n *\n * The inputs should all have the same nested structure (i.e., of arrays and\n * dicts).  The result is a single object with the same nested structure, where\n * the leaves are arrays collecting the values of the inputs at that location\n * (or, optionally, the result of a custom function applied to those arrays).\n *\n * @param inputs: An array of the objects to zip together.\n * @param zipFn: (optional) A function that expects an array of elements at a\n *   single node of the object tree, and returns a `DeepMapResult`.  The\n *   `DeepMapResult` either provides a result value for that node (i.e.,\n *   representing the subtree), or indicates that the node should be processed\n *   recursively.  The default zipFn recurses as far as possible and places\n *   arrays at the leaves.\n */\nexport function deepZip(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult = zipToList): any|any[] {\n  return deepZipInternal(inputs, zipFn);\n}\n\n/**\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepZipInternal(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult,\n    containedIn: Set<{}> = new Set()): any|any[] {\n  // The recursion follows the structure of input 0; it's assumed that all the\n  // other inputs have the same structure.\n  const input = inputs[0];\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  const result = zipFn(inputs);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep zip function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const children = inputs.map(x => x[k]);\n      const childResult = deepZipInternal(children, zipFn, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// tslint:disable-next-line:no-any\nexport function zipToList(x: any[]): DeepMapResult {\n  if (x === null) {\n    return null;\n  }\n  // TODO(soergel): validate array type?\n\n  if (isIterable(x[0])) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: x, recurse: false};\n  }\n}\n\n/**\n * A return value for an async map function for use with deepMapAndAwaitAll.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapAsyncResult = {\n  value: Promise<any>,\n  recurse: boolean\n};\n\n/**\n * Apply an async mapping function to a nested structure in a recursive manner.\n *\n * This first creates a nested structure of Promises, and then awaits all of\n * those, resulting in a single Promise for a resolved nested structure.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapAsyncResult`.  The `DeepMapAsyncResult` either provides\n *   a `Promise` for a replacement value for that node (i.e., replacing the\n *   subtree), or indicates that the node should be processed recursively.  Note\n *   that the decision whether or not to recurse must be made immediately; only\n *   the mapped value may be promised.\n */\nexport async function deepMapAndAwaitAll(\n    input: any, mapFn: (x: any) => DeepMapAsyncResult): Promise<any|any[]> {\n  const seen: Map<any, any> = new Map();\n\n  // First do a normal deepMap, collecting Promises in 'seen' as a side effect.\n  deepMapInternal(input, mapFn, seen);\n\n  // Replace the Promises in 'seen' in place.\n  // Note TypeScript provides no async map iteration, and regular map iteration\n  // is broken too, so sadly we have to do Array.from() to make it work.\n  // (There's no advantage to Promise.all(), and that would be tricky anyway.)\n  for (const key of Array.from(seen.keys())) {\n    const value = seen.get(key);\n    if (tf.util.isPromise(value)) {\n      const mappedValue = await value;\n      seen.set(key, mappedValue);\n    }\n  }\n\n  // Normal deepMap again, this time filling in the resolved values.\n  // It's unfortunate that we have to do two passes.\n  // TODO(soergel): test performance and think harder about a fast solution.\n  const result = deepMapInternal(input, mapFn, seen);\n  return result;\n}\n\n/**\n * Determine whether the argument is iterable.\n *\n * @returns true if the argument is an array or any non-Tensor object.\n */\n// tslint:disable-next-line:no-any\nexport function isIterable(obj: any): boolean {\n  let isTextDecoder = false;\n  if (tf.env().get('IS_BROWSER')) {\n    isTextDecoder = obj instanceof TextDecoder;\n  } else {\n    // tslint:disable-next-line:no-require-imports\n    const {StringDecoder} = require('string_decoder');\n    isTextDecoder = obj instanceof StringDecoder;\n  }\n  return obj != null && (!ArrayBuffer.isView(obj)) &&\n      (Array.isArray(obj) ||\n       (typeof obj === 'object' && !(obj instanceof tf.Tensor) &&\n        !(obj instanceof Promise) && !isTextDecoder));\n}\n\n/**\n * Determine whether the argument can be converted to Tensor.\n *\n * Tensors, primitives, arrays, and TypedArrays all qualify; anything else does\n * not.\n *\n * @returns true if the argument can be converted to Tensor.\n */\n// tslint:disable-next-line:no-any\nexport function canTensorify(obj: any): boolean {\n  return obj == null || isPrimitive(obj) || Array.isArray(obj) ||\n      (typeof obj === 'object' && (obj instanceof tf.Tensor)) ||\n      tf.util.isTypedArray(obj);\n}\n\n/**\n * Returns true if the given `value` is a primitive type. Otherwise returns\n * false. This is equivalant to node util.isPrimitive\n */\nfunction isPrimitive(value: any): boolean {\n  return (\n      value === null ||\n      (typeof value !== 'object' && typeof value !== 'function'));\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {RingBuffer} from './ring_buffer';\n\nexport class GrowingRingBuffer<T> extends RingBuffer<T> {\n  private static INITIAL_CAPACITY = 32;\n\n  /**\n   * Constructs a `GrowingRingBuffer`.\n   */\n  constructor() {\n    super(GrowingRingBuffer.INITIAL_CAPACITY);\n  }\n\n  override isFull() {\n    return false;\n  }\n\n  override push(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.push(value);\n  }\n\n  override unshift(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.unshift(value);\n  }\n\n  /**\n   * Doubles the capacity of the buffer.\n   */\n  private expand() {\n    const newCapacity = this.capacity * 2;\n    const newData = new Array<T>(newCapacity);\n    const len = this.length();\n\n    // Rotate the buffer to start at index 0 again, since we can't just\n    // allocate more space at the end.\n    for (let i = 0; i < len; i++) {\n      newData[i] = this.get(this.wrap(this.begin + i));\n    }\n\n    this.data = newData;\n    this.capacity = newCapacity;\n    this.doubledCapacity = 2 * this.capacity;\n    this.begin = 0;\n    this.end = len;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\n\n/**\n * Represents a potentially large collection of text lines.\n *\n * The results are not batched.\n */\nexport class TextLineDataset extends Dataset<string> {\n  /**\n   * Create a `TextLineDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   */\n  constructor(protected readonly input: DataSource) {\n    super();\n  }\n\n  async iterator(): Promise<LazyIterator<string>> {\n    const inputIterator = await this.input.iterator();\n    const utf8Iterator = inputIterator.decodeUTF8();\n    const lineIterator = utf8Iterator.split('\\n').map(line => {\n      // Windows/DOS format text file has extra line breaker at the end of line.\n      if (line.endsWith('\\r')) {\n        line = line.slice(0, -1);\n      }\n      return line;\n    });\n    return lineIterator;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\nimport {ColumnConfig, CSVConfig} from '../types';\nimport {TextLineDataset} from './text_line_dataset';\n\nconst CODE_QUOTE = '\"';\nconst STATE_OUT = Symbol('out');\nconst STATE_FIELD = Symbol('field');\nconst STATE_QUOTE = Symbol('quote');\nconst STATE_QUOTE_AFTER_QUOTE = Symbol('quoteafterquote');\nconst STATE_WITHIN_QUOTE_IN_QUOTE = Symbol('quoteinquote');\n\n/**\n * Represents a potentially large collection of delimited text records.\n *\n * The produced `TensorContainer`s each contain one key-value pair for\n * every column of the table.  When a field is empty in the incoming data, the\n * resulting value is `undefined`, or throw error if it is required.  Values\n * that can be parsed as numbers are emitted as type `number`, other values\n * are parsed as `string`.\n *\n * The results are not batched.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport class CSVDataset extends Dataset<TensorContainer> {\n  base: TextLineDataset;\n  private hasHeader = true;\n  private fullColumnNames: string[] = null;\n  private columnNamesValidated = false;\n  private columnConfigs: {[key: string]: ColumnConfig} = null;\n  private configuredColumnsOnly = false;\n  private delimiter = ',';\n  private delimWhitespace = false;\n\n  /**\n   * Returns column names of the csv dataset. If `configuredColumnsOnly` is\n   * true, return column names in `columnConfigs`. If `configuredColumnsOnly` is\n   * false and `columnNames` is provided, `columnNames`. If\n   * `configuredColumnsOnly` is false and `columnNames` is not provided, return\n   * all column names parsed from the csv file. For example usage please go to\n   * `tf.data.csv`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async columnNames() {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    return this.configuredColumnsOnly ? Object.keys(this.columnConfigs) :\n                                        this.fullColumnNames;\n  }\n\n  /* 1) If `columnNames` is provided as string[], use this string[] as output\n   * keys in corresponding order. The length must match the number of inferred\n   * columns if `hasHeader` is true .\n   * 2) If `columnNames` is not provided, parse header line as `columnNames` if\n   * hasHeader is true. If `hasHeader` is false, throw an error.\n   * 3) If `columnConfigs` is provided, all the keys in `columnConfigs` must\n   * exist in parsed `columnNames`.\n   */\n  private async setColumnNames() {\n    const columnNamesFromFile = await this.maybeReadHeaderLine();\n    if (!this.fullColumnNames && !columnNamesFromFile) {\n      // Throw an error if columnNames is not provided and no header line.\n      throw new Error(\n          'Column names must be provided if there is no header line.');\n    } else if (this.fullColumnNames && columnNamesFromFile) {\n      // Check provided columnNames match header line.\n      util.assert(\n          columnNamesFromFile.length === this.fullColumnNames.length,\n          () => 'The length of provided columnNames (' +\n              this.fullColumnNames.length.toString() +\n              ') does not match the length of the header line read from ' +\n              'file (' + columnNamesFromFile.length.toString() + ').');\n    }\n    if (!this.fullColumnNames) {\n      this.fullColumnNames = columnNamesFromFile;\n    }\n    // Check if there are duplicate column names.\n    const counts: {[key: string]: number} = this.fullColumnNames.reduce(\n        (countAcc: {[key: string]: number}, name) => {\n          countAcc[name] = (countAcc[name] + 1) || 1;\n          return countAcc;\n        },\n        {});\n    const duplicateNames =\n        Object.keys(counts).filter((name) => (counts[name] > 1));\n    util.assert(\n        duplicateNames.length === 0,\n        () => 'Duplicate column names found: ' + duplicateNames.toString());\n    // Check if keys in columnConfigs match columnNames.\n    if (this.columnConfigs) {\n      for (const key of Object.keys(this.columnConfigs)) {\n        const index = this.fullColumnNames.indexOf(key);\n        if (index === -1) {\n          throw new Error(\n              'The key \"' + key +\n              '\" provided in columnConfigs does not match any of the column ' +\n              'names (' + this.fullColumnNames.toString() + ').');\n        }\n      }\n    }\n    this.columnNamesValidated = true;\n  }\n\n  private async maybeReadHeaderLine() {\n    if (this.hasHeader) {\n      const iter = await this.base.iterator();\n      const firstElement = await iter.next();\n      if (firstElement.done) {\n        throw new Error('No data was found for CSV parsing.');\n      }\n      const firstLine: string = firstElement.value;\n      const headers = this.parseRow(firstLine, false);\n      return headers;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Create a `CSVDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   * @param csvConfig (Optional) A CSVConfig object that contains configurations\n   *     of reading and decoding from CSV file(s).\n   *\n   *     hasHeader: (Optional) A boolean value that indicates whether the first\n   *     row of provided CSV file is a header line with column names, and should\n   *     not be included in the data. Defaults to `true`.\n   *\n   *     columnNames: (Optional) A list of strings that corresponds to\n   *     the CSV column names, in order. If provided, it ignores the column\n   *     names inferred from the header row. If not provided, infers the column\n   *     names from the first row of the records. If hasHeader is false and\n   *     columnNames is not provided, this method throws an error.\n   *\n   *     columnConfigs: (Optional) A dictionary whose key is column names, value\n   *     is an object stating if this column is required, column's data type,\n   *     default value, and if this column is label. If provided, keys must\n   *     correspond to names provided in columnNames or inferred from the file\n   *     header lines. If isLabel is true any column, returns an array of two\n   *     items: the first item is a dict of features key/value pairs, the second\n   *     item is a dict of labels key/value pairs. If no feature is marked as\n   *     label, returns a dict of features only.\n   *\n   *     configuredColumnsOnly (Optional) If true, only columns provided in\n   *     columnConfigs will be parsed and provided during iteration.\n   *\n   *     delimiter (Optional) The string used to parse each line of the input\n   *     file. Defaults to `,`.\n   */\n  constructor(protected readonly input: DataSource, csvConfig?: CSVConfig) {\n    super();\n    this.base = new TextLineDataset(input);\n    if (!csvConfig) {\n      csvConfig = {};\n    }\n    this.hasHeader = csvConfig.hasHeader === false ? false : true;\n    this.fullColumnNames = csvConfig.columnNames;\n    this.columnConfigs = csvConfig.columnConfigs;\n    this.configuredColumnsOnly = csvConfig.configuredColumnsOnly;\n    if (csvConfig.delimWhitespace) {\n      util.assert(\n          csvConfig.delimiter == null,\n          () =>\n              'Delimiter should not be provided when delimWhitespace is true.');\n      this.delimWhitespace = true;\n      this.delimiter = ' ';\n    } else {\n      this.delimiter = csvConfig.delimiter ? csvConfig.delimiter : ',';\n    }\n  }\n\n  async iterator(): Promise<LazyIterator<TensorContainer>> {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    let lines = await this.base.iterator();\n    if (this.hasHeader) {\n      // We previously read the first line to get the columnNames.\n      // Now that we're providing data, skip it.\n      lines = lines.skip(1);\n    }\n    return lines.map(x => this.makeDataElement(x));\n  }\n\n  makeDataElement(line: string): TensorContainer {\n    const values = this.parseRow(line);\n    const features: {[key: string]: TensorContainer} = {};\n    const labels: {[key: string]: TensorContainer} = {};\n\n    for (let i = 0; i < this.fullColumnNames.length; i++) {\n      const key = this.fullColumnNames[i];\n      const config = this.columnConfigs ? this.columnConfigs[key] : null;\n      if (this.configuredColumnsOnly && !config) {\n        // This column is not selected.\n        continue;\n      } else {\n        const value = values[i];\n        let parsedValue = null;\n        if (value === '') {\n          // If default value is provided, use it. If default value is not\n          // provided, set as undefined.\n          if (config && config.default !== undefined) {\n            parsedValue = config.default;\n          } else if (config && (config.required || config.isLabel)) {\n            throw new Error(\n                `Required column ${key} is empty in this line: ${line}`);\n          } else {\n            parsedValue = undefined;\n          }\n        } else {\n          // A value is present, so parse it based on type\n          const valueAsNum = Number(value);\n          if (isNaN(valueAsNum)) {\n            // The value is a string and this column is declared as boolean\n            // in config, parse it as boolean.\n            if (config && config.dtype === 'bool') {\n              parsedValue = this.getBoolean(value);\n            } else {\n              // Set value as string\n              parsedValue = value;\n            }\n          } else if (!config || !config.dtype) {\n            // If this value is a number and no type config is provided, return\n            // it as number.\n            parsedValue = valueAsNum;\n          } else {\n            // If this value is a number and data type is provided, parse it\n            // according to provided data type.\n            switch (config.dtype) {\n              case 'float32':\n                parsedValue = valueAsNum;\n                break;\n              case 'int32':\n                parsedValue = Math.floor(valueAsNum);\n                break;\n              case 'bool':\n                parsedValue = this.getBoolean(value);\n                break;\n              default:\n                parsedValue = valueAsNum;\n            }\n          }\n        }\n        // Check if this column is label.\n        (config && config.isLabel) ? labels[key] = parsedValue :\n                                     features[key] = parsedValue;\n      }\n    }\n    // If label exists, return an object of features and labels as {xs:features,\n    // ys:labels}, otherwise return features only.\n    if (Object.keys(labels).length === 0) {\n      return features;\n\n    } else {\n      return {xs: features, ys: labels};\n    }\n  }\n\n  private getBoolean(value: string): number {\n    if (value === '1' || value.toLowerCase() === 'true') {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n\n  // adapted from https://beta.observablehq.com/@mbostock/streaming-csv\n  private parseRow(line: string, validateElementCount = true): string[] {\n    const result: string[] = [];\n    let readOffset = 0;\n    const readLength = line.length;\n    let currentState = STATE_OUT;\n    // Goes through the line to parse quote.\n    for (let i = 0; i < readLength; i++) {\n      switch (currentState) {\n        // Before enter a new field\n        case STATE_OUT:\n          switch (line.charAt(i)) {\n            // Enter a quoted field\n            case CODE_QUOTE:\n              readOffset = i + 1;\n              currentState = STATE_QUOTE;\n              break;\n            // Read an empty field\n            case this.delimiter:\n              readOffset = i + 1;\n              // If delimiter is white space and configured to collapse\n              // multiple white spaces, ignore this white space.\n              if (this.delimiter === ' ' && this.delimWhitespace) {\n                break;\n              }\n              result.push('');\n              currentState = STATE_OUT;\n              break;\n            // Enter an unquoted field\n            default:\n              currentState = STATE_FIELD;\n              readOffset = i;\n              break;\n          }\n          break;\n        // In an unquoted field\n        case STATE_FIELD:\n          switch (line.charAt(i)) {\n            // Exit an unquoted field, add it to result\n            case this.delimiter:\n              result.push(line.substring(readOffset, i));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            default:\n          }\n          break;\n        // In a quoted field\n        case STATE_QUOTE:\n          switch (line.charAt(i)) {\n            // Read a quote after a quote\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE_AFTER_QUOTE;\n              break;\n            default:\n          }\n          break;\n        // This state means it's right after a second quote in a field\n        case STATE_QUOTE_AFTER_QUOTE:\n          switch (line.charAt(i)) {\n            // Finished a quoted field\n            case this.delimiter:\n              result.push(line.substring(readOffset, i - 1));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            // Finished a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            // In a quoted part in a quoted field\n            default:\n              currentState = STATE_WITHIN_QUOTE_IN_QUOTE;\n              break;\n          }\n          break;\n        case STATE_WITHIN_QUOTE_IN_QUOTE:\n          switch (line.charAt(i)) {\n            // Exit a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            default:\n          }\n          break;\n        default:\n      }\n    }\n    // Adds last item based on if it is quoted.\n    if (currentState === STATE_QUOTE_AFTER_QUOTE) {\n      result.push(line.substring(readOffset, readLength - 1));\n    } else {\n      result.push(line.substring(readOffset));\n    }\n    // Check if each row has the same number of elements as column names.\n    if (validateElementCount && result.length !== this.fullColumnNames.length) {\n      throw new Error(`Invalid row in csv file. Should have ${\n          this.fullColumnNames.length} elements in a row, but got ${result}`);\n    }\n    return result;\n  }\n}\n\n// TODO(soergel): add more basic datasets for parity with tf.data\n// tf.data.FixedLengthRecordDataset()\n// tf.data.TFRecordDataset()\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env, Tensor, tensor, Tensor2D, Tensor3D, TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {MicrophoneConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of tensors from microphone audio stream. The tensors are\n * representing audio data as frequency-domain spectrogram generated with\n * browser's native FFT. Tensors representing time-domain waveform is available\n * based on configuration. Only works in browser environment.\n */\nexport class MicrophoneIterator extends LazyIterator<TensorContainer> {\n  private isClosed = false;\n  private stream: MediaStream;\n  private readonly fftSize: number;\n  private readonly columnTruncateLength: number;\n  private freqData: Float32Array;\n  private timeData: Float32Array;\n  private readonly numFrames: number;\n  private analyser: AnalyserNode;\n  private audioContext: AudioContext;\n  private sampleRateHz: number;\n  private readonly audioTrackConstraints: MediaTrackConstraints;\n  private readonly smoothingTimeConstant: number;\n  private readonly includeSpectrogram: boolean;\n  private readonly includeWaveform: boolean;\n\n  private constructor(protected readonly microphoneConfig: MicrophoneConfig) {\n    super();\n    this.fftSize = microphoneConfig.fftSize || 1024;\n    const fftSizeLog2 = Math.log2(this.fftSize);\n    if (this.fftSize < 0 || fftSizeLog2 < 4 || fftSizeLog2 > 14 ||\n        !Number.isInteger(fftSizeLog2)) {\n      throw new Error(\n          `Invalid fftSize: it must be a power of 2 between ` +\n          `2 to 4 and 2 to 14, but got ${this.fftSize}`);\n    }\n\n    this.numFrames = microphoneConfig.numFramesPerSpectrogram || 43;\n    this.sampleRateHz = microphoneConfig.sampleRateHz;\n    this.columnTruncateLength =\n        microphoneConfig.columnTruncateLength || this.fftSize;\n    this.audioTrackConstraints = microphoneConfig.audioTrackConstraints;\n    this.smoothingTimeConstant = microphoneConfig.smoothingTimeConstant || 0;\n\n    this.includeSpectrogram =\n        microphoneConfig.includeSpectrogram === false ? false : true;\n    this.includeWaveform =\n        microphoneConfig.includeWaveform === true ? true : false;\n    if (!this.includeSpectrogram && !this.includeWaveform) {\n      throw new Error(\n          'Both includeSpectrogram and includeWaveform are false. ' +\n          'At least one type of data should be returned.');\n    }\n  }\n\n  summary() {\n    return `microphone`;\n  }\n\n  // Construct a MicrophoneIterator and start the audio stream.\n  static async create(microphoneConfig: MicrophoneConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'microphone API is only supported in browser environment.');\n    }\n\n    const microphoneIterator = new MicrophoneIterator(microphoneConfig);\n\n    // Call async function start() to initialize the audio stream.\n    await microphoneIterator.start();\n\n    return microphoneIterator;\n  }\n\n  // Start the audio stream and FFT.\n  async start(): Promise<void> {\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        audio: this.audioTrackConstraints == null ? true :\n                                                    this.audioTrackConstraints,\n        video: false\n      });\n    } catch (e) {\n      throw new Error(\n          `Error thrown while initializing video stream: ${e.message}`);\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain audio from microphone.');\n    }\n\n    const ctxConstructor =\n        // tslint:disable-next-line:no-any\n        (window as any).AudioContext || (window as any).webkitAudioContext;\n    this.audioContext = new ctxConstructor();\n\n    if (!this.sampleRateHz) {\n      // If sample rate is not provided, use the available sample rate on\n      // device.\n      this.sampleRateHz = this.audioContext.sampleRate;\n    } else if (this.audioContext.sampleRate !== this.sampleRateHz) {\n      throw new Error(\n          `Mismatch in sampling rate: ` +\n          `Expected: ${this.sampleRateHz}; ` +\n          `Actual: ${this.audioContext.sampleRate}`);\n    }\n\n    const streamSource = this.audioContext.createMediaStreamSource(this.stream);\n    this.analyser = this.audioContext.createAnalyser();\n    this.analyser.fftSize = this.fftSize * 2;\n    this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;\n    streamSource.connect(this.analyser);\n    this.freqData = new Float32Array(this.fftSize);\n    this.timeData = new Float32Array(this.fftSize);\n    return;\n  }\n\n  async next(): Promise<IteratorResult<TensorContainer>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let spectrogramTensor: Tensor;\n    let waveformTensor: Tensor;\n\n    const audioDataQueue = await this.getAudioData();\n    if (this.includeSpectrogram) {\n      const freqData = this.flattenQueue(audioDataQueue.freqDataQueue);\n      spectrogramTensor = this.getTensorFromAudioDataArray(\n          freqData, [this.numFrames, this.columnTruncateLength, 1]);\n    }\n    if (this.includeWaveform) {\n      const timeData = this.flattenQueue(audioDataQueue.timeDataQueue);\n      waveformTensor = this.getTensorFromAudioDataArray(\n          timeData, [this.numFrames * this.fftSize, 1]);\n    }\n\n    return {\n      value: {'spectrogram': spectrogramTensor, 'waveform': waveformTensor},\n      done: false\n    };\n  }\n\n  // Capture one result from the audio stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<{spectrogram: Tensor3D, waveform: Tensor2D}> {\n    return (await this.next()).value as\n        {spectrogram: Tensor3D, waveform: Tensor2D};\n  }\n\n  private async getAudioData():\n      Promise<{freqDataQueue: Float32Array[], timeDataQueue: Float32Array[]}> {\n    const freqDataQueue: Float32Array[] = [];\n    const timeDataQueue: Float32Array[] = [];\n    let currentFrames = 0;\n    return new Promise(resolve => {\n      const intervalID = setInterval(() => {\n        if (this.includeSpectrogram) {\n          this.analyser.getFloatFrequencyData(this.freqData);\n          // If the audio stream is initializing, return empty queue.\n          if (this.freqData[0] === -Infinity) {\n            resolve({freqDataQueue, timeDataQueue});\n          }\n          freqDataQueue.push(this.freqData.slice(0, this.columnTruncateLength));\n        }\n        if (this.includeWaveform) {\n          this.analyser.getFloatTimeDomainData(this.timeData);\n          timeDataQueue.push(this.timeData.slice());\n        }\n\n        // Clean interval and return when all frames have been collected\n        if (++currentFrames === this.numFrames) {\n          clearInterval(intervalID);\n          resolve({freqDataQueue, timeDataQueue});\n        }\n      }, this.fftSize / this.sampleRateHz * 1e3);\n    });\n  }\n\n  // Stop the audio stream and pause the iterator.\n  stop(): void {\n    if (!this.isClosed) {\n      this.isClosed = true;\n      this.analyser.disconnect();\n      this.audioContext.close();\n      if (this.stream != null && this.stream.getTracks().length > 0) {\n        this.stream.getTracks()[0].stop();\n      }\n    }\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor[]> {\n    throw new Error('Can not convert infinite audio stream to array.');\n  }\n\n  // Return audio sampling rate in Hz\n  getSampleRate(): number {\n    return this.sampleRateHz;\n  }\n\n  private flattenQueue(queue: Float32Array[]): Float32Array {\n    const frameSize = queue[0].length;\n    const freqData = new Float32Array(queue.length * frameSize);\n    queue.forEach((data, i) => freqData.set(data, i * frameSize));\n    return freqData;\n  }\n\n  private getTensorFromAudioDataArray(freqData: Float32Array, shape: number[]):\n      Tensor {\n    const vals = new Float32Array(util.sizeFromShape(shape));\n    // If the data is less than the output shape, the rest is padded with zeros.\n    vals.set(freqData, vals.length - freqData.length);\n    return tensor(vals, shape);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {browser, cast, env, expandDims, image, reshape, tensor1d, Tensor1D, tensor2d, Tensor2D, Tensor3D, Tensor4D, tidy, util} from '@tensorflow/tfjs-core';\nimport {WebcamConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of image tensors from webcam video stream. Only works in\n * browser environment.\n */\nexport class WebcamIterator extends LazyIterator<Tensor3D> {\n  private isClosed = true;\n  private stream: MediaStream;\n  private resize = false;\n  private cropSize: [number, number];\n  private cropBox: Tensor2D;\n  private cropBoxInd: Tensor1D;\n\n  private constructor(\n      protected readonly webcamVideoElement: HTMLVideoElement,\n      protected readonly webcamConfig: WebcamConfig) {\n    super();\n    if (this.needToResize()) {\n      this.resize = true;\n      this.cropSize =\n          [this.webcamConfig.resizeHeight, this.webcamConfig.resizeWidth];\n      this.cropBoxInd = tensor1d([0], 'int32');\n      if (this.webcamConfig.centerCrop) {\n        // Calculate the box based on resizing shape.\n        const widthCroppingRatio =\n            this.webcamConfig.resizeWidth * 1.0 / this.webcamVideoElement.width;\n        const heightCroppingRatio = this.webcamConfig.resizeHeight * 1.0 /\n            this.webcamVideoElement.height;\n        const widthCropStart = (1 - widthCroppingRatio) / 2;\n        const heightCropStart = (1 - heightCroppingRatio) / 2;\n        const widthCropEnd = widthCropStart + widthCroppingRatio;\n        const heightCropEnd = heightCroppingRatio + heightCropStart;\n        this.cropBox = tensor2d(\n            [heightCropStart, widthCropStart, heightCropEnd, widthCropEnd],\n            [1, 4]);\n      } else {\n        this.cropBox = tensor2d([0, 0, 1, 1], [1, 4]);\n      }\n    }\n  }\n\n  summary() {\n    return `webcam`;\n  }\n\n  // Construct a WebcamIterator and start it's video stream.\n  static async create(\n      webcamVideoElement?: HTMLVideoElement, webcamConfig: WebcamConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'tf.data.webcam is only supported in browser environment.');\n    }\n\n    if (!webcamVideoElement) {\n      // If webcam video element is not provided, create a hidden video element\n      // with provided width and height.\n      webcamVideoElement = document.createElement('video');\n      if (!webcamConfig.resizeWidth || !webcamConfig.resizeHeight) {\n        throw new Error(\n            'Please provide webcam video element, or resizeWidth and ' +\n            'resizeHeight to create a hidden video element.');\n      }\n      webcamVideoElement.width = webcamConfig.resizeWidth;\n      webcamVideoElement.height = webcamConfig.resizeHeight;\n    }\n    const webcamIterator = new WebcamIterator(webcamVideoElement, webcamConfig);\n\n    // Call async function to initialize the video stream.\n    await webcamIterator.start();\n\n    return webcamIterator;\n  }\n\n  // Async function to start video stream.\n  async start(): Promise<void> {\n    if (this.webcamConfig.facingMode) {\n      util.assert(\n          (this.webcamConfig.facingMode === 'user') ||\n              (this.webcamConfig.facingMode === 'environment'),\n          () =>\n              `Invalid webcam facing mode: ${this.webcamConfig.facingMode}. ` +\n              `Please provide 'user' or 'environment'`);\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          deviceId: this.webcamConfig.deviceId,\n          facingMode: this.webcamConfig.facingMode ?\n              this.webcamConfig.facingMode :\n              'user',\n          width: this.webcamVideoElement.width,\n          height: this.webcamVideoElement.height\n        }\n      });\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message = `Error thrown while initializing video stream: ${e.message}`;\n      throw e;\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain video from webcam.');\n    }\n\n    // Older browsers may not have srcObject\n    try {\n      this.webcamVideoElement.srcObject = this.stream;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = window.URL.createObjectURL(\n        this.stream as unknown as MediaSource);\n    }\n    // Start the webcam video stream\n    this.webcamVideoElement.play();\n\n    this.isClosed = false;\n\n    return new Promise<void>(resolve => {\n      // Add event listener to make sure the webcam has been fully initialized.\n      this.webcamVideoElement.onloadedmetadata = () => {\n        resolve();\n      };\n    });\n  }\n\n  async next(): Promise<IteratorResult<Tensor3D>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let img;\n    try {\n      img = browser.fromPixels(this.webcamVideoElement);\n    } catch (e) {\n      throw new Error(\n          `Error thrown converting video to pixels: ${JSON.stringify(e)}`);\n    }\n    if (this.resize) {\n      try {\n        return {value: this.cropAndResizeFrame(img), done: false};\n      } catch (e) {\n        throw new Error(`Error thrown cropping the video: ${e.message}`);\n      } finally {\n        img.dispose();\n      }\n    } else {\n      return {value: img, done: false};\n    }\n  }\n\n  private needToResize() {\n    // If resizeWidth and resizeHeight are provided, and different from the\n    // width and height of original HTMLVideoElement, then resizing and cropping\n    // is required.\n    if (this.webcamConfig.resizeWidth && this.webcamConfig.resizeHeight &&\n        (this.webcamVideoElement.width !== this.webcamConfig.resizeWidth ||\n         this.webcamVideoElement.height !== this.webcamConfig.resizeHeight)) {\n      return true;\n    }\n    return false;\n  }\n\n  // Cropping and resizing each frame based on config\n  cropAndResizeFrame(img: Tensor3D): Tensor3D {\n    return tidy(() => {\n      const expandedImage: Tensor4D = expandDims(cast(img, 'float32'), (0));\n      let resizedImage;\n      resizedImage = image.cropAndResize(\n          expandedImage, this.cropBox, this.cropBoxInd, this.cropSize,\n          'bilinear');\n      // Extract image from batch cropping.\n      const shape = resizedImage.shape;\n      return reshape(resizedImage, shape.slice(1) as [number, number, number]);\n    });\n  }\n\n  // Capture one frame from the video stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<Tensor3D> {\n    return (await this.next()).value;\n  }\n\n  // Stop the video stream and pause webcam iterator.\n  stop(): void {\n    const tracks = this.stream.getTracks();\n\n    tracks.forEach(track => track.stop());\n\n    try {\n      this.webcamVideoElement.srcObject = null;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = null;\n    }\n    this.isClosed = true;\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor3D[]> {\n    throw new Error('Can not convert infinite video stream to array.');\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {ByteChunkIterator} from './iterators/byte_chunk_iterator';\n\n/**\n * Represents a data source readable as a stream of binary data chunks.\n *\n * Because `Dataset`s can be read repeatedly (via `Dataset.iterator()`), this\n * provides a means to repeatedly create streams from the underlying data\n * sources.\n */\nexport abstract class DataSource {\n  /**\n   * Obtain a new stream of binary data chunks.\n   *\n   * Starts the new stream from the beginning of the data source, even if other\n   * streams have been obtained previously.\n   */\n  abstract iterator(): Promise<ByteChunkIterator>;\n\n  // TODO(soergel): consider chainable Dataset construction here\n}\n\n// TODO(soergel): consider convenience factory functions here\n// in combination with chainable source->dataset above, e.g.:\n// tf.data.url(...).asCsvDataset().shuffle().batch()\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\n\nexport abstract class StringIterator extends LazyIterator<string> {\n  /**\n   * Splits a string stream on a given separator.\n   *\n   * It is assumed that the incoming chunk boundaries have no semantic meaning,\n   * so conceptually the incoming stream is treated simply as the concatenation\n   * of its elements.\n   *\n   * The outgoing stream provides chunks corresponding to the results of the\n   * standard string split() operation (even if such a chunk spanned incoming\n   * chunks).  The separators are not included.\n   *\n   * A typical usage is to split a text file (represented as a stream with\n   * arbitrary chunk boundaries) into lines.\n   *\n   * @param upstream A readable stream of strings that can be treated as\n   *   concatenated.\n   * @param separator A character to split on.\n   */\n  split(separator: string): StringIterator {\n    return new SplitIterator(this, separator);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on StringIterator.  Unfortunately they can't be placed in separate files, due\n// to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class SplitIterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass SplitIterator extends StringIterator {\n  private impl: SplitIteratorImpl;\n\n  constructor(protected upstream: LazyIterator<string>, separator: string) {\n    super();\n    this.impl = new SplitIteratorImpl(upstream, separator);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\nclass SplitIteratorImpl extends OneToManyIterator<string> {\n  // A partial string at the end of an upstream chunk\n  carryover = '';\n\n  constructor(\n      protected upstream: LazyIterator<string>, protected separator: string) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Split('${this.separator}')`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    if (chunkResult.done) {\n      if (this.carryover === '') {\n        return false;\n      }\n\n      // Pretend that the pump succeeded in order to emit the small last batch.\n      // The next pump() call will actually fail.\n      this.outputQueue.push(this.carryover);\n      this.carryover = '';\n      return true;\n    }\n    const lines = chunkResult.value.split(this.separator) as string[];\n    // Note the behavior: \" ab \".split(' ') === ['', 'ab', '']\n    // Thus the carryover may be '' if the separator falls on a chunk\n    // boundary; this produces the correct result.\n\n    lines[0] = this.carryover + lines[0];\n    for (const line of lines.slice(0, -1)) {\n      this.outputQueue.push(line);\n    }\n    this.carryover = lines[lines.length - 1];\n\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\nimport {StringIterator} from './string_iterator';\n\nexport abstract class ByteChunkIterator extends LazyIterator<Uint8Array> {\n  /**\n   * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n   *\n   * The byte arrays producetd from the ByteChunkIterator on which this is\n   * called will be interpreted as concatenated.  No assumptions are made about\n   * the boundaries of the incoming chunks, so a multi-byte UTF8 encoding of a\n   * character may span the boundary between chunks.  This naturally happens,\n   * for instance, when reading fixed-size byte arrays from a file.\n   */\n  decodeUTF8(): StringIterator {\n    return new Utf8Iterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on ByteChunkIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class Utf8Iterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass Utf8Iterator extends StringIterator {\n  private impl: Utf8IteratorImpl;\n\n  constructor(protected upstream: LazyIterator<Uint8Array>) {\n    super();\n    this.impl = new Utf8IteratorImpl(upstream);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\n/**\n * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n *\n * This is tricky because the incoming byte array boundaries may disrupt a\n * multi-byte UTF8 character. Thus any incomplete character data at the end of\n * a chunk must be carried over and prepended to the next chunk before\n * decoding. Luckily with native decoder, TextDecoder in browser and\n * string_decoder in node, byte array boundaries are handled automatically.\n *\n * In the context of an input pipeline for machine learning, UTF8 decoding is\n * needed to parse text files containing training examples or prediction\n * requests (e.g., formatted as CSV or JSON). We cannot use the built-in\n * decoding provided by FileReader.readAsText() because here we are in a\n * streaming context, which FileReader does not support.\n *\n * @param upstream A `LazyIterator` of `Uint8Arrays` containing UTF8-encoded\n *   text, which should be interpreted as concatenated.  No assumptions are\n *   made about the boundaries of the incoming chunks, so a multi-byte UTF8\n *   encoding of a character may span the boundary between chunks.  This\n *   naturally happens, for instance, when reading fixed-size byte arrays from a\n *   file.\n */\nclass Utf8IteratorImpl extends OneToManyIterator<string> {\n  // `decoder` as `any` here to dynamically assign value based on the\n  // environment.\n  // tslint:disable-next-line:no-any\n  decoder: any;\n\n  constructor(protected readonly upstream: LazyIterator<Uint8Array>) {\n    super();\n    if (env().get('IS_BROWSER')) {\n      this.decoder = new TextDecoder('utf-8');\n    } else {\n      // tslint:disable-next-line:no-require-imports\n      const {StringDecoder} = require('string_decoder');\n      this.decoder = new StringDecoder('utf8');\n    }\n  }\n  summary() {\n    return `${this.upstream.summary()} -> Utf8`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    let chunk;\n    if (chunkResult.done) {\n      return false;\n    } else {\n      chunk = chunkResult.value;\n    }\n\n    let text: string;\n    if (env().get('IS_BROWSER')) {\n      text = this.decoder.decode(chunk, {stream: true});\n    } else {\n      text = this.decoder.write(Buffer.from(chunk.buffer));\n    }\n    this.outputQueue.push(text);\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// Skip tslint any type check cause this method is aiming to check type of\n// input.\n// tslint:disable-next-line:no-any\nexport function isLocalPath(source: any): boolean {\n  return (typeof source === 'string') && source.slice(0, 7) === 'file://';\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {urlChunkIterator} from '../iterators/url_chunk_iterator';\nimport {isLocalPath} from '../util/source_util';\nimport {FileDataSource} from './file_data_source';\n\n/*\n * Represents a URL readable as a stream of binary data chunks.\n */\nexport class URLDataSource extends DataSource {\n  /**\n   * Create a `URLDataSource`.\n   *\n   * @param url A source URL string, or a `Request` object.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected readonly url: RequestInfo,\n      protected readonly fileOptions: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  // TODO(soergel): provide appropriate caching options.  Currently this\n  // will download the URL anew for each call to iterator().  Since we have\n  // to treat the downloaded file as a blob/buffer anyway, we may as well retain\n  // it-- but that raises GC issues.  Also we may want a persistent disk cache.\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.url)) {\n      return (new FileDataSource(this.url as string, this.fileOptions))\n          .iterator();\n    } else {\n      return urlChunkIterator(this.url, this.fileOptions);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer} from '@tensorflow/tfjs-core';\nimport {Dataset, datasetFromIteratorFn} from './dataset';\nimport {CSVDataset} from './datasets/csv_dataset';\nimport {iteratorFromFunction} from './iterators/lazy_iterator';\nimport {MicrophoneIterator} from './iterators/microphone_iterator';\nimport {WebcamIterator} from './iterators/webcam_iterator';\nimport {URLDataSource} from './sources/url_data_source';\nimport {CSVConfig, MicrophoneConfig, WebcamConfig} from './types';\n\n/**\n * Create a `CSVDataset` by reading and decoding CSV file(s) from provided URL\n * or local path if it's in Node environment.\n *\n * Note: If isLabel in columnConfigs is `true` for at least one column, the\n * element in returned `CSVDataset` will be an object of\n * `{xs:features, ys:labels}`: xs is a dict of features key/value pairs, ys\n * is a dict of labels key/value pairs. If no column is marked as label,\n * returns a dict of features only.\n *\n * ```js\n * const csvUrl =\n * 'https://storage.googleapis.com/tfjs-examples/multivariate-linear-regression/data/boston-housing-train.csv';\n *\n * async function run() {\n *   // We want to predict the column \"medv\", which represents a median value of\n *   // a home (in $1000s), so we mark it as a label.\n *   const csvDataset = tf.data.csv(\n *     csvUrl, {\n *       columnConfigs: {\n *         medv: {\n *           isLabel: true\n *         }\n *       }\n *     });\n *\n *   // Number of features is the number of column names minus one for the label\n *   // column.\n *   const numOfFeatures = (await csvDataset.columnNames()).length - 1;\n *\n *   // Prepare the Dataset for training.\n *   const flattenedDataset =\n *     csvDataset\n *     .map(({xs, ys}) =>\n *       {\n *         // Convert xs(features) and ys(labels) from object form (keyed by\n *         // column name) to array form.\n *         return {xs:Object.values(xs), ys:Object.values(ys)};\n *       })\n *     .batch(10);\n *\n *   // Define the model.\n *   const model = tf.sequential();\n *   model.add(tf.layers.dense({\n *     inputShape: [numOfFeatures],\n *     units: 1\n *   }));\n *   model.compile({\n *     optimizer: tf.train.sgd(0.000001),\n *     loss: 'meanSquaredError'\n *   });\n *\n *   // Fit the model using the prepared Dataset\n *   return model.fitDataset(flattenedDataset, {\n *     epochs: 10,\n *     callbacks: {\n *       onEpochEnd: async (epoch, logs) => {\n *         console.log(epoch + ':' + logs.loss);\n *       }\n *     }\n *   });\n * }\n *\n * await run();\n * ```\n *\n * @param source URL or local path to get CSV file. If it's a local path, it\n * must have prefix `file://` and it only works in node environment.\n * @param csvConfig (Optional) A CSVConfig object that contains configurations\n *     of reading and decoding from CSV file(s).\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function csv(\n    source: RequestInfo, csvConfig: CSVConfig = {}): CSVDataset {\n  return new CSVDataset(new URLDataSource(source), csvConfig);\n}\n\n/**\n * Create a `Dataset` that produces each element by calling a provided function.\n *\n * Note that repeated iterations over this `Dataset` may produce different\n * results, because the function will be called anew for each element of each\n * iteration.\n *\n * Also, beware that the sequence of calls to this function may be out of order\n * in time with respect to the logical order of the Dataset. This is due to the\n * asynchronous lazy nature of stream processing, and depends on downstream\n * transformations (e.g. .shuffle()). If the provided function is pure, this is\n * no problem, but if it is a closure over a mutable state (e.g., a traversal\n * pointer), then the order of the produced elements may be scrambled.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const ds = tf.data.func(func);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param f A function that produces one data element on each call.\n */\nexport function func<T extends TensorContainer>(\n    f: () => IteratorResult<T>| Promise<IteratorResult<T>>): Dataset<T> {\n  const iter = iteratorFromFunction(f);\n  return datasetFromIteratorFn(async () => iter);\n}\n\n/**\n * Create a `Dataset` that produces each element from provided JavaScript\n * generator, which is a function that returns a (potentially async) iterator.\n *\n * For more information on iterators and generators, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Iterators_and_Generators .\n * For the iterator protocol, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols .\n *\n * Example of creating a dataset from an iterator factory:\n * ```js\n * function makeIterator() {\n *   const numElements = 10;\n *   let index = 0;\n *\n *   const iterator = {\n *     next: () => {\n *       let result;\n *       if (index < numElements) {\n *         result = {value: index, done: false};\n *         index++;\n *         return result;\n *       }\n *       return {value: index, done: true};\n *     }\n *   };\n *   return iterator;\n * }\n * const ds = tf.data.generator(makeIterator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * Example of creating a dataset from a generator:\n * ```js\n * function* dataGenerator() {\n *   const numElements = 10;\n *   let index = 0;\n *   while (index < numElements) {\n *     const x = index;\n *     index++;\n *     yield x;\n *   }\n * }\n *\n * const ds = tf.data.generator(dataGenerator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param generator A JavaScript function that returns\n *     a (potentially async) JavaScript iterator.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function generator<T extends TensorContainer>(\n  generator: () => Iterator<T> | Promise<Iterator<T>> | AsyncIterator<T>,\n): Dataset<T> {\n  return datasetFromIteratorFn(async () => {\n    const gen = await generator();\n    return iteratorFromFunction(() => gen.next());\n  });\n}\n\n/**\n * Create an iterator that generates `Tensor`s from webcam video stream. This\n * API only works in Browser environment when the device has webcam.\n *\n * Note: this code snippet only works when the device has a webcam. It will\n * request permission to open the webcam when running.\n * ```js\n * const videoElement = document.createElement('video');\n * videoElement.width = 100;\n * videoElement.height = 100;\n * const cam = await tf.data.webcam(videoElement);\n * const img = await cam.capture();\n * img.print();\n * cam.stop();\n * ```\n *\n * @param webcamVideoElement A `HTMLVideoElement` used to play video from\n *     webcam. If this element is not provided, a hidden `HTMLVideoElement` will\n *     be created. In that case, `resizeWidth` and `resizeHeight` must be\n *     provided to set the generated tensor shape.\n * @param webcamConfig A `WebcamConfig` object that contains configurations of\n *     reading and manipulating data from webcam video stream.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function webcam(\n    webcamVideoElement?: HTMLVideoElement,\n    webcamConfig?: WebcamConfig): Promise<WebcamIterator> {\n  return WebcamIterator.create(webcamVideoElement, webcamConfig);\n}\n\n/**\n * Create an iterator that generates frequency-domain spectrogram `Tensor`s from\n * microphone audio stream with browser's native FFT. This API only works in\n * browser environment when the device has microphone.\n *\n * Note: this code snippet only works when the device has a microphone. It will\n * request permission to open the microphone when running.\n * ```js\n * const mic = await tf.data.microphone({\n *   fftSize: 1024,\n *   columnTruncateLength: 232,\n *   numFramesPerSpectrogram: 43,\n *   sampleRateHz:44100,\n *   includeSpectrogram: true,\n *   includeWaveform: true\n * });\n * const audioData = await mic.capture();\n * const spectrogramTensor = audioData.spectrogram;\n * spectrogramTensor.print();\n * const waveformTensor = audioData.waveform;\n * waveformTensor.print();\n * mic.stop();\n * ```\n *\n * @param microphoneConfig A `MicrophoneConfig` object that contains\n *     configurations of reading audio data from microphone.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function microphone(microphoneConfig?: MicrophoneConfig):\n    Promise<MicrophoneIterator> {\n  return MicrophoneIterator.create(microphoneConfig);\n}\n", "/** @license See the LICENSE file. */\n\n// This code is auto-generated, do not modify this file!\nconst version = '4.22.0';\nexport {version};\n"], "names": ["global", "module", "define", "Alea", "seed", "n", "me", "this", "mash", "data", "String", "i", "length", "h", "charCodeAt", "next", "t", "s0", "c", "s1", "s2", "copy", "f", "impl", "opts", "xg", "state", "prng", "int32", "double", "quick", "exports", "amd", "alea", "XorGen", "strseed", "x", "y", "z", "w", "k", "result", "xor128", "v", "d", "xorwow", "X", "j", "push", "init", "slice", "Date", "xorshift7", "limit", "Math", "max", "xor4096", "b", "a", "floor", "tychei", "pool", "math", "nodecrypto", "width", "startdenom", "pow", "significance", "overflow", "mask", "seedrandom", "options", "callback", "key", "shortseed", "mixkey", "flatten", "entropy", "tostring", "out", "randomBytes", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "e", "browser", "navigator", "plugins", "screen", "autoseed", "arc4", "ARC4", "g", "S", "pass", "is_math_call", "keylen", "s", "count", "r", "obj", "depth", "prop", "typ", "smear", "stringseed", "fromCharCode", "apply", "random", "require$$0", "ex", "self", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "sr", "ZipMismatchMode", "deepMapInternal", "input", "mapFn", "seen", "Map", "containedIn", "Set", "Blob", "has", "Error", "get", "recurse", "value", "isIterable", "mappedIterable", "Array", "isArray", "add", "childResult", "delete", "__proto__", "set", "deepZip", "inputs", "zipFn", "zipToList", "deepZipInternal", "map", "async", "deepMapAndAwaitAll", "from", "keys", "tf", "util", "isPromise", "mappedValue", "isTextDecoder", "env", "TextDecoder", "StringDecoder", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tensor", "Promise", "deepClone", "container", "cloneIfTensor", "item", "clone", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "capacity", "begin", "end", "RangeError", "doubledCapacity", "wrap", "index", "isFull", "isEmpty", "pushAll", "values", "pop", "undefined", "unshift", "shift", "shuffleExcise", "relativeIndex", "GrowingRingBuffer", "super", "INITIAL_CAPACITY", "expand", "newCapacity", "newData", "len", "iteratorFromItems", "items", "ArrayIterator", "iteratorFromFunction", "func", "FunctionCallIterator", "LazyIterator", "done", "stream", "prefetch", "predicate", "shouldC<PERSON><PERSON>ue", "handleErrors", "handler", "ErrorHandlingLazyIterator", "filter", "FilterIterator", "transform", "MapIterator", "mapAsync", "AsyncMapIterator", "serialMapAsync", "serial", "flatmap", "FlatmapIterator", "resolveFully", "<PERSON><PERSON><PERSON><PERSON>", "rowMajorBatch", "batchSize", "smallLastBatch", "RowMajorBatchIterator", "columnMajorBatch", "concatenate", "iterator", "baseErrorHandler", "ChainedIterator", "take", "TakeIterator", "skip", "SkipIterator", "bufferSize", "PrefetchIterator", "shuffle", "windowSize", "ShuffleIterator", "SerialIterator", "trav", "summary", "nextFn", "message", "upstream", "lastRead", "resolve", "then", "serialNext", "maxCount", "skipped", "dispose", "enableSmallLastBatch", "batch", "inputTensors", "tensor_util", "getTensorsInContainer", "mapped", "outputTensors", "isTensorInList", "OneToManyIterator", "outputQueue", "pump", "mappedArray", "iterators", "moreIterators", "read<PERSON><PERSON><PERSON><PERSON><PERSON>", "iteratorResult", "itemResult", "ZipIterator", "mismatchMode", "FAIL", "currentPromise", "afterState", "numIterators", "iteratorsDone", "SHORTEST", "LONGEST", "nextState", "buffer", "refill", "upstreamExhausted", "seedrandom.alea", "now", "toString", "randomInt", "chooseIndex", "chosenIndex", "Dataset", "size", "base", "assert", "Infinity", "ceil", "datasetFromIteratorFn", "deepBatchConcat", "dataset", "tidy", "forEachAsync", "repeat", "iteratorIterator", "baseIterators", "reshuffleEachIteration", "seed2", "toArray", "toArrayForTest", "iteratorFn", "rows", "exampleRow", "isTypedArray", "arrays", "stack", "tensor", "batchConcat", "MAX_BUFFER_SIZE", "TextLineDataset", "decodeUTF8", "split", "line", "endsWith", "CODE_QUOTE", "STATE_OUT", "Symbol", "STATE_FIELD", "STATE_QUOTE", "STATE_QUOTE_AFTER_QUOTE", "STATE_WITHIN_QUOTE_IN_QUOTE", "CSVDataset", "columnNamesValidated", "setColumnNames", "configuredColumnsOnly", "Object", "columnConfigs", "fullColumnNames", "columnNamesFromFile", "maybeReadHeaderLine", "counts", "reduce", "countAcc", "name", "duplicateNames", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "iter", "firstElement", "firstLine", "parseRow", "csvConfig", "delimiter", "delimWhitespace", "columnNames", "lines", "makeDataElement", "features", "labels", "config", "parsedValue", "default", "required", "isLabel", "valueAsNum", "Number", "isNaN", "dtype", "getBoolean", "xs", "ys", "toLowerCase", "validateElementCount", "readOffset", "readLength", "currentState", "char<PERSON>t", "substring", "MicrophoneIterator", "microphoneConfig", "isClosed", "fftSize", "fftSizeLog2", "log2", "isInteger", "numFrames", "numFramesPerSpectrogram", "sampleRateHz", "columnTruncateLength", "audioTrackConstraints", "smoothingTimeConstant", "includeSpectrogram", "includeWaveform", "static", "microphoneIterator", "start", "mediaDevices", "getUserMedia", "audio", "video", "ctxConstructor", "window", "AudioContext", "webkitAudioContext", "audioContext", "sampleRate", "streamSource", "createMediaStreamSource", "analyser", "create<PERSON><PERSON>yser", "connect", "freqData", "Float32Array", "timeData", "spectrogramTensor", "waveformTensor", "audioDataQueue", "getAudioData", "flattenQueue", "freqDataQueue", "getTensorFromAudioDataArray", "timeDataQueue", "spectrogram", "waveform", "currentFrames", "intervalID", "setInterval", "getFloatFrequencyData", "getFloatTimeDomainData", "clearInterval", "stop", "disconnect", "close", "getTracks", "getSampleRate", "queue", "frameSize", "for<PERSON>ach", "shape", "vals", "sizeFromShape", "WebcamIterator", "webcamVideoElement", "webcamConfig", "resize", "needToResize", "cropSize", "resizeHeight", "resizeWidth", "cropBoxInd", "tensor1d", "centerCrop", "widthCroppingRatio", "heightCroppingRatio", "height", "widthCropStart", "heightCropStart", "widthCropEnd", "heightCropEnd", "cropBox", "tensor2d", "document", "createElement", "webcamIterator", "facingMode", "deviceId", "srcObject", "error", "console", "log", "src", "URL", "createObjectURL", "play", "onloadedmetadata", "img", "fromPixels", "JSON", "stringify", "cropAndResizeFrame", "expandedImage", "expandDims", "cast", "resizedImage", "image", "cropAndResize", "reshape", "track", "DataSource", "StringIterator", "separator", "SplitIterator", "SplitIteratorImpl", "carryover", "chunkResult", "ByteChunkIterator", "Utf8Iterator", "Utf8IteratorImpl", "decoder", "chunk", "text", "decode", "write", "<PERSON><PERSON><PERSON>", "FileChunkIterator", "file", "File", "offset", "chunkSize", "byteLength", "reject", "fileReader", "FileReader", "onload", "event", "TypeError", "<PERSON>ab<PERSON>", "onerror", "type", "readAsA<PERSON>y<PERSON><PERSON>er", "getRequestInitFromRequest", "request", "method", "headers", "body", "mode", "credentials", "cache", "redirect", "referrer", "integrity", "isLocalPath", "source", "FileDataSource", "fs", "readFileSync", "URLDataSource", "url", "fileOptions", "fetchFunc", "urlString", "requestInit", "response", "fetch", "ok", "uint8Array", "arrayBuffer", "statusText", "urlChunkIterator", "generator", "gen", "create", "datasets", "min", "ds", "iteratorFromZipped"], "mappings": ";;;;;;;;;;;;;;;;0sCA2BA,SAAUA,EAAQC,EAAQC,GAE1B,SAASC,EAAKC,GACZ,IAgDIC,EAhDAC,EAAKC,KAAMC,GAgDXH,EAAI,WAEG,SAASI,GAClBA,EAAOC,OAAOD,GACd,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAKG,OAAQD,IAAK,CAEpC,IAAIE,EAAI,oBADRR,GAAKI,EAAKK,WAAWH,IAGrBE,GADAR,EAAIQ,IAAM,EAGVR,GADAQ,GAAKR,KACK,EAEVA,GAAS,YADTQ,GAAKR,EAEN,CACD,OAAmB,wBAAXA,IAAM,EAClB,GA7DEC,EAAGS,KAAO,WACR,IAAIC,EAAI,QAAUV,EAAGW,GAAY,uBAAPX,EAAGY,EAG7B,OAFAZ,EAAGW,GAAKX,EAAGa,GACXb,EAAGa,GAAKb,EAAGc,GACJd,EAAGc,GAAKJ,GAAKV,EAAGY,EAAQ,EAAJF,EAC/B,EAGEV,EAAGY,EAAI,EACPZ,EAAGW,GAAKT,EAAK,KACbF,EAAGa,GAAKX,EAAK,KACbF,EAAGc,GAAKZ,EAAK,KACbF,EAAGW,IAAMT,EAAKJ,GACVE,EAAGW,GAAK,IAAKX,EAAGW,IAAM,GAC1BX,EAAGa,IAAMX,EAAKJ,GACVE,EAAGa,GAAK,IAAKb,EAAGa,IAAM,GAC1Bb,EAAGc,IAAMZ,EAAKJ,GACVE,EAAGc,GAAK,IAAKd,EAAGc,IAAM,GAC1BZ,EAAO,IACR,CAED,SAASa,EAAKC,EAAGN,GAKf,OAJAA,EAAEE,EAAII,EAAEJ,EACRF,EAAEC,GAAKK,EAAEL,GACTD,EAAEG,GAAKG,EAAEH,GACTH,EAAEI,GAAKE,EAAEF,GACFJ,CACR,CAED,SAASO,EAAKnB,EAAMoB,GAClB,IAAIC,EAAK,IAAItB,EAAKC,GACdsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAOF,EAAGV,KAUd,OATAY,EAAKC,MAAQ,WAAa,OAAoB,WAAZH,EAAGV,OAAwB,CAAI,EACjEY,EAAKE,OAAS,WACZ,OAAOF,IAAmC,uBAAhB,QAATA,IAAoB,EACzC,EACEA,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BL,EAAKK,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAwBG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAK0B,KAAOV,CAGb,CAhFD,CAiFEhB,EAC+BN,GAC/B,oDC3GF,SAAUD,EAAQC,EAAQC,GAE1B,SAASgC,EAAO9B,GACd,IAAIE,EAAKC,KAAM4B,EAAU,GAEzB7B,EAAG8B,EAAI,EACP9B,EAAG+B,EAAI,EACP/B,EAAGgC,EAAI,EACPhC,EAAGiC,EAAI,EAGPjC,EAAGS,KAAO,WACR,IAAIC,EAAIV,EAAG8B,EAAK9B,EAAG8B,GAAK,GAIxB,OAHA9B,EAAG8B,EAAI9B,EAAG+B,EACV/B,EAAG+B,EAAI/B,EAAGgC,EACVhC,EAAGgC,EAAIhC,EAAGiC,EACHjC,EAAGiC,GAAMjC,EAAGiC,IAAM,GAAMvB,EAAKA,IAAM,CAC9C,EAEMZ,KAAiB,EAAPA,GAEZE,EAAG8B,EAAIhC,EAGP+B,GAAW/B,EAIb,IAAK,IAAIoC,EAAI,EAAGA,EAAIL,EAAQvB,OAAS,GAAI4B,IACvClC,EAAG8B,GAA6B,EAAxBD,EAAQrB,WAAW0B,GAC3BlC,EAAGS,MAEN,CAED,SAASM,EAAKC,EAAGN,GAKf,OAJAA,EAAEoB,EAAId,EAAEc,EACRpB,EAAEqB,EAAIf,EAAEe,EACRrB,EAAEsB,EAAIhB,EAAEgB,EACRtB,EAAEuB,EAAIjB,EAAEiB,EACDvB,CACR,CAED,SAASO,EAAKnB,EAAMoB,GAClB,IAAIC,EAAK,IAAIS,EAAO9B,GAChBsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAGV,SAAW,GAAK,YAenD,OAdAY,EAAKE,OAAS,WACZ,GACE,IAEIY,IAFMhB,EAAGV,SAAW,KACbU,EAAGV,SAAW,GAAK,aACF,GAAK,UACf,IAAX0B,GACT,OAAOA,CACX,EACEd,EAAKC,MAAQH,EAAGV,KAChBY,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BL,EAAKK,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAEG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAKmC,OAASnB,CAGf,CAvED,CAwEEhB,EAC+BN,GAC/B,oDC1EF,SAAUD,EAAQC,EAAQC,GAE1B,SAASgC,EAAO9B,GACd,IAAIE,EAAKC,KAAM4B,EAAU,GAGzB7B,EAAGS,KAAO,WACR,IAAIC,EAAKV,EAAG8B,EAAK9B,EAAG8B,IAAM,EAE1B,OADA9B,EAAG8B,EAAI9B,EAAG+B,EAAG/B,EAAG+B,EAAI/B,EAAGgC,EAAGhC,EAAGgC,EAAIhC,EAAGiC,EAAGjC,EAAGiC,EAAIjC,EAAGqC,GACzCrC,EAAGsC,EAAKtC,EAAGsC,EAAI,OAAS,IAC5BtC,EAAGqC,EAAKrC,EAAGqC,EAAKrC,EAAGqC,GAAK,EAAO3B,EAAKA,GAAK,GAAO,CACxD,EAEEV,EAAG8B,EAAI,EACP9B,EAAG+B,EAAI,EACP/B,EAAGgC,EAAI,EACPhC,EAAGiC,EAAI,EACPjC,EAAGqC,EAAI,EAEHvC,KAAiB,EAAPA,GAEZE,EAAG8B,EAAIhC,EAGP+B,GAAW/B,EAIb,IAAK,IAAIoC,EAAI,EAAGA,EAAIL,EAAQvB,OAAS,GAAI4B,IACvClC,EAAG8B,GAA6B,EAAxBD,EAAQrB,WAAW0B,GACvBA,GAAKL,EAAQvB,SACfN,EAAGsC,EAAItC,EAAG8B,GAAK,GAAK9B,EAAG8B,IAAM,GAE/B9B,EAAGS,MAEN,CAED,SAASM,EAAKC,EAAGN,GAOf,OANAA,EAAEoB,EAAId,EAAEc,EACRpB,EAAEqB,EAAIf,EAAEe,EACRrB,EAAEsB,EAAIhB,EAAEgB,EACRtB,EAAEuB,EAAIjB,EAAEiB,EACRvB,EAAE2B,EAAIrB,EAAEqB,EACR3B,EAAE4B,EAAItB,EAAEsB,EACD5B,CACR,CAED,SAASO,EAAKnB,EAAMoB,GAClB,IAAIC,EAAK,IAAIS,EAAO9B,GAChBsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAGV,SAAW,GAAK,YAenD,OAdAY,EAAKE,OAAS,WACZ,GACE,IAEIY,IAFMhB,EAAGV,SAAW,KACbU,EAAGV,SAAW,GAAK,aACF,GAAK,UACf,IAAX0B,GACT,OAAOA,CACX,EACEd,EAAKC,MAAQH,EAAGV,KAChBY,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BL,EAAKK,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAEG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAKsC,OAAStB,CAGf,CA5ED,CA6EEhB,EAC+BN,GAC/B,oDC7EF,SAAUD,EAAQC,EAAQC,GAE1B,SAASgC,EAAO9B,GACd,IAAIE,EAAKC,KAGTD,EAAGS,KAAO,eAEgBC,EAAG2B,EAAvBG,EAAIxC,EAAG8B,EAAGzB,EAAIL,EAAGK,EAQrB,OAPAK,EAAI8B,EAAEnC,GAAoBgC,GAAhB3B,GAAMA,IAAM,GAAaA,GAAK,GACpB2B,IAApB3B,EAAI8B,EAAGnC,EAAI,EAAK,IAAcK,IAAM,GAChB2B,IAApB3B,EAAI8B,EAAGnC,EAAI,EAAK,IAAcK,IAAM,EAChB2B,IAApB3B,EAAI8B,EAAGnC,EAAI,EAAK,IAAcK,GAAK,EACnCA,EAAI8B,EAAGnC,EAAI,EAAK,GAAuBgC,IAAnB3B,GAASA,GAAK,IAAeA,GAAK,EACtD8B,EAAEnC,GAAKgC,EACPrC,EAAGK,EAAKA,EAAI,EAAK,EACVgC,CACX,EAEE,SAAcrC,EAAIF,GAChB,IAAI2C,EAAMD,EAAI,GAEd,GAAI1C,KAAiB,EAAPA,GAER0C,EAAE,GAAK1C,OAIX,IADAA,EAAO,GAAKA,EACP2C,EAAI,EAAGA,EAAI3C,EAAKQ,SAAUmC,EAC7BD,EAAM,EAAJC,GAAUD,EAAM,EAAJC,IAAU,GACnB3C,EAAKU,WAAWiC,GAAKD,EAAGC,EAAI,EAAK,IAAM,GAIhD,KAAOD,EAAElC,OAAS,GAAGkC,EAAEE,KAAK,GAC5B,IAAKD,EAAI,EAAGA,EAAI,GAAc,IAATD,EAAEC,KAAYA,GAOnC,IANS,GAALA,EAAYD,EAAE,IAAM,EAAYA,EAAEC,GAEtCzC,EAAG8B,EAAIU,EACPxC,EAAGK,EAAI,EAGFoC,EAAI,IAAKA,EAAI,IAAKA,EACrBzC,EAAGS,MAEN,CAEDkC,CAAK3C,EAAIF,EACV,CAED,SAASiB,EAAKC,EAAGN,GAGf,OAFAA,EAAEoB,EAAId,EAAEc,EAAEc,QACVlC,EAAEL,EAAIW,EAAEX,EACDK,CACR,CAED,SAASO,EAAKnB,EAAMoB,GACN,MAARpB,IAAcA,GAAS,IAAI+C,MAC/B,IAAI1B,EAAK,IAAIS,EAAO9B,GAChBsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAGV,SAAW,GAAK,YAenD,OAdAY,EAAKE,OAAS,WACZ,GACE,IAEIY,IAFMhB,EAAGV,SAAW,KACbU,EAAGV,SAAW,GAAK,aACF,GAAK,UACf,IAAX0B,GACT,OAAOA,CACX,EACEd,EAAKC,MAAQH,EAAGV,KAChBY,EAAKG,MAAQH,EACTD,IACEA,EAAMU,GAAGf,EAAKK,EAAOD,GACzBE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAEG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAK6C,UAAY7B,CAGlB,CAtFD,CAuFEhB,EAC+BN,GAC/B,oDCrEF,SAAUD,EAAQC,EAAQC,GAE1B,SAASgC,EAAO9B,GACd,IAAIE,EAAKC,KAGTD,EAAGS,KAAO,WACR,IACwBC,EAAG2B,EADvBJ,EAAIjC,EAAGiC,EACPO,EAAIxC,EAAGwC,EAAGnC,EAAIL,EAAGK,EAcrB,OAZAL,EAAGiC,EAAIA,EAAKA,EAAI,WAAc,EAE9BI,EAAIG,EAAGnC,EAAI,GAAM,KACjBK,EAAI8B,EAAEnC,EAAMA,EAAI,EAAK,KACrBgC,GAAKA,GAAK,GACV3B,GAAKA,GAAK,GACV2B,GAAKA,IAAM,GACX3B,GAAKA,IAAM,GAEX2B,EAAIG,EAAEnC,GAAKgC,EAAI3B,EACfV,EAAGK,EAAIA,EAECgC,GAAKJ,EAAKA,IAAM,IAAQ,CACpC,EAEE,SAAcjC,EAAIF,GAChB,IAAIY,EAAG2B,EAAGhC,EAAGoC,EAAGR,EAAGO,EAAI,GAAIO,EAAQ,IAYnC,IAXIjD,KAAiB,EAAPA,IAEZuC,EAAIvC,EACJA,EAAO,OAGPA,GAAc,KACduC,EAAI,EACJU,EAAQC,KAAKC,IAAIF,EAAOjD,EAAKQ,SAG1BD,EAAI,EAAGoC,GAAK,GAAIA,EAAIM,IAASN,EAE5B3C,IAAMuC,GAAKvC,EAAKU,YAAYiC,EAAI,IAAM3C,EAAKQ,SAErC,IAANmC,IAASR,EAAII,GACjBA,GAAKA,GAAK,GACVA,GAAKA,IAAM,GACXA,GAAKA,GAAK,EACVA,GAAKA,IAAM,GACPI,GAAK,IACPR,EAAKA,EAAI,WAAc,EAEvB5B,EAAK,IADLK,EAAK8B,EAAM,IAAJC,IAAaJ,EAAIJ,GACT5B,EAAI,EAAI,GAW3B,IAPIA,GAAK,MACPmC,EAA+B,KAA5B1C,GAAQA,EAAKQ,QAAU,KAAa,GAKzCD,EAAI,IACCoC,EAAI,IAASA,EAAI,IAAKA,EACzBJ,EAAIG,EAAGnC,EAAI,GAAM,KACjBK,EAAI8B,EAAEnC,EAAMA,EAAI,EAAK,KACrBgC,GAAKA,GAAK,GACV3B,GAAKA,GAAK,GACV2B,GAAKA,IAAM,GACX3B,GAAKA,IAAM,GACX8B,EAAEnC,GAAKgC,EAAI3B,EAGbV,EAAGiC,EAAIA,EACPjC,EAAGwC,EAAIA,EACPxC,EAAGK,EAAIA,CACR,CAEDsC,CAAK3C,EAAIF,EACV,CAED,SAASiB,EAAKC,EAAGN,GAIf,OAHAA,EAAEL,EAAIW,EAAEX,EACRK,EAAEuB,EAAIjB,EAAEiB,EACRvB,EAAE8B,EAAIxB,EAAEwB,EAAEI,QACHlC,CAET,CACA,SAASO,EAAKnB,EAAMoB,GACN,MAARpB,IAAcA,GAAS,IAAI+C,MAC/B,IAAI1B,EAAK,IAAIS,EAAO9B,GAChBsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAGV,SAAW,GAAK,YAenD,OAdAY,EAAKE,OAAS,WACZ,GACE,IAEIY,IAFMhB,EAAGV,SAAW,KACbU,EAAGV,SAAW,GAAK,aACF,GAAK,UACf,IAAX0B,GACT,OAAOA,CACX,EACEd,EAAKC,MAAQH,EAAGV,KAChBY,EAAKG,MAAQH,EACTD,IACEA,EAAMoB,GAAGzB,EAAKK,EAAOD,GACzBE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAEG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAKiD,QAAUjC,CAGhB,CApHD,CAqHEhB,EAC+BN,GAC/B,oDC5IF,SAAUD,EAAQC,EAAQC,GAE1B,SAASgC,EAAO9B,GACd,IAAIE,EAAKC,KAAM4B,EAAU,GAGzB7B,EAAGS,KAAO,WACR,IAAI0C,EAAInD,EAAGmD,EAAGvC,EAAIZ,EAAGY,EAAG0B,EAAItC,EAAGsC,EAAGc,EAAIpD,EAAGoD,EAQzC,OAPAD,EAAKA,GAAK,GAAOA,IAAM,EAAKvC,EAC5BA,EAAKA,EAAI0B,EAAK,EACdA,EAAKA,GAAK,GAAOA,IAAM,EAAKc,EAC5BA,EAAKA,EAAID,EAAK,EACdnD,EAAGmD,EAAIA,EAAKA,GAAK,GAAOA,IAAM,GAAMvC,EACpCZ,EAAGY,EAAIA,EAAKA,EAAI0B,EAAK,EACrBtC,EAAGsC,EAAKA,GAAK,GAAO1B,IAAM,GAAMwC,EACzBpD,EAAGoD,EAAKA,EAAID,EAAK,CAC5B,EAkBEnD,EAAGoD,EAAI,EACPpD,EAAGmD,EAAI,EACPnD,EAAGY,GAAI,WACPZ,EAAGsC,EAAI,WAEHxC,IAASkD,KAAKK,MAAMvD,IAEtBE,EAAGoD,EAAKtD,EAAO,WAAe,EAC9BE,EAAGmD,EAAW,EAAPrD,GAGP+B,GAAW/B,EAIb,IAAK,IAAIoC,EAAI,EAAGA,EAAIL,EAAQvB,OAAS,GAAI4B,IACvClC,EAAGmD,GAA6B,EAAxBtB,EAAQrB,WAAW0B,GAC3BlC,EAAGS,MAEN,CAED,SAASM,EAAKC,EAAGN,GAKf,OAJAA,EAAE0C,EAAIpC,EAAEoC,EACR1C,EAAEyC,EAAInC,EAAEmC,EACRzC,EAAEE,EAAII,EAAEJ,EACRF,EAAE4B,EAAItB,EAAEsB,EACD5B,CAET,CACA,SAASO,EAAKnB,EAAMoB,GAClB,IAAIC,EAAK,IAAIS,EAAO9B,GAChBsB,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAGV,SAAW,GAAK,YAenD,OAdAY,EAAKE,OAAS,WACZ,GACE,IAEIY,IAFMhB,EAAGV,SAAW,KACbU,EAAGV,SAAW,GAAK,aACF,GAAK,UACf,IAAX0B,GACT,OAAOA,CACX,EACEd,EAAKC,MAAQH,EAAGV,KAChBY,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BL,EAAKK,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOL,EAAKI,EAAI,CAAE,EAAI,GAE3CE,CACR,CAEG1B,GAAUA,EAAO8B,QACnB9B,EAAO8B,QAAUR,EACRrB,GAAUA,EAAO8B,IAC1B9B,GAAO,WAAa,OAAOqB,CAAO,IAElChB,KAAKqD,OAASrC,CAGf,CA5FD,CA6FEhB,EAC+BN,GAC/B,qFC3EF,SAAWD,EAAQ6D,EAAMC,GAKzB,IAQIC,EARAC,EAAQ,IAIRC,EAAaH,EAAKI,IAAIF,EAHb,GAITG,EAAeL,EAAKI,IAAI,EAHf,IAITE,EAA0B,EAAfD,EACXE,EAAOL,IAOX,SAASM,EAAWlE,EAAMmE,EAASC,GACjC,IAAIC,EAAM,GAINC,EAAYC,EAAOC,GAHvBL,EAAsB,GAAXA,EAAmB,CAAEM,SAAS,GAAUN,GAAW,CAAA,GAIpDM,QAAU,CAACzE,EAAM0E,EAASjB,IACzB,MAARzD,EA8IL,WACE,IACE,IAAI2E,EAQJ,OAPIhB,IAAegB,EAAMhB,EAAWiB,aAElCD,EAAMA,EAAIf,IAEVe,EAAM,IAAIE,WAAWjB,IACpBhE,EAAOkF,QAAUlF,EAAOmF,UAAUC,gBAAgBL,IAE9CD,EAASC,EAKjB,CAJC,MAAOM,GACP,IAAIC,EAAUtF,EAAOuF,UACjBC,EAAUF,GAAWA,EAAQE,QACjC,MAAO,EAAE,IAAIrC,KAAMnD,EAAQwF,EAASxF,EAAOyF,OAAQX,EAASjB,GAC7D,CACF,CA9JoB6B,GAAatF,EAAM,GAAIqE,GAGtCkB,EAAO,IAAIC,EAAKnB,GAIhB9C,EAAO,WAIT,IAHA,IAAItB,EAAIsF,EAAKE,EA5BJ,GA6BLjD,EAAIqB,EACJ7B,EAAI,EACD/B,EAAI8D,GACT9D,GAAKA,EAAI+B,GAAK4B,EACdpB,GAAKoB,EACL5B,EAAIuD,EAAKE,EAAE,GAEb,KAAOxF,GAAK+D,GACV/D,GAAK,EACLuC,GAAK,EACLR,KAAO,EAET,OAAQ/B,EAAI+B,GAAKQ,CACrB,EAUE,OARAjB,EAAKC,MAAQ,WAAa,OAAmB,EAAZ+D,EAAKE,EAAE,EAAS,EACjDlE,EAAKG,MAAQ,WAAa,OAAO6D,EAAKE,EAAE,GAAK,UAAc,EAC3DlE,EAAKE,OAASF,EAGdgD,EAAOG,EAASa,EAAKG,GAAIjC,IAGjBU,EAAQwB,MAAQvB,GACpB,SAAS7C,EAAMvB,EAAM4F,EAActE,GAUjC,OATIA,IAEEA,EAAMoE,GAAKzE,EAAKK,EAAOiE,GAE3BhE,EAAKD,MAAQ,WAAa,OAAOL,EAAKsE,EAAM,CAAE,EAAI,GAKhDK,GAAgBlC,EAAY,OAAInC,EAAavB,GAIrCuB,CACb,GACLA,EACA+C,EACA,WAAYH,EAAUA,EAAQvE,OAAUO,MAAQuD,EAChDS,EAAQ7C,MACT,CAYD,SAASkE,EAAKnB,GACZ,IAAIzD,EAAGiF,EAASxB,EAAI7D,OAChBN,EAAKC,KAAMI,EAAI,EAAGoC,EAAIzC,EAAGK,EAAIL,EAAGyC,EAAI,EAAGmD,EAAI5F,EAAGwF,EAAI,GAMtD,IAHKG,IAAUxB,EAAM,CAACwB,MAGftF,EAAIqD,GACTkC,EAAEvF,GAAKA,IAET,IAAKA,EAAI,EAAGA,EAAIqD,EAAOrD,IACrBuF,EAAEvF,GAAKuF,EAAEnD,EAAIsB,EAAQtB,EAAI0B,EAAI9D,EAAIsF,IAAWjF,EAAIkF,EAAEvF,KAClDuF,EAAEnD,GAAK/B,GAIRV,EAAGuF,EAAI,SAASM,GAIf,IAFA,IAAInF,EAAGoF,EAAI,EACPzF,EAAIL,EAAGK,EAAGoC,EAAIzC,EAAGyC,EAAGmD,EAAI5F,EAAGwF,EACxBK,KACLnF,EAAIkF,EAAEvF,EAAI0D,EAAQ1D,EAAI,GACtByF,EAAIA,EAAIpC,EAAQkC,EAAE7B,GAAS6B,EAAEvF,GAAKuF,EAAEnD,EAAIsB,EAAQtB,EAAI/B,KAAQkF,EAAEnD,GAAK/B,IAGrE,OADAV,EAAGK,EAAIA,EAAGL,EAAGyC,EAAIA,EACVqD,CAIR,GAAEpC,EACJ,CAMD,SAAS3C,EAAKC,EAAGN,GAIf,OAHAA,EAAEL,EAAIW,EAAEX,EACRK,EAAE+B,EAAIzB,EAAEyB,EACR/B,EAAE8E,EAAIxE,EAAEwE,EAAE5C,QACHlC,CAET,CAKA,SAAS4D,EAAQyB,EAAKC,GACpB,IAAqCC,EAAjC9D,EAAS,GAAI+D,SAAcH,EAC/B,GAAIC,GAAgB,UAAPE,EACX,IAAKD,KAAQF,EACX,IAAM5D,EAAOO,KAAK4B,EAAQyB,EAAIE,GAAOD,EAAQ,GAAmB,CAAZ,MAAOjB,GAAK,CAGpE,OAAQ5C,EAAO7B,OAAS6B,EAAgB,UAAP+D,EAAkBH,EAAMA,EAAM,IAChE,CAOD,SAAS1B,EAAOvE,EAAMqE,GAEpB,IADA,IAA4BgC,EAAxBC,EAAatG,EAAO,GAAW2C,EAAI,EAChCA,EAAI2D,EAAW9F,QACpB6D,EAAIJ,EAAOtB,GACTsB,GAASoC,GAAyB,GAAhBhC,EAAIJ,EAAOtB,IAAW2D,EAAW5F,WAAWiC,KAElE,OAAO+B,EAASL,EACjB,CA6BD,SAASK,EAASpB,GAChB,OAAOhD,OAAOiG,aAAaC,MAAM,EAAGlD,EACrC,CAeD,GANAiB,EAAOb,EAAK+C,SAAUhD,GAMa5D,EAAO8B,QAAS,CACjD9B,EAAA8B,QAAiBuC,EAEjB,IACEP,EAAa+C,CACA,CAAb,MAAOC,GAAM,OAKfjD,EAAqB,WAAIQ,CAK1B,CA9ND,CAiOmB,oBAAT0C,KAAwBA,KAAOzG,EACvC,GACA+C,cC/OErB,EAAO6E,EAKPpE,EAASuE,EAKTpE,EAASqE,EAQT9D,EAAY+D,EASZ3D,EAAU4D,EAOVxD,EAASyD,EAITC,YAEJA,EAAGrF,KAAOA,EACVqF,EAAG5E,OAASA,EACZ4E,EAAGzE,OAASA,EACZyE,EAAGlE,UAAYA,EACfkE,EAAG9D,QAAUA,EACb8D,EAAG1D,OAASA,EAEZ,ICw5BY2D,EDx5BZjD,EAAiBgD,EEEjB,SAASE,EACLC,EAAYC,EACZC,EAAsB,IAAIC,IAAOC,EAAuB,IAAIC,KAE9D,GAAa,MAATL,EACF,OAAO,KAET,GAAoB,mBAATM,MAAuBN,aAAiBM,KACjD,OAAON,EAAMvE,QAGf,GAAI2E,EAAYG,IAAIP,GAClB,MAAM,IAAIQ,MAAM,0CAElB,GAAIN,EAAKK,IAAIP,GACX,OAAOE,EAAKO,IAAIT,GAElB,MAAMhF,EAASiF,EAAMD,GAErB,GAAIhF,EAAO0F,SAA4B,OAAjB1F,EAAO2F,MAC3B,MAAM,IAAIH,MACN,qEAGN,GAAKxF,EAAO0F,QAGL,IAAIE,EAAWZ,GAAQ,CAE5B,MAAMa,EAA4BC,MAAMC,QAAQf,GAAS,GAAK,GAC9DI,EAAYY,IAAIhB,GAChB,IAAK,MAAMjF,KAAKiF,EAAO,CACrB,MACMiB,EAAclB,EADNC,EAAMjF,GACuBkF,EAAOC,EAAME,GACxDS,EAAe9F,GAAKkG,CACrB,CAKD,OAJAb,EAAYc,OAAOlB,GACfA,EAAMmB,YACRN,EAAeM,UAAYnB,EAAMmB,WAE5BN,CACR,CACC,MAAM,IAAIL,MAAM,yCAAyCR,IAC1D,CAjBC,OADAE,EAAKkB,IAAIpB,EAAOhF,EAAO2F,OAChB3F,EAAO2F,KAkBlB,UA2BgBU,EACZC,EAAeC,EAAsCC,GACvD,OAAOC,EAAgBH,EAAQC,EACjC,CAMA,SAASE,EACLH,EAAeC,EACfnB,EAAuB,IAAIC,KAG7B,MAAML,EAAQsB,EAAO,GACrB,GAAIlB,EAAYG,IAAIP,GAClB,MAAM,IAAIQ,MAAM,0CAElB,MAAMxF,EAASuG,EAAMD,GAErB,GAAItG,EAAO0F,SAA4B,OAAjB1F,EAAO2F,MAC3B,MAAM,IAAIH,MACN,qEAGN,GAAKxF,EAAO0F,QAEL,IAAIE,EAAWZ,GAAQ,CAE5B,MAAMa,EAA4BC,MAAMC,QAAQf,GAAS,GAAK,GAC9DI,EAAYY,IAAIhB,GAChB,IAAK,MAAMjF,KAAKiF,EAAO,CACrB,MACMiB,EAAcQ,EADHH,EAAOI,KAAI/G,GAAKA,EAAEI,KACWwG,EAAOnB,GACrDS,EAAe9F,GAAKkG,CACrB,CAED,OADAb,EAAYc,OAAOlB,GACZa,CACR,CACC,MAAM,IAAIL,MAAM,yCAAyCR,IAC1D,CAdC,OAAOhF,EAAO2F,KAelB,CAGM,SAAUa,EAAU7G,GACxB,OAAU,OAANA,EACK,KAILiG,EAAWjG,EAAE,IACR,CAACgG,MAAO,KAAMD,SAAS,GAEvB,CAACC,MAAOhG,EAAG+F,SAAS,EAE/B,CAmCOiB,eAAeC,EAClB5B,EAAYC,GACd,MAAMC,EAAsB,IAAIC,IAGhCJ,EAAgBC,EAAOC,EAAOC,GAM9B,IAAK,MAAMlD,KAAO8D,MAAMe,KAAK3B,EAAK4B,QAAS,CACzC,MAAMnB,EAAQT,EAAKO,IAAIzD,GACvB,GAAI+E,EAAGC,KAAKC,UAAUtB,GAAQ,CAC5B,MAAMuB,QAAoBvB,EAC1BT,EAAKkB,IAAIpE,EAAKkF,EACf,CACF,CAMD,OADenC,EAAgBC,EAAOC,EAAOC,EAE/C,CAQM,SAAUU,EAAWhC,GACzB,IAAIuD,GAAgB,EACpB,GAAIJ,EAAGK,MAAM3B,IAAI,cACf0B,EAAgBvD,aAAeyD,gBAC1B,CAEL,MAAMC,cAACA,GAAiBC,QAAQ,kBAChCJ,EAAgBvD,aAAe0D,CAChC,CACD,OAAc,MAAP1D,IAAiB4D,YAAYC,OAAO7D,KACtCkC,MAAMC,QAAQnC,IACE,iBAARA,KAAsBA,aAAemD,EAAGW,WAC7C9D,aAAe+D,WAAaR,EACtC,CCtPM,SAAUS,EAAaC,GAC3B,OD8BO9C,EC9BQ8C,EAAWC,EAC5B,CAGA,SAASA,EAAcC,GACrB,OAAIA,aAAgBhB,EAAGW,OACb,CAAC/B,MAAOoC,EAAKC,QAAStC,SAAS,GAC9BE,EAAWmC,GACb,CAACpC,MAAO,KAAMD,SAAS,GAEvB,CAACC,MAAOoC,EAAMrC,SAAS,EAElC,OCbauC,EAcXC,YAAmBC,GACjB,GADiBrK,KAAQqK,SAARA,EAVTrK,KAAAsK,MAAQ,EACRtK,KAAAuK,IAAM,EAUE,MAAZF,EACF,MAAM,IAAIG,WAAW,mDAEvB,GAAIH,EAAW,EACb,MAAM,IAAIG,WAAW,6CAEvBxK,KAAKE,KAAO,IAAI8H,MAASqC,GACzBrK,KAAKyK,gBAAkB,EAAIJ,CAC5B,CAKSK,KAAKC,GAEb,KAAOA,EAAQ,GACbA,GAAS3K,KAAKyK,gBAEhB,OAAOE,EAAQ3K,KAAKyK,eACrB,CAES9C,IAAIgD,GACZ,GAAIA,EAAQ,EACV,MAAM,IAAIH,WAAW,uCAEvB,OAAOxK,KAAKE,KAAKyK,EAAQ3K,KAAKqK,SAC/B,CAES/B,IAAIqC,EAAe9C,GAC3B,GAAI8C,EAAQ,EACV,MAAM,IAAIH,WAAW,uCAEvBxK,KAAKE,KAAKyK,EAAQ3K,KAAKqK,UAAYxC,CACpC,CAKDxH,SACE,IAAIA,EAASL,KAAKuK,IAAMvK,KAAKsK,MAI7B,OAHIjK,EAAS,IACXA,EAASL,KAAKyK,gBAAkBpK,GAE3BA,CACR,CAODuK,SACE,OAAO5K,KAAKK,WAAaL,KAAKqK,QAC/B,CAODQ,UACE,OAAyB,IAAlB7K,KAAKK,QACb,CAKDoC,KAAKoF,GACH,GAAI7H,KAAK4K,SACP,MAAM,IAAIJ,WAAW,wBAEvBxK,KAAKsI,IAAItI,KAAKuK,IAAK1C,GACnB7H,KAAKuK,IAAMvK,KAAK0K,KAAK1K,KAAKuK,IAAM,EACjC,CAKDO,QAAQC,GACN,IAAK,MAAMlD,KAASkD,EAClB/K,KAAKyC,KAAKoF,EAEb,CAKDmD,MACE,GAAIhL,KAAK6K,UACP,MAAM,IAAIL,WAAW,yBAEvBxK,KAAKuK,IAAMvK,KAAK0K,KAAK1K,KAAKuK,IAAM,GAChC,MAAMrI,EAASlC,KAAK2H,IAAI3H,KAAKuK,KAE7B,OADAvK,KAAKsI,IAAItI,KAAKuK,SAAKU,GACZ/I,CACR,CAKDgJ,QAAQrD,GACN,GAAI7H,KAAK4K,SACP,MAAM,IAAIJ,WAAW,wBAEvBxK,KAAKsK,MAAQtK,KAAK0K,KAAK1K,KAAKsK,MAAQ,GACpCtK,KAAKsI,IAAItI,KAAKsK,MAAOzC,EACtB,CAKDsD,QACE,GAAInL,KAAK6K,UACP,MAAM,IAAIL,WAAW,yBAEvB,MAAMtI,EAASlC,KAAK2H,IAAI3H,KAAKsK,OAG7B,OAFAtK,KAAKsI,IAAItI,KAAKsK,WAAOW,GACrBjL,KAAKsK,MAAQtK,KAAK0K,KAAK1K,KAAKsK,MAAQ,GAC7BpI,CACR,CAWDkJ,cAAcC,GACZ,GAAIrL,KAAK6K,UACP,MAAM,IAAIL,WAAW,yBAEvB,MAAMG,EAAQ3K,KAAK0K,KAAK1K,KAAKsK,MAAQe,GAC/BnJ,EAASlC,KAAK2H,IAAIgD,GAExB,OADA3K,KAAKsI,IAAIqC,EAAO3K,KAAKgL,OACd9I,CACR,EC1JH,MAAaoJ,UAA6BnB,EAMxCC,cACEmB,MAAMD,EAAkBE,iBACzB,CAEQZ,SACP,OAAO,CACR,CAEQnI,KAAKoF,GACR0D,MAAMX,UACR5K,KAAKyL,SAEPF,MAAM9I,KAAKoF,EACZ,CAEQqD,QAAQrD,GACX0D,MAAMX,UACR5K,KAAKyL,SAEPF,MAAML,QAAQrD,EACf,CAKO4D,SACN,MAAMC,EAA8B,EAAhB1L,KAAKqK,SACnBsB,EAAU,IAAI3D,MAAS0D,GACvBE,EAAM5L,KAAKK,SAIjB,IAAK,IAAID,EAAI,EAAGA,EAAIwL,EAAKxL,IACvBuL,EAAQvL,GAAKJ,KAAK2H,IAAI3H,KAAK0K,KAAK1K,KAAKsK,MAAQlK,IAG/CJ,KAAKE,KAAOyL,EACZ3L,KAAKqK,SAAWqB,EAChB1L,KAAKyK,gBAAkB,EAAIzK,KAAKqK,SAChCrK,KAAKsK,MAAQ,EACbtK,KAAKuK,IAAMqB,CACZ,EJ5BG,SAAUC,EAAqBC,GACnC,OAAO,IAAIC,EAAcD,EAC3B,CAuBM,SAAUE,EACZC,GAEF,OAAO,IAAIC,EAAqBD,EAClC,CI/CiBX,EAAgBE,iBAAG,SJ+HdW,EAwBpBtD,gBACE,MAAM3G,EAAc,GACpB,IAAIL,QAAU7B,KAAKQ,OACnB,MAAQqB,EAAEuK,MACRlK,EAAOO,KAAKZ,EAAEgG,OACdhG,QAAU7B,KAAKQ,OAEjB,OAAO0B,CACR,CAaD2G,uBACE,MAAMwD,EAASrM,KAAKsM,SAAS,KACvBpK,EAAc,GACpB,IAAIL,QAAUwK,EAAO7L,OACrB,MAAQqB,EAAEuK,MACRlK,EAAOO,KAAKZ,EAAEgG,OACdhG,QAAUwK,EAAO7L,OAEnB,OAAO0B,CACR,CASD2G,qBACE,IAAIhH,QAAU7B,KAAKQ,OACnB,MAAQqB,EAAEuK,MACRvK,QAAU7B,KAAKQ,MAElB,CASDqI,mBAAmB0D,GACjB,IAAI1K,QAAU7B,KAAKQ,OACfgM,EAAiBD,EAAU1K,EAAEgG,OACjC,MAAShG,EAAEuK,MAASI,GAClB3K,QAAU7B,KAAKQ,OACfgM,EAAiBD,EAAU1K,EAAEgG,MAEhC,CAcD4E,aAAaC,GACX,OAAO,IAAIC,EAA0B3M,KAAM0M,EAC5C,CAYDE,OAAOL,GACL,OAAO,IAAIM,EAAe7M,KAAMuM,EACjC,CAUD3D,IAAOkE,GACL,OAAO,IAAIC,EAAY/M,KAAM8M,EAC9B,CAUDE,SAAYF,GACV,OAAO,IAAIG,EAAiBjN,KAAM8M,EACnC,CAUDI,eAAkBJ,GAChB,OAAO,IAAIG,EAAiBjN,KAAM8M,GAAWK,QAC9C,CAUDC,QAAWN,GACT,OAAO,IAAIO,EAAgBrN,KAAM8M,EAClC,CAODjE,mBAAmB9H,GACjB,OAAOf,KAAK4I,IAAI7H,GAAGuM,cACpB,CASDzE,oBAAoB9H,GAClB,OAAOf,KAAKkN,eAAenM,GAAGwM,cAAa1L,IAAY,IAANA,GAClD,CAoBD2L,cAAcC,EAAmBC,GAAiB,GAChD,OAAO,IAAIC,EAAsB3N,KAAMyN,EAAWC,EACnD,CAkCDE,iBACIH,EAAmBC,GAAiB,EAEpCjF,EAAsCC,GAMxC,OAHmB1I,KAAKwN,cAAcC,EAAWC,GAG/B9E,KAAI/G,GAAK0G,EAAQ1G,EAAG4G,IACvC,CAYDoF,YACIC,EACAC,GACF,OAAO,IAAIC,GACPnC,EAAkB,CAAC7L,KAAM8N,IAAYC,EAC1C,CASDE,KAAKrI,GACH,OAAIA,EAAQ,GAAc,MAATA,EACR5F,KAEF,IAAIkO,EAAalO,KAAM4F,EAC/B,CAQDuI,KAAKvI,GACH,OAAIA,EAAQ,GAAc,MAATA,EACR5F,KAEF,IAAIoO,EAAapO,KAAM4F,EAC/B,CAWD0G,SAAS+B,GACP,OAAO,IAAIC,GAAiBtO,KAAMqO,EACnC,CAYDE,QAAQC,EAAoB3O,GAC1B,OAAO,IAAI4O,GAAgBzO,KAAMwO,EAAY3O,EAC9C,CAMDsN,SACE,OAAO,IAAIuB,EAAe1O,KAC3B,EAYH,MAAM+L,UAAyBI,EAE7B/B,YAAsB0B,GACpBP,QADoBvL,KAAK8L,MAALA,EADd9L,KAAI2O,KAAG,CAGd,CAEDC,UACE,MAAO,YAAY5O,KAAK8L,MAAMzL,cAC/B,CAEDwI,aACE,GAAI7I,KAAK2O,MAAQ3O,KAAK8L,MAAMzL,OAC1B,MAAO,CAACwH,MAAO,KAAMuE,MAAM,GAE7B,MAAMnC,EAAOjK,KAAK8L,MAAM9L,KAAK2O,MAE7B,OADA3O,KAAK2O,OACE,CAAC9G,MAAOiC,EAAUG,GAAOmC,MAAM,EACvC,EAGH,MAAMF,UAAgCC,EACpC/B,YACcyE,GACZtD,QADYvL,KAAM6O,OAANA,CAEb,CAEDD,UACE,MAAO,eACR,CAED/F,aACE,IACE,OAAO7I,KAAK6O,QAMb,CALC,MAAO/J,GAIP,MAFAA,EAAEgK,QACE,mDAAmDhK,EAAEgK,UACnDhK,CACP,CACF,EAGH,MAAM4J,UAA0BvC,EAK9B/B,YAAsB2E,GACpBxD,QADoBvL,KAAQ+O,SAARA,EAEpB/O,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDwC,UACE,MAAO,GAAG5O,KAAK+O,SAASH,qBACzB,CAED/F,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEOnG,mBACN,OAAO7I,KAAK+O,SAASvO,MACtB,EAGH,MAAM4N,UAAwBjC,EAQ5B/B,YAAsB2E,EAAqCK,GACzD7D,QADoBvL,KAAQ+O,SAARA,EAAqC/O,KAAQoP,SAARA,EAF3DpP,KAAK4F,MAAG,EAIN5F,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDwC,UACE,MAAO,GAAG5O,KAAK+O,SAASH,mBACzB,CAED/F,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEOnG,mBAKN,KAAO7I,KAAK4F,QAAU5F,KAAKoP,UAAU,CACnC,MAAMC,QAAgBrP,KAAK+O,SAASvO,OAEpC,GAAI6O,EAAQjD,KACV,OAAOiD,EAETpG,EAAGqG,QAAQD,EAAQxH,MACpB,CACD,OAAO7H,KAAK+O,SAASvO,MACtB,EAGH,MAAM0N,UAAwB/B,EAE5B/B,YAAsB2E,EAAqCK,GACzD7D,QADoBvL,KAAQ+O,SAARA,EAAqC/O,KAAQoP,SAARA,EAD3DpP,KAAK4F,MAAG,CAGP,CAEDgJ,UACE,MAAO,GAAG5O,KAAK+O,SAASH,mBACzB,CAED/F,aACE,OAAI7I,KAAK4F,SAAW5F,KAAKoP,SAChB,CAACvH,MAAO,KAAMuE,MAAM,GAEtBpM,KAAK+O,SAASvO,MACtB,EAMH,MAAMmN,UAAiCxB,EAKrC/B,YACc2E,EAAqCtB,EACrC8B,GAAuB,GACnChE,QAFYvL,KAAQ+O,SAARA,EAAqC/O,KAASyN,UAATA,EACrCzN,KAAoBuP,qBAApBA,EAEZvP,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDwC,UACE,MAAO,GAAG5O,KAAK+O,SAASH,4BACzB,CAED/F,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEOnG,mBACN,MAAM2G,EAAa,GACnB,KAAOA,EAAMnP,OAASL,KAAKyN,WAAW,CACpC,MAAMxD,QAAajK,KAAK+O,SAASvO,OACjC,GAAIyJ,EAAKmC,KACP,OAAIpM,KAAKuP,sBAAwBC,EAAMnP,OAAS,EACvC,CAACwH,MAAO2H,EAAOpD,MAAM,GAEvB,CAACvE,MAAO,KAAMuE,MAAM,GAE7BoD,EAAM/M,KAAKwH,EAAKpC,MACjB,CACD,MAAO,CAACA,MAAO2H,EAAOpD,MAAM,EAC7B,EAGH,MAAMS,UAA0BV,EAK9B/B,YACc2E,EACAxC,GACZhB,QAFYvL,KAAQ+O,SAARA,EACA/O,KAASuM,UAATA,EAEZvM,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDwC,UACE,MAAO,GAAG5O,KAAK+O,SAASH,qBACzB,CAED/F,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEOnG,mBACN,OAAa,CACX,MAAMoB,QAAajK,KAAK+O,SAASvO,OACjC,GAAIyJ,EAAKmC,MAAQpM,KAAKuM,UAAUtC,EAAKpC,OACnC,OAAOoC,EAEThB,EAAGqG,QAAQrF,EAAKpC,MACjB,CACF,EAGH,MAAMkF,UAA0BZ,EAC9B/B,YACc2E,EACAjC,GACZvB,QAFYvL,KAAQ+O,SAARA,EACA/O,KAAS8M,UAATA,CAEb,CAED8B,UACE,MAAO,GAAG5O,KAAK+O,SAASH,kBACzB,CAED/F,aACE,MAAMoB,QAAajK,KAAK+O,SAASvO,OACjC,GAAIyJ,EAAKmC,KACP,MAAO,CAACvE,MAAO,KAAMuE,MAAM,GAE7B,MAAMqD,EAAexG,EAAGyG,YAAYC,sBAAsB1F,EAAKpC,OAOzD+H,EAAS5P,KAAK8M,UAAU7C,EAAKpC,OAC7BgI,EAAgB5G,EAAGyG,YAAYC,sBAAsBC,GAI3D,IAAK,MAAMnP,KAAKgP,EACTxG,EAAGyG,YAAYI,eAAerP,EAAGoP,IACpCpP,EAAE6O,UAGN,MAAO,CAACzH,MAAO+H,EAAQxD,MAAM,EAC9B,EAGH,MAAMO,UAAqCR,EAEzC/B,YACc2E,EACArC,GACZnB,QAFYvL,KAAQ+O,SAARA,EACA/O,KAAO0M,QAAPA,EAHd1M,KAAK4F,MAAG,EAKN5F,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDwC,UACE,MAAO,GAAG5O,KAAK+O,SAASH,2BACzB,CAMD/F,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEDnG,mBACE,OACE,IACE,aAAa7I,KAAK+O,SAASvO,MAU5B,CATC,MAAOsE,GACP,IAAK9E,KAAK0M,QAAQ5H,GAChB,MAAO,CAAC+C,MAAO,KAAMuE,MAAM,EAO9B,CAEJ,EAGH,MAAMa,UAA+Bd,EACnC/B,YACc2E,EACAjC,GACZvB,QAFYvL,KAAQ+O,SAARA,EACA/O,KAAS8M,UAATA,CAEb,CAED8B,UACE,MAAO,GAAG5O,KAAK+O,SAASH,uBACzB,CAED/F,aACE,MAAMoB,QAAajK,KAAK+O,SAASvO,OACjC,GAAIyJ,EAAKmC,KACP,MAAO,CAACvE,MAAO,KAAMuE,MAAM,GAE7B,MAAMqD,EAAexG,EAAGyG,YAAYC,sBAAsB1F,EAAKpC,OAOzD+H,QAAe5P,KAAK8M,UAAU7C,EAAKpC,OACnCgI,EAAgB5G,EAAGyG,YAAYC,sBAAsBC,GAI3D,IAAK,MAAMnP,KAAKgP,EACTxG,EAAGyG,YAAYI,eAAerP,EAAGoP,IACpCpP,EAAE6O,UAGN,MAAO,CAACzH,MAAO+H,EAAQxD,MAAM,EAC9B,EAcG,MAAgB2D,UAA6B5D,EAQjD/B,cACEmB,QACAvL,KAAKgQ,YAAc,IAAI1E,EACvBtL,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEDvD,aAME,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAgBDnG,mBAIE,KAAqC,IAA9B7I,KAAKgQ,YAAY3P,UAEtB,UAAWL,KAAKiQ,OACd,MAAO,CAACpI,MAAO,KAAMuE,MAAM,GAG/B,MAAO,CAACvE,MAAO7H,KAAKgQ,YAAY7E,QAASiB,MAAM,EAChD,EAEH,MAAMiB,UAA8B0C,EAClC3F,YACc2E,EACAjC,GACZvB,QAFYvL,KAAQ+O,SAARA,EACA/O,KAAS8M,UAATA,CAEb,CAED8B,UACE,MAAO,GAAG5O,KAAK+O,SAASH,sBACzB,CAED/F,aACE,MAAMoB,QAAajK,KAAK+O,SAASvO,OACjC,GAAIyJ,EAAKmC,KACP,OAAO,EAET,MAAMqD,EAAexG,EAAGyG,YAAYC,sBAAsB1F,EAAKpC,OAMzDqI,EAAclQ,KAAK8M,UAAU7C,EAAKpC,OAClCgI,EACF5G,EAAGyG,YAAYC,sBAAsBO,GACzClQ,KAAKgQ,YAAYlF,QAAQoF,GAIzB,IAAK,MAAMzP,KAAKgP,EACTxG,EAAGyG,YAAYI,eAAerP,EAAGoP,IACpCpP,EAAE6O,UAIN,OAAO,CACR,EAYG,MAAOtB,WAA2B7B,EAStC/B,YACI+F,EACiBpC,GACnBxC,QADmBvL,KAAgB+N,iBAAhBA,EARb/N,KAAQgP,SAA+B,KAGvChP,KAAQ8N,SAAoB,KAOlC9N,KAAKoQ,cAAgBD,CACtB,CAEDvB,UAEE,MAAO,wDACR,CAED/F,aAEE,OADA7I,KAAKgP,SAAWhP,KAAKqQ,cAAcrQ,KAAKgP,UACjChP,KAAKgP,QACb,CAEOnG,oBAAoBmG,GAQ1B,SADMA,EACe,MAAjBhP,KAAK8N,SAAkB,CACzB,MAAMwC,QAAuBtQ,KAAKoQ,cAAc5P,OAChD,GAAI8P,EAAelE,KAEjB,MAAO,CAACvE,MAAO,KAAMuE,MAAM,GAE7BpM,KAAK8N,SAAWwC,EAAezI,MACF,MAAzB7H,KAAK+N,mBACP/N,KAAK8N,SAAW9N,KAAK8N,SAASrB,aAAazM,KAAK+N,kBAEnD,CACD,MAAMwC,QAAmBvQ,KAAK8N,SAAStN,OACvC,OAAI+P,EAAWnE,MACbpM,KAAK8N,SAAW,KACT9N,KAAKqQ,cAAcrB,IAErBuB,CACR,GAGH,SAAYvJ,GACVA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAOA,EAAA,QAAA,GAAA,SACR,CAJD,CAAYA,IAAAA,EAIX,CAAA,IA+BD,MAAMwJ,WAAkDrE,EAItD/B,YACuB+F,EACAM,EAAgCzJ,EAAgB0J,MACrEnF,QAFqBvL,KAASmQ,UAATA,EACAnQ,KAAYyQ,aAAZA,EALfzQ,KAAK4F,MAAG,EACR5F,KAAc2Q,eAA+B,IAMpD,CAED/B,UAEE,MAAO,kDACR,CAEO/F,gBAAgB+H,SAIhBA,EAIN,IAAIC,EAAe,EACfC,EAAgB,EAoBpB,MAAMlB,QAAkB9G,EAAmB9I,KAAKmQ,WAlBhD,SAAiBpG,GACf,GAAIA,aAAqBoC,EAAc,CAErC,MAAO,CACLtE,MAFakC,EAAUvJ,OAET0O,MAAKrN,IACjBgP,IACIhP,EAAEuK,MACJ0E,IAEKjP,EAAEgG,SAEXD,SAAS,EAEZ,CACC,MAAO,CAACC,MAAO,KAAMD,SAAS,EAEjC,IAID,GAAIiJ,IAAiBC,EAEnB,MAAO,CAACjJ,MAAO,KAAMuE,MAAM,GAE7B,GAAI0E,EAAgB,EAClB,OAAQ9Q,KAAKyQ,cACX,KAAKzJ,EAAgB0J,KACnB,MAAM,IAAIhJ,MAEN,qEAAyB1H,KAAK4F,UACpC,KAAKoB,EAAgB+J,SACnB,MAAO,CAAClJ,MAAO,KAAMuE,MAAM,GAC7B,KAAKpF,EAAgBgK,SAOzB,OADAhR,KAAK4F,QACE,CAACiC,MAAO+H,EAAQxD,MAAM,EAC9B,CAEDvD,aAEE,OADA7I,KAAK2Q,eAAiB3Q,KAAKiR,UAAUjR,KAAK2Q,gBACnC3Q,KAAK2Q,cACb,EAaG,MAAOrC,WAA4BnC,EAGvC/B,YACc2E,EAAqCV,GACjD9C,QADYvL,KAAQ+O,SAARA,EAAqC/O,KAAUqO,WAAVA,EAEjDrO,KAAKkR,OAAS,IAAI/G,EAAuCkE,EAC1D,CAEDO,UACE,MAAO,GAAG5O,KAAK+O,SAASH,uBACzB,CAMSuC,SACR,MAAQnR,KAAKkR,OAAOtG,UAAU,CAC5B,MAAMxI,EAAIpC,KAAK+O,SAASvO,OACxBR,KAAKkR,OAAOzO,KAAKL,EAClB,CACF,CAED5B,OAKE,OAJAR,KAAKmR,SAIEnR,KAAKkR,OAAO/F,OACpB,EASG,MAAOsD,WAA2BH,GAUtClE,YACqB2E,EAAqCP,EACtD3O,GACF0L,MAAMwD,EAAUP,GAFGxO,KAAQ+O,SAARA,EAAqC/O,KAAUwO,WAAVA,EAHlDxO,KAAiBoR,mBAAG,EAM1BpR,KAAKsG,OAAS+K,EAAe3P,KAAC7B,GAAQoJ,EAAGC,KAAKoI,MAAMC,YACpDvR,KAAKgP,SAAWnF,QAAQoF,QAAQ,CAACpH,MAAO,KAAMuE,MAAM,GACrD,CAEQvD,aAMP,OADA7I,KAAKgP,SAAWhP,KAAKgP,SAASE,MAAK,IAAMlP,KAAKmP,eACvCnP,KAAKgP,QACb,CAEOwC,UAAUxO,GAChB,OAAOD,KAAKK,MAAMpD,KAAKsG,SAAWtD,EACnC,CAESyO,cACR,OAAOzR,KAAKwR,UAAUxR,KAAKkR,OAAO7Q,SACnC,CAEDwI,mBAKE,IAHK7I,KAAKoR,mBACRpR,KAAKmR,UAECnR,KAAKkR,OAAOrG,WAAW,CAC7B,MAAM6G,EAAc1R,KAAKyR,cACnBvP,QAAelC,KAAKkR,OAAO9F,cAAcsG,GAC/C,IAAIxP,EAAOkK,KAIT,OADApM,KAAKmR,SACEjP,EAHPlC,KAAKoR,mBAAoB,CAK5B,CACD,MAAO,CAACvJ,MAAO,KAAMuE,MAAM,EAC5B,EKvmCH,MAAsBuF,GAAtBvH,cAWWpK,KAAI4R,KAAW,IA2czB,CA5YCpC,MAAM/B,EAAmBC,GAAiB,GACxC,MAAMmE,EAAO7R,KAIb,IAAI4R,EAcJ,OAjBA3I,EAAGC,KAAK4I,OACJrE,EAAY,GAAG,IAAM,oDACrBA,MAKFmE,EAHE5R,KAAK4R,OAASG,KAAyB,MAAb/R,KAAK4R,KAG1B5R,KAAK4R,KACHlE,EAGF3K,KAAKiP,KAAKhS,KAAK4R,KAAOnE,GAItB1K,KAAKK,MAAMpD,KAAK4R,KAAOnE,GAEzBwE,IAAsBpJ,gBACbgJ,EAAK/D,YACdF,iBAAiBH,EAAWC,EAAgBwE,KAChDN,EACJ,CAiBD/D,YAAYsE,GACV,MAAMN,EAAO7R,KACb,IAAI4R,EAcJ,OAVEA,EAHE5R,KAAK4R,OAASG,KAAYI,EAAQP,OAASG,IAGtCA,IACe,MAAb/R,KAAK4R,MAAgC,MAAhBO,EAAQP,KAG/B5R,KAAK4R,KAAOO,EAAQP,KAIpB,KAEFK,IACHpJ,gBACWgJ,EAAK/D,YAAYD,kBAAkBsE,EAAQrE,aACtD8D,EACL,CAkBDhF,OAAOL,GACL,MAAMsF,EAAO7R,KACb,IAAI4R,EASJ,OANEA,EAFE5R,KAAK4R,OAASG,IAETA,IAIA,KAEFE,IAAsBpJ,gBACbgJ,EAAK/D,YAAYlB,QAAO/K,GAAKoH,EAAGmJ,MAAK,IAAM7F,EAAU1K,QAClE+P,EACJ,CAkBD/I,mBAAmB9H,GACjB,aAAcf,KAAK8N,YAAYuE,aAAatR,EAC7C,CAiBD6H,IAAkCkE,GAChC,MAAM+E,EAAO7R,KACb,OAAOiS,IAAsBpJ,gBACbgJ,EAAK/D,YAAYlF,KAAI/G,GAAKoH,EAAGmJ,MAAK,IAAMtF,EAAUjL,QAC/D7B,KAAK4R,KACT,CAyBD5E,SAAuCF,GAErC,MAAM+E,EAAO7R,KACb,OAAOiS,IAAsBpJ,gBACbgJ,EAAK/D,YAAYd,SAASF,IACvC9M,KAAK4R,KACT,CAWDtF,SAAS+B,GACP,GAAkB,MAAdA,EACF,MAAM,IAAI7D,WACN,6DAGN,MAAMqH,EAAO7R,KACb,OAAOiS,IACHpJ,gBAAmBgJ,EAAK/D,YAAYxB,SAAS+B,IAAarO,KAAK4R,KACpE,CAoBDU,OAAO1M,GACL,MAAMiM,EAAO7R,KACb,IAAI4R,EAiBJ,OAZEA,EAJe,MAAb5R,KAAK4R,MAAgBhM,EAAQ,EAIxB5F,KAAK4R,KAAOhM,EACA,IAAVA,EAEF,EACe,MAAb5F,KAAK4R,YAA2B3G,IAAVrF,GAAuBA,EAAQ,GAGvDmM,IAGA,KAEFE,IAAsBpJ,UAC3B,MAAM0J,EAAmBvG,GACrBnD,UAAa,CAAChB,YAAagK,EAAK/D,WAAY1B,MAAM,MACtD,OLvRFoG,EKuRkCD,EAAiBtE,KAAKrI,GLrRnD,IAAIoI,GAAgBwE,EAAezE,GAH5B,IACZyE,EACAzE,CKsR+D,GAC5D6D,EACJ,CAmBDzD,KAAKvI,GACH,MAAMiM,EAAO7R,KACb,IAAI4R,EAgBJ,OAXEA,EAJe,MAAb5R,KAAK4R,MAAgBhM,GAAS,GAAK5F,KAAK4R,MAAQhM,EAI3C5F,KAAK4R,KAAOhM,EAEJ,MAAb5F,KAAK4R,OACJ5R,KAAK4R,KAAOhM,QAAmBqF,IAAVrF,GAAuBA,EAAQ,GAGhD,EAGA,KAEFqM,IACHpJ,gBAAmBgJ,EAAK/D,YAAYK,KAAKvI,IAAQgM,EACtD,CA2BDrD,QAAQF,EAAoBxO,EAAe4S,GAAyB,GAElE,GAAkB,MAAdpE,GAAsBA,EAAa,EACrC,MAAiB,MAAbrO,KAAK4R,KACD,IAAIpH,WACN,4DAEE,IAAIA,WAIN,mNAAmCxK,KAAK4R,kBAGhD,MAAMC,EAAO7R,KACPsG,EAAS+K,EAAe3P,KAAC7B,GAAQoJ,EAAGC,KAAKoI,MAAMC,YACrD,OAAOU,IAAsBpJ,UAC3B,IAAI6J,EAAQpM,EAAOjF,QAInB,OAHIoR,IACFC,GAASpM,EAAOjF,gBAEJwQ,EAAK/D,YAAYS,QAAQF,EAAYqE,EAAMnB,WAAW,GACnEvR,KAAK4R,KACT,CAmBD3D,KAAKrI,GACH,MAAMiM,EAAO7R,KACb,IAAI4R,EAaJ,OATEA,EAHe,MAAb5R,KAAK4R,MAAgB5R,KAAK4R,KAAOhM,EAG5BA,EACe,MAAb5F,KAAK4R,MAAgB5R,KAAK4R,MAAQhM,EAGpC5F,KAAK4R,KAGL,KAEFK,IACHpJ,gBAAmBgJ,EAAK/D,YAAYG,KAAKrI,IAAQgM,EACtD,CAkBD/I,gBACE,GAAI7I,KAAK4R,OAASG,IAChB,MAAM,IAAIrK,MAAM,kDAElB,aAAc1H,KAAK8N,YAAY6E,SAChC,CAaD9J,uBACE,GAAI7I,KAAK4R,OAASG,IAChB,MAAM,IAAIrK,MAAM,kDAElB,aAAc1H,KAAK8N,YAAY8E,gBAChC,WAeaX,GACZY,EACAjB,EAAe,MACjB,OAAO,IAAI,cAAcD,GAAdvH,kCACApK,KAAI4R,KAAGA,CASjB,CAHC/I,iBACE,OAAOgK,GACR,EAGL,CA4GA,SAASX,GAAgBY,GACvB,GAAa,OAATA,EACF,OAAO,KAIT,MAAMC,EAAaD,EAAK,GAExB,GJ7Yc,OADahN,EI8YViN,IJlYH,QAFKlL,EATe/B,IAYZ,iBAAV+B,GAAuC,mBAAVA,GAZCG,MAAMC,QAAQnC,IACpC,iBAARA,GAAqBA,aAAemD,EAAGW,QAC/CX,EAAGC,KAAK8J,aAAalN,GI2YK,CAG5B,MAAO,CAAC+B,MAWZ,SAAwDoL,GAEtD,GAAsB,IAAlBA,EAAO5S,OAET,MAAM,IAAIqH,MAAM,wCAGlB,OAAIuL,EAAO,aAAchK,EAAGW,OAEnBX,EAAGiK,MAAMD,GAGThK,EAAGkK,OAAOF,EAErB,CA1BkBG,CAAYN,GACXlL,SAAS,EACzB,CJlZG,IAAuB9B,EAUR+B,EI2YnB,MAAO,CAACA,MAAO,KAAMD,SAAS,EAChC,CArRkB+J,GAAe0B,gBAAG,IC5X9B,MAAOC,WAAwB3B,GAMnCvH,YAA+BlD,GAC7BqE,QAD6BvL,KAAKkH,MAALA,CAE9B,CAED2B,iBAUE,aAT4B7I,KAAKkH,MAAM4G,YACJyF,aACDC,MAAM,MAAM5K,KAAI6K,IAE5CA,EAAKC,SAAS,QAChBD,EAAOA,EAAK9Q,MAAM,GAAI,IAEjB8Q,IAGV,ECvBH,MAAME,GAAa,IACbC,GAAYC,OAAO,OACnBC,GAAcD,OAAO,SACrBE,GAAcF,OAAO,SACrBG,GAA0BH,OAAO,mBACjCI,GAA8BJ,OAAO,gBAerC,MAAOK,WAAmBvC,GAoB9B9I,oBAIE,OAHK7I,KAAKmU,4BACFnU,KAAKoU,iBAENpU,KAAKqU,sBAAwBC,OAAOtL,KAAKhJ,KAAKuU,eACjBvU,KAAKwU,eAC1C,CAUO3L,uBACN,MAAM4L,QAA4BzU,KAAK0U,sBACvC,IAAK1U,KAAKwU,kBAAoBC,EAE5B,MAAM,IAAI/M,MACN,6DACK1H,KAAKwU,iBAAmBC,GAEjCvL,EAAIA,KAAC4I,OACD2C,EAAoBpU,SAAWL,KAAKwU,gBAAgBnU,QACpD,IAAM,uCACFL,KAAKwU,gBAAgBnU,OAAOkR,WAD1B,kEAGSkD,EAAoBpU,OAAOkR,WAAa,OAExDvR,KAAKwU,kBACRxU,KAAKwU,gBAAkBC,GAGzB,MAAME,EAAkC3U,KAAKwU,gBAAgBI,QACzD,CAACC,EAAmCC,KAClCD,EAASC,GAASD,EAASC,GAAQ,GAAM,EAClCD,IAET,CAAE,GACAE,EACFT,OAAOtL,KAAK2L,GAAQ/H,QAAQkI,GAAUH,EAAOG,GAAQ,IAKzD,GAJA5L,OAAK4I,OACyB,IAA1BiD,EAAe1U,QACf,IAAM,iCAAmC0U,EAAexD,aAExDvR,KAAKuU,cACP,IAAK,MAAMrQ,KAAOoQ,OAAOtL,KAAKhJ,KAAKuU,eAAgB,CAEjD,IAAe,IADDvU,KAAKwU,gBAAgBQ,QAAQ9Q,GAEzC,MAAM,IAAIwD,MACN,YAAcxD,EAAd,uEAEYlE,KAAKwU,gBAAgBjD,WAAa,KAErD,CAEHvR,KAAKmU,sBAAuB,CAC7B,CAEOtL,4BACN,GAAI7I,KAAKiV,UAAW,CAClB,MAAMC,QAAalV,KAAK6R,KAAK/D,WACvBqH,QAAqBD,EAAK1U,OAChC,GAAI2U,EAAa/I,KACf,MAAM,IAAI1E,MAAM,sCAElB,MAAM0N,EAAoBD,EAAatN,MAEvC,OADgB7H,KAAKqV,SAASD,GAAW,EAE1C,CACC,OAAO,IAEV,CAkCDhL,YAA+BlD,EAAmBoO,GAChD/J,QAD6BvL,KAAKkH,MAALA,EA9HvBlH,KAASiV,WAAG,EACZjV,KAAewU,gBAAa,KAC5BxU,KAAoBmU,sBAAG,EACvBnU,KAAauU,cAAkC,KAC/CvU,KAAqBqU,uBAAG,EACxBrU,KAASuV,UAAG,IACZvV,KAAewV,iBAAG,EA0HxBxV,KAAK6R,KAAO,IAAIyB,GAAgBpM,GAC3BoO,IACHA,EAAY,CAAA,GAEdtV,KAAKiV,WAAoC,IAAxBK,EAAUL,UAC3BjV,KAAKwU,gBAAkBc,EAAUG,YACjCzV,KAAKuU,cAAgBe,EAAUf,cAC/BvU,KAAKqU,sBAAwBiB,EAAUjB,sBACnCiB,EAAUE,iBACZtM,EAAIA,KAAC4I,OACsB,MAAvBwD,EAAUC,WACV,IACI,mEACRvV,KAAKwV,iBAAkB,EACvBxV,KAAKuV,UAAY,KAEjBvV,KAAKuV,UAAYD,EAAUC,UAAYD,EAAUC,UAAY,GAEhE,CAED1M,iBACO7I,KAAKmU,4BACFnU,KAAKoU,iBAEb,IAAIsB,QAAc1V,KAAK6R,KAAK/D,WAM5B,OALI9N,KAAKiV,YAGPS,EAAQA,EAAMvH,KAAK,IAEduH,EAAM9M,KAAI/G,GAAK7B,KAAK2V,gBAAgB9T,IAC5C,CAED8T,gBAAgBlC,GACd,MAAM1I,EAAS/K,KAAKqV,SAAS5B,GACvBmC,EAA6C,CAAA,EAC7CC,EAA2C,CAAA,EAEjD,IAAK,IAAIzV,EAAI,EAAGA,EAAIJ,KAAKwU,gBAAgBnU,OAAQD,IAAK,CACpD,MAAM8D,EAAMlE,KAAKwU,gBAAgBpU,GAC3B0V,EAAS9V,KAAKuU,cAAgBvU,KAAKuU,cAAcrQ,GAAO,KAC9D,IAAIlE,KAAKqU,uBAA0ByB,EAG5B,CACL,MAAMjO,EAAQkD,EAAO3K,GACrB,IAAI2V,EAAc,KAClB,GAAc,KAAVlO,EAGF,GAAIiO,QAA6B7K,IAAnB6K,EAAOE,QACnBD,EAAcD,EAAOE,YAChB,IAAIF,IAAWA,EAAOG,UAAYH,EAAOI,SAC9C,MAAM,IAAIxO,MACN,mBAAmBxD,4BAA8BuP,KAErDsC,OAAc9K,CACf,KACI,CAEL,MAAMkL,EAAaC,OAAOvO,GAC1B,GAAIwO,MAAMF,GAINJ,EADED,GAA2B,SAAjBA,EAAOQ,MACLtW,KAAKuW,WAAW1O,GAGhBA,OAEX,GAAKiO,GAAWA,EAAOQ,MAO5B,OAAQR,EAAOQ,OACb,IAAK,UASL,QACEP,EAAcI,QAPhB,IAAK,QACHJ,EAAchT,KAAKK,MAAM+S,GACzB,MACF,IAAK,OACHJ,EAAc/V,KAAKuW,WAAW1O,QAZlCkO,EAAcI,CAkBjB,CAEAL,GAAUA,EAAOI,QAAWL,EAAO3R,GAAO6R,EACdH,EAAS1R,GAAO6R,CAC9C,CACF,CAGD,OAAmC,IAA/BzB,OAAOtL,KAAK6M,GAAQxV,OACfuV,EAGA,CAACY,GAAIZ,EAAUa,GAAIZ,EAE7B,CAEOU,WAAW1O,GACjB,MAAc,MAAVA,GAAyC,SAAxBA,EAAM6O,cAClB,EAEA,CAEV,CAGOrB,SAAS5B,EAAckD,GAAuB,GACpD,MAAMzU,EAAmB,GACzB,IAAI0U,EAAa,EACjB,MAAMC,EAAapD,EAAKpT,OACxB,IAAIyW,EAAelD,GAEnB,IAAK,IAAIxT,EAAI,EAAGA,EAAIyW,EAAYzW,IAC9B,OAAQ0W,GAEN,KAAKlD,GACH,OAAQH,EAAKsD,OAAO3W,IAElB,KAAKuT,GACHiD,EAAaxW,EAAI,EACjB0W,EAAe/C,GACf,MAEF,KAAK/T,KAAKuV,UAIR,GAHAqB,EAAaxW,EAAI,EAGM,MAAnBJ,KAAKuV,WAAqBvV,KAAKwV,gBACjC,MAEFtT,EAAOO,KAAK,IACZqU,EAAelD,GACf,MAEF,QACEkD,EAAehD,GACf8C,EAAaxW,EAGjB,MAEF,KAAK0T,GACH,GAAQL,EAAKsD,OAAO3W,KAEbJ,KAAKuV,UACRrT,EAAOO,KAAKgR,EAAKuD,UAAUJ,EAAYxW,IACvC0W,EAAelD,GACfgD,EAAaxW,EAAI,EAIrB,MAEF,KAAK2T,GACH,GAAQN,EAAKsD,OAAO3W,KAEbuT,GACHmD,EAAe9C,GAInB,MAEF,KAAKA,GACH,OAAQP,EAAKsD,OAAO3W,IAElB,KAAKJ,KAAKuV,UACRrT,EAAOO,KAAKgR,EAAKuD,UAAUJ,EAAYxW,EAAI,IAC3C0W,EAAelD,GACfgD,EAAaxW,EAAI,EACjB,MAEF,KAAKuT,GACHmD,EAAe/C,GACf,MAEF,QACE+C,EAAe7C,GAGnB,MACF,KAAKA,GACH,GAAQR,EAAKsD,OAAO3W,KAEbuT,GACHmD,EAAe/C,GAezB,GANI+C,IAAiB9C,GACnB9R,EAAOO,KAAKgR,EAAKuD,UAAUJ,EAAYC,EAAa,IAEpD3U,EAAOO,KAAKgR,EAAKuD,UAAUJ,IAGzBD,GAAwBzU,EAAO7B,SAAWL,KAAKwU,gBAAgBnU,OACjE,MAAM,IAAIqH,MAAM,wCACZ1H,KAAKwU,gBAAgBnU,qCAAqC6B,KAEhE,OAAOA,CACR,EC1WG,MAAO+U,WAA2B9K,EAgBtC/B,YAAuC8M,GACrC3L,QADqCvL,KAAgBkX,iBAAhBA,EAf/BlX,KAAQmX,UAAG,EAiBjBnX,KAAKoX,QAAUF,EAAiBE,SAAW,KAC3C,MAAMC,EAActU,KAAKuU,KAAKtX,KAAKoX,SACnC,GAAIpX,KAAKoX,QAAU,GAAKC,EAAc,GAAKA,EAAc,KACpDjB,OAAOmB,UAAUF,GACpB,MAAM,IAAI3P,MAEN,gFAA+B1H,KAAKoX,WAc1C,GAXApX,KAAKwX,UAAYN,EAAiBO,yBAA2B,GAC7DzX,KAAK0X,aAAeR,EAAiBQ,aACrC1X,KAAK2X,qBACDT,EAAiBS,sBAAwB3X,KAAKoX,QAClDpX,KAAK4X,sBAAwBV,EAAiBU,sBAC9C5X,KAAK6X,sBAAwBX,EAAiBW,uBAAyB,EAEvE7X,KAAK8X,oBACuC,IAAxCZ,EAAiBY,mBACrB9X,KAAK+X,iBACoC,IAArCb,EAAiBa,iBAChB/X,KAAK8X,qBAAuB9X,KAAK+X,gBACpC,MAAM,IAAIrQ,MACN,uGAGP,CAEDkH,UACE,MAAO,YACR,CAGDoJ,oBAAoBd,EAAqC,IACvD,IAAK5N,EAAGA,MAAG3B,IAAI,cACb,MAAM,IAAID,MACN,4DAGN,MAAMuQ,EAAqB,IAAIhB,GAAmBC,GAKlD,aAFMe,EAAmBC,QAElBD,CACR,CAGDpP,cACE,IACE7I,KAAKqM,aAAerH,UAAUmT,aAAaC,aAAa,CACtDC,MAAqC,MAA9BrY,KAAK4X,uBACgC5X,KAAK4X,sBACjDU,OAAO,GAKV,CAHC,MAAOxT,GACP,MAAM,IAAI4C,MACN,iDAAiD5C,EAAEgK,UACxD,CAED,IAAK9O,KAAKqM,OACR,MAAM,IAAI3E,MAAM,2CAGlB,MAAM6Q,EAEDC,OAAeC,cAAiBD,OAAeE,mBAGpD,GAFA1Y,KAAK2Y,aAAe,IAAIJ,EAEnBvY,KAAK0X,cAIH,GAAI1X,KAAK2Y,aAAaC,aAAe5Y,KAAK0X,aAC/C,MAAM,IAAIhQ,MAEN,wCAAa1H,KAAK0X,yBACP1X,KAAK2Y,aAAaC,mBALjC5Y,KAAK0X,aAAe1X,KAAK2Y,aAAaC,WAQxC,MAAMC,EAAe7Y,KAAK2Y,aAAaG,wBAAwB9Y,KAAKqM,QACpErM,KAAK+Y,SAAW/Y,KAAK2Y,aAAaK,iBAClChZ,KAAK+Y,SAAS3B,QAAyB,EAAfpX,KAAKoX,QAC7BpX,KAAK+Y,SAASlB,sBAAwB7X,KAAK6X,sBAC3CgB,EAAaI,QAAQjZ,KAAK+Y,UAC1B/Y,KAAKkZ,SAAW,IAAIC,aAAanZ,KAAKoX,SACtCpX,KAAKoZ,SAAW,IAAID,aAAanZ,KAAKoX,QAEvC,CAEDvO,aACE,GAAI7I,KAAKmX,SACP,MAAO,CAACtP,MAAO,KAAMuE,MAAM,GAG7B,IAAIiN,EACAC,EAEJ,MAAMC,QAAuBvZ,KAAKwZ,eAClC,GAAIxZ,KAAK8X,mBAAoB,CAC3B,MAAMoB,EAAWlZ,KAAKyZ,aAAaF,EAAeG,eAClDL,EAAoBrZ,KAAK2Z,4BACrBT,EAAU,CAAClZ,KAAKwX,UAAWxX,KAAK2X,qBAAsB,GAC3D,CACD,GAAI3X,KAAK+X,gBAAiB,CACxB,MAAMqB,EAAWpZ,KAAKyZ,aAAaF,EAAeK,eAClDN,EAAiBtZ,KAAK2Z,4BAClBP,EAAU,CAACpZ,KAAKwX,UAAYxX,KAAKoX,QAAS,GAC/C,CAED,MAAO,CACLvP,MAAO,CAACgS,YAAeR,EAAmBS,SAAYR,GACtDlN,MAAM,EAET,CAIDvD,gBACE,aAAc7I,KAAKQ,QAAQqH,KAE5B,CAEOgB,qBAEN,MAAM6Q,EAAgC,GAChCE,EAAgC,GACtC,IAAIG,EAAgB,EACpB,OAAO,IAAIlQ,SAAQoF,IACjB,MAAM+K,EAAaC,aAAY,KACzBja,KAAK8X,qBACP9X,KAAK+Y,SAASmB,sBAAsBla,KAAKkZ,UAErClZ,KAAKkZ,SAAS,MAAQnH,KACxB9C,EAAQ,CAACyK,gBAAeE,kBAE1BF,EAAcjX,KAAKzC,KAAKkZ,SAASvW,MAAM,EAAG3C,KAAK2X,wBAE7C3X,KAAK+X,kBACP/X,KAAK+Y,SAASoB,uBAAuBna,KAAKoZ,UAC1CQ,EAAcnX,KAAKzC,KAAKoZ,SAASzW,YAI7BoX,IAAkB/Z,KAAKwX,YAC3B4C,cAAcJ,GACd/K,EAAQ,CAACyK,gBAAeE,kBACzB,GACA5Z,KAAKoX,QAAUpX,KAAK0X,aAAe,IAAI,GAE7C,CAGD2C,OACOra,KAAKmX,WACRnX,KAAKmX,UAAW,EAChBnX,KAAK+Y,SAASuB,aACdta,KAAK2Y,aAAa4B,QACC,MAAfva,KAAKqM,QAAkBrM,KAAKqM,OAAOmO,YAAYna,OAAS,GAC1DL,KAAKqM,OAAOmO,YAAY,GAAGH,OAGhC,CAGQ1H,UACP,MAAM,IAAIjL,MAAM,kDACjB,CAGD+S,gBACE,OAAOza,KAAK0X,YACb,CAEO+B,aAAaiB,GACnB,MAAMC,EAAYD,EAAM,GAAGra,OACrB6Y,EAAW,IAAIC,aAAauB,EAAMra,OAASsa,GAEjD,OADAD,EAAME,SAAQ,CAAC1a,EAAME,IAAM8Y,EAAS5Q,IAAIpI,EAAME,EAAIua,KAC3CzB,CACR,CAEOS,4BAA4BT,EAAwB2B,GAE1D,MAAMC,EAAO,IAAI3B,aAAajQ,EAAIA,KAAC6R,cAAcF,IAGjD,OADAC,EAAKxS,IAAI4Q,EAAU4B,EAAKza,OAAS6Y,EAAS7Y,QACnC8S,EAAMA,OAAC2H,EAAMD,EACrB,EC9MG,MAAOG,WAAuB7O,EAQlC/B,YACuB6Q,EACAC,GAErB,GADA3P,QAFqBvL,KAAkBib,mBAAlBA,EACAjb,KAAYkb,aAAZA,EATflb,KAAQmX,UAAG,EAEXnX,KAAMmb,QAAG,EASXnb,KAAKob,eAKP,GAJApb,KAAKmb,QAAS,EACdnb,KAAKqb,SACD,CAACrb,KAAKkb,aAAaI,aAActb,KAAKkb,aAAaK,aACvDvb,KAAKwb,WAAaC,EAAQA,SAAC,CAAC,GAAI,SAC5Bzb,KAAKkb,aAAaQ,WAAY,CAEhC,MAAMC,EAC8B,EAAhC3b,KAAKkb,aAAaK,YAAoBvb,KAAKib,mBAAmBxX,MAC5DmY,EAAuD,EAAjC5b,KAAKkb,aAAaI,aAC1Ctb,KAAKib,mBAAmBY,OACtBC,GAAkB,EAAIH,GAAsB,EAC5CI,GAAmB,EAAIH,GAAuB,EAC9CI,EAAeF,EAAiBH,EAChCM,EAAgBL,EAAsBG,EAC5C/b,KAAKkc,QAAUC,WACX,CAACJ,EAAiBD,EAAgBG,EAAeD,GACjD,CAAC,EAAG,GACT,MACChc,KAAKkc,QAAUC,WAAS,CAAC,EAAG,EAAG,EAAG,GAAI,CAAC,EAAG,GAG/C,CAEDvN,UACE,MAAO,QACR,CAGDoJ,oBACIiD,EAAuCC,EAA6B,IACtE,IAAK5R,EAAGA,MAAG3B,IAAI,cACb,MAAM,IAAID,MACN,4DAGN,IAAKuT,EAAoB,CAIvB,GADAA,EAAqBmB,SAASC,cAAc,UACvCnB,EAAaK,cAAgBL,EAAaI,aAC7C,MAAM,IAAI5T,MACN,0GAGNuT,EAAmBxX,MAAQyX,EAAaK,YACxCN,EAAmBY,OAASX,EAAaI,YAC1C,CACD,MAAMgB,EAAiB,IAAItB,GAAeC,EAAoBC,GAK9D,aAFMoB,EAAepE,QAEdoE,CACR,CAGDzT,cACM7I,KAAKkb,aAAaqB,YACpBrT,EAAAA,KAAK4I,OACiC,SAAjC9R,KAAKkb,aAAaqB,YACmB,gBAAjCvc,KAAKkb,aAAaqB,YACvB,IACI,+BAA+Bvc,KAAKkb,aAAaqB,uDAI3D,IACEvc,KAAKqM,aAAerH,UAAUmT,aAAaC,aAAa,CACtDE,MAAO,CACLkE,SAAUxc,KAAKkb,aAAasB,SAC5BD,WAAYvc,KAAKkb,aAAaqB,WAC1Bvc,KAAKkb,aAAaqB,WAClB,OACJ9Y,MAAOzD,KAAKib,mBAAmBxX,MAC/BoY,OAAQ7b,KAAKib,mBAAmBY,SAOrC,CAJC,MAAO/W,GAGP,MADAA,EAAEgK,QAAU,iDAAiDhK,EAAEgK,UACzDhK,CACP,CAED,IAAK9E,KAAKqM,OACR,MAAM,IAAI3E,MAAM,uCAIlB,IACE1H,KAAKib,mBAAmBwB,UAAYzc,KAAKqM,MAK1C,CAJC,MAAOqQ,GACPC,QAAQC,IAAIF,GACZ1c,KAAKib,mBAAmB4B,IAAMrE,OAAOsE,IAAIC,gBACvC/c,KAAKqM,OACR,CAMD,OAJArM,KAAKib,mBAAmB+B,OAExBhd,KAAKmX,UAAW,EAET,IAAItN,SAAcoF,IAEvBjP,KAAKib,mBAAmBgC,iBAAmB,KACzChO,GAAS,CACV,GAEJ,CAEDpG,aACE,GAAI7I,KAAKmX,SACP,MAAO,CAACtP,MAAO,KAAMuE,MAAM,GAG7B,IAAI8Q,EACJ,IACEA,EAAMnY,EAAAA,QAAQoY,WAAWnd,KAAKib,mBAI/B,CAHC,MAAOnW,GACP,MAAM,IAAI4C,MACN,4CAA4C0V,KAAKC,UAAUvY,KAChE,CACD,IAAI9E,KAAKmb,OASP,MAAO,CAACtT,MAAOqV,EAAK9Q,MAAM,GAR1B,IACE,MAAO,CAACvE,MAAO7H,KAAKsd,mBAAmBJ,GAAM9Q,MAAM,EAKpD,CAJC,MAAOtH,GACP,MAAM,IAAI4C,MAAM,oCAAoC5C,EAAEgK,UACvD,CAAS,QACRoO,EAAI5N,SACL,CAIJ,CAEO8L,eAIN,SAAIpb,KAAKkb,aAAaK,cAAevb,KAAKkb,aAAaI,cAClDtb,KAAKib,mBAAmBxX,QAAUzD,KAAKkb,aAAaK,aACpDvb,KAAKib,mBAAmBY,SAAW7b,KAAKkb,aAAaI,aAI3D,CAGDgC,mBAAmBJ,GACjB,OAAO9K,EAAIA,MAAC,KACV,MAAMmL,EAA0BC,EAAAA,WAAWC,EAAIA,KAACP,EAAK,WAAa,GAClE,IAAIQ,EACJA,EAAeC,EAAKA,MAACC,cACjBL,EAAevd,KAAKkc,QAASlc,KAAKwb,WAAYxb,KAAKqb,SACnD,YAEJ,MAAMR,EAAQ6C,EAAa7C,MAC3B,OAAOgD,EAAAA,QAAQH,EAAc7C,EAAMlY,MAAM,GAA+B,GAE3E,CAIDkG,gBACE,aAAc7I,KAAKQ,QAAQqH,KAC5B,CAGDwS,OACiBra,KAAKqM,OAAOmO,YAEpBI,SAAQkD,GAASA,EAAMzD,SAE9B,IACEra,KAAKib,mBAAmBwB,UAAY,IAIrC,CAHC,MAAOC,GACPC,QAAQC,IAAIF,GACZ1c,KAAKib,mBAAmB4B,IAAM,IAC/B,CACD7c,KAAKmX,UAAW,CACjB,CAGQxE,UACP,MAAM,IAAIjL,MAAM,kDACjB,QCnMmBqW,ICPhB,MAAgBC,WAAuB7R,EAmB3CqH,MAAMyK,GACJ,OAAO,IAAIC,GAAcle,KAAMie,EAChC,EAcH,MAAMC,WAAsBF,GAG1B5T,YAAsB2E,EAAgCkP,GACpD1S,QADoBvL,KAAQ+O,SAARA,EAEpB/O,KAAKgB,KAAO,IAAImd,GAAkBpP,EAAUkP,EAC7C,CAEDrP,UACE,OAAO5O,KAAKgB,KAAK4N,SAClB,CAED/F,aACE,OAAO7I,KAAKgB,KAAKR,MAClB,EAGH,MAAM2d,WAA0BpO,EAI9B3F,YACc2E,EAA0CkP,GACtD1S,QADYvL,KAAQ+O,SAARA,EAA0C/O,KAASie,UAATA,EAHxDje,KAASoe,UAAG,EAKX,CAEDxP,UACE,MAAO,GAAG5O,KAAK+O,SAASH,uBAAuB5O,KAAKie,aACrD,CAEDpV,aACE,MAAMwV,QAAoBre,KAAK+O,SAASvO,OACxC,GAAI6d,EAAYjS,KACd,MAAuB,KAAnBpM,KAAKoe,YAMTpe,KAAKgQ,YAAYvN,KAAKzC,KAAKoe,WAC3Bpe,KAAKoe,UAAY,IACV,GAET,MAAM1I,EAAQ2I,EAAYxW,MAAM2L,MAAMxT,KAAKie,WAK3CvI,EAAM,GAAK1V,KAAKoe,UAAY1I,EAAM,GAClC,IAAK,MAAMjC,KAAQiC,EAAM/S,MAAM,GAAI,GACjC3C,KAAKgQ,YAAYvN,KAAKgR,GAIxB,OAFAzT,KAAKoe,UAAY1I,EAAMA,EAAMrV,OAAS,IAE/B,CACR,ECxFG,MAAgBie,WAA0BnS,EAU9CoH,aACE,OAAO,IAAIgL,GAAave,KACzB,EAcH,MAAMue,WAAqBP,GAGzB5T,YAAsB2E,GACpBxD,QADoBvL,KAAQ+O,SAARA,EAEpB/O,KAAKgB,KAAO,IAAIwd,GAAiBzP,EAClC,CAEDH,UACE,OAAO5O,KAAKgB,KAAK4N,SAClB,CAED/F,aACE,OAAO7I,KAAKgB,KAAKR,MAClB,EAyBH,MAAMge,WAAyBzO,EAM7B3F,YAA+B2E,GAE7B,GADAxD,QAD6BvL,KAAQ+O,SAARA,EAEzBzF,QAAM3B,IAAI,cACZ3H,KAAKye,QAAU,IAAIlV,YAAY,aAC1B,CAEL,MAAMC,cAACA,GAAiBC,QAAQ,kBAChCzJ,KAAKye,QAAU,IAAIjV,EAAc,OAClC,CACF,CACDoF,UACE,MAAO,GAAG5O,KAAK+O,SAASH,mBACzB,CAED/F,aACE,MAAMwV,QAAoBre,KAAK+O,SAASvO,OACxC,IAAIke,EAOAC,EANJ,OAAIN,EAAYjS,OAGdsS,EAAQL,EAAYxW,MAKpB8W,EADErV,QAAM3B,IAAI,cACL3H,KAAKye,QAAQG,OAAOF,EAAO,CAACrS,QAAQ,IAEpCrM,KAAKye,QAAQI,MAAMC,OAAO/V,KAAK2V,EAAMxN,SAE9ClR,KAAKgQ,YAAYvN,KAAKkc,IACf,EACR,ECvFG,MAAOI,WAA0BT,GAIrClU,YACc4U,EACAhb,EAAoC,IAChDuH,QAFYvL,KAAIgf,KAAJA,EACAhf,KAAOgE,QAAPA,EAEZkF,OAAK4I,OACAkN,aAAgBta,cACZ4E,QAAM3B,IAAI,gBACLqX,aAAgBC,MAAQD,aAAgBxX,OAElD,IAAM,yEAEVxH,KAAKkf,OAASlb,EAAQkb,QAAU,EAEhClf,KAAKmf,UAAYnb,EAAQmb,WAAa,OACvC,CAEDvQ,UACE,MAAO,cAAc5O,KAAKgf,MAC3B,CAEDnW,aACE,GAAI7I,KAAKkf,SAAYlf,KAAKgf,gBAAgBta,WAClB1E,KAAKgf,KAAKI,WACVpf,KAAKgf,KAAKpN,MAChC,MAAO,CAAC/J,MAAO,KAAMuE,MAAM,GAE7B,MAAMsS,EAAQ,IAAI7U,SAAoB,CAACoF,EAASoQ,KAC9C,MAAM9U,EAAMvK,KAAKkf,OAASlf,KAAKmf,UAC/B,GAAInf,KAAKgf,gBAAgBta,WAGvBuK,EAAQ,IAAIvK,WAAW1E,KAAKgf,KAAKrc,MAAM3C,KAAKkf,OAAQ3U,SAC/C,CAKL,MAAM+U,EAAa,IAAIC,WACvBD,EAAWE,OAAUC,IACnB,IAAIvf,EAAsCof,EAAWpd,OAOrD,GAHIhC,aAAgBwJ,cAClBxJ,EAAO,IAAIwE,WAAWxE,MAElBA,aAAgBwE,YACpB,OAAO2a,EAAO,IAAIK,UAAU,sCAE9BzQ,EAAQ/O,EAAK,EAEfof,EAAWK,QAAWF,GACbJ,EAAO,IAAI3X,MAAM,YAE1B4X,EAAWM,QAAWH,GACbJ,EAAO,IAAI3X,MAAM+X,EAAMI,OAIhC,MAAMld,EAAQ3C,KAAKgf,KAAKrc,MAAM3C,KAAKkf,OAAQ3U,GAG3C+U,EAAWQ,kBAAkBnd,EAC9B,CACD3C,KAAKkf,OAAS3U,CAAG,IAEnB,MAAO,CAAC1C,YAAc6W,EAAQtS,MAAM,EACrC,EC3DH,MAAM2T,GAA6BC,IACpB,CACXC,OAAQD,EAAQC,OAChBC,QAASF,EAAQE,QACjBC,KAAMH,EAAQG,KACdC,KAAMJ,EAAQI,KACdC,YAAaL,EAAQK,YACrBC,MAAON,EAAQM,MACfC,SAAUP,EAAQO,SAClBC,SAAUR,EAAQQ,SAClBC,UAAWT,EAAQS,YCtCjB,SAAUC,GAAYC,GAC1B,MAA0B,iBAAXA,GAA+C,YAAvBA,EAAOhe,MAAM,EAAG,EACzD,CCMM,MAAOie,WAAuB7C,GASlC3T,YACclD,EACSlD,EAAoC,IACzDuH,QAFYvL,KAAKkH,MAALA,EACSlH,KAAOgE,QAAPA,CAEtB,CAED6E,iBACE,GAAI6X,GAAY1gB,KAAKkH,QAAUoC,EAAAA,MAAM3B,IAAI,WAAY,CAEnD,MAAMkZ,EAAKpX,QAAQ,MACnBzJ,KAAKkH,MAAQ2Z,EAAGC,aAAc9gB,KAAKkH,MAAiBvE,MAAM,GAC3D,CAGD,OAAO,IAAIoc,GAAkB/e,KAAKkH,MAAsBlH,KAAKgE,QAC9D,ECzBG,MAAO+c,WAAsBhD,GAQjC3T,YACuB4W,EACAC,EAAwC,IAC7D1V,QAFqBvL,KAAGghB,IAAHA,EACAhhB,KAAWihB,YAAXA,CAEtB,CAMDpY,iBACE,OAAI6X,GAAY1gB,KAAKghB,KACZ,IAAKJ,GAAe5gB,KAAKghB,IAAehhB,KAAKihB,aAC/CnT,WHrBJjF,eACHmY,EAAkBhd,EAAoC,CAAA,EACtDkd,GACF,IAAIC,EACAC,EACiB,iBAATJ,EACVG,EAAYH,GAEZG,EAAaH,EAAgBA,IAC7BI,EAAcrB,GAA0BiB,IAE1C,MAAMK,QAAkBH,GAAahY,EAAIA,KAACoY,OAAOH,EAAWC,GAC5D,GAAIC,EAASE,GAAI,CACf,MAAMC,EAAa,IAAI9c,iBAAiB2c,EAASI,eACjD,OAAO,IAAI1C,GAAkByC,EAAYxd,EAC1C,CACC,MAAM,IAAI0D,MAAM2Z,EAASK,WAE7B,CGKaC,CAAiB3hB,KAAKghB,IAAKhhB,KAAKihB,YAE1C,mGZ+gBG,SAA8CnV,GAClD,OAAOmG,IACHpJ,SAAYgD,EAAkBC,IAAQA,EAAMzL,OAClD,iBa7dIsgB,EAAqBrL,EAAuB,IAC9C,OAAO,IAAIpB,GAAW,IAAI6M,GAAcJ,GAASrL,EACnD,SA0BM,SACFvU,GACF,MAAMmU,EAAOlJ,EAAqBjL,GAClC,OAAOkR,IAAsBpJ,SAAYqM,GAC3C,cA4DM,SACJ0M,GAEA,OAAO3P,IAAsBpJ,UAC3B,MAAMgZ,QAAYD,IAClB,OAAO5V,GAAqB,IAAM6V,EAAIrhB,QAAO,GAEjD,eAwEOqI,eAA0BqO,GAE/B,OAAOD,GAAmB6K,OAAO5K,EACnC,iBCrRgB,kBD0OTrO,eACHoS,EACAC,GACF,OAAOF,GAAe8G,OAAO7G,EAAoBC,EACnD,QbiYM,SAA4C6G,GAGhD,IAAKja,EAAWia,GACd,MAAM,IAAIra,MAAM,qDAElB,IAAIkK,EACJ,GAAI5J,MAAMC,QAAQ8Z,GAChB,IAAK,IAAI3hB,EAAI,EAAGA,EAAI2hB,EAAS1hB,OAAQD,IACnCwR,EAAe,MAARA,EAAgBmQ,EAAS3hB,GAAkBwR,KAC5B7O,KAAKif,IAAIpQ,EAAOmQ,EAAS3hB,GAAkBwR,WAE9D,GAAImQ,aAAoBzN,OAC7B,IAAK,MAAM2N,KAAMF,EACfnQ,EAAe,MAARA,EAAgBmQ,EAASE,GAAmBrQ,KAC7B7O,KAAKif,IAAIpQ,EAAOmQ,EAASE,GAAmBrQ,MAGtE,OAAOK,IAAyBpJ,SL7f5B,SACFsH,EACAM,EAAgCzJ,EAAgB0J,MAClD,OAAO,IAAIF,GAAeL,EAAWM,EACvC,CKqgBWyR,OAXepZ,EAAmBiZ,GAAU1f,IACjD,GAAIA,aAAasP,GACf,MAAO,CAAC9J,MAAOxF,EAAEyL,WAAYlG,SAAS,GACjC,GAAIE,EAAWzF,GACpB,MAAO,CAACwF,MAAO,KAAMD,SAAS,GAE9B,MAAM,IAAIF,MACN,4EAEL,IAEmCV,EAAgB+J,WACrDa,EACL", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}