{"version": 3, "file": "tf-data.min.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../../../node_modules/seedrandom/lib/alea.js", "../../../../node_modules/seedrandom/lib/xor128.js", "../../../../node_modules/seedrandom/lib/xorwow.js", "../../../../node_modules/seedrandom/lib/xorshift7.js", "../../../../node_modules/seedrandom/lib/xor4096.js", "../../../../node_modules/seedrandom/lib/tychei.js", "../../../../node_modules/seedrandom/seedrandom.js", "../../../../node_modules/seedrandom/index.js", "../../../../tfjs-data/src/util/deep_map.ts", "../../../../tfjs-data/src/util/deep_clone.ts", "../../../../tfjs-data/src/util/ring_buffer.ts", "../../../../tfjs-data/src/util/growing_ring_buffer.ts", "../../../../tfjs-data/src/iterators/lazy_iterator.ts", "../../../../tfjs-data/src/dataset.ts", "../../../../tfjs-data/src/datasets/text_line_dataset.ts", "../../../../tfjs-data/src/datasets/csv_dataset.ts", "../../../../tfjs-data/src/iterators/microphone_iterator.ts", "../../../../tfjs-data/src/iterators/webcam_iterator.ts", "../../../../tfjs-data/src/datasource.ts", "../../../../tfjs-data/src/iterators/string_iterator.ts", "../../../../tfjs-data/src/iterators/byte_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/file_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/url_chunk_iterator.ts", "../../../../tfjs-data/src/util/source_util.ts", "../../../../tfjs-data/src/sources/file_data_source.ts", "../../../../tfjs-data/src/sources/url_data_source.ts", "../../../../tfjs-data/src/readers.ts", "../../../../tfjs-data/src/version.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\n\n// tslint:disable:no-any\n\n/**\n * A return value for a mapping function that can be applied via deepMap.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapResult = {\n  value: any,\n  recurse: boolean\n};\n\n/**\n * Apply a mapping function to a nested structure in a recursive manner.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapResult`.  The `DeepMapResult` either provides a\n *   replacement value for that node (i.e., replacing the subtree), or indicates\n *   that the node should be processed recursively.\n */\nexport function deepMap(input: any, mapFn: (x: any) => DeepMapResult): any|\n    any[] {\n  return deepMapInternal(input, mapFn);\n}\n\n/**\n * @param seen: A Map of known object mappings (i.e., memoized results of\n *   `mapFn()`)\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepMapInternal(\n    input: any, mapFn: (x: any) => DeepMapResult,\n    seen: Map<any, any> = new Map(), containedIn: Set<{}> = new Set()): any|\n    any[] {\n  if (input == null) {\n    return null;\n  }\n  if (typeof Blob === 'function' && input instanceof Blob) {\n    return input.slice();\n  }\n\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  if (seen.has(input)) {\n    return seen.get(input);\n  }\n  const result = mapFn(input);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep map function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    seen.set(input, result.value);\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const child = input[k];\n      const childResult = deepMapInternal(child, mapFn, seen, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    if (input.__proto__) {\n      mappedIterable.__proto__ = input.__proto__;\n    }\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// TODO(soergel, kangyizhang) Reconsider naming of deepZip() to avoid confusion\n// with zip()\n\n/**\n * Zip nested structures together in a recursive manner.\n *\n * This has the effect of transposing or pivoting data, e.g. converting it from\n * a row-major representation to a column-major representation.\n *\n * For example, `deepZip([{a: 1, b: 2}, {a: 3, b: 4}])` returns\n * `{a: [1, 3], b: [2, 4]}`.\n *\n * The inputs should all have the same nested structure (i.e., of arrays and\n * dicts).  The result is a single object with the same nested structure, where\n * the leaves are arrays collecting the values of the inputs at that location\n * (or, optionally, the result of a custom function applied to those arrays).\n *\n * @param inputs: An array of the objects to zip together.\n * @param zipFn: (optional) A function that expects an array of elements at a\n *   single node of the object tree, and returns a `DeepMapResult`.  The\n *   `DeepMapResult` either provides a result value for that node (i.e.,\n *   representing the subtree), or indicates that the node should be processed\n *   recursively.  The default zipFn recurses as far as possible and places\n *   arrays at the leaves.\n */\nexport function deepZip(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult = zipToList): any|any[] {\n  return deepZipInternal(inputs, zipFn);\n}\n\n/**\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepZipInternal(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult,\n    containedIn: Set<{}> = new Set()): any|any[] {\n  // The recursion follows the structure of input 0; it's assumed that all the\n  // other inputs have the same structure.\n  const input = inputs[0];\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  const result = zipFn(inputs);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep zip function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const children = inputs.map(x => x[k]);\n      const childResult = deepZipInternal(children, zipFn, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// tslint:disable-next-line:no-any\nexport function zipToList(x: any[]): DeepMapResult {\n  if (x === null) {\n    return null;\n  }\n  // TODO(soergel): validate array type?\n\n  if (isIterable(x[0])) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: x, recurse: false};\n  }\n}\n\n/**\n * A return value for an async map function for use with deepMapAndAwaitAll.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapAsyncResult = {\n  value: Promise<any>,\n  recurse: boolean\n};\n\n/**\n * Apply an async mapping function to a nested structure in a recursive manner.\n *\n * This first creates a nested structure of Promises, and then awaits all of\n * those, resulting in a single Promise for a resolved nested structure.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapAsyncResult`.  The `DeepMapAsyncResult` either provides\n *   a `Promise` for a replacement value for that node (i.e., replacing the\n *   subtree), or indicates that the node should be processed recursively.  Note\n *   that the decision whether or not to recurse must be made immediately; only\n *   the mapped value may be promised.\n */\nexport async function deepMapAndAwaitAll(\n    input: any, mapFn: (x: any) => DeepMapAsyncResult): Promise<any|any[]> {\n  const seen: Map<any, any> = new Map();\n\n  // First do a normal deepMap, collecting Promises in 'seen' as a side effect.\n  deepMapInternal(input, mapFn, seen);\n\n  // Replace the Promises in 'seen' in place.\n  // Note TypeScript provides no async map iteration, and regular map iteration\n  // is broken too, so sadly we have to do Array.from() to make it work.\n  // (There's no advantage to Promise.all(), and that would be tricky anyway.)\n  for (const key of Array.from(seen.keys())) {\n    const value = seen.get(key);\n    if (tf.util.isPromise(value)) {\n      const mappedValue = await value;\n      seen.set(key, mappedValue);\n    }\n  }\n\n  // Normal deepMap again, this time filling in the resolved values.\n  // It's unfortunate that we have to do two passes.\n  // TODO(soergel): test performance and think harder about a fast solution.\n  const result = deepMapInternal(input, mapFn, seen);\n  return result;\n}\n\n/**\n * Determine whether the argument is iterable.\n *\n * @returns true if the argument is an array or any non-Tensor object.\n */\n// tslint:disable-next-line:no-any\nexport function isIterable(obj: any): boolean {\n  let isTextDecoder = false;\n  if (tf.env().get('IS_BROWSER')) {\n    isTextDecoder = obj instanceof TextDecoder;\n  } else {\n    // tslint:disable-next-line:no-require-imports\n    const {StringDecoder} = require('string_decoder');\n    isTextDecoder = obj instanceof StringDecoder;\n  }\n  return obj != null && (!ArrayBuffer.isView(obj)) &&\n      (Array.isArray(obj) ||\n       (typeof obj === 'object' && !(obj instanceof tf.Tensor) &&\n        !(obj instanceof Promise) && !isTextDecoder));\n}\n\n/**\n * Determine whether the argument can be converted to Tensor.\n *\n * Tensors, primitives, arrays, and TypedArrays all qualify; anything else does\n * not.\n *\n * @returns true if the argument can be converted to Tensor.\n */\n// tslint:disable-next-line:no-any\nexport function canTensorify(obj: any): boolean {\n  return obj == null || isPrimitive(obj) || Array.isArray(obj) ||\n      (typeof obj === 'object' && (obj instanceof tf.Tensor)) ||\n      tf.util.isTypedArray(obj);\n}\n\n/**\n * Returns true if the given `value` is a primitive type. Otherwise returns\n * false. This is equivalant to node util.isPrimitive\n */\nfunction isPrimitive(value: any): boolean {\n  return (\n      value === null ||\n      (typeof value !== 'object' && typeof value !== 'function'));\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {RingBuffer} from './ring_buffer';\n\nexport class GrowingRingBuffer<T> extends RingBuffer<T> {\n  private static INITIAL_CAPACITY = 32;\n\n  /**\n   * Constructs a `GrowingRingBuffer`.\n   */\n  constructor() {\n    super(GrowingRingBuffer.INITIAL_CAPACITY);\n  }\n\n  override isFull() {\n    return false;\n  }\n\n  override push(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.push(value);\n  }\n\n  override unshift(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.unshift(value);\n  }\n\n  /**\n   * Doubles the capacity of the buffer.\n   */\n  private expand() {\n    const newCapacity = this.capacity * 2;\n    const newData = new Array<T>(newCapacity);\n    const len = this.length();\n\n    // Rotate the buffer to start at index 0 again, since we can't just\n    // allocate more space at the end.\n    for (let i = 0; i < len; i++) {\n      newData[i] = this.get(this.wrap(this.begin + i));\n    }\n\n    this.data = newData;\n    this.capacity = newCapacity;\n    this.doubledCapacity = 2 * this.capacity;\n    this.begin = 0;\n    this.end = len;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {Container} from '../types';\nimport {deepClone} from '../util/deep_clone';\nimport {deepMapAndAwaitAll, DeepMapAsyncResult, DeepMapResult, deepZip, zipToList} from '../util/deep_map';\nimport {GrowingRingBuffer} from '../util/growing_ring_buffer';\nimport {RingBuffer} from '../util/ring_buffer';\n\n/**\n * A nested structure of LazyIterators, used as the input to zip().\n */\nexport type IteratorContainer = Container<LazyIterator<tf.TensorContainer>>;\n\n// Here we implement a simple asynchronous iterator.\n// This lets us avoid using either third-party stream libraries or\n// recent TypeScript language support requiring polyfills.\n\n/**\n * Create a `LazyIterator` from an array of items.\n */\nexport function iteratorFromItems<T>(items: T[]): LazyIterator<T> {\n  return new ArrayIterator(items);\n}\n\n/**\n * Create a `LazyIterator` of incrementing integers.\n */\nexport function iteratorFromIncrementing(start: number): LazyIterator<number> {\n  let i = start;\n  return iteratorFromFunction(() => ({value: i++, done: false}));\n}\n\n/**\n * Create a `LazyIterator` from a function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * await iter.forEachAsync(e => console.log(e));\n * ```\n *\n * @param func A function that produces data on each call.\n */\nexport function iteratorFromFunction<T>(\n    func: () =>\n        IteratorResult<T>| Promise<IteratorResult<T>>): LazyIterator<T> {\n  return new FunctionCallIterator(func);\n}\n\n/**\n * Create a `LazyIterator` by concatenating underlying streams, which are\n * themselves provided as a stream.\n *\n * This can also be thought of as a \"stream flatten\" operation.\n *\n * @param baseIterators A stream of streams to be concatenated.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenated<T>(\n    baseIterators: LazyIterator<LazyIterator<T>>,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return new ChainedIterator(baseIterators, baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by concatenating streams produced by calling a\n * stream-generating function a given number of times.\n *\n * Since a `LazyIterator` is read-once, it cannot be repeated, but this\n * function can be used to achieve a similar effect:\n *\n *   LazyIterator.ofConcatenatedFunction(() => new MyIterator(), 6);\n *\n * @param iteratorFunc: A function that produces a new stream on each call.\n * @param count: The number of times to call the function.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenatedFunction<T>(\n    iteratorFunc: () => IteratorResult<LazyIterator<T>>, count: number,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return iteratorFromConcatenated(\n      iteratorFromFunction(iteratorFunc).take(count), baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by zipping together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nexport function iteratorFromZipped<O extends tf.TensorContainer>(\n    iterators: IteratorContainer,\n    mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL): LazyIterator<O> {\n  return new ZipIterator<O>(iterators, mismatchMode);\n}\n\n/**\n * An asynchronous iterator, providing lazy access to a potentially\n * unbounded stream of elements.\n *\n * Iterator can be obtained from a dataset:\n * `const iter = await dataset.iterator();`\n */\nexport abstract class LazyIterator<T> {\n  // This class implements AsyncIterator<T>, but we have not yet set the\n  // TypeScript --downlevelIteration flag to enable that.\n\n  abstract summary(): string;\n\n  /**\n   * Returns a `Promise` for the next element in the stream.\n   *\n   * When an item can be provided successfully, the return value is\n   * `{value:T, done:false}`.\n   *\n   * Calling next() on a closed stream returns `{value:null, done:true}`.\n   */\n  abstract next(): Promise<IteratorResult<T>>;\n\n  /**\n   * Collect all remaining elements of a bounded stream into an array.\n   * Obviously this will succeed only for small streams that fit in memory.\n   * Useful for testing.\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArray(): Promise<T[]> {\n    const result: T[] = [];\n    let x = await this.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await this.next();\n    }\n    return result;\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArrayForTest(): Promise<T[]> {\n    const stream = this.prefetch(100);\n    const result: T[] = [];\n    let x = await stream.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await stream.next();\n    }\n    return result;\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveFully(): Promise<void> {\n    let x = await this.next();\n    while (!x.done) {\n      x = await this.next();\n    }\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted, or a predicate fails.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveWhile(predicate: (r: T) => boolean): Promise<void> {\n    let x = await this.next();\n    let shouldContinue = predicate(x.value);\n    while ((!x.done) && shouldContinue) {\n      x = await this.next();\n      shouldContinue = predicate(x.value);\n    }\n  }\n\n  /**\n   * Handles errors thrown on this stream using a provided handler function.\n   *\n   * @param handler A function that handles any `Error` thrown during a `next()`\n   *   call and returns true if the stream should continue (dropping the failed\n   *   call) or false if the stream should quietly terminate.  If the handler\n   *   itself throws (or rethrows) an `Error`, that will be propagated.\n   *\n   * @returns A `LazyIterator` of elements passed through from upstream,\n   *   possibly filtering or terminating on upstream `next()` calls that\n   *   throw an `Error`.\n   */\n  handleErrors(handler: (error: Error) => boolean): LazyIterator<T> {\n    return new ErrorHandlingLazyIterator(this, handler);\n  }\n\n  // TODO(soergel): Implement reduce() etc.\n\n  /**\n   * Filters this stream according to `predicate`.\n   *\n   * @param predicate A function mapping a stream element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `LazyIterator` of elements for which the predicate was true.\n   */\n  filter(predicate: (value: T) => boolean): LazyIterator<T> {\n    return new FilterIterator(this, predicate);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  map<O>(transform: (value: T) => O): LazyIterator<O> {\n    return new MapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through an async 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a `Promise` for a\n   *   transformed stream element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  mapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform, forcing serial execution.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  serialMapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform).serial();\n  }\n\n  /**\n   * Maps this stream through a 1-to-many transform.\n   *\n   * @param transform A function mapping a stream element to an array of\n   *   transformed elements.\n   *\n   * @returns A `DataStream` of transformed elements.\n   */\n  flatmap<O>(transform: (value: T) => O[]): LazyIterator<O> {\n    return new FlatmapIterator(this, transform);\n  }\n\n  /**\n   * Apply a function to every element of the stream.\n   *\n   * @param f A function to apply to each stream element.\n   */\n  async forEachAsync(f: (value: T) => void): Promise<void> {\n    return this.map(f).resolveFully();\n  }\n\n  /**\n   * Apply a function to every element of the stream, forcing serial execution.\n   *\n   * @param f A function to apply to each stream element.  Should return 'true'\n   *   to indicate that the stream should continue, or 'false' to cause it to\n   *   terminate.\n   */\n  async serialForEach(f: (value: T) => Promise<boolean>): Promise<void> {\n    return this.serialMapAsync(f).resolveWhile(x => (x === true));\n  }\n\n  /**\n   * Groups elements into batches, represented as arrays of elements.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"Row-major\" means that the resulting batch is simply a collection of\n   * rows: `[row1, row2, row3, ...]`.  This is contrast to the column-major\n   * form, which is needed for vectorized computation.\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `LazyIterator` of batches of elements, represented as arrays\n   *   of the original element type.\n   */\n  rowMajorBatch(batchSize: number, smallLastBatch = true): LazyIterator<T[]> {\n    return new RowMajorBatchIterator(this, batchSize, smallLastBatch);\n  }\n\n  /**\n   * Groups elements into batches, represented in column-major form.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"column-major\" means that the resulting batch is a (potentially\n   * nested) structure representing the columns.  Each column entry, then,\n   * contains a collection of the values found in that column for a range of\n   * input elements.  This representation allows for vectorized computation, in\n   * contrast to the row-major form.\n   *\n   * The inputs should all have the same nested structure (i.e., of arrays and\n   * dicts).  The result is a single object with the same nested structure,\n   * where the leaves are arrays collecting the values of the inputs at that\n   * location (or, optionally, the result of a custom function applied to those\n   * arrays).\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @param zipFn: (optional) A function that expects an array of elements at a\n   *   single node of the object tree, and returns a `DeepMapResult`.  The\n   *   `DeepMapResult` either provides a result value for that node (i.e.,\n   *   representing the subtree), or indicates that the node should be processed\n   *   recursively.  The default zipFn recurses as far as possible and places\n   *   arrays at the leaves.\n   * @returns A `LazyIterator` of batches of elements, represented as an object\n   *   with collections at the leaves.\n   */\n  columnMajorBatch(\n      batchSize: number, smallLastBatch = true,\n      // tslint:disable-next-line:no-any\n      zipFn: (xs: any[]) => DeepMapResult = zipToList):\n      LazyIterator<tf.TensorContainer> {\n    // First collect the desired number of input elements as a row-major batch.\n    const rowBatches = this.rowMajorBatch(batchSize, smallLastBatch);\n    // Now 'rotate' or 'pivot' the data, collecting all values from each column\n    // in the batch (i.e., for each key within the elements) into an array.\n    return rowBatches.map(x => deepZip(x, zipFn));\n  }\n\n  /**\n   * Concatenate this `LazyIterator` with another.\n   *\n   * @param iterator A `LazyIterator` to be concatenated onto this one.\n   * @param baseErrorHandler An optional function that can intercept `Error`s\n   *   raised during a `next()` call on the base stream.  This function can\n   *   decide whether the error should be propagated, whether the error should\n   *   be ignored, or whether the base stream should be terminated.\n   * @returns A `LazyIterator`.\n   */\n  concatenate(\n      iterator: LazyIterator<T>,\n      baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n    return new ChainedIterator(\n        iteratorFromItems([this, iterator]), baseErrorHandler);\n  }\n\n  /**\n   * Limits this stream to return at most `count` items.\n   *\n   * @param count The maximum number of items to provide from the stream. If\n   * a negative or undefined value is given, the entire stream is returned\n   *   unaltered.\n   */\n  take(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new TakeIterator(this, count);\n  }\n\n  /**\n   * Skips the first `count` items in this stream.\n   *\n   * @param count The number of items to skip.  If a negative or undefined\n   * value is given, the entire stream is returned unaltered.\n   */\n  skip(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new SkipIterator(this, count);\n  }\n\n  /**\n   * Prefetch the first `bufferSize` items in this stream.\n   *\n   * Note this prefetches Promises, but makes no guarantees about when those\n   * Promises resolve.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   */\n  prefetch(bufferSize: number): LazyIterator<T> {\n    return new PrefetchIterator(this, bufferSize);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  /**\n   * Randomly shuffles the elements of this stream.\n   *\n   * @param bufferSize: An integer specifying the number of elements from\n   * this stream from which the new stream will sample.\n   * @param seed: (Optional.) An integer specifying the random seed that\n   * will be used to create the distribution.\n   */\n  shuffle(windowSize: number, seed?: string): LazyIterator<T> {\n    return new ShuffleIterator(this, windowSize, seed);\n  }\n\n  /**\n   * Force an iterator to execute serially: each next() call will await the\n   * prior one, so that they cannot execute concurrently.\n   */\n  serial(): LazyIterator<T> {\n    return new SerialIterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on LazyIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// Iterators that just extend LazyIterator directly\n// ============================================================================\n\nclass ArrayIterator<T> extends LazyIterator<T> {\n  private trav = 0;\n  constructor(protected items: T[]) {\n    super();\n  }\n\n  summary() {\n    return `Array of ${this.items.length} items`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.trav >= this.items.length) {\n      return {value: null, done: true};\n    }\n    const item = this.items[this.trav];\n    this.trav++;\n    return {value: deepClone(item), done: false};\n  }\n}\n\nclass FunctionCallIterator<T> extends LazyIterator<T> {\n  constructor(\n      protected nextFn: () => IteratorResult<T>| Promise<IteratorResult<T>>) {\n    super();\n  }\n\n  summary() {\n    return `Function call`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    try {\n      return this.nextFn();\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message =\n          `Error thrown while iterating through a dataset: ${e.message}`;\n      throw e;\n    }\n  }\n}\n\nclass SerialIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(protected upstream: LazyIterator<T>) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Serial`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    return this.upstream.next();\n  }\n}\n\nclass SkipIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  count = 0;\n\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Skip`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider tradeoffs of reading in parallel, eg.\n    // collecting next() promises in an Array and then waiting for\n    // Promise.all() of those. Benefit: pseudo-parallel execution.  Drawback:\n    // maybe delayed GC.\n    while (this.count++ < this.maxCount) {\n      const skipped = await this.upstream.next();\n      // short-circuit if upstream is already empty\n      if (skipped.done) {\n        return skipped;\n      }\n      tf.dispose(skipped.value as {});\n    }\n    return this.upstream.next();\n  }\n}\n\nclass TakeIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Take`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.count++ >= this.maxCount) {\n      return {value: null, done: true};\n    }\n    return this.upstream.next();\n  }\n}\n\n// Note this batch just groups items into row-wise element arrays.\n// Rotating these to a column-wise representation happens only at the dataset\n// level.\nclass RowMajorBatchIterator<T> extends LazyIterator<T[]> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T[]>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected batchSize: number,\n      protected enableSmallLastBatch = true) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> RowMajorBatch`;\n  }\n\n  async next(): Promise<IteratorResult<T[]>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T[]>> {\n    const batch: T[] = [];\n    while (batch.length < this.batchSize) {\n      const item = await this.upstream.next();\n      if (item.done) {\n        if (this.enableSmallLastBatch && batch.length > 0) {\n          return {value: batch, done: false};\n        }\n        return {value: null, done: true};\n      }\n      batch.push(item.value);\n    }\n    return {value: batch, done: false};\n  }\n}\n\nclass FilterIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected predicate: (value: T) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Filter`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      const item = await this.upstream.next();\n      if (item.done || this.predicate(item.value)) {\n        return item;\n      }\n      tf.dispose(item.value as {});\n    }\n  }\n}\n\nclass MapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Map`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\nclass ErrorHandlingLazyIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected handler: (error: Error) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> handleErrors`;\n  }\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      try {\n        return await this.upstream.next();\n      } catch (e) {\n        if (!this.handler(e)) {\n          return {value: null, done: true};\n        }\n        // If the handler returns true, loop and fetch the next upstream item.\n\n        // If the upstream iterator throws an endless stream of errors, and if\n        // the handler says to ignore them, then we loop forever here.  That is\n        // the correct behavior-- it's up to the handler to decide when to stop.\n      }\n    }\n  }\n}\n\nclass AsyncMapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => Promise<O>) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> AsyncMap`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = await this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\n// Iterators that maintain a queue of pending items\n// ============================================================================\n\n/**\n * A base class for transforming streams that operate by maintaining an\n * output queue of elements that are ready to return via next().  This is\n * commonly required when the transformation is 1-to-many:  A call to next()\n * may trigger a call to the underlying stream, which will produce many\n * mapped elements of this stream-- of which we need to return only one, so\n * we have to queue the rest.\n */\nexport abstract class OneToManyIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  protected outputQueue: RingBuffer<T>;\n\n  constructor() {\n    super();\n    this.outputQueue = new GrowingRingBuffer<T>();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  /**\n   * Read one or more chunks from upstream and process them, possibly\n   * reading or writing a carryover, and adding processed items to the\n   * output queue.  Note it's possible that no items are added to the queue\n   * on a given pump() call, even if the upstream stream is not closed\n   * (e.g., because items are filtered).\n   *\n   * @return `true` if any action was taken, i.e. fetching items from the\n   *   upstream source OR adding items to the output queue.  `false` if the\n   *   upstream source is exhausted AND nothing was added to the queue\n   * (i.e., any remaining carryover).\n   */\n  protected abstract pump(): Promise<boolean>;\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // Fetch so that the queue contains at least one item if possible.\n    // If the upstream source is exhausted, AND there are no items left in\n    // the output queue, then this stream is also exhausted.\n    while (this.outputQueue.length() === 0) {\n      // TODO(soergel): consider parallel reads.\n      if (!await this.pump()) {\n        return {value: null, done: true};\n      }\n    }\n    return {value: this.outputQueue.shift(), done: false};\n  }\n}\nclass FlatmapIterator<I, O> extends OneToManyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O[]) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Flatmap`;\n  }\n\n  async pump(): Promise<boolean> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return false;\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // that's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying any\n    // intermediate Tensors.  Here we are concerned only about the inputs.\n    const mappedArray = this.transform(item.value);\n    const outputTensors =\n        tf.tensor_util.getTensorsInContainer(mappedArray as {});\n    this.outputQueue.pushAll(mappedArray);\n\n    // TODO(soergel) faster intersection, and deduplicate outputTensors\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n\n    return true;\n  }\n}\n\n/**\n * Provides a `LazyIterator` that concatenates a stream of underlying\n * streams.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n */\nexport class ChainedIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>> = null;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private iterator: LazyIterator<T> = null;\n  private moreIterators: LazyIterator<LazyIterator<T>>;\n\n  constructor(\n      iterators: LazyIterator<LazyIterator<T>>,\n      private readonly baseErrorHandler?: (e: Error) => boolean) {\n    super();\n    this.moreIterators = iterators;\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of chained summaries';\n    return `${upstreamSummaries} -> Chained`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    this.lastRead = this.readFromChain(this.lastRead);\n    return this.lastRead;\n  }\n\n  private async readFromChain(lastRead: Promise<IteratorResult<T>>):\n      Promise<IteratorResult<T>> {\n    // Must await on the previous read since the previous read may have advanced\n    // the stream of streams, from which we need to read.\n    // This is unfortunate since we can't parallelize reads. Which means\n    // prefetching of chained streams is a no-op.\n    // One solution is to prefetch immediately upstream of this.\n    await lastRead;\n    if (this.iterator == null) {\n      const iteratorResult = await this.moreIterators.next();\n      if (iteratorResult.done) {\n        // No more streams to stream from.\n        return {value: null, done: true};\n      }\n      this.iterator = iteratorResult.value;\n      if (this.baseErrorHandler != null) {\n        this.iterator = this.iterator.handleErrors(this.baseErrorHandler);\n      }\n    }\n    const itemResult = await this.iterator.next();\n    if (itemResult.done) {\n      this.iterator = null;\n      return this.readFromChain(lastRead);\n    }\n    return itemResult;\n  }\n}\n\nexport enum ZipMismatchMode {\n  FAIL,      // require zipped streams to have the same length\n  SHORTEST,  // terminate zip when the first stream is exhausted\n  LONGEST    // use nulls for exhausted streams; use up the longest stream.\n}\n\n/**\n * Provides a `LazyIterator` that zips together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nclass ZipIterator<O extends tf.TensorContainer> extends LazyIterator<O> {\n  private count = 0;\n  private currentPromise: Promise<IteratorResult<O>> = null;\n\n  constructor(\n      protected readonly iterators: IteratorContainer,\n      protected readonly mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL) {\n    super();\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of zip summaries';\n    return `{${upstreamSummaries}} -> Zip`;\n  }\n\n  private async nextState(afterState: Promise<IteratorResult<O>>):\n      Promise<IteratorResult<O>> {\n    // This chaining ensures that the underlying next() are not even called\n    // before the previous ones have resolved.\n    await afterState;\n\n    // Collect underlying iterator \"done\" signals as a side effect in\n    // getNext()\n    let numIterators = 0;\n    let iteratorsDone = 0;\n\n    function getNext(container: IteratorContainer): DeepMapAsyncResult {\n      if (container instanceof LazyIterator) {\n        const result = container.next();\n        return {\n          value: result.then(x => {\n            numIterators++;\n            if (x.done) {\n              iteratorsDone++;\n            }\n            return x.value;\n          }),\n          recurse: false\n        };\n      } else {\n        return {value: null, recurse: true};\n      }\n    }\n\n    const mapped: O = await deepMapAndAwaitAll(this.iterators, getNext);\n\n    if (numIterators === iteratorsDone) {\n      // The streams have all ended.\n      return {value: null, done: true};\n    }\n    if (iteratorsDone > 0) {\n      switch (this.mismatchMode) {\n        case ZipMismatchMode.FAIL:\n          throw new Error(\n              'Zipped streams should have the same length. ' +\n              `Mismatched at element ${this.count}.`);\n        case ZipMismatchMode.SHORTEST:\n          return {value: null, done: true};\n        case ZipMismatchMode.LONGEST:\n        default:\n          // Continue.  The exhausted streams already produced value: null.\n      }\n    }\n\n    this.count++;\n    return {value: mapped, done: false};\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    this.currentPromise = this.nextState(this.currentPromise);\n    return this.currentPromise;\n  }\n}\n\n// Iterators that maintain a ring buffer of pending promises\n// ============================================================================\n\n/**\n * A stream that prefetches a given number of items from an upstream source,\n * returning them in FIFO order.\n *\n * Note this prefetches Promises, but makes no guarantees about when those\n * Promises resolve.\n */\nexport class PrefetchIterator<T> extends LazyIterator<T> {\n  protected buffer: RingBuffer<Promise<IteratorResult<T>>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected bufferSize: number) {\n    super();\n    this.buffer = new RingBuffer<Promise<IteratorResult<T>>>(bufferSize);\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Prefetch`;\n  }\n\n  /**\n   * Refill the prefetch buffer.  Returns only after the buffer is full, or\n   * the upstream source is exhausted.\n   */\n  protected refill() {\n    while (!this.buffer.isFull()) {\n      const v = this.upstream.next();\n      this.buffer.push(v);\n    }\n  }\n\n  next(): Promise<IteratorResult<T>> {\n    this.refill();\n    // This shift will never throw an error because the buffer is always\n    // full after a refill. If the stream is exhausted, the buffer will be\n    // full of Promises that will resolve to the end-of-stream signal.\n    return this.buffer.shift();\n  }\n}\n\n/**\n * A stream that performs a sliding-window random shuffle on an upstream\n * source. This is like a `PrefetchIterator` except that the items are\n * returned in randomized order.  Mixing naturally improves as the buffer\n * size increases.\n */\nexport class ShuffleIterator<T> extends PrefetchIterator<T> {\n  private readonly random: seedrandom.prng;\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private upstreamExhausted = false;\n\n  constructor(\n    protected override upstream: LazyIterator<T>, protected windowSize: number,\n      seed?: string) {\n    super(upstream, windowSize);\n    this.random = seedrandom.alea(seed || tf.util.now().toString());\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  override async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private randomInt(max: number) {\n    return Math.floor(this.random() * max);\n  }\n\n  protected chooseIndex(): number {\n    return this.randomInt(this.buffer.length());\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider performance\n    if (!this.upstreamExhausted) {\n      this.refill();\n    }\n    while (!this.buffer.isEmpty()) {\n      const chosenIndex = this.chooseIndex();\n      const result = await this.buffer.shuffleExcise(chosenIndex);\n      if (result.done) {\n        this.upstreamExhausted = true;\n      } else {\n        this.refill();\n        return result;\n      }\n    }\n    return {value: null, done: true};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\n\n/**\n * Represents a potentially large collection of text lines.\n *\n * The results are not batched.\n */\nexport class TextLineDataset extends Dataset<string> {\n  /**\n   * Create a `TextLineDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   */\n  constructor(protected readonly input: DataSource) {\n    super();\n  }\n\n  async iterator(): Promise<LazyIterator<string>> {\n    const inputIterator = await this.input.iterator();\n    const utf8Iterator = inputIterator.decodeUTF8();\n    const lineIterator = utf8Iterator.split('\\n').map(line => {\n      // Windows/DOS format text file has extra line breaker at the end of line.\n      if (line.endsWith('\\r')) {\n        line = line.slice(0, -1);\n      }\n      return line;\n    });\n    return lineIterator;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\nimport {ColumnConfig, CSVConfig} from '../types';\nimport {TextLineDataset} from './text_line_dataset';\n\nconst CODE_QUOTE = '\"';\nconst STATE_OUT = Symbol('out');\nconst STATE_FIELD = Symbol('field');\nconst STATE_QUOTE = Symbol('quote');\nconst STATE_QUOTE_AFTER_QUOTE = Symbol('quoteafterquote');\nconst STATE_WITHIN_QUOTE_IN_QUOTE = Symbol('quoteinquote');\n\n/**\n * Represents a potentially large collection of delimited text records.\n *\n * The produced `TensorContainer`s each contain one key-value pair for\n * every column of the table.  When a field is empty in the incoming data, the\n * resulting value is `undefined`, or throw error if it is required.  Values\n * that can be parsed as numbers are emitted as type `number`, other values\n * are parsed as `string`.\n *\n * The results are not batched.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport class CSVDataset extends Dataset<TensorContainer> {\n  base: TextLineDataset;\n  private hasHeader = true;\n  private fullColumnNames: string[] = null;\n  private columnNamesValidated = false;\n  private columnConfigs: {[key: string]: ColumnConfig} = null;\n  private configuredColumnsOnly = false;\n  private delimiter = ',';\n  private delimWhitespace = false;\n\n  /**\n   * Returns column names of the csv dataset. If `configuredColumnsOnly` is\n   * true, return column names in `columnConfigs`. If `configuredColumnsOnly` is\n   * false and `columnNames` is provided, `columnNames`. If\n   * `configuredColumnsOnly` is false and `columnNames` is not provided, return\n   * all column names parsed from the csv file. For example usage please go to\n   * `tf.data.csv`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async columnNames() {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    return this.configuredColumnsOnly ? Object.keys(this.columnConfigs) :\n                                        this.fullColumnNames;\n  }\n\n  /* 1) If `columnNames` is provided as string[], use this string[] as output\n   * keys in corresponding order. The length must match the number of inferred\n   * columns if `hasHeader` is true .\n   * 2) If `columnNames` is not provided, parse header line as `columnNames` if\n   * hasHeader is true. If `hasHeader` is false, throw an error.\n   * 3) If `columnConfigs` is provided, all the keys in `columnConfigs` must\n   * exist in parsed `columnNames`.\n   */\n  private async setColumnNames() {\n    const columnNamesFromFile = await this.maybeReadHeaderLine();\n    if (!this.fullColumnNames && !columnNamesFromFile) {\n      // Throw an error if columnNames is not provided and no header line.\n      throw new Error(\n          'Column names must be provided if there is no header line.');\n    } else if (this.fullColumnNames && columnNamesFromFile) {\n      // Check provided columnNames match header line.\n      util.assert(\n          columnNamesFromFile.length === this.fullColumnNames.length,\n          () => 'The length of provided columnNames (' +\n              this.fullColumnNames.length.toString() +\n              ') does not match the length of the header line read from ' +\n              'file (' + columnNamesFromFile.length.toString() + ').');\n    }\n    if (!this.fullColumnNames) {\n      this.fullColumnNames = columnNamesFromFile;\n    }\n    // Check if there are duplicate column names.\n    const counts: {[key: string]: number} = this.fullColumnNames.reduce(\n        (countAcc: {[key: string]: number}, name) => {\n          countAcc[name] = (countAcc[name] + 1) || 1;\n          return countAcc;\n        },\n        {});\n    const duplicateNames =\n        Object.keys(counts).filter((name) => (counts[name] > 1));\n    util.assert(\n        duplicateNames.length === 0,\n        () => 'Duplicate column names found: ' + duplicateNames.toString());\n    // Check if keys in columnConfigs match columnNames.\n    if (this.columnConfigs) {\n      for (const key of Object.keys(this.columnConfigs)) {\n        const index = this.fullColumnNames.indexOf(key);\n        if (index === -1) {\n          throw new Error(\n              'The key \"' + key +\n              '\" provided in columnConfigs does not match any of the column ' +\n              'names (' + this.fullColumnNames.toString() + ').');\n        }\n      }\n    }\n    this.columnNamesValidated = true;\n  }\n\n  private async maybeReadHeaderLine() {\n    if (this.hasHeader) {\n      const iter = await this.base.iterator();\n      const firstElement = await iter.next();\n      if (firstElement.done) {\n        throw new Error('No data was found for CSV parsing.');\n      }\n      const firstLine: string = firstElement.value;\n      const headers = this.parseRow(firstLine, false);\n      return headers;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Create a `CSVDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   * @param csvConfig (Optional) A CSVConfig object that contains configurations\n   *     of reading and decoding from CSV file(s).\n   *\n   *     hasHeader: (Optional) A boolean value that indicates whether the first\n   *     row of provided CSV file is a header line with column names, and should\n   *     not be included in the data. Defaults to `true`.\n   *\n   *     columnNames: (Optional) A list of strings that corresponds to\n   *     the CSV column names, in order. If provided, it ignores the column\n   *     names inferred from the header row. If not provided, infers the column\n   *     names from the first row of the records. If hasHeader is false and\n   *     columnNames is not provided, this method throws an error.\n   *\n   *     columnConfigs: (Optional) A dictionary whose key is column names, value\n   *     is an object stating if this column is required, column's data type,\n   *     default value, and if this column is label. If provided, keys must\n   *     correspond to names provided in columnNames or inferred from the file\n   *     header lines. If isLabel is true any column, returns an array of two\n   *     items: the first item is a dict of features key/value pairs, the second\n   *     item is a dict of labels key/value pairs. If no feature is marked as\n   *     label, returns a dict of features only.\n   *\n   *     configuredColumnsOnly (Optional) If true, only columns provided in\n   *     columnConfigs will be parsed and provided during iteration.\n   *\n   *     delimiter (Optional) The string used to parse each line of the input\n   *     file. Defaults to `,`.\n   */\n  constructor(protected readonly input: DataSource, csvConfig?: CSVConfig) {\n    super();\n    this.base = new TextLineDataset(input);\n    if (!csvConfig) {\n      csvConfig = {};\n    }\n    this.hasHeader = csvConfig.hasHeader === false ? false : true;\n    this.fullColumnNames = csvConfig.columnNames;\n    this.columnConfigs = csvConfig.columnConfigs;\n    this.configuredColumnsOnly = csvConfig.configuredColumnsOnly;\n    if (csvConfig.delimWhitespace) {\n      util.assert(\n          csvConfig.delimiter == null,\n          () =>\n              'Delimiter should not be provided when delimWhitespace is true.');\n      this.delimWhitespace = true;\n      this.delimiter = ' ';\n    } else {\n      this.delimiter = csvConfig.delimiter ? csvConfig.delimiter : ',';\n    }\n  }\n\n  async iterator(): Promise<LazyIterator<TensorContainer>> {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    let lines = await this.base.iterator();\n    if (this.hasHeader) {\n      // We previously read the first line to get the columnNames.\n      // Now that we're providing data, skip it.\n      lines = lines.skip(1);\n    }\n    return lines.map(x => this.makeDataElement(x));\n  }\n\n  makeDataElement(line: string): TensorContainer {\n    const values = this.parseRow(line);\n    const features: {[key: string]: TensorContainer} = {};\n    const labels: {[key: string]: TensorContainer} = {};\n\n    for (let i = 0; i < this.fullColumnNames.length; i++) {\n      const key = this.fullColumnNames[i];\n      const config = this.columnConfigs ? this.columnConfigs[key] : null;\n      if (this.configuredColumnsOnly && !config) {\n        // This column is not selected.\n        continue;\n      } else {\n        const value = values[i];\n        let parsedValue = null;\n        if (value === '') {\n          // If default value is provided, use it. If default value is not\n          // provided, set as undefined.\n          if (config && config.default !== undefined) {\n            parsedValue = config.default;\n          } else if (config && (config.required || config.isLabel)) {\n            throw new Error(\n                `Required column ${key} is empty in this line: ${line}`);\n          } else {\n            parsedValue = undefined;\n          }\n        } else {\n          // A value is present, so parse it based on type\n          const valueAsNum = Number(value);\n          if (isNaN(valueAsNum)) {\n            // The value is a string and this column is declared as boolean\n            // in config, parse it as boolean.\n            if (config && config.dtype === 'bool') {\n              parsedValue = this.getBoolean(value);\n            } else {\n              // Set value as string\n              parsedValue = value;\n            }\n          } else if (!config || !config.dtype) {\n            // If this value is a number and no type config is provided, return\n            // it as number.\n            parsedValue = valueAsNum;\n          } else {\n            // If this value is a number and data type is provided, parse it\n            // according to provided data type.\n            switch (config.dtype) {\n              case 'float32':\n                parsedValue = valueAsNum;\n                break;\n              case 'int32':\n                parsedValue = Math.floor(valueAsNum);\n                break;\n              case 'bool':\n                parsedValue = this.getBoolean(value);\n                break;\n              default:\n                parsedValue = valueAsNum;\n            }\n          }\n        }\n        // Check if this column is label.\n        (config && config.isLabel) ? labels[key] = parsedValue :\n                                     features[key] = parsedValue;\n      }\n    }\n    // If label exists, return an object of features and labels as {xs:features,\n    // ys:labels}, otherwise return features only.\n    if (Object.keys(labels).length === 0) {\n      return features;\n\n    } else {\n      return {xs: features, ys: labels};\n    }\n  }\n\n  private getBoolean(value: string): number {\n    if (value === '1' || value.toLowerCase() === 'true') {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n\n  // adapted from https://beta.observablehq.com/@mbostock/streaming-csv\n  private parseRow(line: string, validateElementCount = true): string[] {\n    const result: string[] = [];\n    let readOffset = 0;\n    const readLength = line.length;\n    let currentState = STATE_OUT;\n    // Goes through the line to parse quote.\n    for (let i = 0; i < readLength; i++) {\n      switch (currentState) {\n        // Before enter a new field\n        case STATE_OUT:\n          switch (line.charAt(i)) {\n            // Enter a quoted field\n            case CODE_QUOTE:\n              readOffset = i + 1;\n              currentState = STATE_QUOTE;\n              break;\n            // Read an empty field\n            case this.delimiter:\n              readOffset = i + 1;\n              // If delimiter is white space and configured to collapse\n              // multiple white spaces, ignore this white space.\n              if (this.delimiter === ' ' && this.delimWhitespace) {\n                break;\n              }\n              result.push('');\n              currentState = STATE_OUT;\n              break;\n            // Enter an unquoted field\n            default:\n              currentState = STATE_FIELD;\n              readOffset = i;\n              break;\n          }\n          break;\n        // In an unquoted field\n        case STATE_FIELD:\n          switch (line.charAt(i)) {\n            // Exit an unquoted field, add it to result\n            case this.delimiter:\n              result.push(line.substring(readOffset, i));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            default:\n          }\n          break;\n        // In a quoted field\n        case STATE_QUOTE:\n          switch (line.charAt(i)) {\n            // Read a quote after a quote\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE_AFTER_QUOTE;\n              break;\n            default:\n          }\n          break;\n        // This state means it's right after a second quote in a field\n        case STATE_QUOTE_AFTER_QUOTE:\n          switch (line.charAt(i)) {\n            // Finished a quoted field\n            case this.delimiter:\n              result.push(line.substring(readOffset, i - 1));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            // Finished a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            // In a quoted part in a quoted field\n            default:\n              currentState = STATE_WITHIN_QUOTE_IN_QUOTE;\n              break;\n          }\n          break;\n        case STATE_WITHIN_QUOTE_IN_QUOTE:\n          switch (line.charAt(i)) {\n            // Exit a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            default:\n          }\n          break;\n        default:\n      }\n    }\n    // Adds last item based on if it is quoted.\n    if (currentState === STATE_QUOTE_AFTER_QUOTE) {\n      result.push(line.substring(readOffset, readLength - 1));\n    } else {\n      result.push(line.substring(readOffset));\n    }\n    // Check if each row has the same number of elements as column names.\n    if (validateElementCount && result.length !== this.fullColumnNames.length) {\n      throw new Error(`Invalid row in csv file. Should have ${\n          this.fullColumnNames.length} elements in a row, but got ${result}`);\n    }\n    return result;\n  }\n}\n\n// TODO(soergel): add more basic datasets for parity with tf.data\n// tf.data.FixedLengthRecordDataset()\n// tf.data.TFRecordDataset()\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env, Tensor, tensor, Tensor2D, Tensor3D, TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {MicrophoneConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of tensors from microphone audio stream. The tensors are\n * representing audio data as frequency-domain spectrogram generated with\n * browser's native FFT. Tensors representing time-domain waveform is available\n * based on configuration. Only works in browser environment.\n */\nexport class MicrophoneIterator extends LazyIterator<TensorContainer> {\n  private isClosed = false;\n  private stream: MediaStream;\n  private readonly fftSize: number;\n  private readonly columnTruncateLength: number;\n  private freqData: Float32Array;\n  private timeData: Float32Array;\n  private readonly numFrames: number;\n  private analyser: AnalyserNode;\n  private audioContext: AudioContext;\n  private sampleRateHz: number;\n  private readonly audioTrackConstraints: MediaTrackConstraints;\n  private readonly smoothingTimeConstant: number;\n  private readonly includeSpectrogram: boolean;\n  private readonly includeWaveform: boolean;\n\n  private constructor(protected readonly microphoneConfig: MicrophoneConfig) {\n    super();\n    this.fftSize = microphoneConfig.fftSize || 1024;\n    const fftSizeLog2 = Math.log2(this.fftSize);\n    if (this.fftSize < 0 || fftSizeLog2 < 4 || fftSizeLog2 > 14 ||\n        !Number.isInteger(fftSizeLog2)) {\n      throw new Error(\n          `Invalid fftSize: it must be a power of 2 between ` +\n          `2 to 4 and 2 to 14, but got ${this.fftSize}`);\n    }\n\n    this.numFrames = microphoneConfig.numFramesPerSpectrogram || 43;\n    this.sampleRateHz = microphoneConfig.sampleRateHz;\n    this.columnTruncateLength =\n        microphoneConfig.columnTruncateLength || this.fftSize;\n    this.audioTrackConstraints = microphoneConfig.audioTrackConstraints;\n    this.smoothingTimeConstant = microphoneConfig.smoothingTimeConstant || 0;\n\n    this.includeSpectrogram =\n        microphoneConfig.includeSpectrogram === false ? false : true;\n    this.includeWaveform =\n        microphoneConfig.includeWaveform === true ? true : false;\n    if (!this.includeSpectrogram && !this.includeWaveform) {\n      throw new Error(\n          'Both includeSpectrogram and includeWaveform are false. ' +\n          'At least one type of data should be returned.');\n    }\n  }\n\n  summary() {\n    return `microphone`;\n  }\n\n  // Construct a MicrophoneIterator and start the audio stream.\n  static async create(microphoneConfig: MicrophoneConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'microphone API is only supported in browser environment.');\n    }\n\n    const microphoneIterator = new MicrophoneIterator(microphoneConfig);\n\n    // Call async function start() to initialize the audio stream.\n    await microphoneIterator.start();\n\n    return microphoneIterator;\n  }\n\n  // Start the audio stream and FFT.\n  async start(): Promise<void> {\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        audio: this.audioTrackConstraints == null ? true :\n                                                    this.audioTrackConstraints,\n        video: false\n      });\n    } catch (e) {\n      throw new Error(\n          `Error thrown while initializing video stream: ${e.message}`);\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain audio from microphone.');\n    }\n\n    const ctxConstructor =\n        // tslint:disable-next-line:no-any\n        (window as any).AudioContext || (window as any).webkitAudioContext;\n    this.audioContext = new ctxConstructor();\n\n    if (!this.sampleRateHz) {\n      // If sample rate is not provided, use the available sample rate on\n      // device.\n      this.sampleRateHz = this.audioContext.sampleRate;\n    } else if (this.audioContext.sampleRate !== this.sampleRateHz) {\n      throw new Error(\n          `Mismatch in sampling rate: ` +\n          `Expected: ${this.sampleRateHz}; ` +\n          `Actual: ${this.audioContext.sampleRate}`);\n    }\n\n    const streamSource = this.audioContext.createMediaStreamSource(this.stream);\n    this.analyser = this.audioContext.createAnalyser();\n    this.analyser.fftSize = this.fftSize * 2;\n    this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;\n    streamSource.connect(this.analyser);\n    this.freqData = new Float32Array(this.fftSize);\n    this.timeData = new Float32Array(this.fftSize);\n    return;\n  }\n\n  async next(): Promise<IteratorResult<TensorContainer>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let spectrogramTensor: Tensor;\n    let waveformTensor: Tensor;\n\n    const audioDataQueue = await this.getAudioData();\n    if (this.includeSpectrogram) {\n      const freqData = this.flattenQueue(audioDataQueue.freqDataQueue);\n      spectrogramTensor = this.getTensorFromAudioDataArray(\n          freqData, [this.numFrames, this.columnTruncateLength, 1]);\n    }\n    if (this.includeWaveform) {\n      const timeData = this.flattenQueue(audioDataQueue.timeDataQueue);\n      waveformTensor = this.getTensorFromAudioDataArray(\n          timeData, [this.numFrames * this.fftSize, 1]);\n    }\n\n    return {\n      value: {'spectrogram': spectrogramTensor, 'waveform': waveformTensor},\n      done: false\n    };\n  }\n\n  // Capture one result from the audio stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<{spectrogram: Tensor3D, waveform: Tensor2D}> {\n    return (await this.next()).value as\n        {spectrogram: Tensor3D, waveform: Tensor2D};\n  }\n\n  private async getAudioData():\n      Promise<{freqDataQueue: Float32Array[], timeDataQueue: Float32Array[]}> {\n    const freqDataQueue: Float32Array[] = [];\n    const timeDataQueue: Float32Array[] = [];\n    let currentFrames = 0;\n    return new Promise(resolve => {\n      const intervalID = setInterval(() => {\n        if (this.includeSpectrogram) {\n          this.analyser.getFloatFrequencyData(this.freqData);\n          // If the audio stream is initializing, return empty queue.\n          if (this.freqData[0] === -Infinity) {\n            resolve({freqDataQueue, timeDataQueue});\n          }\n          freqDataQueue.push(this.freqData.slice(0, this.columnTruncateLength));\n        }\n        if (this.includeWaveform) {\n          this.analyser.getFloatTimeDomainData(this.timeData);\n          timeDataQueue.push(this.timeData.slice());\n        }\n\n        // Clean interval and return when all frames have been collected\n        if (++currentFrames === this.numFrames) {\n          clearInterval(intervalID);\n          resolve({freqDataQueue, timeDataQueue});\n        }\n      }, this.fftSize / this.sampleRateHz * 1e3);\n    });\n  }\n\n  // Stop the audio stream and pause the iterator.\n  stop(): void {\n    if (!this.isClosed) {\n      this.isClosed = true;\n      this.analyser.disconnect();\n      this.audioContext.close();\n      if (this.stream != null && this.stream.getTracks().length > 0) {\n        this.stream.getTracks()[0].stop();\n      }\n    }\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor[]> {\n    throw new Error('Can not convert infinite audio stream to array.');\n  }\n\n  // Return audio sampling rate in Hz\n  getSampleRate(): number {\n    return this.sampleRateHz;\n  }\n\n  private flattenQueue(queue: Float32Array[]): Float32Array {\n    const frameSize = queue[0].length;\n    const freqData = new Float32Array(queue.length * frameSize);\n    queue.forEach((data, i) => freqData.set(data, i * frameSize));\n    return freqData;\n  }\n\n  private getTensorFromAudioDataArray(freqData: Float32Array, shape: number[]):\n      Tensor {\n    const vals = new Float32Array(util.sizeFromShape(shape));\n    // If the data is less than the output shape, the rest is padded with zeros.\n    vals.set(freqData, vals.length - freqData.length);\n    return tensor(vals, shape);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {browser, cast, env, expandDims, image, reshape, tensor1d, Tensor1D, tensor2d, Tensor2D, Tensor3D, Tensor4D, tidy, util} from '@tensorflow/tfjs-core';\nimport {WebcamConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of image tensors from webcam video stream. Only works in\n * browser environment.\n */\nexport class WebcamIterator extends LazyIterator<Tensor3D> {\n  private isClosed = true;\n  private stream: MediaStream;\n  private resize = false;\n  private cropSize: [number, number];\n  private cropBox: Tensor2D;\n  private cropBoxInd: Tensor1D;\n\n  private constructor(\n      protected readonly webcamVideoElement: HTMLVideoElement,\n      protected readonly webcamConfig: WebcamConfig) {\n    super();\n    if (this.needToResize()) {\n      this.resize = true;\n      this.cropSize =\n          [this.webcamConfig.resizeHeight, this.webcamConfig.resizeWidth];\n      this.cropBoxInd = tensor1d([0], 'int32');\n      if (this.webcamConfig.centerCrop) {\n        // Calculate the box based on resizing shape.\n        const widthCroppingRatio =\n            this.webcamConfig.resizeWidth * 1.0 / this.webcamVideoElement.width;\n        const heightCroppingRatio = this.webcamConfig.resizeHeight * 1.0 /\n            this.webcamVideoElement.height;\n        const widthCropStart = (1 - widthCroppingRatio) / 2;\n        const heightCropStart = (1 - heightCroppingRatio) / 2;\n        const widthCropEnd = widthCropStart + widthCroppingRatio;\n        const heightCropEnd = heightCroppingRatio + heightCropStart;\n        this.cropBox = tensor2d(\n            [heightCropStart, widthCropStart, heightCropEnd, widthCropEnd],\n            [1, 4]);\n      } else {\n        this.cropBox = tensor2d([0, 0, 1, 1], [1, 4]);\n      }\n    }\n  }\n\n  summary() {\n    return `webcam`;\n  }\n\n  // Construct a WebcamIterator and start it's video stream.\n  static async create(\n      webcamVideoElement?: HTMLVideoElement, webcamConfig: WebcamConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'tf.data.webcam is only supported in browser environment.');\n    }\n\n    if (!webcamVideoElement) {\n      // If webcam video element is not provided, create a hidden video element\n      // with provided width and height.\n      webcamVideoElement = document.createElement('video');\n      if (!webcamConfig.resizeWidth || !webcamConfig.resizeHeight) {\n        throw new Error(\n            'Please provide webcam video element, or resizeWidth and ' +\n            'resizeHeight to create a hidden video element.');\n      }\n      webcamVideoElement.width = webcamConfig.resizeWidth;\n      webcamVideoElement.height = webcamConfig.resizeHeight;\n    }\n    const webcamIterator = new WebcamIterator(webcamVideoElement, webcamConfig);\n\n    // Call async function to initialize the video stream.\n    await webcamIterator.start();\n\n    return webcamIterator;\n  }\n\n  // Async function to start video stream.\n  async start(): Promise<void> {\n    if (this.webcamConfig.facingMode) {\n      util.assert(\n          (this.webcamConfig.facingMode === 'user') ||\n              (this.webcamConfig.facingMode === 'environment'),\n          () =>\n              `Invalid webcam facing mode: ${this.webcamConfig.facingMode}. ` +\n              `Please provide 'user' or 'environment'`);\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          deviceId: this.webcamConfig.deviceId,\n          facingMode: this.webcamConfig.facingMode ?\n              this.webcamConfig.facingMode :\n              'user',\n          width: this.webcamVideoElement.width,\n          height: this.webcamVideoElement.height\n        }\n      });\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message = `Error thrown while initializing video stream: ${e.message}`;\n      throw e;\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain video from webcam.');\n    }\n\n    // Older browsers may not have srcObject\n    try {\n      this.webcamVideoElement.srcObject = this.stream;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = window.URL.createObjectURL(\n        this.stream as unknown as MediaSource);\n    }\n    // Start the webcam video stream\n    this.webcamVideoElement.play();\n\n    this.isClosed = false;\n\n    return new Promise<void>(resolve => {\n      // Add event listener to make sure the webcam has been fully initialized.\n      this.webcamVideoElement.onloadedmetadata = () => {\n        resolve();\n      };\n    });\n  }\n\n  async next(): Promise<IteratorResult<Tensor3D>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let img;\n    try {\n      img = browser.fromPixels(this.webcamVideoElement);\n    } catch (e) {\n      throw new Error(\n          `Error thrown converting video to pixels: ${JSON.stringify(e)}`);\n    }\n    if (this.resize) {\n      try {\n        return {value: this.cropAndResizeFrame(img), done: false};\n      } catch (e) {\n        throw new Error(`Error thrown cropping the video: ${e.message}`);\n      } finally {\n        img.dispose();\n      }\n    } else {\n      return {value: img, done: false};\n    }\n  }\n\n  private needToResize() {\n    // If resizeWidth and resizeHeight are provided, and different from the\n    // width and height of original HTMLVideoElement, then resizing and cropping\n    // is required.\n    if (this.webcamConfig.resizeWidth && this.webcamConfig.resizeHeight &&\n        (this.webcamVideoElement.width !== this.webcamConfig.resizeWidth ||\n         this.webcamVideoElement.height !== this.webcamConfig.resizeHeight)) {\n      return true;\n    }\n    return false;\n  }\n\n  // Cropping and resizing each frame based on config\n  cropAndResizeFrame(img: Tensor3D): Tensor3D {\n    return tidy(() => {\n      const expandedImage: Tensor4D = expandDims(cast(img, 'float32'), (0));\n      let resizedImage;\n      resizedImage = image.cropAndResize(\n          expandedImage, this.cropBox, this.cropBoxInd, this.cropSize,\n          'bilinear');\n      // Extract image from batch cropping.\n      const shape = resizedImage.shape;\n      return reshape(resizedImage, shape.slice(1) as [number, number, number]);\n    });\n  }\n\n  // Capture one frame from the video stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<Tensor3D> {\n    return (await this.next()).value;\n  }\n\n  // Stop the video stream and pause webcam iterator.\n  stop(): void {\n    const tracks = this.stream.getTracks();\n\n    tracks.forEach(track => track.stop());\n\n    try {\n      this.webcamVideoElement.srcObject = null;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = null;\n    }\n    this.isClosed = true;\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor3D[]> {\n    throw new Error('Can not convert infinite video stream to array.');\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {ByteChunkIterator} from './iterators/byte_chunk_iterator';\n\n/**\n * Represents a data source readable as a stream of binary data chunks.\n *\n * Because `Dataset`s can be read repeatedly (via `Dataset.iterator()`), this\n * provides a means to repeatedly create streams from the underlying data\n * sources.\n */\nexport abstract class DataSource {\n  /**\n   * Obtain a new stream of binary data chunks.\n   *\n   * Starts the new stream from the beginning of the data source, even if other\n   * streams have been obtained previously.\n   */\n  abstract iterator(): Promise<ByteChunkIterator>;\n\n  // TODO(soergel): consider chainable Dataset construction here\n}\n\n// TODO(soergel): consider convenience factory functions here\n// in combination with chainable source->dataset above, e.g.:\n// tf.data.url(...).asCsvDataset().shuffle().batch()\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\n\nexport abstract class StringIterator extends LazyIterator<string> {\n  /**\n   * Splits a string stream on a given separator.\n   *\n   * It is assumed that the incoming chunk boundaries have no semantic meaning,\n   * so conceptually the incoming stream is treated simply as the concatenation\n   * of its elements.\n   *\n   * The outgoing stream provides chunks corresponding to the results of the\n   * standard string split() operation (even if such a chunk spanned incoming\n   * chunks).  The separators are not included.\n   *\n   * A typical usage is to split a text file (represented as a stream with\n   * arbitrary chunk boundaries) into lines.\n   *\n   * @param upstream A readable stream of strings that can be treated as\n   *   concatenated.\n   * @param separator A character to split on.\n   */\n  split(separator: string): StringIterator {\n    return new SplitIterator(this, separator);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on StringIterator.  Unfortunately they can't be placed in separate files, due\n// to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class SplitIterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass SplitIterator extends StringIterator {\n  private impl: SplitIteratorImpl;\n\n  constructor(protected upstream: LazyIterator<string>, separator: string) {\n    super();\n    this.impl = new SplitIteratorImpl(upstream, separator);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\nclass SplitIteratorImpl extends OneToManyIterator<string> {\n  // A partial string at the end of an upstream chunk\n  carryover = '';\n\n  constructor(\n      protected upstream: LazyIterator<string>, protected separator: string) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Split('${this.separator}')`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    if (chunkResult.done) {\n      if (this.carryover === '') {\n        return false;\n      }\n\n      // Pretend that the pump succeeded in order to emit the small last batch.\n      // The next pump() call will actually fail.\n      this.outputQueue.push(this.carryover);\n      this.carryover = '';\n      return true;\n    }\n    const lines = chunkResult.value.split(this.separator) as string[];\n    // Note the behavior: \" ab \".split(' ') === ['', 'ab', '']\n    // Thus the carryover may be '' if the separator falls on a chunk\n    // boundary; this produces the correct result.\n\n    lines[0] = this.carryover + lines[0];\n    for (const line of lines.slice(0, -1)) {\n      this.outputQueue.push(line);\n    }\n    this.carryover = lines[lines.length - 1];\n\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\nimport {StringIterator} from './string_iterator';\n\nexport abstract class ByteChunkIterator extends LazyIterator<Uint8Array> {\n  /**\n   * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n   *\n   * The byte arrays producetd from the ByteChunkIterator on which this is\n   * called will be interpreted as concatenated.  No assumptions are made about\n   * the boundaries of the incoming chunks, so a multi-byte UTF8 encoding of a\n   * character may span the boundary between chunks.  This naturally happens,\n   * for instance, when reading fixed-size byte arrays from a file.\n   */\n  decodeUTF8(): StringIterator {\n    return new Utf8Iterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on ByteChunkIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class Utf8Iterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass Utf8Iterator extends StringIterator {\n  private impl: Utf8IteratorImpl;\n\n  constructor(protected upstream: LazyIterator<Uint8Array>) {\n    super();\n    this.impl = new Utf8IteratorImpl(upstream);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\n/**\n * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n *\n * This is tricky because the incoming byte array boundaries may disrupt a\n * multi-byte UTF8 character. Thus any incomplete character data at the end of\n * a chunk must be carried over and prepended to the next chunk before\n * decoding. Luckily with native decoder, TextDecoder in browser and\n * string_decoder in node, byte array boundaries are handled automatically.\n *\n * In the context of an input pipeline for machine learning, UTF8 decoding is\n * needed to parse text files containing training examples or prediction\n * requests (e.g., formatted as CSV or JSON). We cannot use the built-in\n * decoding provided by FileReader.readAsText() because here we are in a\n * streaming context, which FileReader does not support.\n *\n * @param upstream A `LazyIterator` of `Uint8Arrays` containing UTF8-encoded\n *   text, which should be interpreted as concatenated.  No assumptions are\n *   made about the boundaries of the incoming chunks, so a multi-byte UTF8\n *   encoding of a character may span the boundary between chunks.  This\n *   naturally happens, for instance, when reading fixed-size byte arrays from a\n *   file.\n */\nclass Utf8IteratorImpl extends OneToManyIterator<string> {\n  // `decoder` as `any` here to dynamically assign value based on the\n  // environment.\n  // tslint:disable-next-line:no-any\n  decoder: any;\n\n  constructor(protected readonly upstream: LazyIterator<Uint8Array>) {\n    super();\n    if (env().get('IS_BROWSER')) {\n      this.decoder = new TextDecoder('utf-8');\n    } else {\n      // tslint:disable-next-line:no-require-imports\n      const {StringDecoder} = require('string_decoder');\n      this.decoder = new StringDecoder('utf8');\n    }\n  }\n  summary() {\n    return `${this.upstream.summary()} -> Utf8`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    let chunk;\n    if (chunkResult.done) {\n      return false;\n    } else {\n      chunk = chunkResult.value;\n    }\n\n    let text: string;\n    if (env().get('IS_BROWSER')) {\n      text = this.decoder.decode(chunk, {stream: true});\n    } else {\n      text = this.decoder.write(Buffer.from(chunk.buffer));\n    }\n    this.outputQueue.push(text);\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// Skip tslint any type check cause this method is aiming to check type of\n// input.\n// tslint:disable-next-line:no-any\nexport function isLocalPath(source: any): boolean {\n  return (typeof source === 'string') && source.slice(0, 7) === 'file://';\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {urlChunkIterator} from '../iterators/url_chunk_iterator';\nimport {isLocalPath} from '../util/source_util';\nimport {FileDataSource} from './file_data_source';\n\n/*\n * Represents a URL readable as a stream of binary data chunks.\n */\nexport class URLDataSource extends DataSource {\n  /**\n   * Create a `URLDataSource`.\n   *\n   * @param url A source URL string, or a `Request` object.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected readonly url: RequestInfo,\n      protected readonly fileOptions: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  // TODO(soergel): provide appropriate caching options.  Currently this\n  // will download the URL anew for each call to iterator().  Since we have\n  // to treat the downloaded file as a blob/buffer anyway, we may as well retain\n  // it-- but that raises GC issues.  Also we may want a persistent disk cache.\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.url)) {\n      return (new FileDataSource(this.url as string, this.fileOptions))\n          .iterator();\n    } else {\n      return urlChunkIterator(this.url, this.fileOptions);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer} from '@tensorflow/tfjs-core';\nimport {Dataset, datasetFromIteratorFn} from './dataset';\nimport {CSVDataset} from './datasets/csv_dataset';\nimport {iteratorFromFunction} from './iterators/lazy_iterator';\nimport {MicrophoneIterator} from './iterators/microphone_iterator';\nimport {WebcamIterator} from './iterators/webcam_iterator';\nimport {URLDataSource} from './sources/url_data_source';\nimport {CSVConfig, MicrophoneConfig, WebcamConfig} from './types';\n\n/**\n * Create a `CSVDataset` by reading and decoding CSV file(s) from provided URL\n * or local path if it's in Node environment.\n *\n * Note: If isLabel in columnConfigs is `true` for at least one column, the\n * element in returned `CSVDataset` will be an object of\n * `{xs:features, ys:labels}`: xs is a dict of features key/value pairs, ys\n * is a dict of labels key/value pairs. If no column is marked as label,\n * returns a dict of features only.\n *\n * ```js\n * const csvUrl =\n * 'https://storage.googleapis.com/tfjs-examples/multivariate-linear-regression/data/boston-housing-train.csv';\n *\n * async function run() {\n *   // We want to predict the column \"medv\", which represents a median value of\n *   // a home (in $1000s), so we mark it as a label.\n *   const csvDataset = tf.data.csv(\n *     csvUrl, {\n *       columnConfigs: {\n *         medv: {\n *           isLabel: true\n *         }\n *       }\n *     });\n *\n *   // Number of features is the number of column names minus one for the label\n *   // column.\n *   const numOfFeatures = (await csvDataset.columnNames()).length - 1;\n *\n *   // Prepare the Dataset for training.\n *   const flattenedDataset =\n *     csvDataset\n *     .map(({xs, ys}) =>\n *       {\n *         // Convert xs(features) and ys(labels) from object form (keyed by\n *         // column name) to array form.\n *         return {xs:Object.values(xs), ys:Object.values(ys)};\n *       })\n *     .batch(10);\n *\n *   // Define the model.\n *   const model = tf.sequential();\n *   model.add(tf.layers.dense({\n *     inputShape: [numOfFeatures],\n *     units: 1\n *   }));\n *   model.compile({\n *     optimizer: tf.train.sgd(0.000001),\n *     loss: 'meanSquaredError'\n *   });\n *\n *   // Fit the model using the prepared Dataset\n *   return model.fitDataset(flattenedDataset, {\n *     epochs: 10,\n *     callbacks: {\n *       onEpochEnd: async (epoch, logs) => {\n *         console.log(epoch + ':' + logs.loss);\n *       }\n *     }\n *   });\n * }\n *\n * await run();\n * ```\n *\n * @param source URL or local path to get CSV file. If it's a local path, it\n * must have prefix `file://` and it only works in node environment.\n * @param csvConfig (Optional) A CSVConfig object that contains configurations\n *     of reading and decoding from CSV file(s).\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function csv(\n    source: RequestInfo, csvConfig: CSVConfig = {}): CSVDataset {\n  return new CSVDataset(new URLDataSource(source), csvConfig);\n}\n\n/**\n * Create a `Dataset` that produces each element by calling a provided function.\n *\n * Note that repeated iterations over this `Dataset` may produce different\n * results, because the function will be called anew for each element of each\n * iteration.\n *\n * Also, beware that the sequence of calls to this function may be out of order\n * in time with respect to the logical order of the Dataset. This is due to the\n * asynchronous lazy nature of stream processing, and depends on downstream\n * transformations (e.g. .shuffle()). If the provided function is pure, this is\n * no problem, but if it is a closure over a mutable state (e.g., a traversal\n * pointer), then the order of the produced elements may be scrambled.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const ds = tf.data.func(func);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param f A function that produces one data element on each call.\n */\nexport function func<T extends TensorContainer>(\n    f: () => IteratorResult<T>| Promise<IteratorResult<T>>): Dataset<T> {\n  const iter = iteratorFromFunction(f);\n  return datasetFromIteratorFn(async () => iter);\n}\n\n/**\n * Create a `Dataset` that produces each element from provided JavaScript\n * generator, which is a function that returns a (potentially async) iterator.\n *\n * For more information on iterators and generators, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Iterators_and_Generators .\n * For the iterator protocol, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols .\n *\n * Example of creating a dataset from an iterator factory:\n * ```js\n * function makeIterator() {\n *   const numElements = 10;\n *   let index = 0;\n *\n *   const iterator = {\n *     next: () => {\n *       let result;\n *       if (index < numElements) {\n *         result = {value: index, done: false};\n *         index++;\n *         return result;\n *       }\n *       return {value: index, done: true};\n *     }\n *   };\n *   return iterator;\n * }\n * const ds = tf.data.generator(makeIterator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * Example of creating a dataset from a generator:\n * ```js\n * function* dataGenerator() {\n *   const numElements = 10;\n *   let index = 0;\n *   while (index < numElements) {\n *     const x = index;\n *     index++;\n *     yield x;\n *   }\n * }\n *\n * const ds = tf.data.generator(dataGenerator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param generator A JavaScript function that returns\n *     a (potentially async) JavaScript iterator.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function generator<T extends TensorContainer>(\n  generator: () => Iterator<T> | Promise<Iterator<T>> | AsyncIterator<T>,\n): Dataset<T> {\n  return datasetFromIteratorFn(async () => {\n    const gen = await generator();\n    return iteratorFromFunction(() => gen.next());\n  });\n}\n\n/**\n * Create an iterator that generates `Tensor`s from webcam video stream. This\n * API only works in Browser environment when the device has webcam.\n *\n * Note: this code snippet only works when the device has a webcam. It will\n * request permission to open the webcam when running.\n * ```js\n * const videoElement = document.createElement('video');\n * videoElement.width = 100;\n * videoElement.height = 100;\n * const cam = await tf.data.webcam(videoElement);\n * const img = await cam.capture();\n * img.print();\n * cam.stop();\n * ```\n *\n * @param webcamVideoElement A `HTMLVideoElement` used to play video from\n *     webcam. If this element is not provided, a hidden `HTMLVideoElement` will\n *     be created. In that case, `resizeWidth` and `resizeHeight` must be\n *     provided to set the generated tensor shape.\n * @param webcamConfig A `WebcamConfig` object that contains configurations of\n *     reading and manipulating data from webcam video stream.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function webcam(\n    webcamVideoElement?: HTMLVideoElement,\n    webcamConfig?: WebcamConfig): Promise<WebcamIterator> {\n  return WebcamIterator.create(webcamVideoElement, webcamConfig);\n}\n\n/**\n * Create an iterator that generates frequency-domain spectrogram `Tensor`s from\n * microphone audio stream with browser's native FFT. This API only works in\n * browser environment when the device has microphone.\n *\n * Note: this code snippet only works when the device has a microphone. It will\n * request permission to open the microphone when running.\n * ```js\n * const mic = await tf.data.microphone({\n *   fftSize: 1024,\n *   columnTruncateLength: 232,\n *   numFramesPerSpectrogram: 43,\n *   sampleRateHz:44100,\n *   includeSpectrogram: true,\n *   includeWaveform: true\n * });\n * const audioData = await mic.capture();\n * const spectrogramTensor = audioData.spectrogram;\n * spectrogramTensor.print();\n * const waveformTensor = audioData.waveform;\n * waveformTensor.print();\n * mic.stop();\n * ```\n *\n * @param microphoneConfig A `MicrophoneConfig` object that contains\n *     configurations of reading audio data from microphone.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function microphone(microphoneConfig?: MicrophoneConfig):\n    Promise<MicrophoneIterator> {\n  return MicrophoneIterator.create(microphoneConfig);\n}\n", "/** @license See the LICENSE file. */\n\n// This code is auto-generated, do not modify this file!\nconst version = '4.22.0';\nexport {version};\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "this", "constructor", "create", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "pop", "length", "push", "__values", "o", "s", "m", "i", "__read", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "l", "slice", "concat", "global", "module", "define", "Alea", "seed", "me", "mash", "data", "h", "charCodeAt", "s0", "c", "s1", "s2", "copy", "impl", "opts", "xg", "state", "prng", "int32", "double", "quick", "exports", "amd", "alea", "XorGen", "strseed", "x", "z", "w", "k", "xor128", "xorwow", "X", "j", "init", "Date", "xorshift7", "limit", "Math", "max", "xor4096", "a", "floor", "tychei", "pool", "math", "nodecrypto", "width", "startdenom", "pow", "significance", "overflow", "mask", "seedrandom", "options", "callback", "key", "shortseed", "mixkey", "flatten", "entropy", "tostring", "out", "randomBytes", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "browser", "navigator", "plugins", "screen", "autoseed", "arc4", "ARC4", "S", "pass", "is_math_call", "keylen", "count", "obj", "depth", "prop", "typ", "smear", "stringseed", "fromCharCode", "random", "require$$0", "ex", "self", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "sr", "deepMapInternal", "input", "mapFn", "seen", "containedIn", "Map", "Set", "Blob", "has", "Error", "get", "recurse", "isIterable", "mappedIterable", "isArray", "add", "childResult", "delete", "set", "deepZip", "inputs", "zipFn", "zipToList", "deepZipInternal", "map", "deepMapAndAwaitAll", "_a", "keys", "_b", "tf", "util", "isPromise", "mappedValue", "_d", "isTextDecoder", "env", "TextDecoder", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tensor", "deepClone", "container", "cloneIfTensor", "item", "clone", "<PERSON><PERSON><PERSON><PERSON>", "capacity", "begin", "end", "RangeError", "doubledCapacity", "wrap", "index", "isFull", "isEmpty", "pushAll", "values", "values_1", "values_1_1", "undefined", "unshift", "shift", "shuffleExcise", "relativeIndex", "GrowingRingBuffer", "_super", "INITIAL_CAPACITY", "expand", "newCapacity", "newData", "len", "iteratorFromItems", "items", "ArrayIterator", "iteratorFromFunction", "func", "FunctionCallIterator", "ZipMismatchMode", "LazyIterator", "toArray", "toArrayForTest", "stream", "prefetch", "resolveFully", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "shouldC<PERSON><PERSON>ue", "handleErrors", "handler", "ErrorHandlingLazyIterator", "filter", "FilterIterator", "transform", "MapIterator", "mapAsync", "AsyncMapIterator", "serialMapAsync", "serial", "flatmap", "FlatmapIterator", "forEachAsync", "serialForEach", "rowMajorBatch", "batchSize", "smallLastBatch", "RowMajorBatchIterator", "columnMajorBatch", "concatenate", "baseErrorHandler", "ChainedIterator", "take", "TakeIterator", "skip", "SkipIterator", "bufferSize", "PrefetchIterator", "shuffle", "windowSize", "ShuffleIterator", "SerialIterator", "_this", "trav", "summary", "nextFn", "message", "upstream", "lastRead", "serialNext", "maxCount", "skipped", "dispose", "enableSmallLastBatch", "batch", "inputTensors", "tensor_util", "getTensorsInContainer", "mapped", "outputTensors", "inputTensors_1", "inputTensors_1_1", "isTensorInList", "e_2", "inputTensors_2", "inputTensors_2_1", "OneToManyIterator", "outputQueue", "pump", "mappedArray", "inputTensors_3", "inputTensors_3_1", "iterators", "moreIterators", "read<PERSON><PERSON><PERSON><PERSON><PERSON>", "iteratorResult", "itemResult", "ZipIterator", "mismatchMode", "FAIL", "currentPromise", "nextState", "afterState", "getNext", "numIterators", "iteratorsDone", "SHORTEST", "LONGEST", "buffer", "refill", "upstreamExhausted", "seedrandom.alea", "now", "toString", "randomInt", "chooseIndex", "chosenIndex", "Dataset", "size", "base", "assert", "datasetFromIteratorFn", "deepBatchConcat", "Infinity", "ceil", "dataset", "_c", "tidy", "repeat", "iteratorIterator", "baseIterators", "reshuffleEachIteration", "seed2", "iteratorFn", "class_1", "rows", "isTypedArray", "canTensorify", "arrays", "stack", "tensor", "batchConcat", "MAX_BUFFER_SIZE", "TextLineDataset", "inputIterator", "utf8Iterator", "decodeUTF8", "split", "line", "endsWith", "CODE_QUOTE", "STATE_OUT", "STATE_FIELD", "STATE_QUOTE", "STATE_QUOTE_AFTER_QUOTE", "STATE_WITHIN_QUOTE_IN_QUOTE", "CSVDataset", "csvConfig", "<PERSON><PERSON><PERSON><PERSON>", "fullColumnNames", "columnNamesValidated", "columnConfigs", "configuredColumnsOnly", "delimiter", "delimWhitespace", "columnNames", "setColumnNames", "maybeReadHeaderLine", "columnNamesFromFile", "counts", "reduce", "countAcc", "name", "duplicateNames", "indexOf", "firstElement", "firstLine", "parseRow", "lines", "makeDataElement", "features", "labels", "config", "parsedValue", "default", "required", "isLabel", "valueAsNum", "Number", "isNaN", "dtype", "getBoolean", "xs", "ys", "toLowerCase", "validateElementCount", "readOffset", "readLength", "currentState", "char<PERSON>t", "substring", "MicrophoneIterator", "microphoneConfig", "isClosed", "fftSize", "fftSizeLog2", "log2", "isInteger", "numFrames", "numFramesPerSpectrogram", "sampleRateHz", "columnTruncateLength", "audioTrackConstraints", "smoothingTimeConstant", "includeSpectrogram", "includeWaveform", "microphoneIterator", "start", "mediaDevices", "getUserMedia", "audio", "video", "e_1", "ctxConstructor", "window", "AudioContext", "webkitAudioContext", "audioContext", "sampleRate", "streamSource", "createMediaStreamSource", "analyser", "create<PERSON><PERSON>yser", "connect", "freqData", "Float32Array", "timeData", "getAudioData", "audioDataQueue", "flattenQueue", "freqDataQueue", "spectrogramTensor", "getTensorFromAudioDataArray", "timeDataQueue", "waveformTensor", "spectrogram", "waveform", "capture", "currentFrames", "intervalID", "setInterval", "getFloatFrequencyData", "getFloatTimeDomainData", "clearInterval", "stop", "disconnect", "close", "getTracks", "getSampleRate", "queue", "frameSize", "for<PERSON>ach", "shape", "vals", "sizeFromShape", "WebcamIterator", "webcamVideoElement", "webcamConfig", "resize", "needToResize", "cropSize", "resizeHeight", "resizeWidth", "cropBoxInd", "tensor1d", "centerCrop", "widthCroppingRatio", "heightCroppingRatio", "height", "widthCropStart", "heightCropStart", "widthCropEnd", "heightCropEnd", "cropBox", "tensor2d", "document", "createElement", "webcamIterator", "facingMode", "deviceId", "srcObject", "console", "log", "src", "URL", "createObjectURL", "play", "onloadedmetadata", "img", "fromPixels", "JSON", "stringify", "cropAndResizeFrame", "resizedImage", "expandedImage", "expandDims", "cast", "image", "cropAndResize", "reshape", "track", "DataSource", "StringIterator", "separator", "SplitIterator", "SplitIteratorImpl", "carryover", "chunkResult", "ByteChunkIterator", "Utf8Iterator", "Utf8IteratorImpl", "decoder", "StringDecoder", "chunk", "text", "decode", "write", "<PERSON><PERSON><PERSON>", "FileChunkIterator", "file", "File", "offset", "chunkSize", "byteLength", "fileReader_1", "FileReader", "onload", "event", "<PERSON>ab<PERSON>", "onerror", "type", "readAsA<PERSON>y<PERSON><PERSON>er", "urlChunkIterator", "url", "fetchFunc", "urlString", "requestInit", "getRequestInitFromRequest", "fetch", "response", "ok", "bind", "arrayBuffer", "uint8Array", "statusText", "request", "method", "headers", "mode", "credentials", "cache", "redirect", "referrer", "integrity", "isLocalPath", "source", "FileDataSource", "fs", "readFileSync", "URLDataSource", "fileOptions", "iter", "gen", "datasets", "min", "ds", "streams"], "mappings": ";;;;;;;;;;;;;;;;+iBAgBIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,CAAE,GACzE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOC,OAAOK,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,GAAG,EAC5FP,EAAcC,EAAGC,EAC5B,EAEgB,SAAAS,EAAUV,EAAGC,GACzB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIU,UAAU,uBAAyBC,OAAOX,GAAK,iCAE7D,SAASY,IAAOC,KAAKC,YAAcf,CAAI,CADvCD,EAAcC,EAAGC,GAEjBD,EAAEO,UAAkB,OAANN,EAAaC,OAAOc,OAAOf,IAAMY,EAAGN,UAAYN,EAAEM,UAAW,IAAIM,EACnF,CAwCM,SAAUI,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,GAAmC,CAAxB,MAAOG,GAAKL,EAAOK,EAAK,CAAE,CAC3F,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,GAAmC,CAAxB,MAAOG,GAAKL,EAAOK,EAAK,CAAE,CAC9F,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,QAJ1CA,EAIyDK,EAAOL,MAJhDA,aAAiBN,EAAIM,EAAQ,IAAIN,GAAE,SAAUG,GAAWA,EAAQG,EAAO,KAIhBO,KAAKR,EAAWK,EAAY,CAC9GH,GAAMN,EAAYA,EAAUa,MAAMhB,EAASC,GAAc,KAAKS,OAClE,GACJ,CAEgB,SAAAO,EAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAK,EAAEK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEZ,KAAMkB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOpC,IAAO,GAAG0B,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIhB,EAAG,MAAM,IAAI1B,UAAU,mCAC3B,KAAO8B,OACH,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARc,EAAG,GAASf,EAAU,OAAIe,EAAG,GAAKf,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE9B,KAAK6B,GAAI,GAAKA,EAAEV,SAAWW,EAAIA,EAAE9B,KAAK6B,EAAGe,EAAG,KAAKrB,KAAM,OAAOO,EAE3J,OADID,EAAI,EAAGC,IAAGc,EAAK,CAAS,EAARA,EAAG,GAAQd,EAAEb,QACzB2B,EAAG,IACP,KAAK,EAAG,KAAK,EAAGd,EAAIc,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEhB,MAAO2B,EAAG,GAAIrB,MAAM,GAChD,KAAK,EAAGS,EAAEC,QAASJ,EAAIe,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIS,MAAOb,EAAEG,KAAKU,MAAO,SACxC,QACI,KAAMf,EAAIE,EAAEG,MAAML,EAAIA,EAAEgB,OAAS,GAAKhB,EAAEA,EAAEgB,OAAS,KAAkB,IAAVF,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVY,EAAG,MAAcd,GAAMc,EAAG,GAAKd,EAAE,IAAMc,EAAG,GAAKd,EAAE,IAAM,CAAEE,EAAEC,MAAQW,EAAG,GAAI,KAAQ,CACtF,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIc,EAAI,KAAQ,CACrE,GAAId,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIW,KAAKH,GAAK,KAAQ,CAC/Dd,EAAE,IAAIE,EAAEI,IAAIS,MAChBb,EAAEG,KAAKU,MAAO,SAEtBD,EAAKjB,EAAK3B,KAAKS,EAASuB,GAC1B,MAAOZ,GAAKwB,EAAK,CAAC,EAAGxB,GAAIS,EAAI,CAAI,CAAS,QAAED,EAAIE,EAAI,CAAI,CAC1D,GAAY,EAARc,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3B,MAAO2B,EAAG,GAAKA,EAAG,QAAK,EAAQrB,MAAM,EAC7E,CAtB+CL,CAAK,CAACwB,EAAGC,GAAM,CAAG,CAuBtE,CAkBM,SAAUK,EAASC,GACrB,IAAIC,EAAsB,mBAAXV,QAAyBA,OAAOC,SAAUU,EAAID,GAAKD,EAAEC,GAAIE,EAAI,EAC5E,GAAID,EAAG,OAAOA,EAAEnD,KAAKiD,GACrB,GAAIA,GAAyB,iBAAbA,EAAEH,OAAqB,MAAO,CAC1C3B,KAAM,WAEF,OADI8B,GAAKG,GAAKH,EAAEH,SAAQG,OAAI,GACrB,CAAEhC,MAAOgC,GAAKA,EAAEG,KAAM7B,MAAO0B,EACvC,GAEL,MAAM,IAAI/C,UAAUgD,EAAI,0BAA4B,kCACxD,CAEgB,SAAAG,EAAOJ,EAAGP,GACtB,IAAIS,EAAsB,mBAAXX,QAAyBS,EAAET,OAAOC,UACjD,IAAKU,EAAG,OAAOF,EACf,IAAmBK,EAAYlC,EAA3BgC,EAAID,EAAEnD,KAAKiD,GAAOM,EAAK,GAC3B,IACI,WAAc,IAANb,GAAgBA,KAAM,MAAQY,EAAIF,EAAEjC,QAAQI,MAAMgC,EAAGR,KAAKO,EAAErC,MAQvE,CAND,MAAOuC,GAASpC,EAAI,CAAEoC,MAAOA,EAAU,CAC/B,QACJ,IACQF,IAAMA,EAAE/B,OAAS4B,EAAIC,EAAU,SAAID,EAAEnD,KAAKoD,EAEjB,CAAzB,QAAE,GAAIhC,EAAG,MAAMA,EAAEoC,KAAQ,CACpC,CACD,OAAOD,CACX,UAkBgBE,EAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBC,UAAUf,OAAc,IAAK,IAA4BS,EAAxBH,EAAI,EAAGU,EAAIH,EAAKb,OAAYM,EAAIU,EAAGV,KACxEG,GAAQH,KAAKO,IACRJ,IAAIA,EAAK3D,MAAME,UAAUiE,MAAM/D,KAAK2D,EAAM,EAAGP,IAClDG,EAAGH,GAAKO,EAAKP,IAGrB,OAAOM,EAAGM,OAAOT,GAAM3D,MAAME,UAAUiE,MAAM/D,KAAK2D,GACtD,gqBCrJA,SAAUM,EAAQC,EAAQC,GAE1B,SAASC,EAAKC,GACZ,IAgDI3B,EAhDA4B,EAAKjE,KAAMkE,GAgDX7B,EAAI,WAEG,SAAS8B,GAClBA,EAAOrE,OAAOqE,GACd,IAAK,IAAIpB,EAAI,EAAGA,EAAIoB,EAAK1B,OAAQM,IAAK,CAEpC,IAAIqB,EAAI,oBADR/B,GAAK8B,EAAKE,WAAWtB,IAGrBqB,GADA/B,EAAI+B,IAAM,EAGV/B,GADA+B,GAAK/B,KACK,EAEVA,GAAS,YADT+B,GAAK/B,EAEN,CACD,OAAmB,wBAAXA,IAAM,EAClB,GA7DE4B,EAAGnD,KAAO,WACR,IAAIW,EAAI,QAAUwC,EAAGK,GAAY,uBAAPL,EAAGM,EAG7B,OAFAN,EAAGK,GAAKL,EAAGO,GACXP,EAAGO,GAAKP,EAAGQ,GACJR,EAAGQ,GAAKhD,GAAKwC,EAAGM,EAAQ,EAAJ9C,EAC/B,EAGEwC,EAAGM,EAAI,EACPN,EAAGK,GAAKJ,EAAK,KACbD,EAAGO,GAAKN,EAAK,KACbD,EAAGQ,GAAKP,EAAK,KACbD,EAAGK,IAAMJ,EAAKF,GACVC,EAAGK,GAAK,IAAKL,EAAGK,IAAM,GAC1BL,EAAGO,IAAMN,EAAKF,GACVC,EAAGO,GAAK,IAAKP,EAAGO,IAAM,GAC1BP,EAAGQ,IAAMP,EAAKF,GACVC,EAAGQ,GAAK,IAAKR,EAAGQ,IAAM,GAC1BP,EAAO,IACR,CAED,SAASQ,EAAKnD,EAAGE,GAKf,OAJAA,EAAE8C,EAAIhD,EAAEgD,EACR9C,EAAE6C,GAAK/C,EAAE+C,GACT7C,EAAE+C,GAAKjD,EAAEiD,GACT/C,EAAEgD,GAAKlD,EAAEkD,GACFhD,CACR,CAED,SAASkD,EAAKX,EAAMY,GAClB,IAAIC,EAAK,IAAId,EAAKC,GACdc,EAAQF,GAAQA,EAAKE,MACrBC,EAAOF,EAAG/D,KAUd,OATAiE,EAAKC,MAAQ,WAAa,OAAoB,WAAZH,EAAG/D,OAAwB,GAC7DiE,EAAKE,OAAS,WACZ,OAAOF,IAAmC,uBAAhB,QAATA,IAAoB,EACzC,EACEA,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BJ,EAAKI,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAwBGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAKqF,KAAOV,CAGb,CAhFD,CAiFE3E,EAC+B6D,GAC/B,oDC3GF,SAAUD,EAAQC,EAAQC,GAE1B,SAASwB,EAAOtB,GACd,IAAIC,EAAKjE,KAAMuF,EAAU,GAEzBtB,EAAGuB,EAAI,EACPvB,EAAGzC,EAAI,EACPyC,EAAGwB,EAAI,EACPxB,EAAGyB,EAAI,EAGPzB,EAAGnD,KAAO,WACR,IAAIW,EAAIwC,EAAGuB,EAAKvB,EAAGuB,GAAK,GAIxB,OAHAvB,EAAGuB,EAAIvB,EAAGzC,EACVyC,EAAGzC,EAAIyC,EAAGwB,EACVxB,EAAGwB,EAAIxB,EAAGyB,EACHzB,EAAGyB,GAAMzB,EAAGyB,IAAM,GAAMjE,EAAKA,IAAM,CAC9C,EAEMuC,KAAiB,EAAPA,GAEZC,EAAGuB,EAAIxB,EAGPuB,GAAWvB,EAIb,IAAK,IAAI2B,EAAI,EAAGA,EAAIJ,EAAQ9C,OAAS,GAAIkD,IACvC1B,EAAGuB,GAA6B,EAAxBD,EAAQlB,WAAWsB,GAC3B1B,EAAGnD,MAEN,CAED,SAAS4D,EAAKnD,EAAGE,GAKf,OAJAA,EAAE+D,EAAIjE,EAAEiE,EACR/D,EAAED,EAAID,EAAEC,EACRC,EAAEgE,EAAIlE,EAAEkE,EACRhE,EAAEiE,EAAInE,EAAEmE,EACDjE,CACR,CAED,SAASkD,EAAKX,EAAMY,GAClB,IAAIC,EAAK,IAAIS,EAAOtB,GAChBc,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAG/D,SAAW,GAAK,YAenD,OAdAiE,EAAKE,OAAS,WACZ,GACE,IAEIhE,IAFM4D,EAAG/D,SAAW,KACb+D,EAAG/D,SAAW,GAAK,aACF,GAAK,UACf,IAAXG,GACT,OAAOA,CACX,EACE8D,EAAKC,MAAQH,EAAG/D,KAChBiE,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BJ,EAAKI,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAEGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAK4F,OAASjB,CAGf,CAvED,CAwEE3E,EAC+B6D,GAC/B,oDC1EF,SAAUD,EAAQC,EAAQC,GAE1B,SAASwB,EAAOtB,GACd,IAAIC,EAAKjE,KAAMuF,EAAU,GAGzBtB,EAAGnD,KAAO,WACR,IAAIW,EAAKwC,EAAGuB,EAAKvB,EAAGuB,IAAM,EAE1B,OADAvB,EAAGuB,EAAIvB,EAAGzC,EAAGyC,EAAGzC,EAAIyC,EAAGwB,EAAGxB,EAAGwB,EAAIxB,EAAGyB,EAAGzB,EAAGyB,EAAIzB,EAAG3B,GACzC2B,EAAG/E,EAAK+E,EAAG/E,EAAI,OAAS,IAC5B+E,EAAG3B,EAAK2B,EAAG3B,EAAK2B,EAAG3B,GAAK,EAAOb,EAAKA,GAAK,GAAO,CACxD,EAEEwC,EAAGuB,EAAI,EACPvB,EAAGzC,EAAI,EACPyC,EAAGwB,EAAI,EACPxB,EAAGyB,EAAI,EACPzB,EAAG3B,EAAI,EAEH0B,KAAiB,EAAPA,GAEZC,EAAGuB,EAAIxB,EAGPuB,GAAWvB,EAIb,IAAK,IAAI2B,EAAI,EAAGA,EAAIJ,EAAQ9C,OAAS,GAAIkD,IACvC1B,EAAGuB,GAA6B,EAAxBD,EAAQlB,WAAWsB,GACvBA,GAAKJ,EAAQ9C,SACfwB,EAAG/E,EAAI+E,EAAGuB,GAAK,GAAKvB,EAAGuB,IAAM,GAE/BvB,EAAGnD,MAEN,CAED,SAAS4D,EAAKnD,EAAGE,GAOf,OANAA,EAAE+D,EAAIjE,EAAEiE,EACR/D,EAAED,EAAID,EAAEC,EACRC,EAAEgE,EAAIlE,EAAEkE,EACRhE,EAAEiE,EAAInE,EAAEmE,EACRjE,EAAEa,EAAIf,EAAEe,EACRb,EAAEvC,EAAIqC,EAAErC,EACDuC,CACR,CAED,SAASkD,EAAKX,EAAMY,GAClB,IAAIC,EAAK,IAAIS,EAAOtB,GAChBc,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAG/D,SAAW,GAAK,YAenD,OAdAiE,EAAKE,OAAS,WACZ,GACE,IAEIhE,IAFM4D,EAAG/D,SAAW,KACb+D,EAAG/D,SAAW,GAAK,aACF,GAAK,UACf,IAAXG,GACT,OAAOA,CACX,EACE8D,EAAKC,MAAQH,EAAG/D,KAChBiE,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BJ,EAAKI,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAEGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAK6F,OAASlB,CAGf,CA5ED,CA6EE3E,EAC+B6D,GAC/B,oDC7EF,SAAUD,EAAQC,EAAQC,GAE1B,SAASwB,EAAOtB,GACd,IAAIC,EAAKjE,KAGTiE,EAAGnD,KAAO,WAER,IAAwBW,EAAGa,EAAvBwD,EAAI7B,EAAGuB,EAAGzC,EAAIkB,EAAGlB,EAQrB,OAPAtB,EAAIqE,EAAE/C,GAAoBT,GAAhBb,GAAMA,IAAM,GAAaA,GAAK,GACpBa,IAApBb,EAAIqE,EAAG/C,EAAI,EAAK,IAActB,IAAM,GAChBa,IAApBb,EAAIqE,EAAG/C,EAAI,EAAK,IAActB,IAAM,EAChBa,IAApBb,EAAIqE,EAAG/C,EAAI,EAAK,IAActB,GAAK,EACnCA,EAAIqE,EAAG/C,EAAI,EAAK,GAAuBT,IAAnBb,GAASA,GAAK,IAAeA,GAAK,EACtDqE,EAAE/C,GAAKT,EACP2B,EAAGlB,EAAKA,EAAI,EAAK,EACVT,CACX,EAEE,SAAc2B,EAAID,GAChB,IAAI+B,EAAMD,EAAI,GAEd,GAAI9B,KAAiB,EAAPA,GAER8B,EAAE,GAAK9B,OAIX,IADAA,EAAO,GAAKA,EACP+B,EAAI,EAAGA,EAAI/B,EAAKvB,SAAUsD,EAC7BD,EAAM,EAAJC,GAAUD,EAAM,EAAJC,IAAU,GACnB/B,EAAKK,WAAW0B,GAAKD,EAAGC,EAAI,EAAK,IAAM,GAIhD,KAAOD,EAAErD,OAAS,GAAGqD,EAAEpD,KAAK,GAC5B,IAAKqD,EAAI,EAAGA,EAAI,GAAc,IAATD,EAAEC,KAAYA,GAOnC,IANS,GAALA,EAAYD,EAAE,IAAM,EAAYA,EAAEC,GAEtC9B,EAAGuB,EAAIM,EACP7B,EAAGlB,EAAI,EAGFgD,EAAI,IAAKA,EAAI,IAAKA,EACrB9B,EAAGnD,MAEN,CAEDkF,CAAK/B,EAAID,EACV,CAED,SAASU,EAAKnD,EAAGE,GAGf,OAFAA,EAAE+D,EAAIjE,EAAEiE,EAAE9B,QACVjC,EAAEsB,EAAIxB,EAAEwB,EACDtB,CACR,CAED,SAASkD,EAAKX,EAAMY,GACN,MAARZ,IAAcA,GAAO,IAAMiC,MAC/B,IAAIpB,EAAK,IAAIS,EAAOtB,GAChBc,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAG/D,SAAW,GAAK,YAenD,OAdAiE,EAAKE,OAAS,WACZ,GACE,IAEIhE,IAFM4D,EAAG/D,SAAW,KACb+D,EAAG/D,SAAW,GAAK,aACF,GAAK,UACf,IAAXG,GACT,OAAOA,CACX,EACE8D,EAAKC,MAAQH,EAAG/D,KAChBiE,EAAKG,MAAQH,EACTD,IACEA,EAAMU,GAAGd,EAAKI,EAAOD,GACzBE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAEGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAKkG,UAAYvB,CAGlB,CAtFD,CAuFE3E,EAC+B6D,GAC/B,oDCrEF,SAAUD,EAAQC,EAAQC,GAE1B,SAASwB,EAAOtB,GACd,IAAIC,EAAKjE,KAGTiE,EAAGnD,KAAO,WACR,IACwBW,EAAGa,EADvBoD,EAAIzB,EAAGyB,EACPI,EAAI7B,EAAG6B,EAAG/C,EAAIkB,EAAGlB,EAcrB,OAZAkB,EAAGyB,EAAIA,EAAKA,EAAI,WAAc,EAE9BpD,EAAIwD,EAAG/C,EAAI,GAAM,KACjBtB,EAAIqE,EAAE/C,EAAMA,EAAI,EAAK,KACrBT,GAAKA,GAAK,GACVb,GAAKA,GAAK,GACVa,GAAKA,IAAM,GACXb,GAAKA,IAAM,GAEXa,EAAIwD,EAAE/C,GAAKT,EAAIb,EACfwC,EAAGlB,EAAIA,EAECT,GAAKoD,EAAKA,IAAM,IAAQ,CACpC,EAEE,SAAczB,EAAID,GAChB,IAAIvC,EAAGa,EAAGS,EAAGgD,EAAGL,EAAGI,EAAI,GAAIK,EAAQ,IAYnC,IAXInC,KAAiB,EAAPA,IAEZ1B,EAAI0B,EACJA,EAAO,OAGPA,GAAc,KACd1B,EAAI,EACJ6D,EAAQC,KAAKC,IAAIF,EAAOnC,EAAKvB,SAG1BM,EAAI,EAAGgD,GAAK,GAAIA,EAAII,IAASJ,EAE5B/B,IAAM1B,GAAK0B,EAAKK,YAAY0B,EAAI,IAAM/B,EAAKvB,SAErC,IAANsD,IAASL,EAAIpD,GACjBA,GAAKA,GAAK,GACVA,GAAKA,IAAM,GACXA,GAAKA,GAAK,EACVA,GAAKA,IAAM,GACPyD,GAAK,IACPL,EAAKA,EAAI,WAAc,EAEvB3C,EAAK,IADLtB,EAAKqE,EAAM,IAAJC,IAAazD,EAAIoD,GACT3C,EAAI,EAAI,GAW3B,IAPIA,GAAK,MACP+C,EAA+B,KAA5B9B,GAAQA,EAAKvB,QAAU,KAAa,GAKzCM,EAAI,IACCgD,EAAI,IAASA,EAAI,IAAKA,EACzBzD,EAAIwD,EAAG/C,EAAI,GAAM,KACjBtB,EAAIqE,EAAE/C,EAAMA,EAAI,EAAK,KACrBT,GAAKA,GAAK,GACVb,GAAKA,GAAK,GACVa,GAAKA,IAAM,GACXb,GAAKA,IAAM,GACXqE,EAAE/C,GAAKT,EAAIb,EAGbwC,EAAGyB,EAAIA,EACPzB,EAAG6B,EAAIA,EACP7B,EAAGlB,EAAIA,CACR,CAEDiD,CAAK/B,EAAID,EACV,CAED,SAASU,EAAKnD,EAAGE,GAIf,OAHAA,EAAEsB,EAAIxB,EAAEwB,EACRtB,EAAEiE,EAAInE,EAAEmE,EACRjE,EAAEqE,EAAIvE,EAAEuE,EAAEpC,QACHjC,CACR,CAED,SAASkD,EAAKX,EAAMY,GACN,MAARZ,IAAcA,GAAO,IAAMiC,MAC/B,IAAIpB,EAAK,IAAIS,EAAOtB,GAChBc,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAG/D,SAAW,GAAK,YAenD,OAdAiE,EAAKE,OAAS,WACZ,GACE,IAEIhE,IAFM4D,EAAG/D,SAAW,KACb+D,EAAG/D,SAAW,GAAK,aACF,GAAK,UACf,IAAXG,GACT,OAAOA,CACX,EACE8D,EAAKC,MAAQH,EAAG/D,KAChBiE,EAAKG,MAAQH,EACTD,IACEA,EAAMgB,GAAGpB,EAAKI,EAAOD,GACzBE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAEGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAKsG,QAAU3B,CAGhB,CApHD,CAqHE3E,EAC+B6D,GAC/B,oDC5IF,SAAUD,EAAQC,EAAQC,GAE1B,SAASwB,EAAOtB,GACd,IAAIC,EAAKjE,KAAMuF,EAAU,GAGzBtB,EAAGnD,KAAO,WACR,IAAI3B,EAAI8E,EAAG9E,EAAGoF,EAAIN,EAAGM,EAAGrF,EAAI+E,EAAG/E,EAAGqH,EAAItC,EAAGsC,EAQzC,OAPApH,EAAKA,GAAK,GAAOA,IAAM,EAAKoF,EAC5BA,EAAKA,EAAIrF,EAAK,EACdA,EAAKA,GAAK,GAAOA,IAAM,EAAKqH,EAC5BA,EAAKA,EAAIpH,EAAK,EACd8E,EAAG9E,EAAIA,EAAKA,GAAK,GAAOA,IAAM,GAAMoF,EACpCN,EAAGM,EAAIA,EAAKA,EAAIrF,EAAK,EACrB+E,EAAG/E,EAAKA,GAAK,GAAOqF,IAAM,GAAMgC,EACzBtC,EAAGsC,EAAKA,EAAIpH,EAAK,CAC5B,EAkBE8E,EAAGsC,EAAI,EACPtC,EAAG9E,EAAI,EACP8E,EAAGM,GAAI,WACPN,EAAG/E,EAAI,WAEH8E,IAASoC,KAAKI,MAAMxC,IAEtBC,EAAGsC,EAAKvC,EAAO,WAAe,EAC9BC,EAAG9E,EAAW,EAAP6E,GAGPuB,GAAWvB,EAIb,IAAK,IAAI2B,EAAI,EAAGA,EAAIJ,EAAQ9C,OAAS,GAAIkD,IACvC1B,EAAG9E,GAA6B,EAAxBoG,EAAQlB,WAAWsB,GAC3B1B,EAAGnD,MAEN,CAED,SAAS4D,EAAKnD,EAAGE,GAKf,OAJAA,EAAE8E,EAAIhF,EAAEgF,EACR9E,EAAEtC,EAAIoC,EAAEpC,EACRsC,EAAE8C,EAAIhD,EAAEgD,EACR9C,EAAEvC,EAAIqC,EAAErC,EACDuC,CACR,CAED,SAASkD,EAAKX,EAAMY,GAClB,IAAIC,EAAK,IAAIS,EAAOtB,GAChBc,EAAQF,GAAQA,EAAKE,MACrBC,EAAO,WAAa,OAAQF,EAAG/D,SAAW,GAAK,YAenD,OAdAiE,EAAKE,OAAS,WACZ,GACE,IAEIhE,IAFM4D,EAAG/D,SAAW,KACb+D,EAAG/D,SAAW,GAAK,aACF,GAAK,UACf,IAAXG,GACT,OAAOA,CACX,EACE8D,EAAKC,MAAQH,EAAG/D,KAChBiE,EAAKG,MAAQH,EACTD,IACmB,iBAAjB,GAA2BJ,EAAKI,EAAOD,GAC3CE,EAAKD,MAAQ,WAAa,OAAOJ,EAAKG,EAAI,CAAA,KAErCE,CACR,CAEGlB,GAAUA,EAAOsB,QACnBtB,EAAOsB,QAAUR,EACRb,GAAUA,EAAOsB,IAC1BtB,GAAO,WAAa,OAAOa,CAAO,IAElC3E,KAAKyG,OAAS9B,CAGf,CA5FD,CA6FE3E,EAC+B6D,GAC/B,qFC3EF,SAAWD,EAAQ8C,EAAMC,GAKzB,IAQIC,EARAC,EAAQ,IAIRC,EAAaH,EAAKI,IAAIF,EAHb,GAITG,EAAeL,EAAKI,IAAI,EAHf,IAITE,EAA0B,EAAfD,EACXE,EAAOL,IAOX,SAASM,EAAWnD,EAAMoD,EAASC,GACjC,IAAIC,EAAM,GAINC,EAAYC,EAAOC,GAHvBL,EAAsB,GAAXA,EAAmB,CAAEM,SAAS,GAAUN,GAAW,CAAA,GAIpDM,QAAU,CAAC1D,EAAM2D,EAASjB,IACzB,MAAR1C,EA8IL,WACE,IACE,IAAI4D,EAQJ,OAPIhB,IAAegB,EAAMhB,EAAWiB,aAElCD,EAAMA,EAAIf,IAEVe,EAAM,IAAIE,WAAWjB,IACpBjD,EAAOmE,QAAUnE,EAAOoE,UAAUC,gBAAgBL,IAE9CD,EAASC,EAKjB,CAJC,MAAO7G,GACP,IAAImH,EAAUtE,EAAOuE,UACjBC,EAAUF,GAAWA,EAAQE,QACjC,MAAO,EAAE,IAAInC,KAAMrC,EAAQwE,EAASxE,EAAOyE,OAAQV,EAASjB,GAC7D,CACF,CA9JoB4B,GAAatE,EAAM,GAAIsD,GAGtCiB,EAAO,IAAIC,EAAKlB,GAIhBvC,EAAO,WAIT,IAHA,IAAI1C,EAAIkG,EAAK7G,EA5BJ,GA6BLxC,EAAI4H,EACJtB,EAAI,EACDnD,EAAI2E,GACT3E,GAAKA,EAAImD,GAAKqB,EACd3H,GAAK2H,EACLrB,EAAI+C,EAAK7G,EAAE,GAEb,KAAOW,GAAK4E,GACV5E,GAAK,EACLnD,GAAK,EACLsG,KAAO,EAET,OAAQnD,EAAImD,GAAKtG,CACrB,EAUE,OARA6F,EAAKC,MAAQ,WAAa,OAAmB,EAAZuD,EAAK7G,EAAE,IACxCqD,EAAKG,MAAQ,WAAa,OAAOqD,EAAK7G,EAAE,GAAK,YAC7CqD,EAAKE,OAASF,EAGdyC,EAAOG,EAASY,EAAKE,GAAI/B,IAGjBU,EAAQsB,MAAQrB,GACpB,SAAStC,EAAMf,EAAM2E,EAAc7D,GAUjC,OATIA,IAEEA,EAAM2D,GAAK/D,EAAKI,EAAOyD,GAE3BxD,EAAKD,MAAQ,WAAa,OAAOJ,EAAK6D,EAAM,CAAA,KAK1CI,GAAgBhC,EAAY,OAAI5B,EAAaf,GAIrCe,CACb,GACLA,EACAwC,EACA,WAAYH,EAAUA,EAAQxD,OAAU5D,MAAQ2G,EAChDS,EAAQtC,MACT,CAYD,SAAS0D,EAAKlB,GACZ,IAAI7F,EAAGmH,EAAStB,EAAI7E,OAChBwB,EAAKjE,KAAM+C,EAAI,EAAGgD,EAAI9B,EAAGlB,EAAIkB,EAAG8B,EAAI,EAAGlD,EAAIoB,EAAGwE,EAAI,GAMtD,IAHKG,IAAUtB,EAAM,CAACsB,MAGf7F,EAAI8D,GACThE,EAAEE,GAAKA,IAET,IAAKA,EAAI,EAAGA,EAAI8D,EAAO9D,IACrBF,EAAEE,GAAKF,EAAEkD,EAAImB,EAAQnB,EAAIuB,EAAIvE,EAAI6F,IAAWnH,EAAIoB,EAAEE,KAClDF,EAAEkD,GAAKtE,GAIRwC,EAAGvC,EAAI,SAASmH,GAIf,IAFA,IAAIpH,EAAGwB,EAAI,EACPF,EAAIkB,EAAGlB,EAAGgD,EAAI9B,EAAG8B,EAAGlD,EAAIoB,EAAGwE,EACxBI,KACLpH,EAAIoB,EAAEE,EAAImE,EAAQnE,EAAI,GACtBE,EAAIA,EAAI4D,EAAQhE,EAAEqE,GAASrE,EAAEE,GAAKF,EAAEkD,EAAImB,EAAQnB,EAAItE,KAAQoB,EAAEkD,GAAKtE,IAGrE,OADAwC,EAAGlB,EAAIA,EAAGkB,EAAG8B,EAAIA,EACV9C,CAIR,GAAE4D,EACJ,CAMD,SAASnC,EAAKnD,EAAGE,GAIf,OAHAA,EAAEsB,EAAIxB,EAAEwB,EACRtB,EAAEsE,EAAIxE,EAAEwE,EACRtE,EAAEgH,EAAIlH,EAAEkH,EAAE/E,QACHjC,CACR,CAMD,SAASgG,EAAQqB,EAAKC,GACpB,IAAqCC,EAAjC/H,EAAS,GAAIgI,SAAcH,EAC/B,GAAIC,GAAgB,UAAPE,EACX,IAAKD,KAAQF,EACX,IAAM7H,EAAOyB,KAAK+E,EAAQqB,EAAIE,GAAOD,EAAQ,GAAmB,CAAZ,MAAOhI,GAAK,CAGpE,OAAQE,EAAOwB,OAASxB,EAAgB,UAAPgI,EAAkBH,EAAMA,EAAM,IAChE,CAOD,SAAStB,EAAOxD,EAAMsD,GAEpB,IADA,IAA4B4B,EAAxBC,EAAanF,EAAO,GAAW+B,EAAI,EAChCA,EAAIoD,EAAW1G,QACpB6E,EAAIJ,EAAOnB,GACTmB,GAASgC,GAAyB,GAAhB5B,EAAIJ,EAAOnB,IAAWoD,EAAW9E,WAAW0B,KAElE,OAAO4B,EAASL,EACjB,CA6BD,SAASK,EAASpB,GAChB,OAAOzG,OAAOsJ,aAAahI,MAAM,EAAGmF,EACrC,CAeD,GANAiB,EAAOb,EAAK0C,SAAU3C,GAMa7C,EAAOsB,QAAS,CACjDtB,EAAAsB,QAAiBgC,EAEjB,IACEP,EAAa0C,CACA,CAAb,MAAOC,GAAM,CAChB,MAIC5C,EAAqB,WAAIQ,CAK1B,CA9ND,CAiOmB,oBAATqC,KAAwBA,KAAOxJ,EACvC,GACAoG,cC/OEf,EAAOiE,EAKP1D,EAAS6D,EAKT5D,EAAS6D,EAQTxD,EAAYyD,EASZrD,EAAUsD,EAOVnD,EAASoD,EAITC,YAEJA,EAAGzE,KAAOA,EACVyE,EAAGlE,OAASA,EACZkE,EAAGjE,OAASA,EACZiE,EAAG5D,UAAYA,EACf4D,EAAGxD,QAAUA,EACbwD,EAAGrD,OAASA,EAEZ,IAAAU,EAAiB2C,ECEjB,SAASC,EACLC,EAAYC,EACZC,EAAiCC,GAEnC,QAFE,IAAAD,IAAAA,EAA0B,IAAAE,UAAO,IAAAD,IAAAA,EAA2B,IAAAE,KAEjD,MAATL,EACF,OAAO,KAET,GAAoB,mBAATM,MAAuBN,aAAiBM,KACjD,OAAON,EAAMtG,QAGf,GAAIyG,EAAYI,IAAIP,GAClB,MAAM,IAAIQ,MAAM,0CAElB,GAAIN,EAAKK,IAAIP,GACX,OAAOE,EAAKO,IAAIT,GAElB,IAAM/I,EAASgJ,EAAMD,GAErB,GAAI/I,EAAOyJ,SAA4B,OAAjBzJ,EAAOL,MAC3B,MAAM,IAAI4J,MACN,qEAGN,GAAKvJ,EAAOyJ,QAGL,IAAIC,EAAWX,GAAQ,CAE5B,IAAMY,EAA4BrL,MAAMsL,QAAQb,GAAS,GAAK,GAE9D,IAAK,IAAMrE,KADXwE,EAAYW,IAAId,GACAA,EAAO,CACrB,IACMe,EAAchB,EADNC,EAAMrE,GACuBsE,EAAOC,EAAMC,GACxDS,EAAejF,GAAKoF,CACrB,CAKD,OAJAZ,EAAYa,OAAOhB,GACfA,EAAM1K,YACRsL,EAAetL,UAAY0K,EAAM1K,WAE5BsL,CACR,CACC,MAAM,IAAIJ,MAAM,gDAAyCR,GAC1D,CAjBC,OADAE,EAAKe,IAAIjB,EAAO/I,EAAOL,OAChBK,EAAOL,KAkBlB,CA2BgB,SAAAsK,EACZC,EAAeC,GACjB,YADiB,IAAAA,IAAAA,EAA+CC,GACzDC,EAAgBH,EAAQC,EACjC,CAMA,SAASE,EACLH,EAAeC,EACfjB,QAAA,IAAAA,IAAAA,EAA2B,IAAAE,KAG7B,IAAML,EAAQmB,EAAO,GACrB,GAAIhB,EAAYI,IAAIP,GAClB,MAAM,IAAIQ,MAAM,0CAElB,IAAMvJ,EAASmK,EAAMD,GAErB,GAAIlK,EAAOyJ,SAA4B,OAAjBzJ,EAAOL,MAC3B,MAAM,IAAI4J,MACN,qEAGN,GAAKvJ,EAAOyJ,QAEL,IAAIC,EAAWX,GAAQ,CAE5B,IAAMY,EAA4BrL,MAAMsL,QAAQb,GAAS,GAAK,GAC9DG,EAAYW,IAAId,kBACLrE,GACT,IACMoF,EAAcO,EADHH,EAAOI,KAAI,SAAA/F,GAAK,OAAAA,EAAEG,EAAF,IACayF,EAAOjB,GACrDS,EAAejF,GAAKoF,GAHtB,IAAK,IAAMpF,KAAKqE,IAALrE,GAMX,OADAwE,EAAYa,OAAOhB,GACZY,CACR,CACC,MAAM,IAAIJ,MAAM,gDAAyCR,GAC1D,CAdC,OAAO/I,EAAOL,KAelB,CAGM,SAAUyK,EAAU7F,GACxB,OAAU,OAANA,EACK,KAILmF,EAAWnF,EAAE,IACR,CAAC5E,MAAO,KAAM8J,SAAS,GAEvB,CAAC9J,MAAO4E,EAAGkF,SAAS,EAE/B,CAmCsB,SAAAc,EAClBxB,EAAYC,mHACRC,EAAsB,IAAIE,IAGhCL,EAAgBC,EAAOC,EAAOC,2CAMZuB,EAAA9I,EAAApD,MAAM+D,KAAK4G,EAAKwB,SAAOC,EAAAF,EAAA3K,6CAA9BwG,EAAGqE,EAAA/K,MACNA,EAAQsJ,EAAKO,IAAInD,GACnBsE,EAAGC,KAAKC,UAAUlL,GACA,CAAA,EAAMA,GADA,CAAA,EAAA,WACpBmL,EAAcC,EAAWnK,OAC/BqI,EAAKe,IAAI3D,EAAKyE,qMAQlB,MAAA,CAAA,EADehC,EAAgBC,EAAOC,EAAOC,UAE9C,CAQK,SAAUS,EAAW7B,GACzB,IAAImD,GAAgB,EAChBL,EAAGM,MAAMzB,IAAI,cACfwB,EAAgBnD,aAAeqD,YAI/BF,EAAgBnD,aADQsD,QAAQ,gCAGlC,OAAc,MAAPtD,IAAiBuD,YAAYC,OAAOxD,KACtCvJ,MAAMsL,QAAQ/B,IACE,iBAARA,KAAsBA,aAAe8C,EAAGW,WAC7CzD,aAAetI,WAAayL,EACtC,CCtPM,SAAUO,EAAaC,GAC3B,OD8BO1C,EC9BQ0C,EAAWC,EAC5B,CAGA,SAASA,EAAcC,GACrB,OAAIA,aAAgBf,EAAGW,OACb,CAAC3L,MAAO+L,EAAKC,QAASlC,SAAS,GAC9BC,EAAWgC,GACb,CAAC/L,MAAO,KAAM8J,SAAS,GAEvB,CAAC9J,MAAO+L,EAAMjC,SAAS,EAElC,CCbA,IAAAmC,EAAA,WAcE,SAAAA,EAAmBC,GACjB,GADiB9M,KAAQ8M,SAARA,EAVT9M,KAAA+M,MAAQ,EACR/M,KAAAgN,IAAM,EAUE,MAAZF,EACF,MAAM,IAAIG,WAAW,mDAEvB,GAAIH,EAAW,EACb,MAAM,IAAIG,WAAW,6CAEvBjN,KAAKmE,KAAO,IAAI5E,MAASuN,GACzB9M,KAAKkN,gBAAkB,EAAIJ,CAC5B,CAmIF,OA9HWD,EAAIpN,UAAA0N,KAAJ,SAAKC,GAEb,KAAOA,EAAQ,GACbA,GAASpN,KAAKkN,gBAEhB,OAAOE,EAAQpN,KAAKkN,iBAGZL,EAAGpN,UAAAgL,IAAH,SAAI2C,GACZ,GAAIA,EAAQ,EACV,MAAM,IAAIH,WAAW,uCAEvB,OAAOjN,KAAKmE,KAAKiJ,EAAQpN,KAAK8M,WAGtBD,EAAApN,UAAAwL,IAAA,SAAImC,EAAexM,GAC3B,GAAIwM,EAAQ,EACV,MAAM,IAAIH,WAAW,uCAEvBjN,KAAKmE,KAAKiJ,EAAQpN,KAAK8M,UAAYlM,GAMrCiM,EAAApN,UAAAgD,OAAA,WACE,IAAIA,EAASzC,KAAKgN,IAAMhN,KAAK+M,MAI7B,OAHItK,EAAS,IACXA,EAASzC,KAAKkN,gBAAkBzK,GAE3BA,GAQToK,EAAApN,UAAA4N,OAAA,WACE,OAAOrN,KAAKyC,WAAazC,KAAK8M,UAQhCD,EAAApN,UAAA6N,QAAA,WACE,OAAyB,IAAlBtN,KAAKyC,UAMdoK,EAAIpN,UAAAiD,KAAJ,SAAK9B,GACH,GAAIZ,KAAKqN,SACP,MAAM,IAAIJ,WAAW,wBAEvBjN,KAAKiL,IAAIjL,KAAKgN,IAAKpM,GACnBZ,KAAKgN,IAAMhN,KAAKmN,KAAKnN,KAAKgN,IAAM,IAMlCH,EAAOpN,UAAA8N,QAAP,SAAQC,eACN,IAAoB,IAAAC,EAAA9K,EAAA6K,eAAQE,EAAAxM,KAAAwM,EAAAD,EAAA3M,OAAA,CAAvB,IAAMF,EAAK8M,EAAA9M,MACdZ,KAAK0C,KAAK9B,EACX,qGAMHiM,EAAApN,UAAA+C,IAAA,WACE,GAAIxC,KAAKsN,UACP,MAAM,IAAIL,WAAW,yBAEvBjN,KAAKgN,IAAMhN,KAAKmN,KAAKnN,KAAKgN,IAAM,GAChC,IAAM/L,EAASjB,KAAKyK,IAAIzK,KAAKgN,KAE7B,OADAhN,KAAKiL,IAAIjL,KAAKgN,SAAKW,GACZ1M,GAMT4L,EAAOpN,UAAAmO,QAAP,SAAQhN,GACN,GAAIZ,KAAKqN,SACP,MAAM,IAAIJ,WAAW,wBAEvBjN,KAAK+M,MAAQ/M,KAAKmN,KAAKnN,KAAK+M,MAAQ,GACpC/M,KAAKiL,IAAIjL,KAAK+M,MAAOnM,IAMvBiM,EAAApN,UAAAoO,MAAA,WACE,GAAI7N,KAAKsN,UACP,MAAM,IAAIL,WAAW,yBAEvB,IAAMhM,EAASjB,KAAKyK,IAAIzK,KAAK+M,OAG7B,OAFA/M,KAAKiL,IAAIjL,KAAK+M,WAAOY,GACrB3N,KAAK+M,MAAQ/M,KAAKmN,KAAKnN,KAAK+M,MAAQ,GAC7B9L,GAYT4L,EAAapN,UAAAqO,cAAb,SAAcC,GACZ,GAAI/N,KAAKsN,UACP,MAAM,IAAIL,WAAW,yBAEvB,IAAMG,EAAQpN,KAAKmN,KAAKnN,KAAK+M,MAAQgB,GAC/B9M,EAASjB,KAAKyK,IAAI2C,GAExB,OADApN,KAAKiL,IAAImC,EAAOpN,KAAKwC,OACdvB,GAEV4L,CAAA,IC3JDmB,EAAA,SAAAC,GAME,SAAAD,WACEC,EAAMtO,KAAAK,KAAAgO,EAAkBE,mBAAiBlO,IAC1C,QARuCJ,EAAaoO,EAAAC,GAU5CD,EAAAvO,UAAA4N,OAAA,WACP,OAAO,GAGAW,EAAIvO,UAAAiD,KAAJ,SAAK9B,GACRqN,EAAAxO,UAAM4N,OAAM1N,KAAAK,OACdA,KAAKmO,SAEPF,EAAAxO,UAAMiD,KAAI/C,KAAAK,KAACY,IAGJoN,EAAOvO,UAAAmO,QAAP,SAAQhN,GACXqN,EAAAxO,UAAM4N,OAAM1N,KAAAK,OACdA,KAAKmO,SAEPF,EAAAxO,UAAMmO,QAAOjO,KAAAK,KAACY,IAMRoN,EAAAvO,UAAA0O,OAAA,WAON,IANA,IAAMC,EAA8B,EAAhBpO,KAAK8M,SACnBuB,EAAU,IAAI9O,MAAS6O,GACvBE,EAAMtO,KAAKyC,SAIRM,EAAI,EAAGA,EAAIuL,EAAKvL,IACvBsL,EAAQtL,GAAK/C,KAAKyK,IAAIzK,KAAKmN,KAAKnN,KAAK+M,MAAQhK,IAG/C/C,KAAKmE,KAAOkK,EACZrO,KAAK8M,SAAWsB,EAChBpO,KAAKkN,gBAAkB,EAAIlN,KAAK8M,SAChC9M,KAAK+M,MAAQ,EACb/M,KAAKgN,IAAMsB,KA9Cf,CAA0CzB,GCmBpC,SAAU0B,EAAqBC,GACnC,OAAO,IAAIC,EAAcD,EAC3B,CAuBM,SAAUE,EACZC,GAEF,OAAO,IAAIC,EAAqBD,EAClC,CD/CiBX,EAAgBE,iBAAG,GC+HpC,IA+zBYW,EA/zBZC,EAAA,WAAA,SAAAA,IAwUC,CAAA,OAhTOA,EAAArP,UAAAsP,QAAN,6GAEU,OADF9N,EAAc,GACZ,CAAA,EAAMjB,KAAKc,eAAf0E,EAAIiG,EAAiB5J,+BACjB2D,EAAEtE,KAAI,CAAA,EAAA,IACZD,EAAOyB,KAAK8C,EAAE5E,OACV,CAAA,EAAMZ,KAAKc,uBAAf0E,EAAIiG,eAEN,KAAA,EAAA,MAAA,CAAA,EAAOxK,SACR,EAaK6N,EAAArP,UAAAuP,eAAN,+GAGU,OAFFC,EAASjP,KAAKkP,SAAS,KACvBjO,EAAc,GACZ,CAAA,EAAMgO,EAAOnO,eAAjB0E,EAAIiG,EAAmB5J,+BACnB2D,EAAEtE,KAAI,CAAA,EAAA,IACZD,EAAOyB,KAAK8C,EAAE5E,OACV,CAAA,EAAMqO,EAAOnO,uBAAjB0E,EAAIiG,eAEN,KAAA,EAAA,MAAA,CAAA,EAAOxK,SACR,EASK6N,EAAArP,UAAA0P,aAAN,oGACU,KAAA,EAAA,MAAA,CAAA,EAAMnP,KAAKc,eAAf0E,EAAIiG,EAAiB5J,+BACjB2D,EAAEtE,KAAI,CAAA,EAAA,GACR,CAAA,EAAMlB,KAAKc,sBAAf0E,EAAIiG,sCAEP,EASKqD,EAAYrP,UAAA2P,aAAlB,SAAmBC,8FACT,KAAA,EAAA,MAAA,CAAA,EAAMrP,KAAKc,eAAf0E,EAAIiG,EAAiB5J,OACrByN,EAAiBD,EAAU7J,EAAE5E,+BACxB4E,EAAEtE,OAASoO,EAAc,CAAA,EAAA,GAC5B,CAAA,EAAMtP,KAAKc,sBAAf0E,EAAIiG,SACJ6D,EAAiBD,EAAU7J,EAAE5E,oCAEhC,EAcDkO,EAAYrP,UAAA8P,aAAZ,SAAaC,GACX,OAAO,IAAIC,GAA0BzP,KAAMwP,IAa7CV,EAAMrP,UAAAiQ,OAAN,SAAOL,GACL,OAAO,IAAIM,GAAe3P,KAAMqP,IAWlCP,EAAGrP,UAAA8L,IAAH,SAAOqE,GACL,OAAO,IAAIC,GAAY7P,KAAM4P,IAW/Bd,EAAQrP,UAAAqQ,SAAR,SAAYF,GACV,OAAO,IAAIG,GAAiB/P,KAAM4P,IAWpCd,EAAcrP,UAAAuQ,eAAd,SAAkBJ,GAChB,OAAO,IAAIG,GAAiB/P,KAAM4P,GAAWK,UAW/CnB,EAAOrP,UAAAyQ,QAAP,SAAWN,GACT,OAAO,IAAIO,GAAgBnQ,KAAM4P,IAQ7Bd,EAAYrP,UAAA2Q,aAAlB,SAAmB7O,sEACjB,MAAO,CAAA,EAAAvB,KAAKuL,IAAIhK,GAAG4N,qBACpB,EASKL,EAAarP,UAAA4Q,cAAnB,SAAoB9O,sEAClB,MAAO,CAAA,EAAAvB,KAAKgQ,eAAezO,GAAG6N,cAAa,SAAA5J,GAAK,OAAO,IAANA,CAAW,UAC7D,EAoBDsJ,EAAArP,UAAA6Q,cAAA,SAAcC,EAAmBC,GAC/B,YAD+B,IAAAA,IAAAA,GAAqB,GAC7C,IAAIC,GAAsBzQ,KAAMuQ,EAAWC,IAmCpD1B,EAAArP,UAAAiR,iBAAA,SACIH,EAAmBC,EAEnBpF,GAMF,YARqB,IAAAoF,IAAAA,GAAqB,QAExC,IAAApF,IAAAA,EAA+CC,GAG9BrL,KAAKsQ,cAAcC,EAAWC,GAG/BjF,KAAI,SAAA/F,GAAK,OAAA0F,EAAQ1F,EAAG4F,EAAX,KAa7B0D,EAAArP,UAAAkR,YAAA,SACIvO,EACAwO,GACF,OAAO,IAAIC,GACPtC,EAAkB,CAACvO,KAAMoC,IAAYwO,IAU3C9B,EAAIrP,UAAAqR,KAAJ,SAAKjI,GACH,OAAIA,EAAQ,GAAc,MAATA,EACR7I,KAEF,IAAI+Q,EAAa/Q,KAAM6I,IAShCiG,EAAIrP,UAAAuR,KAAJ,SAAKnI,GACH,OAAIA,EAAQ,GAAc,MAATA,EACR7I,KAEF,IAAIiR,EAAajR,KAAM6I,IAYhCiG,EAAQrP,UAAAyP,SAAR,SAASgC,GACP,OAAO,IAAIC,GAAiBnR,KAAMkR,IAapCpC,EAAArP,UAAA2R,QAAA,SAAQC,EAAoBrN,GAC1B,OAAO,IAAIsN,GAAgBtR,KAAMqR,EAAYrN,IAO/C8K,EAAArP,UAAAwQ,OAAA,WACE,OAAO,IAAIsB,EAAevR,OAE7B8O,CAAA,IAWDL,EAAA,SAAAR,GAEE,SAAAQ,EAAsBD,GAAtB,IAAAgD,EACEvD,cACDjO,YAFqBwR,EAAKhD,MAALA,EADdgD,EAAIC,KAAG,GAGd,CAcF,OAlB8B7R,EAAe6O,EAAAR,GAM5CQ,EAAAhP,UAAAiS,QAAA,WACE,MAAO,mBAAY1R,KAAKwO,MAAM/L,kBAG1BgM,EAAAhP,UAAAqB,KAAN,oFACE,OAAId,KAAKyR,MAAQzR,KAAKwO,MAAM/L,OACnB,CAAA,EAAA,CAAC7B,MAAO,KAAMM,MAAM,KAEvByL,EAAO3M,KAAKwO,MAAMxO,KAAKyR,MAC7BzR,KAAKyR,OACL,CAAA,EAAO,CAAC7Q,MAAO4L,EAAUG,GAAOzL,MAAM,UACvC,EACFuN,CAAA,CAlBD,CAA+BK,GAoB/BF,EAAA,SAAAX,GACE,SAAAW,EACc+C,GADd,IAAAH,EAEEvD,cACDjO,YAFawR,EAAMG,OAANA,GAEb,CAgBF,OApBqC/R,EAAegP,EAAAX,GAMnDW,EAAAnP,UAAAiS,QAAA,WACE,MAAO,iBAGH9C,EAAAnP,UAAAqB,KAAN,8EACE,IACE,MAAA,CAAA,EAAOd,KAAK2R,SAMb,CALC,MAAO5Q,GAIP,MAFAA,EAAE6Q,QACE,mDAAAjO,OAAmD5C,EAAE6Q,SACnD7Q,CACP,gBACF,EACF6N,CAAA,CApBD,CAAsCE,GAsBtCyC,EAAA,SAAAtD,GAKE,SAAAsD,EAAsBM,GAAtB,IAAAL,EACEvD,cAEDjO,YAHqBwR,EAAQK,SAARA,EAEpBL,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CAkBF,OA1B+BtB,EAAe2R,EAAAtD,GAU7CsD,EAAA9R,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,yBAGpBH,EAAA9R,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEaP,EAAA9R,UAAAsS,WAAN,8EACN,MAAA,CAAA,EAAO/R,KAAK6R,SAAS/Q,aACtB,EACFyQ,CAAA,CA1BD,CAAgCzC,GA4BhCmC,EAAA,SAAAhD,GAQE,SAAsBgD,EAAAY,EAAqCG,GAA3D,IAAAR,EACEvD,cAEDjO,YAHqBwR,EAAQK,SAARA,EAAqCL,EAAQQ,SAARA,EAF3DR,EAAK3I,MAAG,EAIN2I,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CA8BF,OAzC6BtB,EAAeqR,EAAAhD,GAa3CgD,EAAAxR,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,uBAGpBT,EAAAxR,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEab,EAAAxR,UAAAsS,WAAN,kHAKC/R,KAAK6I,QAAU7I,KAAKgS,SACT,CAAA,EAAMhS,KAAK6R,SAAS/Q,QADH,CAAA,EAAA,UAGjC,OAFMmR,EAAUxG,EAA0B5J,QAE9BX,KACV,CAAA,EAAO+Q,IAETrG,EAAGsG,QAAQD,EAAQrR,cAErB,KAAA,EAAA,MAAA,CAAA,EAAOZ,KAAK6R,SAAS/Q,cACtB,EACFmQ,CAAA,CAzCD,CAA8BnC,GA2C9BiC,EAAA,SAAA9C,GAEE,SAAsB8C,EAAAc,EAAqCG,GAA3D,IAAAR,EACEvD,cACDjO,YAFqBwR,EAAQK,SAARA,EAAqCL,EAAQQ,SAARA,EAD3DR,EAAK3I,MAAG,GAGP,CAYF,OAhB6BjJ,EAAemR,EAAA9C,GAM3C8C,EAAAtR,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,uBAGpBX,EAAAtR,UAAAqB,KAAN,8EACE,OAAId,KAAK6I,SAAW7I,KAAKgS,SAChB,CAAA,EAAA,CAACpR,MAAO,KAAMM,MAAM,IAE7B,CAAA,EAAOlB,KAAK6R,SAAS/Q,aACtB,EACFiQ,CAAA,CAhBD,CAA8BjC,GAqB9B2B,GAAA,SAAAxC,GAKE,SAAAwC,EACcoB,EAAqCtB,EACrC4B,QAAA,IAAAA,IAAAA,GAA2B,GAFzC,IAAAX,EAGEvD,cAEDjO,YAJawR,EAAQK,SAARA,EAAqCL,EAASjB,UAATA,EACrCiB,EAAoBW,qBAApBA,EAEZX,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CA6BF,OAvCsCtB,EAAiB6Q,EAAAxC,GAYtDwC,EAAAhR,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,gCAGpBjB,EAAAhR,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEarB,EAAAhR,UAAAsS,WAAN,6GACAK,EAAa,oBACZ,OAAAA,EAAM3P,OAASzC,KAAKuQ,UACZ,CAAA,EAAMvQ,KAAK6R,SAAS/Q,QADC,CAAA,EAAA,UAElC,OADM6L,EAAOlB,EAA0B5J,QAC9BX,KACHlB,KAAKmS,sBAAwBC,EAAM3P,OAAS,EACvC,CAAA,EAAA,CAAC7B,MAAOwR,EAAOlR,MAAM,IAEvB,CAAA,EAAA,CAACN,MAAO,KAAMM,MAAM,KAE7BkR,EAAM1P,KAAKiK,EAAK/L,qBAElB,MAAO,CAAA,EAAA,CAACA,MAAOwR,EAAOlR,MAAM,UAC7B,EACFuP,CAAA,CAvCD,CAAuC3B,GAyCvCa,GAAA,SAAA1B,GAKE,SACc0B,EAAAkC,EACAxC,GAFd,IAAAmC,EAGEvD,cAEDjO,YAJawR,EAAQK,SAARA,EACAL,EAASnC,UAATA,EAEZmC,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CAwBF,OAlC+BtB,EAAe+P,EAAA1B,GAY7C0B,EAAAlQ,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,yBAGpB/B,EAAAlQ,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEanC,EAAAlQ,UAAAsS,WAAN,2GAES,MAAA,CAAA,EAAM/R,KAAK6R,SAAS/Q,eACjC,OADM6L,EAAOlB,EAA0B5J,QAC9BX,MAAQlB,KAAKqP,UAAU1C,EAAK/L,OACnC,CAAA,EAAO+L,IAETf,EAAGsG,QAAQvF,EAAK/L,qCAEnB,EACF+O,CAAA,CAlCD,CAAgCb,GAoChCe,GAAA,SAAA5B,GACE,SACc4B,EAAAgC,EACAjC,GAFd,IAAA4B,EAGEvD,cACDjO,YAHawR,EAAQK,SAARA,EACAL,EAAS5B,UAATA,GAEb,CA8BF,OAnC+BhQ,EAAeiQ,EAAA5B,GAO7C4B,EAAApQ,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,sBAGpB7B,EAAApQ,UAAAqB,KAAN,oHACe,KAAA,EAAA,MAAA,CAAA,EAAMd,KAAK6R,SAAS/Q,eACjC,IADM6L,EAAOhB,EAA0B9J,QAC9BX,KACP,MAAO,CAAA,EAAA,CAACN,MAAO,KAAMM,MAAM,IAEvBmR,EAAezG,EAAG0G,YAAYC,sBAAsB5F,EAAK/L,OAOzD4R,EAASxS,KAAK4P,UAAUjD,EAAK/L,OAC7B6R,EAAgB7G,EAAG0G,YAAYC,sBAAsBC,OAI3D,IAAgBE,EAAA/P,EAAA0P,GAAcM,EAAAD,EAAA5R,QAAA6R,EAAAzR,KAAAyR,EAAAD,EAAA5R,OAAnBW,EAACkR,EAAA/R,MACLgL,EAAG0G,YAAYM,eAAenR,EAAGgR,IACpChR,EAAEyQ,2GAGN,MAAO,CAAA,EAAA,CAACtR,MAAO4R,EAAQtR,MAAM,UAC9B,EACF2O,CAAA,CAnCD,CAAgCf,GAqChCW,GAAA,SAAAxB,GAEE,SACcwB,EAAAoC,EACArC,GAFd,IAAAgC,EAGEvD,cAEDjO,YAJawR,EAAQK,SAARA,EACAL,EAAOhC,QAAPA,EAHdgC,EAAK3I,MAAG,EAKN2I,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CAmCF,OA1C0CtB,EAAe6P,EAAAxB,GASxDwB,EAAAhQ,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,+BAOpBjC,EAAAhQ,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEKrC,EAAAhQ,UAAAsS,WAAN,4HAGa,6BAAA,CAAA,EAAM/R,KAAK6R,SAAS/Q,QAA3B,KAAA,EAAA,MAAA,CAAA,EAAO2K,iBAEP,kBAAKzL,KAAKwP,QAAQqD,SACT,CAAA,EAAA,CAACjS,MAAO,KAAMM,MAAM,8CASlC,EACFuO,CAAA,CA1CD,CAA2CX,GA4C3CiB,GAAA,SAAA9B,GACE,SACc8B,EAAA8B,EACAjC,GAFd,IAAA4B,EAGEvD,cACDjO,YAHawR,EAAQK,SAARA,EACAL,EAAS5B,UAATA,GAEb,CA8BF,OAnCoChQ,EAAemQ,EAAA9B,GAOlD8B,EAAAtQ,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,2BAGpB3B,EAAAtQ,UAAAqB,KAAN,oHACe,KAAA,EAAA,MAAA,CAAA,EAAMd,KAAK6R,SAAS/Q,eACjC,OADM6L,EAAOhB,EAA0B9J,QAC9BX,KACA,CAAA,EAAA,CAACN,MAAO,KAAMM,MAAM,KAEvBmR,EAAezG,EAAG0G,YAAYC,sBAAsB5F,EAAK/L,OAO1C,CAAA,EAAAZ,KAAK4P,UAAUjD,EAAK/L,gBAAnC4R,EAAS7G,EAAgC9J,OACzC4Q,EAAgB7G,EAAG0G,YAAYC,sBAAsBC,OAI3D,IAAgBM,EAAAnQ,EAAA0P,GAAcU,EAAAD,EAAAhS,QAAAiS,EAAA7R,KAAA6R,EAAAD,EAAAhS,OAAnBW,EAACsR,EAAAnS,MACLgL,EAAG0G,YAAYM,eAAenR,EAAGgR,IACpChR,EAAEyQ,2GAGN,MAAO,CAAA,EAAA,CAACtR,MAAO4R,EAAQtR,MAAM,UAC9B,EACF6O,CAAA,CAnCD,CAAqCjB,GAgDrCkE,GAAA,SAAA/E,GAQE,SAAA+E,IAAA,IAAAxB,EACEvD,cAGDjO,YAFCwR,EAAKyB,YAAc,IAAIjF,EACvBwD,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CAqCF,OAjDkDtB,EAAeoT,EAAA/E,GAc1D+E,EAAAvT,UAAAqB,KAAN,yFAME,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAgBKkB,EAAAvT,UAAAsS,WAAN,4GAIuC,IAA9B/R,KAAKiT,YAAYxQ,SAAc,CAAA,EAAA,GAE/B,CAAA,EAAMzC,KAAKkT,eAAhB,OAAKzH,EAAiB5J,aACb,CAAA,EAAA,CAACjB,MAAO,KAAMM,MAAM,IAG/B,KAAA,EAAA,MAAA,CAAA,EAAO,CAACN,MAAOZ,KAAKiT,YAAYpF,QAAS3M,MAAM,UAChD,EACF8R,CAAA,CAjDD,CAAmDlE,GAkDnDqB,GAAA,SAAAlC,GACE,SACckC,EAAA0B,EACAjC,GAFd,IAAA4B,EAGEvD,cACDjO,YAHawR,EAAQK,SAARA,EACAL,EAAS5B,UAATA,GAEb,CAgCF,OArCmChQ,EAAoBuQ,EAAAlC,GAOtDkC,EAAA1Q,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,0BAGpBvB,EAAA1Q,UAAAyT,KAAN,oHACe,KAAA,EAAA,MAAA,CAAA,EAAMlT,KAAK6R,SAAS/Q,eACjC,IADM6L,EAAOhB,EAA0B9J,QAC9BX,KACP,MAAA,CAAA,GAAO,GAEHmR,EAAezG,EAAG0G,YAAYC,sBAAsB5F,EAAK/L,OAMzDuS,EAAcnT,KAAK4P,UAAUjD,EAAK/L,OAClC6R,EACF7G,EAAG0G,YAAYC,sBAAsBY,GACzCnT,KAAKiT,YAAY1F,QAAQ4F,OAIzB,IAAgBC,EAAAzQ,EAAA0P,GAAcgB,EAAAD,EAAAtS,QAAAuS,EAAAnS,KAAAmS,EAAAD,EAAAtS,OAAnBW,EAAC4R,EAAAzS,MACLgL,EAAG0G,YAAYM,eAAenR,EAAGgR,IACpChR,EAAEyQ,2GAIN,MAAA,CAAA,GAAO,SACR,EACF/B,CAAA,CArCD,CAAoC6C,IAgDpCnC,GAAA,SAAA5C,GASE,SACI4C,EAAAyC,EACiB1C,GAFrB,IAAAY,EAGEvD,cAEDjO,YAHoBwR,EAAgBZ,iBAAhBA,EARbY,EAAQM,SAA+B,KAGvCN,EAAQpP,SAAoB,KAOlCoP,EAAK+B,cAAgBD,GACtB,CAsCF,OApDuC1T,EAAeiR,EAAA5C,GAgBrD4C,EAAApR,UAAAiS,QAAA,WAEE,MAAO,GAAA/N,OADmB,8CACC,gBAGvBkN,EAAApR,UAAAqB,KAAN,8EAEE,OADAd,KAAK8R,SAAW9R,KAAKwT,cAAcxT,KAAK8R,UACjC,CAAA,EAAA9R,KAAK8R,eACb,EAEajB,EAAapR,UAAA+T,cAAnB,SAAoB1B,qGAO1B,MAAA,CAAA,EAAMA,UACF,OADJrG,EAAA5J,OACqB,MAAjB7B,KAAKoC,SAAgB,CAAA,EAAA,GACA,CAAA,EAAMpC,KAAKuT,cAAczS,eAChD,IADM2S,EAAiBhI,EAA+B5J,QACnCX,KAEjB,MAAO,CAAA,EAAA,CAACN,MAAO,KAAMM,MAAM,IAE7BlB,KAAKoC,SAAWqR,EAAe7S,MACF,MAAzBZ,KAAK4Q,mBACP5Q,KAAKoC,SAAWpC,KAAKoC,SAASmN,aAAavP,KAAK4Q,6BAGjC,KAAA,EAAA,MAAA,CAAA,EAAM5Q,KAAKoC,SAAStB,eACvC,OADM4S,EAAajI,EAA0B5J,QAC9BX,MACblB,KAAKoC,SAAW,KAChB,CAAA,EAAOpC,KAAKwT,cAAc1B,KAE5B,CAAA,EAAO4B,SACR,EACF7C,CAAA,CApDD,CAAwC/B,IAsDxC,SAAYD,GACVA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAOA,EAAA,QAAA,GAAA,SACR,CAJD,CAAYA,IAAAA,EAIX,CAAA,IA+BD,IAAA8E,GAAA,SAAA1F,GAIE,SACuB0F,EAAAL,EACAM,QAAA,IAAAA,IAAAA,EAAgC/E,EAAgBgF,MAFvE,IAAArC,EAGEvD,cACDjO,YAHsBwR,EAAS8B,UAATA,EACA9B,EAAYoC,aAAZA,EALfpC,EAAK3I,MAAG,EACR2I,EAAcsC,eAA+B,MAMpD,CAgEF,OAxEuDlU,EAAe+T,EAAA1F,GAUrE0F,EAAAlU,UAAAiS,QAAA,WAEE,MAAO,IAAA/N,OADmB,0CACE,aAGhBgQ,EAASlU,UAAAsU,UAAf,SAAgBC,2CAWtB,SAASC,EAAQxH,GACf,OAAIA,aAAqBqC,EAEhB,CACLlO,MAFa6L,EAAU3L,OAETK,MAAK,SAAAqE,GAKjB,OAJA0O,IACI1O,EAAEtE,MACJiT,IAEK3O,EAAE5E,KACX,IACA8J,SAAS,GAGJ,CAAC9J,MAAO,KAAM8J,SAAS,EAEjC,6DAvBD,MAAA,CAAA,EAAMsJ,UAyBY,OAzBlBvI,EAAA5J,OAIIqS,EAAe,EACfC,EAAgB,EAoBI,CAAA,EAAA3I,EAAmBxL,KAAKsT,UAAWW,WAE3D,GAFMzB,EAAY/G,EAAiD5J,OAE/DqS,IAAiBC,EAEnB,MAAO,CAAA,EAAA,CAACvT,MAAO,KAAMM,MAAM,IAE7B,GAAIiT,EAAgB,EAClB,OAAQnU,KAAK4T,cACX,KAAK/E,EAAgBgF,KACnB,MAAM,IAAIrJ,MACN,+CACA,yBAAA7G,OAAyB3D,KAAK6I,MAAK,MACzC,KAAKgG,EAAgBuF,SACnB,MAAO,CAAA,EAAA,CAACxT,MAAO,KAAMM,MAAM,IAC7B,KAAK2N,EAAgBwF,SAOzB,OADArU,KAAK6I,QACE,CAAA,EAAA,CAACjI,MAAO4R,EAAQtR,MAAM,UAC9B,EAEKyS,EAAAlU,UAAAqB,KAAN,8EAEE,OADAd,KAAK8T,eAAiB9T,KAAK+T,UAAU/T,KAAK8T,gBACnC,CAAA,EAAA9T,KAAK8T,qBACb,EACFH,CAAA,CAxED,CAAwD7E,GAoFxDqC,GAAA,SAAAlD,GAGE,SACckD,EAAAU,EAAqCX,GADnD,IAAAM,EAEEvD,cAEDjO,YAHawR,EAAQK,SAARA,EAAqCL,EAAUN,WAAVA,EAEjDM,EAAK8C,OAAS,IAAIzH,EAAuCqE,IAC1D,CAwBF,OA/BwCtR,EAAeuR,EAAAlD,GAStDkD,EAAA1R,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,2BAOhBP,EAAA1R,UAAA8U,OAAA,WACR,MAAQvU,KAAKsU,OAAOjH,UAAU,CAC5B,IAAM/K,EAAItC,KAAK6R,SAAS/Q,OACxBd,KAAKsU,OAAO5R,KAAKJ,EAClB,GAGH6O,EAAA1R,UAAAqB,KAAA,WAKE,OAJAd,KAAKuU,SAIEvU,KAAKsU,OAAOzG,SAEtBsD,CAAA,CA/BD,CAAyCrC,GAuCzCwC,GAAA,SAAArD,GAUE,SAAAqD,EACqBO,EAAqCR,EACtDrN,GAFJ,IAAAwN,EAGEvD,EAAMtO,KAAAK,KAAA6R,EAAUR,IAGjBrR,YALoBwR,EAAQK,SAARA,EAAqCL,EAAUH,WAAVA,EAHlDG,EAAiBgD,mBAAG,EAM1BhD,EAAKnI,OAASoL,EAAepP,KAACrB,GAAQ4H,EAAGC,KAAK6I,MAAMC,YACpDnD,EAAKM,SAAWtR,QAAQC,QAAQ,CAACG,MAAO,KAAMM,MAAM,KACrD,CAoCF,OApDuCtB,EAAmB0R,EAAArD,GAkB1CqD,EAAA7R,UAAAqB,KAAN,yFAMP,OADAd,KAAK8R,SAAW9R,KAAK8R,SAAS3Q,MAAK,WAAM,OAAAqQ,EAAKO,YAAL,IAClC,CAAA,EAAA/R,KAAK8R,eACb,EAEOR,EAAS7R,UAAAmV,UAAT,SAAUvO,GAChB,OAAOD,KAAKI,MAAMxG,KAAKqJ,SAAWhD,IAG1BiL,EAAA7R,UAAAoV,YAAA,WACR,OAAO7U,KAAK4U,UAAU5U,KAAKsU,OAAO7R,WAG9B6O,EAAA7R,UAAAsS,WAAN,6GAEO/R,KAAKwU,mBACRxU,KAAKuU,0BAEA,OAACvU,KAAKsU,OAAOhH,UAAS,CAAA,EAAA,IACrBwH,EAAc9U,KAAK6U,cACJ,CAAA,EAAA7U,KAAKsU,OAAOxG,cAAcgH,YAC/C,OADM7T,EAASwK,EAA4C5J,QAChDX,MACTlB,KAAKwU,mBAAoB,UAEzBxU,KAAKuU,SACL,CAAA,EAAOtT,WAGX,MAAO,CAAA,EAAA,CAACL,MAAO,KAAMM,MAAM,UAC5B,EACFoQ,CAAA,CApDD,CAAwCH,ICpjCxC4D,GAAA,WAAA,SAAAA,IAWW/U,KAAIgV,KAAW,IA2czB,QA5YCD,EAAAtV,UAAA2S,MAAA,SAAM7B,EAAmBC,GAAzB,IAuBCgB,EAAAxR,UAvBwB,IAAAwQ,IAAAA,GAAqB,GAC5C,IAAMyE,EAAOjV,KAkBb,OAjBA4L,EAAGC,KAAKqJ,OACJ3E,EAAY,GAAG,WAAM,MAAA,oDACrB5M,OAAA4M,EAAW,IAeR4E,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,sDACnB,KAAA,EAAA,MAAA,CAAA,EAAMyD,EAAK7S,mBAAnB,MAAO,CAAA,EAACqJ,SACHiF,iBAAiBH,EAAWC,EAAgB4E,cAf/CpV,KAAKgV,OAASK,KAAyB,MAAbrV,KAAKgV,KAG1BhV,KAAKgV,KACHxE,EAGFpK,KAAKkP,KAAKtV,KAAKgV,KAAOzE,GAItBnK,KAAKI,MAAMxG,KAAKgV,KAAOzE,KAuBlCwE,EAAWtV,UAAAkR,YAAX,SAAY4E,GAAZ,IAoBC/D,EAAAxR,KAnBOiV,EAAOjV,KAeb,OAAOmV,IACH,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,IAAA/F,EAAAE,EAAA,OAAAtK,EAAArB,MAAA,SAAAwV,mBACK,KAAA,EAAA,MAAA,CAAA,EAAMP,EAAK7S,mBAAwB,OAApCuJ,GAAAF,EAAC+J,EAAA3T,QAAuB8O,YAAY,CAAA,EAAM4E,EAAQnT,mBAAlD,MAAA,CAAA,EAAAuJ,EAAAvK,MAAAqK,EAAA,CAAoC+J,EAAwB3T,gBAAC,GAfjE7B,KAAKgV,OAASK,KAAYE,EAAQP,OAASK,IAGtCA,IACe,MAAbrV,KAAKgV,MAAgC,MAAhBO,EAAQP,KAG/BhV,KAAKgV,KAAOO,EAAQP,KAIpB,OAwBXD,EAAMtV,UAAAiQ,OAAN,SAAOL,GAAP,IAcCmC,EAAAxR,KAbOiV,EAAOjV,KAUb,OAAOmV,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,sDACnB,KAAA,EAAA,MAAA,CAAA,EAAMyD,EAAK7S,mBAAnB,MAAO,CAAA,EAACqJ,EAAqB5J,OAAE6N,QAAO,SAAAlK,GAAK,OAAAoG,EAAG6J,MAAK,WAAM,OAAApG,EAAU7J,EAAE,GAAC,cATpExF,KAAKgV,OAASK,IAETA,IAIA,OAuBLN,EAAYtV,UAAA2Q,aAAlB,SAAmB7O,sFACT,KAAA,EAAA,MAAA,CAAA,EAAMvB,KAAKoC,mBAAnB,MAAO,CAAA,EAACqJ,SAAuB2E,aAAa7O,UAC7C,EAiBDwT,EAAGtV,UAAA8L,IAAH,SAAkCqE,GAAlC,IAKC4B,EAAAxR,KAJOiV,EAAOjV,KACb,OAAOmV,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,sDACnB,KAAA,EAAA,MAAA,CAAA,EAAMyD,EAAK7S,mBAAnB,MAAO,CAAA,EAACqJ,EAAqB5J,OAAE0J,KAAI,SAAA/F,GAAK,OAAAoG,EAAG6J,MAAK,WAAM,OAAA7F,EAAUpK,EAAE,GAAC,QACpE,GAAA,GAAExF,KAAKgV,OA0BVD,EAAQtV,UAAAqQ,SAAR,SAAuCF,GAAvC,IAMC4B,EAAAxR,KAJOiV,EAAOjV,KACb,OAAOmV,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,sDACnB,KAAA,EAAA,MAAA,CAAA,EAAMyD,EAAK7S,mBAAnB,MAAO,CAAA,EAACqJ,SAAuBqE,SAASF,OACzC,GAAA,GAAE5P,KAAKgV,OAYVD,EAAQtV,UAAAyP,SAAR,SAASgC,GAAT,IASCM,EAAAxR,KARC,GAAkB,MAAdkR,EACF,MAAM,IAAIjE,WACN,6DAGN,IAAMgI,EAAOjV,KACb,OAAOmV,IACH,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,OAAAnQ,EAAArB,MAAA,SAAAyL,mBAAa,KAAA,EAAA,MAAA,CAAA,EAAMwJ,EAAK7S,mBAAZ,MAAA,CAAA,EAACqJ,SAAuByD,SAASgC,IAAW,GAAA,GAAA,GAAElR,KAAKgV,OAqBrED,EAAMtV,UAAAiW,OAAN,SAAO7M,GAAP,IAwBC2I,EAAAxR,KAvBOiV,EAAOjV,KAkBb,OAAOmV,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,mDAG3B,OAFMmE,EAAmBjH,GACrB,WAAA,OAAAvO,EAAAqR,OAAA,OAAA,GAAA,mEAAqB,YAAA,CAAA,EAAMyD,EAAK7S,mBAApB,MAAA,CAAA,GAAEqJ,QAAOE,EAAA9J,OAAuB4J,EAAIvK,MAAE,EAAKuK,OAAE,GAAA,IACtD,CAAA,GDvRTmK,ECuRkCD,EAAiB7E,KAAKjI,GDrRnD,IAAIgI,GAAgB+E,EAAehF,KAH5B,IACZgF,EACAhF,UCmQiB,MAAb5Q,KAAKgV,MAAgBnM,EAAQ,EAIxB7I,KAAKgV,KAAOnM,EACA,IAAVA,EAEF,EACe,MAAb7I,KAAKgV,YAA2BrH,IAAV9E,GAAuBA,EAAQ,GAGvDwM,IAGA,OA0BXN,EAAItV,UAAAuR,KAAJ,SAAKnI,GAAL,IAoBC2I,EAAAxR,KAnBOiV,EAAOjV,KAiBb,OAAOmV,IACH,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,OAAAnQ,EAAArB,MAAA,SAAAyL,mBAAa,KAAA,EAAA,MAAA,CAAA,EAAMwJ,EAAK7S,mBAAZ,MAAA,CAAA,EAACqJ,SAAuBuF,KAAKnI,UAAM,GAhBlC,MAAb7I,KAAKgV,MAAgBnM,GAAS,GAAK7I,KAAKgV,MAAQnM,EAI3C7I,KAAKgV,KAAOnM,EAEJ,MAAb7I,KAAKgV,OACJhV,KAAKgV,KAAOnM,QAAmB8E,IAAV9E,GAAuBA,EAAQ,GAGhD,EAGA,OA+BXkM,EAAAtV,UAAA2R,QAAA,SAAQF,EAAoBlN,EAAe6R,GAA3C,IAuBCrE,EAAAxR,KArBC,QAFyC,IAAA6V,IAAAA,GAA6B,GAEpD,MAAd3E,GAAsBA,EAAa,EACrC,MAAiB,MAAblR,KAAKgV,KACD,IAAI/H,WACN,4DAEE,IAAIA,WACN,+KAGA,mCAAAtJ,OAAmC3D,KAAKgV,KAAI,eAGpD,IAAMC,EAAOjV,KACPqJ,EAASoL,EAAepP,KAACrB,GAAQ4H,EAAGC,KAAK6I,MAAMC,YACrD,OAAOQ,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,mEAKnB,OAJJsE,EAAQzM,EAAOrE,QACf6Q,IACFC,GAASzM,EAAOrE,SAEV,CAAA,EAAMiQ,EAAK7S,YAAnB,KAAA,EAAA,MAAA,CAAA,EAAQqJ,EAAA5J,OAAuBuP,QAAQF,EAAY4E,EAAMnB,gBAC1D,GAAA,GAAE3U,KAAKgV,OAoBVD,EAAItV,UAAAqR,KAAJ,SAAKjI,GAAL,IAiBC2I,EAAAxR,KAhBOiV,EAAOjV,KAcb,OAAOmV,IACH,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,OAAAnQ,EAAArB,MAAA,SAAAyL,mBAAa,KAAA,EAAA,MAAA,CAAA,EAAMwJ,EAAK7S,mBAAZ,MAAA,CAAA,EAACqJ,SAAuBqF,KAAKjI,UAAM,GAblC,MAAb7I,KAAKgV,MAAgBhV,KAAKgV,KAAOnM,EAG5BA,EACe,MAAb7I,KAAKgV,MAAgBhV,KAAKgV,MAAQnM,EAGpC7I,KAAKgV,KAGL,OAsBLD,EAAAtV,UAAAsP,QAAN,qGACE,GAAI/O,KAAKgV,OAASK,IAChB,MAAM,IAAI7K,MAAM,kDAEV,MAAA,CAAA,EAAMxK,KAAKoC,YAAnB,KAAA,EAAA,MAAA,CAAA,EAAQqJ,EAAqB5J,OAAEkN,iBAChC,EAaKgG,EAAAtV,UAAAuP,eAAN,qGACE,GAAIhP,KAAKgV,OAASK,IAChB,MAAM,IAAI7K,MAAM,kDAEV,MAAA,CAAA,EAAMxK,KAAKoC,YAAnB,KAAA,EAAA,MAAA,CAAA,EAAQqJ,EAAqB5J,OAAEmN,wBAChC,OAea,SAAAmG,GACZY,EACAf,GACF,YADE,IAAAA,IAAAA,EAAmB,MACd,IAAA,SAAA/G,GAAI,SAAA+H,IAAA,IAAAxE,EAAAvD,EAAA7M,MAAApB,KAAAoD,EAAA,GAAAJ,cAUV,KAAAhD,YATUwR,EAAIwD,KAAGA,GASjB,CAAA,OAVwBpV,EAAUoW,EAAA/H,GAO3B+H,EAAAvW,UAAA2C,SAAN,8EACE,MAAO,CAAA,EAAA2T,UACR,EACFC,CAAA,CAVM,CAAkBjB,IAY3B,CA4GA,SAASK,GAAgBa,GACvB,OAAa,OAATA,EACK,KLxYL,SAAuBnN,GAC3B,OAAc,MAAPA,GAWO,QAFKlI,EATekI,IAYZ,iBAAVlI,GAAuC,mBAAVA,GAZCrB,MAAMsL,QAAQ/B,IACpC,iBAARA,GAAqBA,aAAe8C,EAAGW,QAC/CX,EAAGC,KAAKqK,aAAapN,GAO3B,IAAqBlI,CANrB,CK0YMuV,CAFeF,EAAK,IAKf,CAACrV,MAWZ,SAAwDwV,GAEtD,GAAsB,IAAlBA,EAAO3T,OAET,MAAM,IAAI+H,MAAM,wCAGlB,OAAI4L,EAAO,aAAcxK,EAAGW,OAEnBX,EAAGyK,MAAMD,GAGTxK,EAAG0K,OAAOF,EAErB,CA1BkBG,CAAYN,GACXvL,SAAS,GAInB,CAAC9J,MAAO,KAAM8J,SAAS,EAChC,CArRkBqK,GAAeyB,gBAAG,IC5XpC,IAAAC,GAAA,SAAAxI,GAME,SAAAwI,EAA+BzM,GAA/B,IAAAwH,EACEvD,cACDjO,YAF8BwR,EAAKxH,MAALA,GAE9B,CAcF,OAtBoCpK,EAAe6W,EAAAxI,GAU5CwI,EAAAhX,UAAA2C,SAAN,sGACwB,KAAA,EAAA,MAAA,CAAA,EAAMpC,KAAKgK,MAAM5H,mBASvC,OATMsU,EAAgBjL,EAA2B5J,OAC3C8U,EAAeD,EAAcE,aAQnC,CAAA,EAPqBD,EAAaE,MAAM,MAAMtL,KAAI,SAAAuL,GAKhD,OAHIA,EAAKC,SAAS,QAChBD,EAAOA,EAAKpT,MAAM,GAAI,IAEjBoT,CACT,WAED,EACFL,CAAA,CAtBD,CAAqC1B,ICF/BiC,GAAa,IACbC,GAAY9U,OAAO,OACnB+U,GAAc/U,OAAO,SACrBgV,GAAchV,OAAO,SACrBiV,GAA0BjV,OAAO,mBACjCkV,GAA8BlV,OAAO,gBAe3CmV,GAAA,SAAArJ,GAgIE,SAA+BqJ,EAAAtN,EAAmBuN,GAAlD,IAAA/F,EACEvD,cAmBDjO,YApB8BwR,EAAKxH,MAALA,EA9HvBwH,EAASgG,WAAG,EACZhG,EAAeiG,gBAAa,KAC5BjG,EAAoBkG,sBAAG,EACvBlG,EAAamG,cAAkC,KAC/CnG,EAAqBoG,uBAAG,EACxBpG,EAASqG,UAAG,IACZrG,EAAesG,iBAAG,EA0HxBtG,EAAKyD,KAAO,IAAIwB,GAAgBzM,GAC3BuN,IACHA,EAAY,CAAA,GAEd/F,EAAKgG,WAAoC,IAAxBD,EAAUC,UAC3BhG,EAAKiG,gBAAkBF,EAAUQ,YACjCvG,EAAKmG,cAAgBJ,EAAUI,cAC/BnG,EAAKoG,sBAAwBL,EAAUK,sBACnCL,EAAUO,iBACZjM,OAAKqJ,OACsB,MAAvBqC,EAAUM,WACV,WACI,MAAA,gEAAA,IACRrG,EAAKsG,iBAAkB,EACvBtG,EAAKqG,UAAY,KAEjBrG,EAAKqG,UAAYN,EAAUM,UAAYN,EAAUM,UAAY,KAEhE,CAsMF,OA1V+BjY,EAAwB0X,EAAArJ,GAoBhDqJ,EAAA7X,UAAAsY,YAAN,qGACM,OAAC/X,KAAK0X,qBAAoB,CAAA,EAAA,GAC5B,CAAA,EAAM1X,KAAKgY,yBAAXvM,EAAA5J,iBAEF,KAAA,EAAA,MAAA,CAAA,EAAO7B,KAAK4X,sBAAwBxY,OAAOsM,KAAK1L,KAAK2X,eACjB3X,KAAKyX,uBAC1C,EAUaH,EAAA7X,UAAAuY,eAAN,yHACsB,KAAA,EAAA,MAAA,CAAA,EAAMhY,KAAKiY,8BACvC,GADMC,EAAsBlM,EAAgCnK,QACvD7B,KAAKyX,kBAAoBS,EAE5B,MAAM,IAAI1N,MACN,6DA0BN,GAzBWxK,KAAKyX,iBAAmBS,GAEjCrM,OAAKqJ,OACDgD,EAAoBzV,SAAWzC,KAAKyX,gBAAgBhV,QACpD,WAAM,MAAA,uCACF+O,EAAKiG,gBAAgBhV,OAAOkS,WAD1B,kEAGSuD,EAAoBzV,OAAOkS,WAAa,IAAI,IAE5D3U,KAAKyX,kBACRzX,KAAKyX,gBAAkBS,GAGnBC,EAAkCnY,KAAKyX,gBAAgBW,QACzD,SAACC,EAAmCC,GAElC,OADAD,EAASC,GAASD,EAASC,GAAQ,GAAM,EAClCD,CACR,GACD,CAAE,GACAE,EACFnZ,OAAOsM,KAAKyM,GAAQzI,QAAO,SAAC4I,GAAS,OAACH,EAAOG,GAAQ,CAAE,IAC3DzM,EAAAA,KAAKqJ,OACyB,IAA1BqD,EAAe9V,QACf,WAAM,MAAA,iCAAmC8V,EAAe5D,UAAU,IAElE3U,KAAK2X,kBACP,IAAkBlM,EAAA9I,EAAAvD,OAAOsM,KAAK1L,KAAK2X,gBAAgBhM,EAAAF,EAAA3K,QAAA6K,EAAAzK,KAAAyK,EAAAF,EAAA3K,OAEjD,GAFSwG,EAAGqE,EAAA/K,OAEG,IADDZ,KAAKyX,gBAAgBe,QAAQlR,GAEzC,MAAM,IAAIkD,MACN,YAAclD,EAAd,uEAEYtH,KAAKyX,gBAAgB9C,WAAa,8GAIxD3U,KAAK0X,sBAAuB,YAC7B,EAEaJ,EAAA7X,UAAAwY,oBAAN,oHACFjY,KAAKwX,UACM,CAAA,EAAMxX,KAAKiV,KAAK7S,YADb,CAAA,EAAA,UAEK,MAAA,CAAA,EADRqJ,EAA0B5J,OACPf,eAChC,IADM2X,EAAehN,EAAiB5J,QACrBX,KACf,MAAM,IAAIsJ,MAAM,sCAIlB,OAFMkO,EAAoBD,EAAa7X,MAEvC,CAAA,EADgBZ,KAAK2Y,SAASD,GAAW,IAGzC,KAAA,EAAA,MAAA,CAAA,EAAO,YAEV,EAwDKpB,EAAA7X,UAAA2C,SAAN,kHACM,OAACpC,KAAK0X,qBAAoB,CAAA,EAAA,GAC5B,CAAA,EAAM1X,KAAKgY,yBAAXvM,EAAA5J,iBAEU,KAAA,EAAA,MAAA,CAAA,EAAM7B,KAAKiV,KAAK7S,mBAM5B,OANIwW,EAAQnN,EAA0B5J,OAClC7B,KAAKwX,YAGPoB,EAAQA,EAAM5H,KAAK,IAErB,CAAA,EAAO4H,EAAMrN,KAAI,SAAA/F,GAAK,OAAAgM,EAAKqH,gBAAgBrT,EAArB,WACvB,EAED8R,EAAe7X,UAAAoZ,gBAAf,SAAgB/B,GAKd,IAJA,IAAMtJ,EAASxN,KAAK2Y,SAAS7B,GACvBgC,EAA6C,CAAA,EAC7CC,EAA2C,CAAA,EAExChW,EAAI,EAAGA,EAAI/C,KAAKyX,gBAAgBhV,OAAQM,IAAK,CACpD,IAAMuE,EAAMtH,KAAKyX,gBAAgB1U,GAC3BiW,EAAShZ,KAAK2X,cAAgB3X,KAAK2X,cAAcrQ,GAAO,KAC9D,IAAItH,KAAK4X,uBAA0BoB,EAAnC,CAIE,IAAMpY,EAAQ4M,EAAOzK,GACjBkW,EAAc,KAClB,GAAc,KAAVrY,EAGF,GAAIoY,QAA6BrL,IAAnBqL,EAAOE,QACnBD,EAAcD,EAAOE,YAChB,IAAIF,IAAWA,EAAOG,UAAYH,EAAOI,SAC9C,MAAM,IAAI5O,MACN,mBAAA7G,OAAmB2D,EAA8B,4BAAA3D,OAAAmT,IAErDmC,OAActL,CACf,KACI,CAEL,IAAM0L,EAAaC,OAAO1Y,GAC1B,GAAI2Y,MAAMF,GAINJ,EADED,GAA2B,SAAjBA,EAAOQ,MACLxZ,KAAKyZ,WAAW7Y,GAGhBA,OAEX,GAAKoY,GAAWA,EAAOQ,MAO5B,OAAQR,EAAOQ,OACb,IAAK,UASL,QACEP,EAAcI,QAPhB,IAAK,QACHJ,EAAc7S,KAAKI,MAAM6S,GACzB,MACF,IAAK,OACHJ,EAAcjZ,KAAKyZ,WAAW7Y,QAZlCqY,EAAcI,CAkBjB,CAEAL,GAAUA,EAAOI,QAAWL,EAAOzR,GAAO2R,EACdH,EAASxR,GAAO2R,CAC9C,CACF,CAGD,OAAmC,IAA/B7Z,OAAOsM,KAAKqN,GAAQtW,OACfqW,EAGA,CAACY,GAAIZ,EAAUa,GAAIZ,IAItBzB,EAAU7X,UAAAga,WAAV,SAAW7Y,GACjB,MAAc,MAAVA,GAAyC,SAAxBA,EAAMgZ,cAClB,EAEA,GAKHtC,EAAA7X,UAAAkZ,SAAA,SAAS7B,EAAc+C,QAAA,IAAAA,IAAAA,GAA2B,GAMxD,IALA,IAAM5Y,EAAmB,GACrB6Y,EAAa,EACXC,EAAajD,EAAKrU,OACpBuX,EAAe/C,GAEVlU,EAAI,EAAGA,EAAIgX,EAAYhX,IAC9B,OAAQiX,GAEN,KAAK/C,GACH,OAAQH,EAAKmD,OAAOlX,IAElB,KAAKiU,GACH8C,EAAa/W,EAAI,EACjBiX,EAAe7C,GACf,MAEF,KAAKnX,KAAK6X,UAIR,GAHAiC,EAAa/W,EAAI,EAGM,MAAnB/C,KAAK6X,WAAqB7X,KAAK8X,gBACjC,MAEF7W,EAAOyB,KAAK,IACZsX,EAAe/C,GACf,MAEF,QACE+C,EAAe9C,GACf4C,EAAa/W,EAGjB,MAEF,KAAKmU,GACH,GAAQJ,EAAKmD,OAAOlX,KAEb/C,KAAK6X,UACR5W,EAAOyB,KAAKoU,EAAKoD,UAAUJ,EAAY/W,IACvCiX,EAAe/C,GACf6C,EAAa/W,EAAI,EAIrB,MAEF,KAAKoU,GACH,GAAQL,EAAKmD,OAAOlX,KAEbiU,GACHgD,EAAe5C,GAInB,MAEF,KAAKA,GACH,OAAQN,EAAKmD,OAAOlX,IAElB,KAAK/C,KAAK6X,UACR5W,EAAOyB,KAAKoU,EAAKoD,UAAUJ,EAAY/W,EAAI,IAC3CiX,EAAe/C,GACf6C,EAAa/W,EAAI,EACjB,MAEF,KAAKiU,GACHgD,EAAe7C,GACf,MAEF,QACE6C,EAAe3C,GAGnB,MACF,KAAKA,GACH,GAAQP,EAAKmD,OAAOlX,KAEbiU,GACHgD,EAAe7C,GAezB,GANI6C,IAAiB5C,GACnBnW,EAAOyB,KAAKoU,EAAKoD,UAAUJ,EAAYC,EAAa,IAEpD9Y,EAAOyB,KAAKoU,EAAKoD,UAAUJ,IAGzBD,GAAwB5Y,EAAOwB,SAAWzC,KAAKyX,gBAAgBhV,OACjE,MAAM,IAAI+H,MAAM,wCAAA7G,OACZ3D,KAAKyX,gBAAgBhV,OAAM,gCAAAkB,OAA+B1C,IAEhE,OAAOA,GAEVqW,CAAA,CA1VD,CAAgCvC,ICjBhCoF,GAAA,SAAAlM,GAgBE,SAAAkM,EAAuCC,GAAvC,IAAA5I,EACEvD,cA0BDjO,KA3BsCwR,EAAgB4I,iBAAhBA,EAf/B5I,EAAQ6I,UAAG,EAiBjB7I,EAAK8I,QAAUF,EAAiBE,SAAW,KAC3C,IAAMC,EAAcnU,KAAKoU,KAAKhJ,EAAK8I,SACnC,GAAI9I,EAAK8I,QAAU,GAAKC,EAAc,GAAKA,EAAc,KACpDjB,OAAOmB,UAAUF,GACpB,MAAM,IAAI/P,MACN,oDACA,+BAAA7G,OAA+B6N,EAAK8I,UAc1C,GAXA9I,EAAKkJ,UAAYN,EAAiBO,yBAA2B,GAC7DnJ,EAAKoJ,aAAeR,EAAiBQ,aACrCpJ,EAAKqJ,qBACDT,EAAiBS,sBAAwBrJ,EAAK8I,QAClD9I,EAAKsJ,sBAAwBV,EAAiBU,sBAC9CtJ,EAAKuJ,sBAAwBX,EAAiBW,uBAAyB,EAEvEvJ,EAAKwJ,oBACuC,IAAxCZ,EAAiBY,mBACrBxJ,EAAKyJ,iBACoC,IAArCb,EAAiBa,iBAChBzJ,EAAKwJ,qBAAuBxJ,EAAKyJ,gBACpC,MAAM,IAAIzQ,MACN,gHAGP,CAkKF,OA7MuC5K,EAA6Bua,EAAAlM,GA6CnEkM,EAAA1a,UAAAiS,QAAA,WACE,MAAO,cAIIyI,EAAMja,OAAnB,SAAoBka,eAAA,IAAAA,IAAAA,EAAuC,CAAA,4FACzD,IAAKlO,EAAGA,MAAGzB,IAAI,cACb,MAAM,IAAID,MACN,4DAMN,MAAA,CAAA,GAHM0Q,EAAqB,IAAIf,EAAmBC,IAGzBe,gBAEzB,OAFA1P,EAAA5J,OAEA,CAAA,EAAOqZ,SACR,EAGKf,EAAA1a,UAAA0b,MAAN,iHAEkB,6BAAd1P,EAAAzL,KAAc,CAAA,EAAMmI,UAAUiT,aAAaC,aAAa,CACtDC,MAAqC,MAA9Btb,KAAK8a,uBACgC9a,KAAK8a,sBACjDS,OAAO,mBAHT9P,EAAKwD,OAAStD,EAAA9J,oBAMd,iBAAM,IAAI2I,MACN,iDAAA7G,OAAiD6X,EAAE5J,iBAGzD,IAAK5R,KAAKiP,OACR,MAAM,IAAIzE,MAAM,2CAQlB,GALMiR,EAEDC,OAAeC,cAAiBD,OAAeE,mBACpD5b,KAAK6b,aAAe,IAAIJ,EAEnBzb,KAAK4a,cAIH,GAAI5a,KAAK6b,aAAaC,aAAe9b,KAAK4a,aAC/C,MAAM,IAAIpQ,MACN,8BACA,aAAa7G,OAAA3D,KAAK4a,aAAgB,MAClC,WAAAjX,OAAW3D,KAAK6b,aAAaC,kBALjC9b,KAAK4a,aAAe5a,KAAK6b,aAAaC,WAexC,OAPMC,EAAe/b,KAAK6b,aAAaG,wBAAwBhc,KAAKiP,QACpEjP,KAAKic,SAAWjc,KAAK6b,aAAaK,iBAClClc,KAAKic,SAAS3B,QAAyB,EAAfta,KAAKsa,QAC7Bta,KAAKic,SAASlB,sBAAwB/a,KAAK+a,sBAC3CgB,EAAaI,QAAQnc,KAAKic,UAC1Bjc,KAAKoc,SAAW,IAAIC,aAAarc,KAAKsa,SACtCta,KAAKsc,SAAW,IAAID,aAAarc,KAAKsa,SAC/B,CAAA,SACR,EAEKH,EAAA1a,UAAAqB,KAAN,mHACE,OAAId,KAAKqa,SACA,CAAA,EAAA,CAACzZ,MAAO,KAAMM,MAAM,IAMN,CAAA,EAAMlB,KAAKuc,uBAYlC,OAZMC,EAAiB/Q,EAAyB5J,OAC5C7B,KAAKgb,qBACDoB,EAAWpc,KAAKyc,aAAaD,EAAeE,eAClDC,EAAoB3c,KAAK4c,4BACrBR,EAAU,CAACpc,KAAK0a,UAAW1a,KAAK6a,qBAAsB,KAExD7a,KAAKib,kBACDqB,EAAWtc,KAAKyc,aAAaD,EAAeK,eAClDC,EAAiB9c,KAAK4c,4BAClBN,EAAU,CAACtc,KAAK0a,UAAY1a,KAAKsa,QAAS,KAGzC,CAAA,EAAA,CACL1Z,MAAO,CAACmc,YAAeJ,EAAmBK,SAAYF,GACtD5b,MAAM,UAET,EAIKiZ,EAAA1a,UAAAwd,QAAN,8FACU,KAAA,EAAA,MAAA,CAAA,EAAMjd,KAAKc,QAAnB,KAAA,EAAA,MAAA,CAAA,EAAQ2K,EAAA5J,OAAmBjB,aAE5B,EAEauZ,EAAA1a,UAAA8c,aAAN,+FAKN,OAHMG,EAAgC,GAChCG,EAAgC,GAClCK,EAAgB,EACpB,CAAA,EAAO,IAAI1c,SAAQ,SAAAC,GACjB,IAAM0c,EAAaC,aAAY,WACzB5L,EAAKwJ,qBACPxJ,EAAKyK,SAASoB,sBAAsB7L,EAAK4K,UAErC5K,EAAK4K,SAAS,MAAQ/G,KACxB5U,EAAQ,CAACic,cAAaA,EAAEG,cAAaA,IAEvCH,EAAcha,KAAK8O,EAAK4K,SAAS1Y,MAAM,EAAG8N,EAAKqJ,wBAE7CrJ,EAAKyJ,kBACPzJ,EAAKyK,SAASqB,uBAAuB9L,EAAK8K,UAC1CO,EAAcna,KAAK8O,EAAK8K,SAAS5Y,YAI7BwZ,IAAkB1L,EAAKkJ,YAC3B6C,cAAcJ,GACd1c,EAAQ,CAACic,cAAaA,EAAEG,cAAaA,IAExC,GAAErL,EAAK8I,QAAU9I,EAAKoJ,aAAe,IACvC,UACF,EAGDT,EAAA1a,UAAA+d,KAAA,WACOxd,KAAKqa,WACRra,KAAKqa,UAAW,EAChBra,KAAKic,SAASwB,aACdzd,KAAK6b,aAAa6B,QACC,MAAf1d,KAAKiP,QAAkBjP,KAAKiP,OAAO0O,YAAYlb,OAAS,GAC1DzC,KAAKiP,OAAO0O,YAAY,GAAGH,SAMxBrD,EAAA1a,UAAAsP,QAAA,WACP,MAAM,IAAIvE,MAAM,oDAIlB2P,EAAA1a,UAAAme,cAAA,WACE,OAAO5d,KAAK4a,cAGNT,EAAY1a,UAAAgd,aAAZ,SAAaoB,GACnB,IAAMC,EAAYD,EAAM,GAAGpb,OACrB2Z,EAAW,IAAIC,aAAawB,EAAMpb,OAASqb,GAEjD,OADAD,EAAME,SAAQ,SAAC5Z,EAAMpB,GAAM,OAAAqZ,EAASnR,IAAI9G,EAAMpB,EAAI+a,EAAU,IACrD1B,GAGDjC,EAAA1a,UAAAmd,4BAAA,SAA4BR,EAAwB4B,GAE1D,IAAMC,EAAO,IAAI5B,aAAaxQ,EAAIA,KAACqS,cAAcF,IAGjD,OADAC,EAAKhT,IAAImR,EAAU6B,EAAKxb,OAAS2Z,EAAS3Z,QACnC6T,EAAMA,OAAC2H,EAAMD,IAEvB7D,CAAA,CA7MD,CAAwCrL,GCFxCqP,GAAA,SAAAlQ,GAQE,SACuBkQ,EAAAC,EACAC,GAFvB,IAAA7M,EAGEvD,cAuBDjO,KAtBC,GAHqBwR,EAAkB4M,mBAAlBA,EACA5M,EAAY6M,aAAZA,EATf7M,EAAQ6I,UAAG,EAEX7I,EAAM8M,QAAG,EASX9M,EAAK+M,eAKP,GAJA/M,EAAK8M,QAAS,EACd9M,EAAKgN,SACD,CAAChN,EAAK6M,aAAaI,aAAcjN,EAAK6M,aAAaK,aACvDlN,EAAKmN,WAAaC,EAAQA,SAAC,CAAC,GAAI,SAC5BpN,EAAK6M,aAAaQ,WAAY,CAEhC,IAAMC,EAC8B,EAAhCtN,EAAK6M,aAAaK,YAAoBlN,EAAK4M,mBAAmBvX,MAC5DkY,EAAuD,EAAjCvN,EAAK6M,aAAaI,aAC1CjN,EAAK4M,mBAAmBY,OACtBC,GAAkB,EAAIH,GAAsB,EAC5CI,GAAmB,EAAIH,GAAuB,EAC9CI,EAAeF,EAAiBH,EAChCM,EAAgBL,EAAsBG,EAC5C1N,EAAK6N,QAAUC,WACX,CAACJ,EAAiBD,EAAgBG,EAAeD,GACjD,CAAC,EAAG,GACT,MACC3N,EAAK6N,QAAUC,WAAS,CAAC,EAAG,EAAG,EAAG,GAAI,CAAC,EAAG,YAG/C,CAmKF,OArMmC1f,EAAsBue,EAAAlQ,GAoCxDkQ,EAAA1e,UAAAiS,QAAA,WACE,MAAO,UAIIyM,EAAAje,OAAb,SACIke,EAAuCC,eAAA,IAAAA,IAAAA,EAA+B,CAAA,4FACxE,IAAKnS,EAAGA,MAAGzB,IAAI,cACb,MAAM,IAAID,MACN,4DAGN,IAAK4T,EAAoB,CAIvB,GADAA,EAAqBmB,SAASC,cAAc,UACvCnB,EAAaK,cAAgBL,EAAaI,aAC7C,MAAM,IAAIjU,MACN,0GAGN4T,EAAmBvX,MAAQwX,EAAaK,YACxCN,EAAmBY,OAASX,EAAaI,YAC1C,CAID,MAAA,CAAA,GAHMgB,EAAiB,IAAItB,EAAeC,EAAoBC,IAGzClD,gBAErB,OAFA1P,EAAA5J,OAEA,CAAA,EAAO4d,SACR,EAGKtB,EAAA1e,UAAA0b,MAAN,oHACMnb,KAAKqe,aAAaqB,YACpB7T,EAAAA,KAAKqJ,OACiC,SAAjClV,KAAKqe,aAAaqB,YACmB,gBAAjC1f,KAAKqe,aAAaqB,YACvB,WACI,MAAA,+BAA+B/b,OAAA6N,EAAK6M,aAAaqB,WAAc,MAC/D,wCAAwC,qBAIlC,6BAAdjU,EAAAzL,KAAc,CAAA,EAAMmI,UAAUiT,aAAaC,aAAa,CACtDE,MAAO,CACLoE,SAAU3f,KAAKqe,aAAasB,SAC5BD,WAAY1f,KAAKqe,aAAaqB,WAC1B1f,KAAKqe,aAAaqB,WAClB,OACJ7Y,MAAO7G,KAAKoe,mBAAmBvX,MAC/BmY,OAAQhf,KAAKoe,mBAAmBY,yBAPpCvT,EAAKwD,OAAStD,EAAA9J,oBAad,kBADE+P,QAAU,iDAAAjO,OAAiD6X,EAAE5J,SACzD4J,SAGR,IAAKxb,KAAKiP,OACR,MAAM,IAAIzE,MAAM,uCAIlB,IACExK,KAAKoe,mBAAmBwB,UAAY5f,KAAKiP,MAK1C,CAJC,MAAO9L,GACP0c,QAAQC,IAAI3c,GACZnD,KAAKoe,mBAAmB2B,IAAMrE,OAAOsE,IAAIC,gBACvCjgB,KAAKiP,OACR,CAMD,OAJAjP,KAAKoe,mBAAmB8B,OAExBlgB,KAAKqa,UAAW,EAEhB,CAAA,EAAO,IAAI7Z,SAAc,SAAAC,GAEvB+Q,EAAK4M,mBAAmB+B,iBAAmB,WACzC1f,GACF,CACD,WACF,EAEK0d,EAAA1e,UAAAqB,KAAN,oFACE,GAAId,KAAKqa,SACP,MAAO,CAAA,EAAA,CAACzZ,MAAO,KAAMM,MAAM,IAI7B,IACEkf,EAAMlY,EAAAA,QAAQmY,WAAWrgB,KAAKoe,mBAI/B,CAHC,MAAOrd,GACP,MAAM,IAAIyJ,MACN,4CAAA7G,OAA4C2c,KAAKC,UAAUxf,IAChE,CACD,IAAIf,KAAKse,OASP,MAAO,CAAA,EAAA,CAAC1d,MAAOwf,EAAKlf,MAAM,IAR1B,IACE,MAAA,CAAA,EAAO,CAACN,MAAOZ,KAAKwgB,mBAAmBJ,GAAMlf,MAAM,GAKpD,CAJC,MAAOH,GACP,MAAM,IAAIyJ,MAAM,oCAAA7G,OAAoC5C,EAAE6Q,SACvD,CAAS,QACRwO,EAAIlO,SACL,gBAIJ,EAEOiM,EAAA1e,UAAA8e,aAAA,WAIN,SAAIve,KAAKqe,aAAaK,cAAe1e,KAAKqe,aAAaI,cAClDze,KAAKoe,mBAAmBvX,QAAU7G,KAAKqe,aAAaK,aACpD1e,KAAKoe,mBAAmBY,SAAWhf,KAAKqe,aAAaI,eAO5DN,EAAkB1e,UAAA+gB,mBAAlB,SAAmBJ,GAAnB,IAWC5O,EAAAxR,KAVC,OAAOyV,EAAIA,MAAC,WACV,IACIgL,EADEC,EAA0BC,EAAAA,WAAWC,EAAIA,KAACR,EAAK,WAAa,GAM5DpC,GAJNyC,EAAeI,EAAKA,MAACC,cACjBJ,EAAelP,EAAK6N,QAAS7N,EAAKmN,WAAYnN,EAAKgN,SACnD,aAEuBR,MAC3B,OAAO+C,EAAAA,QAAQN,EAAczC,EAAMta,MAAM,GAC3C,KAKIya,EAAA1e,UAAAwd,QAAN,8FACU,KAAA,EAAA,MAAA,CAAA,EAAMjd,KAAKc,QAAnB,KAAA,EAAA,MAAA,CAAA,EAAQ2K,EAAA5J,OAAmBjB,aAC5B,EAGDud,EAAA1e,UAAA+d,KAAA,WACiBxd,KAAKiP,OAAO0O,YAEpBI,SAAQ,SAAAiD,GAAS,OAAAA,EAAMxD,MAAN,IAExB,IACExd,KAAKoe,mBAAmBwB,UAAY,IAIrC,CAHC,MAAOzc,GACP0c,QAAQC,IAAI3c,GACZnD,KAAKoe,mBAAmB2B,IAAM,IAC/B,CACD/f,KAAKqa,UAAW,GAIT8D,EAAA1e,UAAAsP,QAAA,WACP,MAAM,IAAIvE,MAAM,oDAEnB2T,CAAA,CArMD,CAAoCrP,GCCpCmS,GAAA,WAUC,ECjBDC,GAAA,SAAAjT,GAAA,SAAAiT,kDAsBC,CAAA,OAtB4CthB,EAAoBshB,EAAAjT,GAmB/DiT,EAAKzhB,UAAAoX,MAAL,SAAMsK,GACJ,OAAO,IAAIC,GAAcphB,KAAMmhB,IAElCD,CAAA,CAtBD,CAA6CpS,GAmC7CsS,GAAA,SAAAnT,GAGE,SAAsBmT,EAAAvP,EAAgCsP,GAAtD,IAAA3P,EACEvD,cAEDjO,YAHqBwR,EAAQK,SAARA,EAEpBL,EAAK7M,KAAO,IAAI0c,GAAkBxP,EAAUsP,IAC7C,CASF,OAf2BvhB,EAAcwhB,EAAAnT,GAQxCmT,EAAA3hB,UAAAiS,QAAA,WACE,OAAO1R,KAAK2E,KAAK+M,WAGb0P,EAAA3hB,UAAAqB,KAAN,8EACE,MAAA,CAAA,EAAOd,KAAK2E,KAAK7D,aAClB,EACFsgB,CAAA,CAfD,CAA4BF,IAiB5BG,GAAA,SAAApT,GAIE,SACcoT,EAAAxP,EAA0CsP,GADxD,IAAA3P,EAEEvD,cACDjO,YAFawR,EAAQK,SAARA,EAA0CL,EAAS2P,UAATA,EAHxD3P,EAAS8P,UAAG,IAKX,CAgCF,OAvC+B1hB,EAAyByhB,EAAApT,GASvDoT,EAAA5hB,UAAAiS,QAAA,WACE,MAAO,GAAG/N,OAAA3D,KAAK6R,SAASH,UAAS,eAAA/N,OAAc3D,KAAKmhB,UAAS,OAGzDE,EAAA5hB,UAAAyT,KAAN,gHACsB,KAAA,EAAA,MAAA,CAAA,EAAMlT,KAAK6R,SAAS/Q,eACxC,IADMygB,EAAcvV,EAA0BnK,QAC9BX,KACd,MAAuB,KAAnBlB,KAAKshB,UACP,CAAA,GAAO,IAKTthB,KAAKiT,YAAYvQ,KAAK1C,KAAKshB,WAC3BthB,KAAKshB,UAAY,GACjB,CAAA,GAAO,KAEH1I,EAAQ2I,EAAY3gB,MAAMiW,MAAM7W,KAAKmhB,YAKrC,GAAKnhB,KAAKshB,UAAY1I,EAAM,OAClC,IAAmBnN,EAAA9I,EAAAiW,EAAMlV,MAAM,GAAI,IAAIiI,EAAAF,EAAA3K,QAAA6K,EAAAzK,KAAAyK,EAAAF,EAAA3K,OAA5BgW,EAAInL,EAAA/K,MACbZ,KAAKiT,YAAYvQ,KAAKoU,oGAIxB,OAFA9W,KAAKshB,UAAY1I,EAAMA,EAAMnW,OAAS,GAEtC,CAAA,GAAO,SACR,EACF4e,CAAA,CAvCD,CAAgCrO,IClDhCwO,GAAA,SAAAvT,GAAA,SAAAuT,kDAaC,CAAA,OAb+C5hB,EAAwB4hB,EAAAvT,GAUtEuT,EAAA/hB,UAAAmX,WAAA,WACE,OAAO,IAAI6K,GAAazhB,OAE3BwhB,CAAA,CAbD,CAAgD1S,GA0BhD2S,GAAA,SAAAxT,GAGE,SAAAwT,EAAsB5P,GAAtB,IAAAL,EACEvD,cAEDjO,YAHqBwR,EAAQK,SAARA,EAEpBL,EAAK7M,KAAO,IAAI+c,GAAiB7P,IAClC,CASF,OAf0BjS,EAAc6hB,EAAAxT,GAQvCwT,EAAAhiB,UAAAiS,QAAA,WACE,OAAO1R,KAAK2E,KAAK+M,WAGb+P,EAAAhiB,UAAAqB,KAAN,8EACE,MAAA,CAAA,EAAOd,KAAK2E,KAAK7D,aAClB,EACF2gB,CAAA,CAfD,CAA2BP,IAuC3BQ,GAAA,SAAAzT,GAME,SAAAyT,EAA+B7P,GAA/B,IAAAL,EACEvD,cAQDjO,KAPC,GAF6BwR,EAAQK,SAARA,EAEzB3F,QAAMzB,IAAI,cACZ+G,EAAKmQ,QAAU,IAAIxV,YAAY,aAC1B,CAEE,IAAAyV,EAAiBxV,QAAQ,gCAChCoF,EAAKmQ,QAAU,IAAIC,EAAc,OAClC,SACF,CAuBF,OAtC8BhiB,EAAyB8hB,EAAAzT,GAgBtDyT,EAAAjiB,UAAAiS,QAAA,WACE,MAAO,GAAA/N,OAAG3D,KAAK6R,SAASH,uBAGpBgQ,EAAAjiB,UAAAyT,KAAN,wGACsB,KAAA,EAAA,MAAA,CAAA,EAAMlT,KAAK6R,SAAS/Q,eAExC,OAFMygB,EAAc9V,EAA0B5J,QAE9BX,KACd,CAAA,GAAO,IAEP2gB,EAAQN,EAAY3gB,MAKpBkhB,EADE5V,QAAMzB,IAAI,cACLzK,KAAK2hB,QAAQI,OAAOF,EAAO,CAAC5S,QAAQ,IAEpCjP,KAAK2hB,QAAQK,MAAMC,OAAO3e,KAAKue,EAAMvN,SAE9CtU,KAAKiT,YAAYvQ,KAAKof,GACtB,CAAA,GAAO,UACR,EACFJ,CAAA,CAtCD,CAA+B1O,IClD/BkP,GAAA,SAAAjU,GAIE,SACciU,EAAAC,EACA/a,QAAA,IAAAA,IAAAA,EAAsC,CAAA,GAFpD,IAAAoK,EAGEvD,cAWDjO,YAbawR,EAAI2Q,KAAJA,EACA3Q,EAAOpK,QAAPA,EAEZyE,OAAKqJ,OACAiN,aAAgBra,cACZoE,QAAMzB,IAAI,gBACL0X,aAAgBC,MAAQD,aAAgB7X,OAElD,WAAM,MAAA,sEACU,IACpBkH,EAAK6Q,OAASjb,EAAQib,QAAU,EAEhC7Q,EAAK8Q,UAAYlb,EAAQkb,WAAa,SACvC,CAsDF,OAxEsC1iB,EAAiBsiB,EAAAjU,GAoBtDiU,EAAAziB,UAAAiS,QAAA,WACE,MAAO,cAAc/N,OAAA3D,KAAKmiB,OAGtBD,EAAAziB,UAAAqB,KAAN,oHACE,OAAId,KAAKqiB,SAAYriB,KAAKmiB,gBAAgBra,WAClB9H,KAAKmiB,KAAKI,WACVviB,KAAKmiB,KAAKnN,MACzB,CAAA,EAAA,CAACpU,MAAO,KAAMM,MAAM,KAEvB2gB,EAAQ,IAAIrhB,SAAoB,SAACC,EAASC,GAC9C,IAAMsM,EAAMwE,EAAK6Q,OAAS7Q,EAAK8Q,UAC/B,GAAI9Q,EAAK2Q,gBAAgBra,WAGvBrH,EAAQ,IAAIqH,WAAW0J,EAAK2Q,KAAKze,MAAM8N,EAAK6Q,OAAQrV,SAC/C,CAKL,IAAMwV,EAAa,IAAIC,WACvBD,EAAWE,OAAS,SAACC,GACnB,IAAIxe,EAAsCqe,EAAWvhB,OAOrD,GAHIkD,aAAgBkI,cAClBlI,EAAO,IAAI2D,WAAW3D,MAElBA,aAAgB2D,YACpB,OAAOpH,EAAO,IAAIb,UAAU,sCAE9BY,EAAQ0D,EACV,EACAqe,EAAWI,QAAU,SAACD,GACpB,OAAOjiB,EAAO,IAAI8J,MAAM,WAC1B,EACAgY,EAAWK,QAAU,SAACF,GACpB,OAAOjiB,EAAO,IAAI8J,MAAMmY,EAAMG,MAChC,EAGA,IAAMpf,EAAQ8N,EAAK2Q,KAAKze,MAAM8N,EAAK6Q,OAAQrV,GAG3CwV,EAAWO,kBAAkBrf,EAC9B,CACD8N,EAAK6Q,OAASrV,CAChB,SACgB,CAAA,EAAM6U,WAAtB,MAAQ,CAAA,GAAApW,EAAA7K,MAAQ+K,EAAA9J,OAAc4J,EAAAvK,MAAM,EAAOuK,UAC5C,EACFyW,CAAA,CAxED,CAAuCV,aCTjBwB,GAClBC,EAAkB7b,EAClB8b,eADkB,IAAA9b,IAAAA,EAAsC,CAAA,oGAUzC,MANI,iBAAT6b,EACVE,EAAYF,GAEZE,EAAaF,EAAgBA,IAC7BG,EAAcC,GAA0BJ,IAEzB,CAAA,GAAOC,GAAarX,EAAAA,KAAKyX,OAAOH,EAAWC,kBAAtDG,EAAW5X,EAAuD9J,QAC3D2hB,MACY1b,WAAU2b,KAAC,CAAA,EAAMF,EAASG,gBADpC,CAAA,EAAA,UAEb,OADMC,EAAa,IAAAlY,EAAArK,MAAI0G,WAAU,MAAA,EAAC6D,YAClC,CAAA,EAAO,IAAIuW,GAAkByB,EAAYvc,IAEzC,KAAA,EAAA,MAAM,IAAIoD,MAAM+Y,EAASK,kBAE5B,CAGD,IAAMP,GAA4B,SAACQ,GAYjC,MAXa,CACXC,OAAQD,EAAQC,OAChBC,QAASF,EAAQE,QACjBziB,KAAMuiB,EAAQviB,KACd0iB,KAAMH,EAAQG,KACdC,YAAaJ,EAAQI,YACrBC,MAAOL,EAAQK,MACfC,SAAUN,EAAQM,SAClBC,SAAUP,EAAQO,SAClBC,UAAWR,EAAQQ,UAGvB,ECzCM,SAAUC,GAAYC,GAC1B,MAA0B,iBAAXA,GAA+C,YAAvBA,EAAO7gB,MAAM,EAAG,EACzD,CCMA,IAAA8gB,GAAA,SAAAvW,GASE,SACcuW,EAAAxa,EACS5C,QAAA,IAAAA,IAAAA,EAAsC,CAAA,GAF7D,IAAAoK,EAGEvD,cACDjO,YAHawR,EAAKxH,MAALA,EACSwH,EAAOpK,QAAPA,GAEtB,CAYF,OAzBmCxH,EAAU4kB,EAAAvW,GAetCuW,EAAA/kB,UAAA2C,SAAN,oFAQE,OAPIkiB,GAAYtkB,KAAKgK,QAAUkC,EAAAA,MAAMzB,IAAI,aAEjCga,EAAKrY,QAAQ,MACnBpM,KAAKgK,MAAQya,EAAGC,aAAc1kB,KAAKgK,MAAiBtG,MAAM,KAIrD,CAAA,EAAA,IAAIwe,GAAkBliB,KAAKgK,MAAsBhK,KAAKoH,eAC9D,EACFod,CAAA,CAzBD,CAAoCvD,ICDpC0D,GAAA,SAAA1W,GAQE,SACuB0W,EAAA1B,EACA2B,QAAA,IAAAA,IAAAA,EAA0C,CAAA,GAFjE,IAAApT,EAGEvD,cACDjO,YAHsBwR,EAAGyR,IAAHA,EACAzR,EAAWoT,YAAXA,GAEtB,CAcF,OA1BkChlB,EAAU+kB,EAAA1W,GAkBrC0W,EAAAllB,UAAA2C,SAAN,8EACE,OAAIkiB,GAAYtkB,KAAKijB,KACnB,CAAA,EAAO,IAAKuB,GAAexkB,KAAKijB,IAAejjB,KAAK4kB,aAC/CxiB,YAEE,CAAA,EAAA4gB,GAAiBhjB,KAAKijB,IAAKjjB,KAAK4kB,mBAE1C,EACFD,CAAA,CA1BD,CAAmC1D,qGZwiB7B,SAA8CzS,GAApD,IAGCgD,EAAAxR,KAFC,OAAOmV,IACH,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,OAAAnQ,EAAArB,MAAA,SAAAyL,GAAY,MAAA,CAAA,EAAA8C,EAAkBC,GAAM,GAAA,GAAA,GAAEA,EAAM/L,OAClD,Qa9dgB,SACZ8hB,EAAqBhN,GACvB,YADuB,IAAAA,IAAAA,EAAyB,CAAA,GACzC,IAAID,GAAW,IAAIqN,GAAcJ,GAAShN,EACnD,SA0BM,SACFhW,GADJ,IAICiQ,EAAAxR,KAFO6kB,EAAOnW,EAAqBnN,GAClC,OAAO4T,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,WAAA,OAAAnQ,EAAArB,MAAA,SAAAyL,GAAY,MAAA,CAAA,EAAAoZ,EAAI,GAAA,GAAA,GAC/C,cA4DM,SACJtkB,GADF,IAOCiR,EAAAxR,KAJC,OAAOmV,IAAsB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,mEACf,MAAM,CAAA,EAAAjR,YAClB,OADMukB,EAAMrZ,EAAiB5J,OACtB,CAAA,EAAA6M,GAAqB,WAAM,OAAAoW,EAAIhkB,MAAM,QAC7C,GAAA,GACH,eAwEM,SAA2BsZ,sEAE/B,MAAA,CAAA,EAAOD,GAAmBja,OAAOka,SAClC,iBCrRe,kBD0OM,SAClBgE,EACAC,sEACF,MAAO,CAAA,EAAAF,GAAeje,OAAOke,EAAoBC,SAClD,QbiYK,SAA4C0G,GAAlD,IAMM/P,EA0BLxD,EAAAxR,KA7BC,IAAK2K,EAAWoa,GACd,MAAM,IAAIva,MAAM,qDAGlB,GAAIjL,MAAMsL,QAAQka,GAChB,IAAK,IAAIhiB,EAAI,EAAGA,EAAIgiB,EAAStiB,OAAQM,IACnCiS,EAAe,MAARA,EAAgB+P,EAAShiB,GAAkBiS,KAC5B5O,KAAK4e,IAAIhQ,EAAO+P,EAAShiB,GAAkBiS,WAE9D,GAAI+P,aAAoB3lB,OAC7B,IAAK,IAAM6lB,KAAMF,EACf/P,EAAe,MAARA,EAAgB+P,EAASE,GAAmBjQ,KAC7B5O,KAAK4e,IAAIhQ,EAAO+P,EAASE,GAAmBjQ,MAGtE,OAAOG,IAAyB,WAAA,OAAAhV,EAAAqR,OAAA,OAAA,GAAA,4DACd,KAAA,EAAA,MAAA,CAAA,EAAMhG,EAAmBuZ,GAAU,SAAA7lB,GACjD,GAAIA,aAAa6V,GACf,MAAO,CAACnU,MAAO1B,EAAEkD,WAAYsI,SAAS,GACjC,GAAIC,EAAWzL,GACpB,MAAO,CAAC0B,MAAO,KAAM8J,SAAS,GAE9B,MAAM,IAAIF,MACN,4EAGP,YACD,OAXM0a,EAAUzZ,EAUd5J,OACK,CAAA,GDxgBPyR,ECwgB6B4R,EDvgB7BtR,ECugBsC/E,EAAgBuF,cDvgBtD,IAAAR,IAAAA,EAAgC/E,EAAgBgF,MAC3C,IAAIF,GAAeL,EAAWM,KAHvB,IACZN,EACAM,UCwgBCoB,EACL", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}