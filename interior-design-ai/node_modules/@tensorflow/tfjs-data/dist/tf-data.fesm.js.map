{"version": 3, "file": "tf-data.fesm.js", "sources": ["../../../../node_modules/seedrandom/lib/alea.js", "../../../../node_modules/seedrandom/lib/xor128.js", "../../../../node_modules/seedrandom/lib/xorwow.js", "../../../../node_modules/seedrandom/lib/xorshift7.js", "../../../../node_modules/seedrandom/lib/xor4096.js", "../../../../node_modules/seedrandom/lib/tychei.js", "../../../../node_modules/seedrandom/seedrandom.js", "../../../../node_modules/seedrandom/index.js", "../../../../tfjs-data/src/util/deep_map.ts", "../../../../tfjs-data/src/util/deep_clone.ts", "../../../../tfjs-data/src/util/ring_buffer.ts", "../../../../tfjs-data/src/util/growing_ring_buffer.ts", "../../../../tfjs-data/src/iterators/lazy_iterator.ts", "../../../../tfjs-data/src/dataset.ts", "../../../../tfjs-data/src/datasets/text_line_dataset.ts", "../../../../tfjs-data/src/datasets/csv_dataset.ts", "../../../../tfjs-data/src/iterators/microphone_iterator.ts", "../../../../tfjs-data/src/iterators/webcam_iterator.ts", "../../../../tfjs-data/src/datasource.ts", "../../../../tfjs-data/src/iterators/string_iterator.ts", "../../../../tfjs-data/src/iterators/byte_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/file_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/url_chunk_iterator.ts", "../../../../tfjs-data/src/util/source_util.ts", "../../../../tfjs-data/src/sources/file_data_source.ts", "../../../../tfjs-data/src/sources/url_data_source.ts", "../../../../tfjs-data/src/readers.ts", "../../../../tfjs-data/src/version.ts"], "sourcesContent": ["// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\n\n// tslint:disable:no-any\n\n/**\n * A return value for a mapping function that can be applied via deepMap.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapResult = {\n  value: any,\n  recurse: boolean\n};\n\n/**\n * Apply a mapping function to a nested structure in a recursive manner.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapResult`.  The `DeepMapResult` either provides a\n *   replacement value for that node (i.e., replacing the subtree), or indicates\n *   that the node should be processed recursively.\n */\nexport function deepMap(input: any, mapFn: (x: any) => DeepMapResult): any|\n    any[] {\n  return deepMapInternal(input, mapFn);\n}\n\n/**\n * @param seen: A Map of known object mappings (i.e., memoized results of\n *   `mapFn()`)\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepMapInternal(\n    input: any, mapFn: (x: any) => DeepMapResult,\n    seen: Map<any, any> = new Map(), containedIn: Set<{}> = new Set()): any|\n    any[] {\n  if (input == null) {\n    return null;\n  }\n  if (typeof Blob === 'function' && input instanceof Blob) {\n    return input.slice();\n  }\n\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  if (seen.has(input)) {\n    return seen.get(input);\n  }\n  const result = mapFn(input);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep map function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    seen.set(input, result.value);\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const child = input[k];\n      const childResult = deepMapInternal(child, mapFn, seen, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    if (input.__proto__) {\n      mappedIterable.__proto__ = input.__proto__;\n    }\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// TODO(soergel, kangyizhang) Reconsider naming of deepZip() to avoid confusion\n// with zip()\n\n/**\n * Zip nested structures together in a recursive manner.\n *\n * This has the effect of transposing or pivoting data, e.g. converting it from\n * a row-major representation to a column-major representation.\n *\n * For example, `deepZip([{a: 1, b: 2}, {a: 3, b: 4}])` returns\n * `{a: [1, 3], b: [2, 4]}`.\n *\n * The inputs should all have the same nested structure (i.e., of arrays and\n * dicts).  The result is a single object with the same nested structure, where\n * the leaves are arrays collecting the values of the inputs at that location\n * (or, optionally, the result of a custom function applied to those arrays).\n *\n * @param inputs: An array of the objects to zip together.\n * @param zipFn: (optional) A function that expects an array of elements at a\n *   single node of the object tree, and returns a `DeepMapResult`.  The\n *   `DeepMapResult` either provides a result value for that node (i.e.,\n *   representing the subtree), or indicates that the node should be processed\n *   recursively.  The default zipFn recurses as far as possible and places\n *   arrays at the leaves.\n */\nexport function deepZip(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult = zipToList): any|any[] {\n  return deepZipInternal(inputs, zipFn);\n}\n\n/**\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepZipInternal(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult,\n    containedIn: Set<{}> = new Set()): any|any[] {\n  // The recursion follows the structure of input 0; it's assumed that all the\n  // other inputs have the same structure.\n  const input = inputs[0];\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  const result = zipFn(inputs);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep zip function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const children = inputs.map(x => x[k]);\n      const childResult = deepZipInternal(children, zipFn, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// tslint:disable-next-line:no-any\nexport function zipToList(x: any[]): DeepMapResult {\n  if (x === null) {\n    return null;\n  }\n  // TODO(soergel): validate array type?\n\n  if (isIterable(x[0])) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: x, recurse: false};\n  }\n}\n\n/**\n * A return value for an async map function for use with deepMapAndAwaitAll.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapAsyncResult = {\n  value: Promise<any>,\n  recurse: boolean\n};\n\n/**\n * Apply an async mapping function to a nested structure in a recursive manner.\n *\n * This first creates a nested structure of Promises, and then awaits all of\n * those, resulting in a single Promise for a resolved nested structure.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapAsyncResult`.  The `DeepMapAsyncResult` either provides\n *   a `Promise` for a replacement value for that node (i.e., replacing the\n *   subtree), or indicates that the node should be processed recursively.  Note\n *   that the decision whether or not to recurse must be made immediately; only\n *   the mapped value may be promised.\n */\nexport async function deepMapAndAwaitAll(\n    input: any, mapFn: (x: any) => DeepMapAsyncResult): Promise<any|any[]> {\n  const seen: Map<any, any> = new Map();\n\n  // First do a normal deepMap, collecting Promises in 'seen' as a side effect.\n  deepMapInternal(input, mapFn, seen);\n\n  // Replace the Promises in 'seen' in place.\n  // Note TypeScript provides no async map iteration, and regular map iteration\n  // is broken too, so sadly we have to do Array.from() to make it work.\n  // (There's no advantage to Promise.all(), and that would be tricky anyway.)\n  for (const key of Array.from(seen.keys())) {\n    const value = seen.get(key);\n    if (tf.util.isPromise(value)) {\n      const mappedValue = await value;\n      seen.set(key, mappedValue);\n    }\n  }\n\n  // Normal deepMap again, this time filling in the resolved values.\n  // It's unfortunate that we have to do two passes.\n  // TODO(soergel): test performance and think harder about a fast solution.\n  const result = deepMapInternal(input, mapFn, seen);\n  return result;\n}\n\n/**\n * Determine whether the argument is iterable.\n *\n * @returns true if the argument is an array or any non-Tensor object.\n */\n// tslint:disable-next-line:no-any\nexport function isIterable(obj: any): boolean {\n  let isTextDecoder = false;\n  if (tf.env().get('IS_BROWSER')) {\n    isTextDecoder = obj instanceof TextDecoder;\n  } else {\n    // tslint:disable-next-line:no-require-imports\n    const {StringDecoder} = require('string_decoder');\n    isTextDecoder = obj instanceof StringDecoder;\n  }\n  return obj != null && (!ArrayBuffer.isView(obj)) &&\n      (Array.isArray(obj) ||\n       (typeof obj === 'object' && !(obj instanceof tf.Tensor) &&\n        !(obj instanceof Promise) && !isTextDecoder));\n}\n\n/**\n * Determine whether the argument can be converted to Tensor.\n *\n * Tensors, primitives, arrays, and TypedArrays all qualify; anything else does\n * not.\n *\n * @returns true if the argument can be converted to Tensor.\n */\n// tslint:disable-next-line:no-any\nexport function canTensorify(obj: any): boolean {\n  return obj == null || isPrimitive(obj) || Array.isArray(obj) ||\n      (typeof obj === 'object' && (obj instanceof tf.Tensor)) ||\n      tf.util.isTypedArray(obj);\n}\n\n/**\n * Returns true if the given `value` is a primitive type. Otherwise returns\n * false. This is equivalant to node util.isPrimitive\n */\nfunction isPrimitive(value: any): boolean {\n  return (\n      value === null ||\n      (typeof value !== 'object' && typeof value !== 'function'));\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {RingBuffer} from './ring_buffer';\n\nexport class GrowingRingBuffer<T> extends RingBuffer<T> {\n  private static INITIAL_CAPACITY = 32;\n\n  /**\n   * Constructs a `GrowingRingBuffer`.\n   */\n  constructor() {\n    super(GrowingRingBuffer.INITIAL_CAPACITY);\n  }\n\n  override isFull() {\n    return false;\n  }\n\n  override push(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.push(value);\n  }\n\n  override unshift(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.unshift(value);\n  }\n\n  /**\n   * Doubles the capacity of the buffer.\n   */\n  private expand() {\n    const newCapacity = this.capacity * 2;\n    const newData = new Array<T>(newCapacity);\n    const len = this.length();\n\n    // Rotate the buffer to start at index 0 again, since we can't just\n    // allocate more space at the end.\n    for (let i = 0; i < len; i++) {\n      newData[i] = this.get(this.wrap(this.begin + i));\n    }\n\n    this.data = newData;\n    this.capacity = newCapacity;\n    this.doubledCapacity = 2 * this.capacity;\n    this.begin = 0;\n    this.end = len;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {Container} from '../types';\nimport {deepClone} from '../util/deep_clone';\nimport {deepMapAndAwaitAll, DeepMapAsyncResult, DeepMapResult, deepZip, zipToList} from '../util/deep_map';\nimport {GrowingRingBuffer} from '../util/growing_ring_buffer';\nimport {RingBuffer} from '../util/ring_buffer';\n\n/**\n * A nested structure of LazyIterators, used as the input to zip().\n */\nexport type IteratorContainer = Container<LazyIterator<tf.TensorContainer>>;\n\n// Here we implement a simple asynchronous iterator.\n// This lets us avoid using either third-party stream libraries or\n// recent TypeScript language support requiring polyfills.\n\n/**\n * Create a `LazyIterator` from an array of items.\n */\nexport function iteratorFromItems<T>(items: T[]): LazyIterator<T> {\n  return new ArrayIterator(items);\n}\n\n/**\n * Create a `LazyIterator` of incrementing integers.\n */\nexport function iteratorFromIncrementing(start: number): LazyIterator<number> {\n  let i = start;\n  return iteratorFromFunction(() => ({value: i++, done: false}));\n}\n\n/**\n * Create a `LazyIterator` from a function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * await iter.forEachAsync(e => console.log(e));\n * ```\n *\n * @param func A function that produces data on each call.\n */\nexport function iteratorFromFunction<T>(\n    func: () =>\n        IteratorResult<T>| Promise<IteratorResult<T>>): LazyIterator<T> {\n  return new FunctionCallIterator(func);\n}\n\n/**\n * Create a `LazyIterator` by concatenating underlying streams, which are\n * themselves provided as a stream.\n *\n * This can also be thought of as a \"stream flatten\" operation.\n *\n * @param baseIterators A stream of streams to be concatenated.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenated<T>(\n    baseIterators: LazyIterator<LazyIterator<T>>,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return new ChainedIterator(baseIterators, baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by concatenating streams produced by calling a\n * stream-generating function a given number of times.\n *\n * Since a `LazyIterator` is read-once, it cannot be repeated, but this\n * function can be used to achieve a similar effect:\n *\n *   LazyIterator.ofConcatenatedFunction(() => new MyIterator(), 6);\n *\n * @param iteratorFunc: A function that produces a new stream on each call.\n * @param count: The number of times to call the function.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenatedFunction<T>(\n    iteratorFunc: () => IteratorResult<LazyIterator<T>>, count: number,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return iteratorFromConcatenated(\n      iteratorFromFunction(iteratorFunc).take(count), baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by zipping together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nexport function iteratorFromZipped<O extends tf.TensorContainer>(\n    iterators: IteratorContainer,\n    mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL): LazyIterator<O> {\n  return new ZipIterator<O>(iterators, mismatchMode);\n}\n\n/**\n * An asynchronous iterator, providing lazy access to a potentially\n * unbounded stream of elements.\n *\n * Iterator can be obtained from a dataset:\n * `const iter = await dataset.iterator();`\n */\nexport abstract class LazyIterator<T> {\n  // This class implements AsyncIterator<T>, but we have not yet set the\n  // TypeScript --downlevelIteration flag to enable that.\n\n  abstract summary(): string;\n\n  /**\n   * Returns a `Promise` for the next element in the stream.\n   *\n   * When an item can be provided successfully, the return value is\n   * `{value:T, done:false}`.\n   *\n   * Calling next() on a closed stream returns `{value:null, done:true}`.\n   */\n  abstract next(): Promise<IteratorResult<T>>;\n\n  /**\n   * Collect all remaining elements of a bounded stream into an array.\n   * Obviously this will succeed only for small streams that fit in memory.\n   * Useful for testing.\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArray(): Promise<T[]> {\n    const result: T[] = [];\n    let x = await this.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await this.next();\n    }\n    return result;\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArrayForTest(): Promise<T[]> {\n    const stream = this.prefetch(100);\n    const result: T[] = [];\n    let x = await stream.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await stream.next();\n    }\n    return result;\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveFully(): Promise<void> {\n    let x = await this.next();\n    while (!x.done) {\n      x = await this.next();\n    }\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted, or a predicate fails.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveWhile(predicate: (r: T) => boolean): Promise<void> {\n    let x = await this.next();\n    let shouldContinue = predicate(x.value);\n    while ((!x.done) && shouldContinue) {\n      x = await this.next();\n      shouldContinue = predicate(x.value);\n    }\n  }\n\n  /**\n   * Handles errors thrown on this stream using a provided handler function.\n   *\n   * @param handler A function that handles any `Error` thrown during a `next()`\n   *   call and returns true if the stream should continue (dropping the failed\n   *   call) or false if the stream should quietly terminate.  If the handler\n   *   itself throws (or rethrows) an `Error`, that will be propagated.\n   *\n   * @returns A `LazyIterator` of elements passed through from upstream,\n   *   possibly filtering or terminating on upstream `next()` calls that\n   *   throw an `Error`.\n   */\n  handleErrors(handler: (error: Error) => boolean): LazyIterator<T> {\n    return new ErrorHandlingLazyIterator(this, handler);\n  }\n\n  // TODO(soergel): Implement reduce() etc.\n\n  /**\n   * Filters this stream according to `predicate`.\n   *\n   * @param predicate A function mapping a stream element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `LazyIterator` of elements for which the predicate was true.\n   */\n  filter(predicate: (value: T) => boolean): LazyIterator<T> {\n    return new FilterIterator(this, predicate);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  map<O>(transform: (value: T) => O): LazyIterator<O> {\n    return new MapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through an async 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a `Promise` for a\n   *   transformed stream element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  mapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform, forcing serial execution.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  serialMapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform).serial();\n  }\n\n  /**\n   * Maps this stream through a 1-to-many transform.\n   *\n   * @param transform A function mapping a stream element to an array of\n   *   transformed elements.\n   *\n   * @returns A `DataStream` of transformed elements.\n   */\n  flatmap<O>(transform: (value: T) => O[]): LazyIterator<O> {\n    return new FlatmapIterator(this, transform);\n  }\n\n  /**\n   * Apply a function to every element of the stream.\n   *\n   * @param f A function to apply to each stream element.\n   */\n  async forEachAsync(f: (value: T) => void): Promise<void> {\n    return this.map(f).resolveFully();\n  }\n\n  /**\n   * Apply a function to every element of the stream, forcing serial execution.\n   *\n   * @param f A function to apply to each stream element.  Should return 'true'\n   *   to indicate that the stream should continue, or 'false' to cause it to\n   *   terminate.\n   */\n  async serialForEach(f: (value: T) => Promise<boolean>): Promise<void> {\n    return this.serialMapAsync(f).resolveWhile(x => (x === true));\n  }\n\n  /**\n   * Groups elements into batches, represented as arrays of elements.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"Row-major\" means that the resulting batch is simply a collection of\n   * rows: `[row1, row2, row3, ...]`.  This is contrast to the column-major\n   * form, which is needed for vectorized computation.\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `LazyIterator` of batches of elements, represented as arrays\n   *   of the original element type.\n   */\n  rowMajorBatch(batchSize: number, smallLastBatch = true): LazyIterator<T[]> {\n    return new RowMajorBatchIterator(this, batchSize, smallLastBatch);\n  }\n\n  /**\n   * Groups elements into batches, represented in column-major form.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"column-major\" means that the resulting batch is a (potentially\n   * nested) structure representing the columns.  Each column entry, then,\n   * contains a collection of the values found in that column for a range of\n   * input elements.  This representation allows for vectorized computation, in\n   * contrast to the row-major form.\n   *\n   * The inputs should all have the same nested structure (i.e., of arrays and\n   * dicts).  The result is a single object with the same nested structure,\n   * where the leaves are arrays collecting the values of the inputs at that\n   * location (or, optionally, the result of a custom function applied to those\n   * arrays).\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @param zipFn: (optional) A function that expects an array of elements at a\n   *   single node of the object tree, and returns a `DeepMapResult`.  The\n   *   `DeepMapResult` either provides a result value for that node (i.e.,\n   *   representing the subtree), or indicates that the node should be processed\n   *   recursively.  The default zipFn recurses as far as possible and places\n   *   arrays at the leaves.\n   * @returns A `LazyIterator` of batches of elements, represented as an object\n   *   with collections at the leaves.\n   */\n  columnMajorBatch(\n      batchSize: number, smallLastBatch = true,\n      // tslint:disable-next-line:no-any\n      zipFn: (xs: any[]) => DeepMapResult = zipToList):\n      LazyIterator<tf.TensorContainer> {\n    // First collect the desired number of input elements as a row-major batch.\n    const rowBatches = this.rowMajorBatch(batchSize, smallLastBatch);\n    // Now 'rotate' or 'pivot' the data, collecting all values from each column\n    // in the batch (i.e., for each key within the elements) into an array.\n    return rowBatches.map(x => deepZip(x, zipFn));\n  }\n\n  /**\n   * Concatenate this `LazyIterator` with another.\n   *\n   * @param iterator A `LazyIterator` to be concatenated onto this one.\n   * @param baseErrorHandler An optional function that can intercept `Error`s\n   *   raised during a `next()` call on the base stream.  This function can\n   *   decide whether the error should be propagated, whether the error should\n   *   be ignored, or whether the base stream should be terminated.\n   * @returns A `LazyIterator`.\n   */\n  concatenate(\n      iterator: LazyIterator<T>,\n      baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n    return new ChainedIterator(\n        iteratorFromItems([this, iterator]), baseErrorHandler);\n  }\n\n  /**\n   * Limits this stream to return at most `count` items.\n   *\n   * @param count The maximum number of items to provide from the stream. If\n   * a negative or undefined value is given, the entire stream is returned\n   *   unaltered.\n   */\n  take(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new TakeIterator(this, count);\n  }\n\n  /**\n   * Skips the first `count` items in this stream.\n   *\n   * @param count The number of items to skip.  If a negative or undefined\n   * value is given, the entire stream is returned unaltered.\n   */\n  skip(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new SkipIterator(this, count);\n  }\n\n  /**\n   * Prefetch the first `bufferSize` items in this stream.\n   *\n   * Note this prefetches Promises, but makes no guarantees about when those\n   * Promises resolve.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   */\n  prefetch(bufferSize: number): LazyIterator<T> {\n    return new PrefetchIterator(this, bufferSize);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  /**\n   * Randomly shuffles the elements of this stream.\n   *\n   * @param bufferSize: An integer specifying the number of elements from\n   * this stream from which the new stream will sample.\n   * @param seed: (Optional.) An integer specifying the random seed that\n   * will be used to create the distribution.\n   */\n  shuffle(windowSize: number, seed?: string): LazyIterator<T> {\n    return new ShuffleIterator(this, windowSize, seed);\n  }\n\n  /**\n   * Force an iterator to execute serially: each next() call will await the\n   * prior one, so that they cannot execute concurrently.\n   */\n  serial(): LazyIterator<T> {\n    return new SerialIterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on LazyIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// Iterators that just extend LazyIterator directly\n// ============================================================================\n\nclass ArrayIterator<T> extends LazyIterator<T> {\n  private trav = 0;\n  constructor(protected items: T[]) {\n    super();\n  }\n\n  summary() {\n    return `Array of ${this.items.length} items`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.trav >= this.items.length) {\n      return {value: null, done: true};\n    }\n    const item = this.items[this.trav];\n    this.trav++;\n    return {value: deepClone(item), done: false};\n  }\n}\n\nclass FunctionCallIterator<T> extends LazyIterator<T> {\n  constructor(\n      protected nextFn: () => IteratorResult<T>| Promise<IteratorResult<T>>) {\n    super();\n  }\n\n  summary() {\n    return `Function call`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    try {\n      return this.nextFn();\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message =\n          `Error thrown while iterating through a dataset: ${e.message}`;\n      throw e;\n    }\n  }\n}\n\nclass SerialIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(protected upstream: LazyIterator<T>) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Serial`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    return this.upstream.next();\n  }\n}\n\nclass SkipIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  count = 0;\n\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Skip`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider tradeoffs of reading in parallel, eg.\n    // collecting next() promises in an Array and then waiting for\n    // Promise.all() of those. Benefit: pseudo-parallel execution.  Drawback:\n    // maybe delayed GC.\n    while (this.count++ < this.maxCount) {\n      const skipped = await this.upstream.next();\n      // short-circuit if upstream is already empty\n      if (skipped.done) {\n        return skipped;\n      }\n      tf.dispose(skipped.value as {});\n    }\n    return this.upstream.next();\n  }\n}\n\nclass TakeIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Take`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.count++ >= this.maxCount) {\n      return {value: null, done: true};\n    }\n    return this.upstream.next();\n  }\n}\n\n// Note this batch just groups items into row-wise element arrays.\n// Rotating these to a column-wise representation happens only at the dataset\n// level.\nclass RowMajorBatchIterator<T> extends LazyIterator<T[]> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T[]>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected batchSize: number,\n      protected enableSmallLastBatch = true) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> RowMajorBatch`;\n  }\n\n  async next(): Promise<IteratorResult<T[]>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T[]>> {\n    const batch: T[] = [];\n    while (batch.length < this.batchSize) {\n      const item = await this.upstream.next();\n      if (item.done) {\n        if (this.enableSmallLastBatch && batch.length > 0) {\n          return {value: batch, done: false};\n        }\n        return {value: null, done: true};\n      }\n      batch.push(item.value);\n    }\n    return {value: batch, done: false};\n  }\n}\n\nclass FilterIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected predicate: (value: T) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Filter`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      const item = await this.upstream.next();\n      if (item.done || this.predicate(item.value)) {\n        return item;\n      }\n      tf.dispose(item.value as {});\n    }\n  }\n}\n\nclass MapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Map`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\nclass ErrorHandlingLazyIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected handler: (error: Error) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> handleErrors`;\n  }\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      try {\n        return await this.upstream.next();\n      } catch (e) {\n        if (!this.handler(e)) {\n          return {value: null, done: true};\n        }\n        // If the handler returns true, loop and fetch the next upstream item.\n\n        // If the upstream iterator throws an endless stream of errors, and if\n        // the handler says to ignore them, then we loop forever here.  That is\n        // the correct behavior-- it's up to the handler to decide when to stop.\n      }\n    }\n  }\n}\n\nclass AsyncMapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => Promise<O>) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> AsyncMap`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = await this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\n// Iterators that maintain a queue of pending items\n// ============================================================================\n\n/**\n * A base class for transforming streams that operate by maintaining an\n * output queue of elements that are ready to return via next().  This is\n * commonly required when the transformation is 1-to-many:  A call to next()\n * may trigger a call to the underlying stream, which will produce many\n * mapped elements of this stream-- of which we need to return only one, so\n * we have to queue the rest.\n */\nexport abstract class OneToManyIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  protected outputQueue: RingBuffer<T>;\n\n  constructor() {\n    super();\n    this.outputQueue = new GrowingRingBuffer<T>();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  /**\n   * Read one or more chunks from upstream and process them, possibly\n   * reading or writing a carryover, and adding processed items to the\n   * output queue.  Note it's possible that no items are added to the queue\n   * on a given pump() call, even if the upstream stream is not closed\n   * (e.g., because items are filtered).\n   *\n   * @return `true` if any action was taken, i.e. fetching items from the\n   *   upstream source OR adding items to the output queue.  `false` if the\n   *   upstream source is exhausted AND nothing was added to the queue\n   * (i.e., any remaining carryover).\n   */\n  protected abstract pump(): Promise<boolean>;\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // Fetch so that the queue contains at least one item if possible.\n    // If the upstream source is exhausted, AND there are no items left in\n    // the output queue, then this stream is also exhausted.\n    while (this.outputQueue.length() === 0) {\n      // TODO(soergel): consider parallel reads.\n      if (!await this.pump()) {\n        return {value: null, done: true};\n      }\n    }\n    return {value: this.outputQueue.shift(), done: false};\n  }\n}\nclass FlatmapIterator<I, O> extends OneToManyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O[]) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Flatmap`;\n  }\n\n  async pump(): Promise<boolean> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return false;\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // that's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying any\n    // intermediate Tensors.  Here we are concerned only about the inputs.\n    const mappedArray = this.transform(item.value);\n    const outputTensors =\n        tf.tensor_util.getTensorsInContainer(mappedArray as {});\n    this.outputQueue.pushAll(mappedArray);\n\n    // TODO(soergel) faster intersection, and deduplicate outputTensors\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n\n    return true;\n  }\n}\n\n/**\n * Provides a `LazyIterator` that concatenates a stream of underlying\n * streams.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n */\nexport class ChainedIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>> = null;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private iterator: LazyIterator<T> = null;\n  private moreIterators: LazyIterator<LazyIterator<T>>;\n\n  constructor(\n      iterators: LazyIterator<LazyIterator<T>>,\n      private readonly baseErrorHandler?: (e: Error) => boolean) {\n    super();\n    this.moreIterators = iterators;\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of chained summaries';\n    return `${upstreamSummaries} -> Chained`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    this.lastRead = this.readFromChain(this.lastRead);\n    return this.lastRead;\n  }\n\n  private async readFromChain(lastRead: Promise<IteratorResult<T>>):\n      Promise<IteratorResult<T>> {\n    // Must await on the previous read since the previous read may have advanced\n    // the stream of streams, from which we need to read.\n    // This is unfortunate since we can't parallelize reads. Which means\n    // prefetching of chained streams is a no-op.\n    // One solution is to prefetch immediately upstream of this.\n    await lastRead;\n    if (this.iterator == null) {\n      const iteratorResult = await this.moreIterators.next();\n      if (iteratorResult.done) {\n        // No more streams to stream from.\n        return {value: null, done: true};\n      }\n      this.iterator = iteratorResult.value;\n      if (this.baseErrorHandler != null) {\n        this.iterator = this.iterator.handleErrors(this.baseErrorHandler);\n      }\n    }\n    const itemResult = await this.iterator.next();\n    if (itemResult.done) {\n      this.iterator = null;\n      return this.readFromChain(lastRead);\n    }\n    return itemResult;\n  }\n}\n\nexport enum ZipMismatchMode {\n  FAIL,      // require zipped streams to have the same length\n  SHORTEST,  // terminate zip when the first stream is exhausted\n  LONGEST    // use nulls for exhausted streams; use up the longest stream.\n}\n\n/**\n * Provides a `LazyIterator` that zips together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nclass ZipIterator<O extends tf.TensorContainer> extends LazyIterator<O> {\n  private count = 0;\n  private currentPromise: Promise<IteratorResult<O>> = null;\n\n  constructor(\n      protected readonly iterators: IteratorContainer,\n      protected readonly mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL) {\n    super();\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of zip summaries';\n    return `{${upstreamSummaries}} -> Zip`;\n  }\n\n  private async nextState(afterState: Promise<IteratorResult<O>>):\n      Promise<IteratorResult<O>> {\n    // This chaining ensures that the underlying next() are not even called\n    // before the previous ones have resolved.\n    await afterState;\n\n    // Collect underlying iterator \"done\" signals as a side effect in\n    // getNext()\n    let numIterators = 0;\n    let iteratorsDone = 0;\n\n    function getNext(container: IteratorContainer): DeepMapAsyncResult {\n      if (container instanceof LazyIterator) {\n        const result = container.next();\n        return {\n          value: result.then(x => {\n            numIterators++;\n            if (x.done) {\n              iteratorsDone++;\n            }\n            return x.value;\n          }),\n          recurse: false\n        };\n      } else {\n        return {value: null, recurse: true};\n      }\n    }\n\n    const mapped: O = await deepMapAndAwaitAll(this.iterators, getNext);\n\n    if (numIterators === iteratorsDone) {\n      // The streams have all ended.\n      return {value: null, done: true};\n    }\n    if (iteratorsDone > 0) {\n      switch (this.mismatchMode) {\n        case ZipMismatchMode.FAIL:\n          throw new Error(\n              'Zipped streams should have the same length. ' +\n              `Mismatched at element ${this.count}.`);\n        case ZipMismatchMode.SHORTEST:\n          return {value: null, done: true};\n        case ZipMismatchMode.LONGEST:\n        default:\n          // Continue.  The exhausted streams already produced value: null.\n      }\n    }\n\n    this.count++;\n    return {value: mapped, done: false};\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    this.currentPromise = this.nextState(this.currentPromise);\n    return this.currentPromise;\n  }\n}\n\n// Iterators that maintain a ring buffer of pending promises\n// ============================================================================\n\n/**\n * A stream that prefetches a given number of items from an upstream source,\n * returning them in FIFO order.\n *\n * Note this prefetches Promises, but makes no guarantees about when those\n * Promises resolve.\n */\nexport class PrefetchIterator<T> extends LazyIterator<T> {\n  protected buffer: RingBuffer<Promise<IteratorResult<T>>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected bufferSize: number) {\n    super();\n    this.buffer = new RingBuffer<Promise<IteratorResult<T>>>(bufferSize);\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Prefetch`;\n  }\n\n  /**\n   * Refill the prefetch buffer.  Returns only after the buffer is full, or\n   * the upstream source is exhausted.\n   */\n  protected refill() {\n    while (!this.buffer.isFull()) {\n      const v = this.upstream.next();\n      this.buffer.push(v);\n    }\n  }\n\n  next(): Promise<IteratorResult<T>> {\n    this.refill();\n    // This shift will never throw an error because the buffer is always\n    // full after a refill. If the stream is exhausted, the buffer will be\n    // full of Promises that will resolve to the end-of-stream signal.\n    return this.buffer.shift();\n  }\n}\n\n/**\n * A stream that performs a sliding-window random shuffle on an upstream\n * source. This is like a `PrefetchIterator` except that the items are\n * returned in randomized order.  Mixing naturally improves as the buffer\n * size increases.\n */\nexport class ShuffleIterator<T> extends PrefetchIterator<T> {\n  private readonly random: seedrandom.prng;\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private upstreamExhausted = false;\n\n  constructor(\n    protected override upstream: LazyIterator<T>, protected windowSize: number,\n      seed?: string) {\n    super(upstream, windowSize);\n    this.random = seedrandom.alea(seed || tf.util.now().toString());\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  override async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private randomInt(max: number) {\n    return Math.floor(this.random() * max);\n  }\n\n  protected chooseIndex(): number {\n    return this.randomInt(this.buffer.length());\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider performance\n    if (!this.upstreamExhausted) {\n      this.refill();\n    }\n    while (!this.buffer.isEmpty()) {\n      const chosenIndex = this.chooseIndex();\n      const result = await this.buffer.shuffleExcise(chosenIndex);\n      if (result.done) {\n        this.upstreamExhausted = true;\n      } else {\n        this.refill();\n        return result;\n      }\n    }\n    return {value: null, done: true};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\n\n/**\n * Represents a potentially large collection of text lines.\n *\n * The results are not batched.\n */\nexport class TextLineDataset extends Dataset<string> {\n  /**\n   * Create a `TextLineDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   */\n  constructor(protected readonly input: DataSource) {\n    super();\n  }\n\n  async iterator(): Promise<LazyIterator<string>> {\n    const inputIterator = await this.input.iterator();\n    const utf8Iterator = inputIterator.decodeUTF8();\n    const lineIterator = utf8Iterator.split('\\n').map(line => {\n      // Windows/DOS format text file has extra line breaker at the end of line.\n      if (line.endsWith('\\r')) {\n        line = line.slice(0, -1);\n      }\n      return line;\n    });\n    return lineIterator;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\nimport {ColumnConfig, CSVConfig} from '../types';\nimport {TextLineDataset} from './text_line_dataset';\n\nconst CODE_QUOTE = '\"';\nconst STATE_OUT = Symbol('out');\nconst STATE_FIELD = Symbol('field');\nconst STATE_QUOTE = Symbol('quote');\nconst STATE_QUOTE_AFTER_QUOTE = Symbol('quoteafterquote');\nconst STATE_WITHIN_QUOTE_IN_QUOTE = Symbol('quoteinquote');\n\n/**\n * Represents a potentially large collection of delimited text records.\n *\n * The produced `TensorContainer`s each contain one key-value pair for\n * every column of the table.  When a field is empty in the incoming data, the\n * resulting value is `undefined`, or throw error if it is required.  Values\n * that can be parsed as numbers are emitted as type `number`, other values\n * are parsed as `string`.\n *\n * The results are not batched.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport class CSVDataset extends Dataset<TensorContainer> {\n  base: TextLineDataset;\n  private hasHeader = true;\n  private fullColumnNames: string[] = null;\n  private columnNamesValidated = false;\n  private columnConfigs: {[key: string]: ColumnConfig} = null;\n  private configuredColumnsOnly = false;\n  private delimiter = ',';\n  private delimWhitespace = false;\n\n  /**\n   * Returns column names of the csv dataset. If `configuredColumnsOnly` is\n   * true, return column names in `columnConfigs`. If `configuredColumnsOnly` is\n   * false and `columnNames` is provided, `columnNames`. If\n   * `configuredColumnsOnly` is false and `columnNames` is not provided, return\n   * all column names parsed from the csv file. For example usage please go to\n   * `tf.data.csv`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async columnNames() {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    return this.configuredColumnsOnly ? Object.keys(this.columnConfigs) :\n                                        this.fullColumnNames;\n  }\n\n  /* 1) If `columnNames` is provided as string[], use this string[] as output\n   * keys in corresponding order. The length must match the number of inferred\n   * columns if `hasHeader` is true .\n   * 2) If `columnNames` is not provided, parse header line as `columnNames` if\n   * hasHeader is true. If `hasHeader` is false, throw an error.\n   * 3) If `columnConfigs` is provided, all the keys in `columnConfigs` must\n   * exist in parsed `columnNames`.\n   */\n  private async setColumnNames() {\n    const columnNamesFromFile = await this.maybeReadHeaderLine();\n    if (!this.fullColumnNames && !columnNamesFromFile) {\n      // Throw an error if columnNames is not provided and no header line.\n      throw new Error(\n          'Column names must be provided if there is no header line.');\n    } else if (this.fullColumnNames && columnNamesFromFile) {\n      // Check provided columnNames match header line.\n      util.assert(\n          columnNamesFromFile.length === this.fullColumnNames.length,\n          () => 'The length of provided columnNames (' +\n              this.fullColumnNames.length.toString() +\n              ') does not match the length of the header line read from ' +\n              'file (' + columnNamesFromFile.length.toString() + ').');\n    }\n    if (!this.fullColumnNames) {\n      this.fullColumnNames = columnNamesFromFile;\n    }\n    // Check if there are duplicate column names.\n    const counts: {[key: string]: number} = this.fullColumnNames.reduce(\n        (countAcc: {[key: string]: number}, name) => {\n          countAcc[name] = (countAcc[name] + 1) || 1;\n          return countAcc;\n        },\n        {});\n    const duplicateNames =\n        Object.keys(counts).filter((name) => (counts[name] > 1));\n    util.assert(\n        duplicateNames.length === 0,\n        () => 'Duplicate column names found: ' + duplicateNames.toString());\n    // Check if keys in columnConfigs match columnNames.\n    if (this.columnConfigs) {\n      for (const key of Object.keys(this.columnConfigs)) {\n        const index = this.fullColumnNames.indexOf(key);\n        if (index === -1) {\n          throw new Error(\n              'The key \"' + key +\n              '\" provided in columnConfigs does not match any of the column ' +\n              'names (' + this.fullColumnNames.toString() + ').');\n        }\n      }\n    }\n    this.columnNamesValidated = true;\n  }\n\n  private async maybeReadHeaderLine() {\n    if (this.hasHeader) {\n      const iter = await this.base.iterator();\n      const firstElement = await iter.next();\n      if (firstElement.done) {\n        throw new Error('No data was found for CSV parsing.');\n      }\n      const firstLine: string = firstElement.value;\n      const headers = this.parseRow(firstLine, false);\n      return headers;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Create a `CSVDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   * @param csvConfig (Optional) A CSVConfig object that contains configurations\n   *     of reading and decoding from CSV file(s).\n   *\n   *     hasHeader: (Optional) A boolean value that indicates whether the first\n   *     row of provided CSV file is a header line with column names, and should\n   *     not be included in the data. Defaults to `true`.\n   *\n   *     columnNames: (Optional) A list of strings that corresponds to\n   *     the CSV column names, in order. If provided, it ignores the column\n   *     names inferred from the header row. If not provided, infers the column\n   *     names from the first row of the records. If hasHeader is false and\n   *     columnNames is not provided, this method throws an error.\n   *\n   *     columnConfigs: (Optional) A dictionary whose key is column names, value\n   *     is an object stating if this column is required, column's data type,\n   *     default value, and if this column is label. If provided, keys must\n   *     correspond to names provided in columnNames or inferred from the file\n   *     header lines. If isLabel is true any column, returns an array of two\n   *     items: the first item is a dict of features key/value pairs, the second\n   *     item is a dict of labels key/value pairs. If no feature is marked as\n   *     label, returns a dict of features only.\n   *\n   *     configuredColumnsOnly (Optional) If true, only columns provided in\n   *     columnConfigs will be parsed and provided during iteration.\n   *\n   *     delimiter (Optional) The string used to parse each line of the input\n   *     file. Defaults to `,`.\n   */\n  constructor(protected readonly input: DataSource, csvConfig?: CSVConfig) {\n    super();\n    this.base = new TextLineDataset(input);\n    if (!csvConfig) {\n      csvConfig = {};\n    }\n    this.hasHeader = csvConfig.hasHeader === false ? false : true;\n    this.fullColumnNames = csvConfig.columnNames;\n    this.columnConfigs = csvConfig.columnConfigs;\n    this.configuredColumnsOnly = csvConfig.configuredColumnsOnly;\n    if (csvConfig.delimWhitespace) {\n      util.assert(\n          csvConfig.delimiter == null,\n          () =>\n              'Delimiter should not be provided when delimWhitespace is true.');\n      this.delimWhitespace = true;\n      this.delimiter = ' ';\n    } else {\n      this.delimiter = csvConfig.delimiter ? csvConfig.delimiter : ',';\n    }\n  }\n\n  async iterator(): Promise<LazyIterator<TensorContainer>> {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    let lines = await this.base.iterator();\n    if (this.hasHeader) {\n      // We previously read the first line to get the columnNames.\n      // Now that we're providing data, skip it.\n      lines = lines.skip(1);\n    }\n    return lines.map(x => this.makeDataElement(x));\n  }\n\n  makeDataElement(line: string): TensorContainer {\n    const values = this.parseRow(line);\n    const features: {[key: string]: TensorContainer} = {};\n    const labels: {[key: string]: TensorContainer} = {};\n\n    for (let i = 0; i < this.fullColumnNames.length; i++) {\n      const key = this.fullColumnNames[i];\n      const config = this.columnConfigs ? this.columnConfigs[key] : null;\n      if (this.configuredColumnsOnly && !config) {\n        // This column is not selected.\n        continue;\n      } else {\n        const value = values[i];\n        let parsedValue = null;\n        if (value === '') {\n          // If default value is provided, use it. If default value is not\n          // provided, set as undefined.\n          if (config && config.default !== undefined) {\n            parsedValue = config.default;\n          } else if (config && (config.required || config.isLabel)) {\n            throw new Error(\n                `Required column ${key} is empty in this line: ${line}`);\n          } else {\n            parsedValue = undefined;\n          }\n        } else {\n          // A value is present, so parse it based on type\n          const valueAsNum = Number(value);\n          if (isNaN(valueAsNum)) {\n            // The value is a string and this column is declared as boolean\n            // in config, parse it as boolean.\n            if (config && config.dtype === 'bool') {\n              parsedValue = this.getBoolean(value);\n            } else {\n              // Set value as string\n              parsedValue = value;\n            }\n          } else if (!config || !config.dtype) {\n            // If this value is a number and no type config is provided, return\n            // it as number.\n            parsedValue = valueAsNum;\n          } else {\n            // If this value is a number and data type is provided, parse it\n            // according to provided data type.\n            switch (config.dtype) {\n              case 'float32':\n                parsedValue = valueAsNum;\n                break;\n              case 'int32':\n                parsedValue = Math.floor(valueAsNum);\n                break;\n              case 'bool':\n                parsedValue = this.getBoolean(value);\n                break;\n              default:\n                parsedValue = valueAsNum;\n            }\n          }\n        }\n        // Check if this column is label.\n        (config && config.isLabel) ? labels[key] = parsedValue :\n                                     features[key] = parsedValue;\n      }\n    }\n    // If label exists, return an object of features and labels as {xs:features,\n    // ys:labels}, otherwise return features only.\n    if (Object.keys(labels).length === 0) {\n      return features;\n\n    } else {\n      return {xs: features, ys: labels};\n    }\n  }\n\n  private getBoolean(value: string): number {\n    if (value === '1' || value.toLowerCase() === 'true') {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n\n  // adapted from https://beta.observablehq.com/@mbostock/streaming-csv\n  private parseRow(line: string, validateElementCount = true): string[] {\n    const result: string[] = [];\n    let readOffset = 0;\n    const readLength = line.length;\n    let currentState = STATE_OUT;\n    // Goes through the line to parse quote.\n    for (let i = 0; i < readLength; i++) {\n      switch (currentState) {\n        // Before enter a new field\n        case STATE_OUT:\n          switch (line.charAt(i)) {\n            // Enter a quoted field\n            case CODE_QUOTE:\n              readOffset = i + 1;\n              currentState = STATE_QUOTE;\n              break;\n            // Read an empty field\n            case this.delimiter:\n              readOffset = i + 1;\n              // If delimiter is white space and configured to collapse\n              // multiple white spaces, ignore this white space.\n              if (this.delimiter === ' ' && this.delimWhitespace) {\n                break;\n              }\n              result.push('');\n              currentState = STATE_OUT;\n              break;\n            // Enter an unquoted field\n            default:\n              currentState = STATE_FIELD;\n              readOffset = i;\n              break;\n          }\n          break;\n        // In an unquoted field\n        case STATE_FIELD:\n          switch (line.charAt(i)) {\n            // Exit an unquoted field, add it to result\n            case this.delimiter:\n              result.push(line.substring(readOffset, i));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            default:\n          }\n          break;\n        // In a quoted field\n        case STATE_QUOTE:\n          switch (line.charAt(i)) {\n            // Read a quote after a quote\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE_AFTER_QUOTE;\n              break;\n            default:\n          }\n          break;\n        // This state means it's right after a second quote in a field\n        case STATE_QUOTE_AFTER_QUOTE:\n          switch (line.charAt(i)) {\n            // Finished a quoted field\n            case this.delimiter:\n              result.push(line.substring(readOffset, i - 1));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            // Finished a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            // In a quoted part in a quoted field\n            default:\n              currentState = STATE_WITHIN_QUOTE_IN_QUOTE;\n              break;\n          }\n          break;\n        case STATE_WITHIN_QUOTE_IN_QUOTE:\n          switch (line.charAt(i)) {\n            // Exit a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            default:\n          }\n          break;\n        default:\n      }\n    }\n    // Adds last item based on if it is quoted.\n    if (currentState === STATE_QUOTE_AFTER_QUOTE) {\n      result.push(line.substring(readOffset, readLength - 1));\n    } else {\n      result.push(line.substring(readOffset));\n    }\n    // Check if each row has the same number of elements as column names.\n    if (validateElementCount && result.length !== this.fullColumnNames.length) {\n      throw new Error(`Invalid row in csv file. Should have ${\n          this.fullColumnNames.length} elements in a row, but got ${result}`);\n    }\n    return result;\n  }\n}\n\n// TODO(soergel): add more basic datasets for parity with tf.data\n// tf.data.FixedLengthRecordDataset()\n// tf.data.TFRecordDataset()\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env, Tensor, tensor, Tensor2D, Tensor3D, TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {MicrophoneConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of tensors from microphone audio stream. The tensors are\n * representing audio data as frequency-domain spectrogram generated with\n * browser's native FFT. Tensors representing time-domain waveform is available\n * based on configuration. Only works in browser environment.\n */\nexport class MicrophoneIterator extends LazyIterator<TensorContainer> {\n  private isClosed = false;\n  private stream: MediaStream;\n  private readonly fftSize: number;\n  private readonly columnTruncateLength: number;\n  private freqData: Float32Array;\n  private timeData: Float32Array;\n  private readonly numFrames: number;\n  private analyser: AnalyserNode;\n  private audioContext: AudioContext;\n  private sampleRateHz: number;\n  private readonly audioTrackConstraints: MediaTrackConstraints;\n  private readonly smoothingTimeConstant: number;\n  private readonly includeSpectrogram: boolean;\n  private readonly includeWaveform: boolean;\n\n  private constructor(protected readonly microphoneConfig: MicrophoneConfig) {\n    super();\n    this.fftSize = microphoneConfig.fftSize || 1024;\n    const fftSizeLog2 = Math.log2(this.fftSize);\n    if (this.fftSize < 0 || fftSizeLog2 < 4 || fftSizeLog2 > 14 ||\n        !Number.isInteger(fftSizeLog2)) {\n      throw new Error(\n          `Invalid fftSize: it must be a power of 2 between ` +\n          `2 to 4 and 2 to 14, but got ${this.fftSize}`);\n    }\n\n    this.numFrames = microphoneConfig.numFramesPerSpectrogram || 43;\n    this.sampleRateHz = microphoneConfig.sampleRateHz;\n    this.columnTruncateLength =\n        microphoneConfig.columnTruncateLength || this.fftSize;\n    this.audioTrackConstraints = microphoneConfig.audioTrackConstraints;\n    this.smoothingTimeConstant = microphoneConfig.smoothingTimeConstant || 0;\n\n    this.includeSpectrogram =\n        microphoneConfig.includeSpectrogram === false ? false : true;\n    this.includeWaveform =\n        microphoneConfig.includeWaveform === true ? true : false;\n    if (!this.includeSpectrogram && !this.includeWaveform) {\n      throw new Error(\n          'Both includeSpectrogram and includeWaveform are false. ' +\n          'At least one type of data should be returned.');\n    }\n  }\n\n  summary() {\n    return `microphone`;\n  }\n\n  // Construct a MicrophoneIterator and start the audio stream.\n  static async create(microphoneConfig: MicrophoneConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'microphone API is only supported in browser environment.');\n    }\n\n    const microphoneIterator = new MicrophoneIterator(microphoneConfig);\n\n    // Call async function start() to initialize the audio stream.\n    await microphoneIterator.start();\n\n    return microphoneIterator;\n  }\n\n  // Start the audio stream and FFT.\n  async start(): Promise<void> {\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        audio: this.audioTrackConstraints == null ? true :\n                                                    this.audioTrackConstraints,\n        video: false\n      });\n    } catch (e) {\n      throw new Error(\n          `Error thrown while initializing video stream: ${e.message}`);\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain audio from microphone.');\n    }\n\n    const ctxConstructor =\n        // tslint:disable-next-line:no-any\n        (window as any).AudioContext || (window as any).webkitAudioContext;\n    this.audioContext = new ctxConstructor();\n\n    if (!this.sampleRateHz) {\n      // If sample rate is not provided, use the available sample rate on\n      // device.\n      this.sampleRateHz = this.audioContext.sampleRate;\n    } else if (this.audioContext.sampleRate !== this.sampleRateHz) {\n      throw new Error(\n          `Mismatch in sampling rate: ` +\n          `Expected: ${this.sampleRateHz}; ` +\n          `Actual: ${this.audioContext.sampleRate}`);\n    }\n\n    const streamSource = this.audioContext.createMediaStreamSource(this.stream);\n    this.analyser = this.audioContext.createAnalyser();\n    this.analyser.fftSize = this.fftSize * 2;\n    this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;\n    streamSource.connect(this.analyser);\n    this.freqData = new Float32Array(this.fftSize);\n    this.timeData = new Float32Array(this.fftSize);\n    return;\n  }\n\n  async next(): Promise<IteratorResult<TensorContainer>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let spectrogramTensor: Tensor;\n    let waveformTensor: Tensor;\n\n    const audioDataQueue = await this.getAudioData();\n    if (this.includeSpectrogram) {\n      const freqData = this.flattenQueue(audioDataQueue.freqDataQueue);\n      spectrogramTensor = this.getTensorFromAudioDataArray(\n          freqData, [this.numFrames, this.columnTruncateLength, 1]);\n    }\n    if (this.includeWaveform) {\n      const timeData = this.flattenQueue(audioDataQueue.timeDataQueue);\n      waveformTensor = this.getTensorFromAudioDataArray(\n          timeData, [this.numFrames * this.fftSize, 1]);\n    }\n\n    return {\n      value: {'spectrogram': spectrogramTensor, 'waveform': waveformTensor},\n      done: false\n    };\n  }\n\n  // Capture one result from the audio stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<{spectrogram: Tensor3D, waveform: Tensor2D}> {\n    return (await this.next()).value as\n        {spectrogram: Tensor3D, waveform: Tensor2D};\n  }\n\n  private async getAudioData():\n      Promise<{freqDataQueue: Float32Array[], timeDataQueue: Float32Array[]}> {\n    const freqDataQueue: Float32Array[] = [];\n    const timeDataQueue: Float32Array[] = [];\n    let currentFrames = 0;\n    return new Promise(resolve => {\n      const intervalID = setInterval(() => {\n        if (this.includeSpectrogram) {\n          this.analyser.getFloatFrequencyData(this.freqData);\n          // If the audio stream is initializing, return empty queue.\n          if (this.freqData[0] === -Infinity) {\n            resolve({freqDataQueue, timeDataQueue});\n          }\n          freqDataQueue.push(this.freqData.slice(0, this.columnTruncateLength));\n        }\n        if (this.includeWaveform) {\n          this.analyser.getFloatTimeDomainData(this.timeData);\n          timeDataQueue.push(this.timeData.slice());\n        }\n\n        // Clean interval and return when all frames have been collected\n        if (++currentFrames === this.numFrames) {\n          clearInterval(intervalID);\n          resolve({freqDataQueue, timeDataQueue});\n        }\n      }, this.fftSize / this.sampleRateHz * 1e3);\n    });\n  }\n\n  // Stop the audio stream and pause the iterator.\n  stop(): void {\n    if (!this.isClosed) {\n      this.isClosed = true;\n      this.analyser.disconnect();\n      this.audioContext.close();\n      if (this.stream != null && this.stream.getTracks().length > 0) {\n        this.stream.getTracks()[0].stop();\n      }\n    }\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor[]> {\n    throw new Error('Can not convert infinite audio stream to array.');\n  }\n\n  // Return audio sampling rate in Hz\n  getSampleRate(): number {\n    return this.sampleRateHz;\n  }\n\n  private flattenQueue(queue: Float32Array[]): Float32Array {\n    const frameSize = queue[0].length;\n    const freqData = new Float32Array(queue.length * frameSize);\n    queue.forEach((data, i) => freqData.set(data, i * frameSize));\n    return freqData;\n  }\n\n  private getTensorFromAudioDataArray(freqData: Float32Array, shape: number[]):\n      Tensor {\n    const vals = new Float32Array(util.sizeFromShape(shape));\n    // If the data is less than the output shape, the rest is padded with zeros.\n    vals.set(freqData, vals.length - freqData.length);\n    return tensor(vals, shape);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {browser, cast, env, expandDims, image, reshape, tensor1d, Tensor1D, tensor2d, Tensor2D, Tensor3D, Tensor4D, tidy, util} from '@tensorflow/tfjs-core';\nimport {WebcamConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of image tensors from webcam video stream. Only works in\n * browser environment.\n */\nexport class WebcamIterator extends LazyIterator<Tensor3D> {\n  private isClosed = true;\n  private stream: MediaStream;\n  private resize = false;\n  private cropSize: [number, number];\n  private cropBox: Tensor2D;\n  private cropBoxInd: Tensor1D;\n\n  private constructor(\n      protected readonly webcamVideoElement: HTMLVideoElement,\n      protected readonly webcamConfig: WebcamConfig) {\n    super();\n    if (this.needToResize()) {\n      this.resize = true;\n      this.cropSize =\n          [this.webcamConfig.resizeHeight, this.webcamConfig.resizeWidth];\n      this.cropBoxInd = tensor1d([0], 'int32');\n      if (this.webcamConfig.centerCrop) {\n        // Calculate the box based on resizing shape.\n        const widthCroppingRatio =\n            this.webcamConfig.resizeWidth * 1.0 / this.webcamVideoElement.width;\n        const heightCroppingRatio = this.webcamConfig.resizeHeight * 1.0 /\n            this.webcamVideoElement.height;\n        const widthCropStart = (1 - widthCroppingRatio) / 2;\n        const heightCropStart = (1 - heightCroppingRatio) / 2;\n        const widthCropEnd = widthCropStart + widthCroppingRatio;\n        const heightCropEnd = heightCroppingRatio + heightCropStart;\n        this.cropBox = tensor2d(\n            [heightCropStart, widthCropStart, heightCropEnd, widthCropEnd],\n            [1, 4]);\n      } else {\n        this.cropBox = tensor2d([0, 0, 1, 1], [1, 4]);\n      }\n    }\n  }\n\n  summary() {\n    return `webcam`;\n  }\n\n  // Construct a WebcamIterator and start it's video stream.\n  static async create(\n      webcamVideoElement?: HTMLVideoElement, webcamConfig: WebcamConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'tf.data.webcam is only supported in browser environment.');\n    }\n\n    if (!webcamVideoElement) {\n      // If webcam video element is not provided, create a hidden video element\n      // with provided width and height.\n      webcamVideoElement = document.createElement('video');\n      if (!webcamConfig.resizeWidth || !webcamConfig.resizeHeight) {\n        throw new Error(\n            'Please provide webcam video element, or resizeWidth and ' +\n            'resizeHeight to create a hidden video element.');\n      }\n      webcamVideoElement.width = webcamConfig.resizeWidth;\n      webcamVideoElement.height = webcamConfig.resizeHeight;\n    }\n    const webcamIterator = new WebcamIterator(webcamVideoElement, webcamConfig);\n\n    // Call async function to initialize the video stream.\n    await webcamIterator.start();\n\n    return webcamIterator;\n  }\n\n  // Async function to start video stream.\n  async start(): Promise<void> {\n    if (this.webcamConfig.facingMode) {\n      util.assert(\n          (this.webcamConfig.facingMode === 'user') ||\n              (this.webcamConfig.facingMode === 'environment'),\n          () =>\n              `Invalid webcam facing mode: ${this.webcamConfig.facingMode}. ` +\n              `Please provide 'user' or 'environment'`);\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          deviceId: this.webcamConfig.deviceId,\n          facingMode: this.webcamConfig.facingMode ?\n              this.webcamConfig.facingMode :\n              'user',\n          width: this.webcamVideoElement.width,\n          height: this.webcamVideoElement.height\n        }\n      });\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message = `Error thrown while initializing video stream: ${e.message}`;\n      throw e;\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain video from webcam.');\n    }\n\n    // Older browsers may not have srcObject\n    try {\n      this.webcamVideoElement.srcObject = this.stream;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = window.URL.createObjectURL(\n        this.stream as unknown as MediaSource);\n    }\n    // Start the webcam video stream\n    this.webcamVideoElement.play();\n\n    this.isClosed = false;\n\n    return new Promise<void>(resolve => {\n      // Add event listener to make sure the webcam has been fully initialized.\n      this.webcamVideoElement.onloadedmetadata = () => {\n        resolve();\n      };\n    });\n  }\n\n  async next(): Promise<IteratorResult<Tensor3D>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let img;\n    try {\n      img = browser.fromPixels(this.webcamVideoElement);\n    } catch (e) {\n      throw new Error(\n          `Error thrown converting video to pixels: ${JSON.stringify(e)}`);\n    }\n    if (this.resize) {\n      try {\n        return {value: this.cropAndResizeFrame(img), done: false};\n      } catch (e) {\n        throw new Error(`Error thrown cropping the video: ${e.message}`);\n      } finally {\n        img.dispose();\n      }\n    } else {\n      return {value: img, done: false};\n    }\n  }\n\n  private needToResize() {\n    // If resizeWidth and resizeHeight are provided, and different from the\n    // width and height of original HTMLVideoElement, then resizing and cropping\n    // is required.\n    if (this.webcamConfig.resizeWidth && this.webcamConfig.resizeHeight &&\n        (this.webcamVideoElement.width !== this.webcamConfig.resizeWidth ||\n         this.webcamVideoElement.height !== this.webcamConfig.resizeHeight)) {\n      return true;\n    }\n    return false;\n  }\n\n  // Cropping and resizing each frame based on config\n  cropAndResizeFrame(img: Tensor3D): Tensor3D {\n    return tidy(() => {\n      const expandedImage: Tensor4D = expandDims(cast(img, 'float32'), (0));\n      let resizedImage;\n      resizedImage = image.cropAndResize(\n          expandedImage, this.cropBox, this.cropBoxInd, this.cropSize,\n          'bilinear');\n      // Extract image from batch cropping.\n      const shape = resizedImage.shape;\n      return reshape(resizedImage, shape.slice(1) as [number, number, number]);\n    });\n  }\n\n  // Capture one frame from the video stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<Tensor3D> {\n    return (await this.next()).value;\n  }\n\n  // Stop the video stream and pause webcam iterator.\n  stop(): void {\n    const tracks = this.stream.getTracks();\n\n    tracks.forEach(track => track.stop());\n\n    try {\n      this.webcamVideoElement.srcObject = null;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = null;\n    }\n    this.isClosed = true;\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor3D[]> {\n    throw new Error('Can not convert infinite video stream to array.');\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {ByteChunkIterator} from './iterators/byte_chunk_iterator';\n\n/**\n * Represents a data source readable as a stream of binary data chunks.\n *\n * Because `Dataset`s can be read repeatedly (via `Dataset.iterator()`), this\n * provides a means to repeatedly create streams from the underlying data\n * sources.\n */\nexport abstract class DataSource {\n  /**\n   * Obtain a new stream of binary data chunks.\n   *\n   * Starts the new stream from the beginning of the data source, even if other\n   * streams have been obtained previously.\n   */\n  abstract iterator(): Promise<ByteChunkIterator>;\n\n  // TODO(soergel): consider chainable Dataset construction here\n}\n\n// TODO(soergel): consider convenience factory functions here\n// in combination with chainable source->dataset above, e.g.:\n// tf.data.url(...).asCsvDataset().shuffle().batch()\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\n\nexport abstract class StringIterator extends LazyIterator<string> {\n  /**\n   * Splits a string stream on a given separator.\n   *\n   * It is assumed that the incoming chunk boundaries have no semantic meaning,\n   * so conceptually the incoming stream is treated simply as the concatenation\n   * of its elements.\n   *\n   * The outgoing stream provides chunks corresponding to the results of the\n   * standard string split() operation (even if such a chunk spanned incoming\n   * chunks).  The separators are not included.\n   *\n   * A typical usage is to split a text file (represented as a stream with\n   * arbitrary chunk boundaries) into lines.\n   *\n   * @param upstream A readable stream of strings that can be treated as\n   *   concatenated.\n   * @param separator A character to split on.\n   */\n  split(separator: string): StringIterator {\n    return new SplitIterator(this, separator);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on StringIterator.  Unfortunately they can't be placed in separate files, due\n// to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class SplitIterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass SplitIterator extends StringIterator {\n  private impl: SplitIteratorImpl;\n\n  constructor(protected upstream: LazyIterator<string>, separator: string) {\n    super();\n    this.impl = new SplitIteratorImpl(upstream, separator);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\nclass SplitIteratorImpl extends OneToManyIterator<string> {\n  // A partial string at the end of an upstream chunk\n  carryover = '';\n\n  constructor(\n      protected upstream: LazyIterator<string>, protected separator: string) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Split('${this.separator}')`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    if (chunkResult.done) {\n      if (this.carryover === '') {\n        return false;\n      }\n\n      // Pretend that the pump succeeded in order to emit the small last batch.\n      // The next pump() call will actually fail.\n      this.outputQueue.push(this.carryover);\n      this.carryover = '';\n      return true;\n    }\n    const lines = chunkResult.value.split(this.separator) as string[];\n    // Note the behavior: \" ab \".split(' ') === ['', 'ab', '']\n    // Thus the carryover may be '' if the separator falls on a chunk\n    // boundary; this produces the correct result.\n\n    lines[0] = this.carryover + lines[0];\n    for (const line of lines.slice(0, -1)) {\n      this.outputQueue.push(line);\n    }\n    this.carryover = lines[lines.length - 1];\n\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\nimport {StringIterator} from './string_iterator';\n\nexport abstract class ByteChunkIterator extends LazyIterator<Uint8Array> {\n  /**\n   * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n   *\n   * The byte arrays producetd from the ByteChunkIterator on which this is\n   * called will be interpreted as concatenated.  No assumptions are made about\n   * the boundaries of the incoming chunks, so a multi-byte UTF8 encoding of a\n   * character may span the boundary between chunks.  This naturally happens,\n   * for instance, when reading fixed-size byte arrays from a file.\n   */\n  decodeUTF8(): StringIterator {\n    return new Utf8Iterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on ByteChunkIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class Utf8Iterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass Utf8Iterator extends StringIterator {\n  private impl: Utf8IteratorImpl;\n\n  constructor(protected upstream: LazyIterator<Uint8Array>) {\n    super();\n    this.impl = new Utf8IteratorImpl(upstream);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\n/**\n * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n *\n * This is tricky because the incoming byte array boundaries may disrupt a\n * multi-byte UTF8 character. Thus any incomplete character data at the end of\n * a chunk must be carried over and prepended to the next chunk before\n * decoding. Luckily with native decoder, TextDecoder in browser and\n * string_decoder in node, byte array boundaries are handled automatically.\n *\n * In the context of an input pipeline for machine learning, UTF8 decoding is\n * needed to parse text files containing training examples or prediction\n * requests (e.g., formatted as CSV or JSON). We cannot use the built-in\n * decoding provided by FileReader.readAsText() because here we are in a\n * streaming context, which FileReader does not support.\n *\n * @param upstream A `LazyIterator` of `Uint8Arrays` containing UTF8-encoded\n *   text, which should be interpreted as concatenated.  No assumptions are\n *   made about the boundaries of the incoming chunks, so a multi-byte UTF8\n *   encoding of a character may span the boundary between chunks.  This\n *   naturally happens, for instance, when reading fixed-size byte arrays from a\n *   file.\n */\nclass Utf8IteratorImpl extends OneToManyIterator<string> {\n  // `decoder` as `any` here to dynamically assign value based on the\n  // environment.\n  // tslint:disable-next-line:no-any\n  decoder: any;\n\n  constructor(protected readonly upstream: LazyIterator<Uint8Array>) {\n    super();\n    if (env().get('IS_BROWSER')) {\n      this.decoder = new TextDecoder('utf-8');\n    } else {\n      // tslint:disable-next-line:no-require-imports\n      const {StringDecoder} = require('string_decoder');\n      this.decoder = new StringDecoder('utf8');\n    }\n  }\n  summary() {\n    return `${this.upstream.summary()} -> Utf8`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    let chunk;\n    if (chunkResult.done) {\n      return false;\n    } else {\n      chunk = chunkResult.value;\n    }\n\n    let text: string;\n    if (env().get('IS_BROWSER')) {\n      text = this.decoder.decode(chunk, {stream: true});\n    } else {\n      text = this.decoder.write(Buffer.from(chunk.buffer));\n    }\n    this.outputQueue.push(text);\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// Skip tslint any type check cause this method is aiming to check type of\n// input.\n// tslint:disable-next-line:no-any\nexport function isLocalPath(source: any): boolean {\n  return (typeof source === 'string') && source.slice(0, 7) === 'file://';\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {urlChunkIterator} from '../iterators/url_chunk_iterator';\nimport {isLocalPath} from '../util/source_util';\nimport {FileDataSource} from './file_data_source';\n\n/*\n * Represents a URL readable as a stream of binary data chunks.\n */\nexport class URLDataSource extends DataSource {\n  /**\n   * Create a `URLDataSource`.\n   *\n   * @param url A source URL string, or a `Request` object.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected readonly url: RequestInfo,\n      protected readonly fileOptions: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  // TODO(soergel): provide appropriate caching options.  Currently this\n  // will download the URL anew for each call to iterator().  Since we have\n  // to treat the downloaded file as a blob/buffer anyway, we may as well retain\n  // it-- but that raises GC issues.  Also we may want a persistent disk cache.\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.url)) {\n      return (new FileDataSource(this.url as string, this.fileOptions))\n          .iterator();\n    } else {\n      return urlChunkIterator(this.url, this.fileOptions);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer} from '@tensorflow/tfjs-core';\nimport {Dataset, datasetFromIteratorFn} from './dataset';\nimport {CSVDataset} from './datasets/csv_dataset';\nimport {iteratorFromFunction} from './iterators/lazy_iterator';\nimport {MicrophoneIterator} from './iterators/microphone_iterator';\nimport {WebcamIterator} from './iterators/webcam_iterator';\nimport {URLDataSource} from './sources/url_data_source';\nimport {CSVConfig, MicrophoneConfig, WebcamConfig} from './types';\n\n/**\n * Create a `CSVDataset` by reading and decoding CSV file(s) from provided URL\n * or local path if it's in Node environment.\n *\n * Note: If isLabel in columnConfigs is `true` for at least one column, the\n * element in returned `CSVDataset` will be an object of\n * `{xs:features, ys:labels}`: xs is a dict of features key/value pairs, ys\n * is a dict of labels key/value pairs. If no column is marked as label,\n * returns a dict of features only.\n *\n * ```js\n * const csvUrl =\n * 'https://storage.googleapis.com/tfjs-examples/multivariate-linear-regression/data/boston-housing-train.csv';\n *\n * async function run() {\n *   // We want to predict the column \"medv\", which represents a median value of\n *   // a home (in $1000s), so we mark it as a label.\n *   const csvDataset = tf.data.csv(\n *     csvUrl, {\n *       columnConfigs: {\n *         medv: {\n *           isLabel: true\n *         }\n *       }\n *     });\n *\n *   // Number of features is the number of column names minus one for the label\n *   // column.\n *   const numOfFeatures = (await csvDataset.columnNames()).length - 1;\n *\n *   // Prepare the Dataset for training.\n *   const flattenedDataset =\n *     csvDataset\n *     .map(({xs, ys}) =>\n *       {\n *         // Convert xs(features) and ys(labels) from object form (keyed by\n *         // column name) to array form.\n *         return {xs:Object.values(xs), ys:Object.values(ys)};\n *       })\n *     .batch(10);\n *\n *   // Define the model.\n *   const model = tf.sequential();\n *   model.add(tf.layers.dense({\n *     inputShape: [numOfFeatures],\n *     units: 1\n *   }));\n *   model.compile({\n *     optimizer: tf.train.sgd(0.000001),\n *     loss: 'meanSquaredError'\n *   });\n *\n *   // Fit the model using the prepared Dataset\n *   return model.fitDataset(flattenedDataset, {\n *     epochs: 10,\n *     callbacks: {\n *       onEpochEnd: async (epoch, logs) => {\n *         console.log(epoch + ':' + logs.loss);\n *       }\n *     }\n *   });\n * }\n *\n * await run();\n * ```\n *\n * @param source URL or local path to get CSV file. If it's a local path, it\n * must have prefix `file://` and it only works in node environment.\n * @param csvConfig (Optional) A CSVConfig object that contains configurations\n *     of reading and decoding from CSV file(s).\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function csv(\n    source: RequestInfo, csvConfig: CSVConfig = {}): CSVDataset {\n  return new CSVDataset(new URLDataSource(source), csvConfig);\n}\n\n/**\n * Create a `Dataset` that produces each element by calling a provided function.\n *\n * Note that repeated iterations over this `Dataset` may produce different\n * results, because the function will be called anew for each element of each\n * iteration.\n *\n * Also, beware that the sequence of calls to this function may be out of order\n * in time with respect to the logical order of the Dataset. This is due to the\n * asynchronous lazy nature of stream processing, and depends on downstream\n * transformations (e.g. .shuffle()). If the provided function is pure, this is\n * no problem, but if it is a closure over a mutable state (e.g., a traversal\n * pointer), then the order of the produced elements may be scrambled.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const ds = tf.data.func(func);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param f A function that produces one data element on each call.\n */\nexport function func<T extends TensorContainer>(\n    f: () => IteratorResult<T>| Promise<IteratorResult<T>>): Dataset<T> {\n  const iter = iteratorFromFunction(f);\n  return datasetFromIteratorFn(async () => iter);\n}\n\n/**\n * Create a `Dataset` that produces each element from provided JavaScript\n * generator, which is a function that returns a (potentially async) iterator.\n *\n * For more information on iterators and generators, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Iterators_and_Generators .\n * For the iterator protocol, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols .\n *\n * Example of creating a dataset from an iterator factory:\n * ```js\n * function makeIterator() {\n *   const numElements = 10;\n *   let index = 0;\n *\n *   const iterator = {\n *     next: () => {\n *       let result;\n *       if (index < numElements) {\n *         result = {value: index, done: false};\n *         index++;\n *         return result;\n *       }\n *       return {value: index, done: true};\n *     }\n *   };\n *   return iterator;\n * }\n * const ds = tf.data.generator(makeIterator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * Example of creating a dataset from a generator:\n * ```js\n * function* dataGenerator() {\n *   const numElements = 10;\n *   let index = 0;\n *   while (index < numElements) {\n *     const x = index;\n *     index++;\n *     yield x;\n *   }\n * }\n *\n * const ds = tf.data.generator(dataGenerator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param generator A JavaScript function that returns\n *     a (potentially async) JavaScript iterator.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function generator<T extends TensorContainer>(\n  generator: () => Iterator<T> | Promise<Iterator<T>> | AsyncIterator<T>,\n): Dataset<T> {\n  return datasetFromIteratorFn(async () => {\n    const gen = await generator();\n    return iteratorFromFunction(() => gen.next());\n  });\n}\n\n/**\n * Create an iterator that generates `Tensor`s from webcam video stream. This\n * API only works in Browser environment when the device has webcam.\n *\n * Note: this code snippet only works when the device has a webcam. It will\n * request permission to open the webcam when running.\n * ```js\n * const videoElement = document.createElement('video');\n * videoElement.width = 100;\n * videoElement.height = 100;\n * const cam = await tf.data.webcam(videoElement);\n * const img = await cam.capture();\n * img.print();\n * cam.stop();\n * ```\n *\n * @param webcamVideoElement A `HTMLVideoElement` used to play video from\n *     webcam. If this element is not provided, a hidden `HTMLVideoElement` will\n *     be created. In that case, `resizeWidth` and `resizeHeight` must be\n *     provided to set the generated tensor shape.\n * @param webcamConfig A `WebcamConfig` object that contains configurations of\n *     reading and manipulating data from webcam video stream.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function webcam(\n    webcamVideoElement?: HTMLVideoElement,\n    webcamConfig?: WebcamConfig): Promise<WebcamIterator> {\n  return WebcamIterator.create(webcamVideoElement, webcamConfig);\n}\n\n/**\n * Create an iterator that generates frequency-domain spectrogram `Tensor`s from\n * microphone audio stream with browser's native FFT. This API only works in\n * browser environment when the device has microphone.\n *\n * Note: this code snippet only works when the device has a microphone. It will\n * request permission to open the microphone when running.\n * ```js\n * const mic = await tf.data.microphone({\n *   fftSize: 1024,\n *   columnTruncateLength: 232,\n *   numFramesPerSpectrogram: 43,\n *   sampleRateHz:44100,\n *   includeSpectrogram: true,\n *   includeWaveform: true\n * });\n * const audioData = await mic.capture();\n * const spectrogramTensor = audioData.spectrogram;\n * spectrogramTensor.print();\n * const waveformTensor = audioData.waveform;\n * waveformTensor.print();\n * mic.stop();\n * ```\n *\n * @param microphoneConfig A `MicrophoneConfig` object that contains\n *     configurations of reading audio data from microphone.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function microphone(microphoneConfig?: MicrophoneConfig):\n    Promise<MicrophoneIterator> {\n  return MicrophoneIterator.create(microphoneConfig);\n}\n", "/** @license See the LICENSE file. */\n\n// This code is auto-generated, do not modify this file!\nconst version = '4.22.0';\nexport {version};\n"], "names": ["this", "define", "require$$0", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "require$$6", "seedrandom.alea"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,IAAI,CAAC,IAAI,EAAE;GAClB,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAC/B;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;AACvB,KAAI,IAAI,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,sBAAsB,CAAC;AAC5D,KAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAClB,KAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAClB,KAAI,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,IAAG,CAAC;AACJ;AACA;AACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;GACT,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;GAClB,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;GAClB,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;GAClB,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;GAC9B,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;GAC9B,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;GAC9B,IAAI,GAAG,IAAI,CAAC;EACb;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AACd,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AACd,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;GACZ,OAAO,CAAC,CAAC;EACV;AACD;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;AACzB,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACrB,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC,GAAE;AACnE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,OAAO,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAsB,CAAC;AACrE,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;AACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,SAAS,IAAI,GAAG;AAChB,GAAE,IAAI,CAAC,GAAG,UAAU,CAAC;AACrB;AACA,GAAE,IAAI,IAAI,GAAG,SAAS,IAAI,EAAE;AAC5B,KAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACxB,KAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;OACpC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9B,OAAM,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC;AACtC,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;OACZ,CAAC,IAAI,CAAC,CAAC;OACP,CAAC,IAAI,CAAC,CAAC;AACb,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;OACZ,CAAC,IAAI,CAAC,CAAC;AACb,OAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;MACtB;AACL,KAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;AAC9C,IAAG,CAAC;AACJ;GACE,OAAO,IAAI,CAAC;EACb;AACD;AACA;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;EAClB;AACD;EACC;AACD,GAAEA,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;AC/GD;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,MAAM,CAAC,IAAI,EAAE;GACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;AACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX;AACA;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;AACvB,KAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAChC,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAChB,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAChB,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAChB,KAAI,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,IAAG,CAAC;AACJ;AACA,GAAE,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;AAC3B;AACA,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,IAAG,MAAM;AACT;KACI,OAAO,IAAI,IAAI,CAAC;IACjB;AACH;AACA;AACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;IACX;EACF;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,OAAO,CAAC,CAAC;EACV;AACD;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;AAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,GAAG;OACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;WACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;WACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;KACvB,OAAO,MAAM,CAAC;AAClB,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;AACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;AACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACpB;AACD;EACC;AACD,GAAED,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;AC9ED;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,MAAM,CAAC,IAAI,EAAE;GACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;AACA;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;AACvB,KAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACvD,KAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;SAC9B,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAG,CAAC;AACJ;AACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX;AACA,GAAE,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;AAC3B;AACA,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,IAAG,MAAM;AACT;KACI,OAAO,IAAI,IAAI,CAAC;IACjB;AACH;AACA;AACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,KAAI,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;AAC7B,OAAM,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;MAChC;AACL,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;IACX;EACF;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,OAAO,CAAC,CAAC;EACV;AACD;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;AAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,GAAG;OACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;WACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;WACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;KACvB,OAAO,MAAM,CAAC;AAClB,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;AACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;AACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACpB;AACD;EACC;AACD,GAAED,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;ACnFD;AACA;AACA;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,MAAM,CAAC,IAAI,EAAE;AACtB,GAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB;AACA;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;AACvB;AACA,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAE,CAAA,CAAC,EAAE,CAAC,CAAI;KAChC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KAC5C,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;KACxC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACvC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACT,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACnB,OAAO,CAAC,CAAC;AACb,IAAG,CAAC;AACJ;AACA,GAAE,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE;KACtB,IAAI,CAAC,CAAE,CAAG,CAAC,GAAG,GAAG;AACrB;AACA,KAAI,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;AAC7B;OACU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACtB,MAAK,MAAM;AACX;AACA,OAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;AACvB,OAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACxC,SAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;AAClC,cAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACjD;MACF;AACL;AACA,KAAI,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,KAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KACtC,IAAI,CAAC,IAAI,CAAC,EAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb;AACA;KACI,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAC9B,OAAM,EAAE,CAAC,IAAI,EAAE,CAAC;MACX;IACF;AACH;AACA,GAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAChB;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;GAClB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,OAAO,CAAC,CAAC;EACV;AACD;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;GACxB,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;AACvC,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;AAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,GAAG;OACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;WACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;WACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;KACvB,OAAO,MAAM,CAAC;AAClB,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;AACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;KACT,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACjC,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;EACvB;AACD;EACC;AACD,GAAED,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;AC/FD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,MAAM,CAAC,IAAI,EAAE;AACtB,GAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB;AACA;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;AACvB,KAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAChB,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjC;AACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;AACpC;KACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAC1B,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAC/B,KAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACjB,KAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACjB,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAClB,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAClB;KACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrB,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb;AACA,KAAI,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACtC,IAAG,CAAC;AACJ;AACA,GAAE,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE;AAC1B,KAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;AAC3C,KAAI,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;AAC7B;OACM,CAAC,GAAG,IAAI,CAAC;OACT,IAAI,GAAG,IAAI,CAAC;AAClB,MAAK,MAAM;AACX;AACA,OAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;OACnB,CAAC,GAAG,CAAC,CAAC;AACZ,OAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;MACtC;AACL;AACA,KAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;AACzC;AACA,OAAM,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7D;OACM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACzB,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACnB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACpB,OAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACpB,OAAM,IAAI,CAAC,IAAI,CAAC,EAAE;SACV,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;AACjC,SAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,SAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1B;MACF;AACL;AACA,KAAI,IAAI,CAAC,IAAI,GAAG,EAAE;AAClB,OAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1C;AACL;AACA;AACA;KACI,CAAC,GAAG,GAAG,CAAC;AACZ,KAAI,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;OAC5B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;AAC5B,OAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AACjC,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACnB,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACnB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACpB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;OACd,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACd;AACL;AACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACV;AACH;AACA,GAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAChB;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;GAClB,OAAO,CAAC,CAAC;AACX,EACA;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;GACxB,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;AACvC,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;AAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,GAAG;OACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;WACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;WACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;KACvB,OAAO,MAAM,CAAC;AAClB,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;AACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;KACT,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACjC,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACrB;AACD;EACC;AACD,GAAED,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;ACjJD;AACA;AACA;AACA;AACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;CACA,SAAS,MAAM,CAAC,IAAI,EAAE;GACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;AACA;AACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;KACnB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/C,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;KAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpB,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;KAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpB,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AAC1C,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;KAClC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAG,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX,GAAE,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;AACxB,GAAE,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;AACpB;GACE,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACjC;KACI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,CAAC;AACpC,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACpB,IAAG,MAAM;AACT;KACI,OAAO,IAAI,IAAI,CAAC;IACjB;AACH;AACA;AACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;IACX;EACF;AACD;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,OAAO,CAAC,CAAC;AACX,EACA;AACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;AAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;AAC3B,KAAI,GAAG;OACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;WACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;WACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;KACvB,OAAO,MAAM,CAAC;AAClB,IAAG,CAAC;AACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;AACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;GAClB,IAAI,KAAK,EAAE;AACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;IACjD;GACD,OAAO,IAAI,CAAC;EACb;AACD;AACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;GAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,EAAC,MAAM;AACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACpB;AACD;EACC;AACD,GAAED,cAAI;AACN,GAAiC,MAAM;AACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;EACxC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5ED,CAAA,CAAC,UAAU,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;AAC/B;AACA;AACA;AACA;CACA,IAAI,KAAK,GAAG,GAAG;KACX,MAAM,GAAG,CAAC;KACV,MAAM,GAAG,EAAE;KACX,OAAO,GAAG,QAAQ;KAClB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;KACpC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;AACtC,KAAI,QAAQ,GAAG,YAAY,GAAG,CAAC;AAC/B,KAAI,IAAI,GAAG,KAAK,GAAG,CAAC;AACpB,KAAI,UAAU,CAAC;AACf;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC7C,GAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,GAAE,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE,CAAC,CAAC;AACpE;AACA;AACA,GAAE,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO;KAC5B,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC5C,KAAI,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACjD;AACA;GACE,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B;AACA;AACA;GACE,IAAI,IAAI,GAAG,WAAW;KACpB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;SAClB,CAAC,GAAG,UAAU;SACd,CAAC,GAAG,CAAC,CAAC;AACd,KAAI,OAAO,CAAC,GAAG,YAAY,EAAE;OACvB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;OACpB,CAAC,IAAI,KAAK,CAAC;OACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;AACL,KAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;OACpB,CAAC,IAAI,CAAC,CAAC;OACP,CAAC,IAAI,CAAC,CAAC;OACP,CAAC,MAAM,CAAC,CAAC;MACV;AACL,KAAI,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,IAAG,CAAC;AACJ;AACA,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAE;AACnD,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAE;AAC7D,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB;AACA;GACE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC;AACA;AACA,GAAE,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ;OAC5B,SAAS,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE;SACxC,IAAI,KAAK,EAAE;AACnB;AACA,WAAU,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE;AAC7C;AACA,WAAU,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,GAAE;UACnD;AACT;AACA;AACA;AACA,SAAQ,IAAI,YAAY,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE;AAChE;AACA;AACA;cACa,OAAO,IAAI,CAAC;QAClB;AACP,GAAE,IAAI;AACN,GAAE,SAAS;GACT,QAAQ,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AACvD,GAAE,OAAO,CAAC,KAAK,CAAC,CAAC;EAChB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;CACA,SAAS,IAAI,CAAC,GAAG,EAAE;AACnB,GAAE,IAAI,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM;OACtB,EAAE,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAC3D;AACA;AACA,GAAE,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;AACpC;AACA;AACA,GAAE,OAAO,CAAC,GAAG,KAAK,EAAE;AACpB,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IACZ;GACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AAC9B,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACV;AACH;AACA;AACA,GAAE,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE;AAC1B;AACA,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;AAChB,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KACjC,OAAO,KAAK,EAAE,EAAE;AACpB,OAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,OAAM,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzE;AACL,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;KACnB,OAAO,CAAC,CAAC;AACb;AACA;AACA;IACG,EAAE,KAAK,CAAC,CAAC;EACX;AACD;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;GAClB,OAAO,CAAC,CAAC;AACX,EACA;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE;AAC7B,GAAE,IAAI,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;AAC5C,GAAE,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;AAChC,KAAI,KAAK,IAAI,IAAI,GAAG,EAAE;OAChB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;MACjE;IACF;AACH,GAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;EACtE;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;AAC3B,GAAE,IAAI,UAAU,GAAG,IAAI,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;AAC3C,GAAE,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE;AAChC,KAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;OACX,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE;AACH,GAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;EACtB;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,QAAQ,GAAG;AACpB,GAAE,IAAI;KACF,IAAI,GAAG,CAAC;KACR,IAAI,UAAU,KAAK,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE;AACtD;AACA,OAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACvB,MAAK,MAAM;AACX,OAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AAClC,OAAM,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;MACzD;AACL,KAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC,OAAO,CAAC,EAAE;AACd,KAAI,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS;AAClC,SAAQ,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAC7C,KAAI,OAAO,CAAC,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE;EACF;AACD;AACA;AACA;AACA;AACA;CACA,SAAS,QAAQ,CAAC,CAAC,EAAE;GACnB,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;CACA,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5B;AACA;AACA;AACA;AACA;CACA,IAAmC,MAAM,CAAC,OAAO,EAAE;GACjD,MAAA,CAAA,OAAA,GAAiB,UAAU,CAAC;AAC9B;AACA,GAAE,IAAI;KACF,UAAU,GAAG,UAAiB,CAAC;AACnC,IAAG,CAAC,OAAO,EAAE,EAAE,EAAE;EAChB,MAEM;AACP;GACE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC;EACrC;AACD;AACA;AACA;EACC;AACD;AACA;GACE,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAGD,cAAI;AAC7C,GAAE,EAAE;AACJ,GAAE,IAAI;EACL,CAAA;;;;;AC5PD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,GAAGE,WAAqB,CAAC;AACjC;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,GAAGC,gBAA0B,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAGC,cAAwB,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;AACA;AACA;AACA,IAAI,EAAE,GAAGC,iBAAuB,CAAC;AACjC;AACA,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AACf,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AACnB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AACnB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACzB,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;AACrB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AACnB;AACA,IAAA,UAAc,GAAG,EAAE;;AC3DnB;;;;;;;;;;;;;;;;AAgBG;AAiBH;;;;;;;;;;;;;;;;AAgBG;AACa,SAAA,OAAO,CAAC,KAAU,EAAE,KAAgC,EAAA;AAElE,IAAA,OAAO,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;AAED;;;;;AAKG;AACH,SAAS,eAAe,CACpB,KAAU,EAAE,KAAgC,EAC5C,IAAA,GAAsB,IAAI,GAAG,EAAE,EAAE,WAAuB,GAAA,IAAI,GAAG,EAAE,EAAA;IAEnE,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,EAAE;AACvD,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;AACtB,KAAA;AAED,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC3D,KAAA;AACD,IAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACnB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACxB,KAAA;AACD,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE5B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;AAC1E,KAAA;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC,KAAK,CAAC;AACrB,KAAA;AAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;AAE5B,QAAA,MAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;AACrB,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACvB,YAAA,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AACrE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;AACjC,SAAA;AACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,KAAK,CAAC,SAAS,EAAE;AACnB,YAAA,cAAc,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAC5C,SAAA;AACD,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,CAAA,CAAE,CAAC,CAAC;AACnE,KAAA;AACH,CAAC;AAED;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBG;SACa,OAAO,CACnB,MAAa,EAAE,QAAsC,SAAS,EAAA;AAChE,IAAA,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC;AAED;;;AAGG;AACH,SAAS,eAAe,CACpB,MAAa,EAAE,KAAmC,EAClD,WAAA,GAAuB,IAAI,GAAG,EAAE,EAAA;;;AAGlC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC3D,KAAA;AACD,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAE7B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;AAC1E,KAAA;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;QACnB,OAAO,MAAM,CAAC,KAAK,CAAC;AACrB,KAAA;AAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;AAE5B,QAAA,MAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;AACrB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAClE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;AACjC,SAAA;AACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,CAAA,CAAE,CAAC,CAAC;AACnE,KAAA;AACH,CAAC;AAED;AACM,SAAU,SAAS,CAAC,CAAQ,EAAA;IAChC,IAAI,CAAC,KAAK,IAAI,EAAE;AACd,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;AAGD,IAAA,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACrC,KAAA;AAAM,SAAA;QACL,OAAO,EAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;AACnC,KAAA;AACH,CAAC;AAaD;;;;;;;;;;;;;;;;;;;;;AAqBG;AACI,eAAe,kBAAkB,CACpC,KAAU,EAAE,KAAqC,EAAA;AACnD,IAAA,MAAM,IAAI,GAAkB,IAAI,GAAG,EAAE,CAAC;;AAGtC,IAAA,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;;;;AAMpC,IAAA,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC;AAChC,YAAA,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC5B,SAAA;AACF,KAAA;;;;IAKD,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACnD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;AAIG;AACH;AACM,SAAU,UAAU,CAAC,GAAQ,EAAA;IACjC,IAAI,aAAa,GAAG,KAAK,CAAC;IAC1B,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AAC9B,QAAA,aAAa,GAAG,GAAG,YAAY,WAAW,CAAC;AAC5C,KAAA;AAAM,SAAA;;QAEL,MAAM,EAAC,aAAa,EAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAClD,QAAA,aAAa,GAAG,GAAG,YAAY,aAAa,CAAC;AAC9C,KAAA;AACD,IAAA,OAAO,GAAG,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5C,SAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AAClB,aAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,EAAE,GAAG,YAAY,EAAE,CAAC,MAAM,CAAC;gBACtD,EAAE,GAAG,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;AAOG;AACH;AACM,SAAU,YAAY,CAAC,GAAQ,EAAA;AACnC,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACxD,SAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,GAAG,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;AACvD,QAAA,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED;;;AAGG;AACH,SAAS,WAAW,CAAC,KAAU,EAAA;IAC7B,QACI,KAAK,KAAK,IAAI;SACb,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE;AAClE;;ACpSA;;;;;;;;;;;;;;;;AAgBG;AAKG,SAAU,SAAS,CAAI,SAAY,EAAA;AACvC,IAAA,OAAO,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC3C,CAAC;AAED;AACA,SAAS,aAAa,CAAC,IAAS,EAAA;AAC9B,IAAA,IAAI,IAAI,YAAY,EAAE,CAAC,MAAM,EAAE;AAC7B,QAAA,QAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,EAAE;AAChD,KAAA;AAAM,SAAA,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACrC,KAAA;AAAM,SAAA;QACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;AACtC,KAAA;AACH;;AClCA;;;;;;;;;;;;;;;;AAgBG;AAEH;;AAEG;MACU,UAAU,CAAA;AAUrB;;;AAGG;AACH,IAAA,WAAA,CAAmB,QAAgB,EAAA;QAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;;;AAVzB,QAAA,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;AACV,QAAA,IAAA,CAAA,GAAG,GAAG,CAAC,CAAC;QAUhB,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,YAAA,MAAM,IAAI,UAAU,CAAC,kDAAkD,CAAC,CAAC;AAC1E,SAAA;QACD,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChB,YAAA,MAAM,IAAI,UAAU,CAAC,4CAA4C,CAAC,CAAC;AACpE,SAAA;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAI,QAAQ,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,QAAQ,CAAC;KACrC;AAED;;AAEG;AACO,IAAA,IAAI,CAAC,KAAa,EAAA;;QAE1B,OAAO,KAAK,GAAG,CAAC,EAAE;AAChB,YAAA,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC;AAC/B,SAAA;AACD,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;KACrC;AAES,IAAA,GAAG,CAAC,KAAa,EAAA;QACzB,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;AAC9D,SAAA;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;KACzC;IAES,GAAG,CAAC,KAAa,EAAE,KAAQ,EAAA;QACnC,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;AAC9D,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;KAC1C;AAED;;AAEG;IACH,MAAM,GAAA;QACJ,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACnC,IAAI,MAAM,GAAG,CAAC,EAAE;AACd,YAAA,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AACxC,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;AAIG;IACH,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;KACxC;AAED;;;;AAIG;IACH,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC5B;AAED;;AAEG;AACH,IAAA,IAAI,CAAC,KAAQ,EAAA;AACX,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAC9C,SAAA;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;KACpC;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,MAAW,EAAA;AACjB,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,SAAA;KACF;AAED;;AAEG;IACH,GAAG,GAAA;AACD,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;AAC/C,SAAA;AACD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC9B,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,KAAQ,EAAA;AACd,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;AACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAC9C,SAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAC7B;AAED;;AAEG;IACH,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;AAC/C,SAAA;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,CAAC,aAAqB,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;AAC/C,SAAA;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5B,QAAA,OAAO,MAAM,CAAC;KACf;AACF;;AC/KD;;;;;;;;;;;;;;;;AAgBG;AAIH,MAAa,iBAAqB,SAAQ,UAAa,CAAA;AAGrD;;AAEG;AACH,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;KAC3C;IAEQ,MAAM,GAAA;AACb,QAAA,OAAO,KAAK,CAAC;KACd;AAEQ,IAAA,IAAI,CAAC,KAAQ,EAAA;AACpB,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;YAClB,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;AACD,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnB;AAEQ,IAAA,OAAO,CAAC,KAAQ,EAAA;AACvB,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;YAClB,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;AACD,QAAA,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;AAED;;AAEG;IACK,MAAM,GAAA;AACZ,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtC,QAAA,MAAM,OAAO,GAAG,IAAI,KAAK,CAAI,WAAW,CAAC,CAAC;AAC1C,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;;;QAI1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5B,YAAA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAClD,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAChB;;AA9Cc,iBAAgB,CAAA,gBAAA,GAAG,EAAE;;ACrBtC;;;;;;;;;;;;;;;;AAgBG;AAgBH;AACA;AACA;AAEA;;AAEG;AACG,SAAU,iBAAiB,CAAI,KAAU,EAAA;AAC7C,IAAA,OAAO,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AAClC,CAAC;AAUD;;;;;;;;;;;;AAYG;AACG,SAAU,oBAAoB,CAChC,IACiD,EAAA;AACnD,IAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;;AAWG;AACa,SAAA,wBAAwB,CACpC,aAA4C,EAC5C,gBAAwC,EAAA;AAC1C,IAAA,OAAO,IAAI,eAAe,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAC9D,CAAC;AAyBD;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAU,kBAAkB,CAC9B,SAA4B,EAC5B,YAAgC,GAAA,eAAe,CAAC,IAAI,EAAA;AACtD,IAAA,OAAO,IAAI,WAAW,CAAI,SAAS,EAAE,YAAY,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;AAMG;MACmB,YAAY,CAAA;AAgBhC;;;;;;;AAOG;AACH,IAAA,MAAM,OAAO,GAAA;QACX,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAC1B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;AACd,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACrB,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACvB,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;;;;;;AAUG;AACH,IAAA,MAAM,cAAc,GAAA;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,MAAM,GAAQ,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AAC5B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;AACd,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACrB,YAAA,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;;AAMG;AACH,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAC1B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;AACd,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACvB,SAAA;KACF;AAED;;;;;;AAMG;IACH,MAAM,YAAY,CAAC,SAA4B,EAAA;AAC7C,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACxC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;AAClC,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACtB,YAAA,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACrC,SAAA;KACF;AAED;;;;;;;;;;;AAWG;AACH,IAAA,YAAY,CAAC,OAAkC,EAAA;AAC7C,QAAA,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACrD;;AAID;;;;;;;AAOG;AACH,IAAA,MAAM,CAAC,SAAgC,EAAA;AACrC,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC5C;AAED;;;;;;;AAOG;AACH,IAAA,GAAG,CAAI,SAA0B,EAAA;AAC/B,QAAA,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KACzC;AAED;;;;;;;AAOG;AACH,IAAA,QAAQ,CAAI,SAAmC,EAAA;AAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC9C;AAED;;;;;;;AAOG;AACH,IAAA,cAAc,CAAI,SAAmC,EAAA;QACnD,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;KACvD;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,CAAI,SAA4B,EAAA;AACrC,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC7C;AAED;;;;AAIG;IACH,MAAM,YAAY,CAAC,CAAqB,EAAA;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;KACnC;AAED;;;;;;AAMG;IACH,MAAM,aAAa,CAAC,CAAiC,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;KAC/D;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,aAAa,CAAC,SAAiB,EAAE,cAAc,GAAG,IAAI,EAAA;QACpD,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;KACnE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;AACH,IAAA,gBAAgB,CACZ,SAAiB,EAAE,cAAc,GAAG,IAAI;;AAExC,IAAA,KAAA,GAAsC,SAAS,EAAA;;QAGjD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;;AAGjE,QAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;KAC/C;AAED;;;;;;;;;AASG;IACH,WAAW,CACP,QAAyB,EACzB,gBAAwC,EAAA;AAC1C,QAAA,OAAO,IAAI,eAAe,CACtB,iBAAiB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;KAC5D;AAED;;;;;;AAMG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;AAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtC;AAED;;;;;AAKG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;AAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtC;AAED;;;;;;;;AAQG;AACH,IAAA,QAAQ,CAAC,UAAkB,EAAA;AACzB,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC/C;;AAID;;;;;;;AAOG;IACH,OAAO,CAAC,UAAkB,EAAE,IAAa,EAAA;QACvC,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KACpD;AAED;;;AAGG;IACH,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;KACjC;AACF,CAAA;AAED;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,MAAM,aAAiB,SAAQ,YAAe,CAAA;AAE5C,IAAA,WAAA,CAAsB,KAAU,EAAA;AAC9B,QAAA,KAAK,EAAE,CAAC;QADY,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;QADxB,IAAI,CAAA,IAAA,GAAG,CAAC,CAAC;KAGhB;IAED,OAAO,GAAA;AACL,QAAA,OAAO,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC;KAC9C;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,OAAO,EAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KAC9C;AACF,CAAA;AAED,MAAM,oBAAwB,SAAQ,YAAe,CAAA;AACnD,IAAA,WAAA,CACc,MAA2D,EAAA;AACvE,QAAA,KAAK,EAAE,CAAC;QADI,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqD;KAExE;IAED,OAAO,GAAA;AACL,QAAA,OAAO,eAAe,CAAC;KACxB;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI;AACF,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AACtB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAEV,YAAA,CAAC,CAAC,OAAO;AACL,gBAAA,CAAA,gDAAA,EAAmD,CAAC,CAAC,OAAO,CAAA,CAAE,CAAC;AACnE,YAAA,MAAM,CAAC,CAAC;AACT,SAAA;KACF;AACF,CAAA;AAED,MAAM,cAAkB,SAAQ,YAAe,CAAA;AAK7C,IAAA,WAAA,CAAsB,QAAyB,EAAA;AAC7C,QAAA,KAAK,EAAE,CAAC;QADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;AAE7C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC;KAC/C;AAED,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,UAAU,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC7B;AACF,CAAA;AAED,MAAM,YAAgB,SAAQ,YAAe,CAAA;IAQ3C,WAAsB,CAAA,QAAyB,EAAY,QAAgB,EAAA;AACzE,QAAA,KAAK,EAAE,CAAC;QADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAAY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;QAF3E,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;AAIR,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;KAC7C;AAED,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,UAAU,GAAA;;;;;QAKtB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;YACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;YAE3C,IAAI,OAAO,CAAC,IAAI,EAAE;AAChB,gBAAA,OAAO,OAAO,CAAC;AAChB,aAAA;AACD,YAAA,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAW,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC7B;AACF,CAAA;AAED,MAAM,YAAgB,SAAQ,YAAe,CAAA;IAE3C,WAAsB,CAAA,QAAyB,EAAY,QAAgB,EAAA;AACzE,QAAA,KAAK,EAAE,CAAC;QADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAAY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAD3E,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;KAGT;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;KAC7C;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC7B;AACF,CAAA;AAED;AACA;AACA;AACA,MAAM,qBAAyB,SAAQ,YAAiB,CAAA;AAKtD,IAAA,WAAA,CACc,QAAyB,EAAY,SAAiB,EACtD,uBAAuB,IAAI,EAAA;AACvC,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAAY,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QACtD,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAO;AAEvC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC;KACtD;AAED,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,UAAU,GAAA;QACtB,MAAM,KAAK,GAAQ,EAAE,CAAC;AACtB,QAAA,OAAO,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;YACpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,IAAI,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjD,OAAO,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;AACpC,iBAAA;gBACD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,aAAA;AACD,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,SAAA;QACD,OAAO,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KACpC;AACF,CAAA;AAED,MAAM,cAAkB,SAAQ,YAAe,CAAA;IAK7C,WACc,CAAA,QAAyB,EACzB,SAAgC,EAAA;AAC5C,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAuB;AAE5C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC;KAC/C;AAED,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,UAAU,GAAA;AACtB,QAAA,OAAO,IAAI,EAAE;YACX,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACxC,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC3C,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACD,YAAA,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;AAC9B,SAAA;KACF;AACF,CAAA;AAED,MAAM,WAAkB,SAAQ,YAAe,CAAA;IAC7C,WACc,CAAA,QAAyB,EACzB,SAA0B,EAAA;AACtC,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;KAEvC;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC;KAC5C;AAED,IAAA,MAAM,IAAI,GAAA;QACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,YAAY,GAAG,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;;QAO5E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;AAIzE,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;YAC5B,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;gBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;AACb,aAAA;AACF,SAAA;QACD,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KACrC;AACF,CAAA;AAED,MAAM,yBAA6B,SAAQ,YAAe,CAAA;IAExD,WACc,CAAA,QAAyB,EACzB,OAAkC,EAAA;AAC9C,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA2B;QAHhD,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC;KACrD;AAMD,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,EAAE;YACX,IAAI;AACF,gBAAA,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACnC,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACpB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,iBAAA;;;;;AAMF,aAAA;AACF,SAAA;KACF;AACF,CAAA;AAED,MAAM,gBAAuB,SAAQ,YAAe,CAAA;IAClD,WACc,CAAA,QAAyB,EACzB,SAAmC,EAAA;AAC/C,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAA0B;KAEhD;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;KACjD;AAED,IAAA,MAAM,IAAI,GAAA;QACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;AACD,QAAA,MAAM,YAAY,GAAG,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;;QAO5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;AAIzE,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;YAC5B,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;gBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;AACb,aAAA;AACF,SAAA;QACD,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KACrC;AACF,CAAA;AAED;AACA;AAEA;;;;;;;AAOG;AACG,MAAgB,iBAAqB,SAAQ,YAAe,CAAA;AAQhE,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,EAAK,CAAC;AAC9C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;AAED,IAAA,MAAM,IAAI,GAAA;;;;;AAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAgBD,IAAA,MAAM,UAAU,GAAA;;;;QAId,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;;AAEtC,YAAA,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE;gBACtB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KACvD;AACF,CAAA;AACD,MAAM,eAAsB,SAAQ,iBAAoB,CAAA;IACtD,WACc,CAAA,QAAyB,EACzB,SAA4B,EAAA;AACxC,QAAA,KAAK,EAAE,CAAC;QAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;KAEzC;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC;KAChD;AAED,IAAA,MAAM,IAAI,GAAA;QACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,MAAM,YAAY,GAAG,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;QAM5E,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,aAAa,GACf,EAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,WAAiB,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;;AAItC,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;YAC5B,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;gBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;AACb,aAAA;AACF,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AACF,CAAA;AAED;;;;;;;;AAQG;AACG,MAAO,eAAmB,SAAQ,YAAe,CAAA;IASrD,WACI,CAAA,SAAwC,EACvB,gBAAwC,EAAA;AAC3D,QAAA,KAAK,EAAE,CAAC;QADW,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAwB;;;QARrD,IAAQ,CAAA,QAAA,GAA+B,IAAI,CAAC;;QAG5C,IAAQ,CAAA,QAAA,GAAoB,IAAI,CAAC;AAOvC,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;KAChC;IAED,OAAO,GAAA;QACL,MAAM,iBAAiB,GAAG,6CAA6C,CAAC;QACxE,OAAO,CAAA,EAAG,iBAAiB,CAAA,WAAA,CAAa,CAAC;KAC1C;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAEO,MAAM,aAAa,CAAC,QAAoC,EAAA;;;;;;AAO9D,QAAA,MAAM,QAAQ,CAAC;AACf,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YACvD,IAAI,cAAc,CAAC,IAAI,EAAE;;gBAEvB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,aAAA;AACD,YAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;AACrC,YAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;AACjC,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACnE,aAAA;AACF,SAAA;QACD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC9C,IAAI,UAAU,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAA;AACD,QAAA,OAAO,UAAU,CAAC;KACnB;AACF,CAAA;AAED,IAAY,eAIX,CAAA;AAJD,CAAA,UAAY,eAAe,EAAA;AACzB,IAAA,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,eAAA,CAAA,eAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;IACR,eAAO,CAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;AACT,CAAC,EAJW,eAAe,KAAf,eAAe,GAI1B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACH,MAAM,WAA0C,SAAQ,YAAe,CAAA;AAIrE,IAAA,WAAA,CACuB,SAA4B,EAC5B,YAAgC,GAAA,eAAe,CAAC,IAAI,EAAA;AACzE,QAAA,KAAK,EAAE,CAAC;QAFa,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;QAC5B,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAwC;QALnE,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;QACV,IAAc,CAAA,cAAA,GAA+B,IAAI,CAAC;KAMzD;IAED,OAAO,GAAA;QACL,MAAM,iBAAiB,GAAG,yCAAyC,CAAC;QACpE,OAAO,CAAA,CAAA,EAAI,iBAAiB,CAAA,QAAA,CAAU,CAAC;KACxC;IAEO,MAAM,SAAS,CAAC,UAAsC,EAAA;;;AAI5D,QAAA,MAAM,UAAU,CAAC;;;QAIjB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,SAAS,OAAO,CAAC,SAA4B,EAAA;YAC3C,IAAI,SAAS,YAAY,YAAY,EAAE;AACrC,gBAAA,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;gBAChC,OAAO;AACL,oBAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAG;AACrB,wBAAA,YAAY,EAAE,CAAC;wBACf,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,4BAAA,aAAa,EAAE,CAAC;AACjB,yBAAA;wBACD,OAAO,CAAC,CAAC,KAAK,CAAC;AACjB,qBAAC,CAAC;AACF,oBAAA,OAAO,EAAE,KAAK;iBACf,CAAC;AACH,aAAA;AAAM,iBAAA;gBACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACrC,aAAA;SACF;QAED,MAAM,MAAM,GAAM,MAAM,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEpE,IAAI,YAAY,KAAK,aAAa,EAAE;;YAElC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;QACD,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB,QAAQ,IAAI,CAAC,YAAY;gBACvB,KAAK,eAAe,CAAC,IAAI;oBACvB,MAAM,IAAI,KAAK,CACX,8CAA8C;AAC9C,wBAAA,CAAA,sBAAA,EAAyB,IAAI,CAAC,KAAK,CAAA,CAAA,CAAG,CAAC,CAAC;gBAC9C,KAAK,eAAe,CAAC,QAAQ;oBAC3B,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;gBACnC,KAAK,eAAe,CAAC,OAAO,CAAC;;AAG9B,aAAA;AACF,SAAA;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KACrC;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;AACF,CAAA;AAED;AACA;AAEA;;;;;;AAMG;AACG,MAAO,gBAAoB,SAAQ,YAAe,CAAA;IAGtD,WACc,CAAA,QAAyB,EAAY,UAAkB,EAAA;AACnE,QAAA,KAAK,EAAE,CAAC;QADI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAAY,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAEnE,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAA6B,UAAU,CAAC,CAAC;KACtE;IAED,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;KACjD;AAED;;;AAGG;IACO,MAAM,GAAA;AACd,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,SAAA;KACF;IAED,IAAI,GAAA;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;;;;AAId,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;KAC5B;AACF,CAAA;AAED;;;;;AAKG;AACG,MAAO,eAAmB,SAAQ,gBAAmB,CAAA;AAUzD,IAAA,WAAA,CACqB,QAAyB,EAAY,UAAkB,EACxE,IAAa,EAAA;AACf,QAAA,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAFT,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;QAAY,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;;QAHpE,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;AAMhC,QAAA,IAAI,CAAC,MAAM,GAAGC,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;KAC7D;AAEQ,IAAA,MAAM,IAAI,GAAA;;;;;AAKjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,SAAS,CAAC,GAAW,EAAA;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;KACxC;IAES,WAAW,GAAA;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;KAC7C;AAED,IAAA,MAAM,UAAU,GAAA;;AAEd,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;AACD,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;AAC7B,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,MAAM,CAAC,IAAI,EAAE;AACf,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC/B,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,gBAAA,OAAO,MAAM,CAAC;AACf,aAAA;AACF,SAAA;QACD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;KAClC;AACF;;ACrqCD;;;;;;;;;;;;;;;;AAgBG;AAeH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACH,MAAsB,OAAO,CAAA;AAA7B,IAAA,WAAA,GAAA;QAWW,IAAI,CAAA,IAAA,GAAW,IAAI,CAAC;KA2c9B;;;;;;AAncC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDG;AACH,IAAA,KAAK,CAAC,SAAiB,EAAE,cAAc,GAAG,IAAI,EAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,EAAE,CAAC,IAAI,CAAC,MAAM,CACV,SAAS,GAAG,CAAC,EAAE,MAAM,CAAA;QACrB,SAAS,CAAA,CAAE,CAAC,CAAC;AACjB,QAAA,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;;;AAG/C,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAClB,SAAA;AAAM,aAAA,IAAI,cAAc,EAAE;;;YAGzB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;AACzC,SAAA;AAAM,aAAA;;;YAGL,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;AAC1C,SAAA;AACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;AACtC,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;AACxB,iBAAA,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;SACnE,EAAE,IAAI,CAAC,CAAC;KACV;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,WAAW,CAAC,OAAmB,EAAA;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;;;YAGvD,IAAI,GAAG,QAAQ,CAAC;AACjB,SAAA;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;;;YAGpD,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,SAAA;AAAM,aAAA;;;YAGL,IAAI,GAAG,IAAI,CAAC;AACb,SAAA;QACD,OAAO,qBAAqB,CACxB,YACI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,WAAW,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,CAAC,EACjE,IAAI,CAAC,CAAC;KACX;AAED;;;;;;;;;;;;;;;AAeG;AACH,IAAA,MAAM,CAAC,SAAgC,EAAA;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC;AACT,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;;YAE1B,IAAI,GAAG,QAAQ,CAAC;AACjB,SAAA;AAAM,aAAA;;;YAGL,IAAI,GAAG,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;YACtC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE,EAAE,IAAI,CAAC,CAAC;KACV;AAED;;;;;;;;;;;;;;;AAeG;IACH,MAAM,YAAY,CAAC,CAAqB,EAAA;AACtC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;KAChD;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,GAAG,CAA+B,SAA0B,EAAA;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,OAAO,qBAAqB,CAAC,YAAW;YACtC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACf;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACH,IAAA,QAAQ,CAA+B,SAAmC,EAAA;QAExE,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,OAAO,qBAAqB,CAAC,YAAW;AACtC,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;AACrD,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACf;AAED;;;;;;;;AAQG;AACH,IAAA,QAAQ,CAAC,UAAkB,EAAA;QACzB,IAAI,UAAU,IAAI,IAAI,EAAE;AACtB,YAAA,MAAM,IAAI,UAAU,CAChB,2DAA2D,CAAC,CAAC;AAClE,SAAA;QAED,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1E;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,MAAM,CAAC,KAAc,EAAA;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;;;;AAIlC,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,SAAA;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;;YAEtB,IAAI,GAAG,CAAC,CAAC;AACV,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;YAGlE,IAAI,GAAG,QAAQ,CAAC;AACjB,SAAA;AAAM,aAAA;;YAEL,IAAI,GAAG,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;YACtC,MAAM,gBAAgB,GAAG,oBAAoB,CACzC,aAAa,EAAC,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;YAC/D,OAAO,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAC/D,EAAE,IAAI,CAAC,CAAC;KACV;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC;AACT,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;;AAIzD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,SAAA;AAAM,aAAA,IACH,IAAI,CAAC,IAAI,IAAI,IAAI;AACjB,aAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;YAG3D,IAAI,GAAG,CAAC,CAAC;AACV,SAAA;AAAM,aAAA;;YAEL,IAAI,GAAG,IAAI,CAAC;AACb,SAAA;QACD,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;KAC5D;AAMD;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,OAAO,CAAC,UAAkB,EAAE,IAAa,EAAE,sBAAsB,GAAG,IAAI,EAAA;AAEtE,QAAA,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE;AACxC,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;AACrB,gBAAA,MAAM,IAAI,UAAU,CAChB,0DAA0D,CAAC,CAAC;AACjE,aAAA;AAAM,iBAAA;gBACL,MAAM,IAAI,UAAU,CAChB,4DAA4D;oBAC5D,6DAA6D;oBAC7D,yDAAyD;AACzD,oBAAA,CAAA,gCAAA,EAAmC,IAAI,CAAC,IAAI,CAAA,UAAA,CAAY,CAAC,CAAC;AAC/D,aAAA;AACF,SAAA;QACD,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,MAAM,MAAM,GAAGA,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjE,QAAA,OAAO,qBAAqB,CAAC,YAAW;AACtC,YAAA,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAC3B,YAAA,IAAI,sBAAsB,EAAE;AAC1B,gBAAA,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;AACzB,aAAA;AACD,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACvE,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;KACf;AAED;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE;;;YAG1C,IAAI,GAAG,KAAK,CAAC;AACd,SAAA;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;AAGlD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAClB,SAAA;AAAM,aAAA;;YAEL,IAAI,GAAG,IAAI,CAAC;AACb,SAAA;QACD,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;KAC5D;AAED;;;;;;;;;;;;;;;AAeG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACnE,SAAA;QACD,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC;KAC1C;AAED;;;;;;;;;;AAUG;AACH,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACnE,SAAA;QACD,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,cAAc,EAAE,CAAC;KACjD;;AA7HD;AAEgB,OAAe,CAAA,eAAA,GAAG,KAAH,CAAS;AA8H1C;;;;;;;;;;;AAWG;SACa,qBAAqB,CACjC,UAA0C,EAC1C,OAAe,IAAI,EAAA;IACrB,OAAO,IAAI,cAAc,OAAU,CAAA;AAAxB,QAAA,WAAA,GAAA;;YACA,IAAI,CAAA,IAAA,GAAG,IAAI,CAAC;SAStB;AAPC;;;AAGG;AACH,QAAA,MAAM,QAAQ,GAAA;YACZ,OAAO,UAAU,EAAE,CAAC;SACrB;AACF,KAAA,EACC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;AAiBG;AACG,SAAU,KAAK,CAA+B,KAAU,EAAA;AAC5D,IAAA,OAAO,qBAAqB,CACxB,YAAY,iBAAiB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;AACG,SAAU,GAAG,CAA+B,QAA0B,EAAA;;AAG1E,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACzB,QAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;AACtE,KAAA;AACD,IAAA,IAAI,IAAI,CAAC;AACT,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI;AAChC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI,CAAC,CAAC;AACxE,SAAA;AACF,KAAA;SAAM,IAAI,QAAQ,YAAY,MAAM,EAAE;AACrC,QAAA,KAAK,MAAM,EAAE,IAAI,QAAQ,EAAE;AACzB,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI;AACjC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI,CAAC,CAAC;AACzE,SAAA;AACF,KAAA;AACD,IAAA,OAAO,qBAAqB,CAAI,YAAW;QACzC,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,QAAQ,EAAE,CAAC,IAAG;YACrD,IAAI,CAAC,YAAY,OAAO,EAAE;AACxB,gBAAA,OAAO,EAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;AAC9C,aAAA;AAAM,iBAAA,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;gBACxB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;gBACL,MAAM,IAAI,KAAK,CACX,4DAA4D;AAC5D,oBAAA,iBAAiB,CAAC,CAAC;AACxB,aAAA;AACH,SAAC,CAAC,CAAC;QACH,OAAO,kBAAkB,CAAI,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;KACjE,EAAE,IAAI,CAAC,CAAC;AACX,CAAC;AAED;;;;;;AAMG;AACH;AACA,SAAS,eAAe,CAAC,IAAW,EAAA;IAClC,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;AAGD,IAAA,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAE3B,IAAA,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;;AAE5B,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AAChC,QAAA,OAAO,EAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;AAChC,KAAA;;IAGD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;AACtC,CAAC;AAED;;;AAGG;AACH,SAAS,WAAW,CAAoC,MAAW,EAAA;AAEjE,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEvB,QAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC1D,KAAA;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE;;AAElC,QAAA,OAAO,EAAE,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;AACxC,KAAA;AAAM,SAAA;;AAEL,QAAA,OAAO,EAAE,CAAC,MAAM,CAAC,MAAoB,CAAC,CAAC;AACxC,KAAA;AACH;;AChsBA;;;;;;;;;;;;;;;;AAgBG;AAMH;;;;AAIG;AACG,MAAO,eAAgB,SAAQ,OAAe,CAAA;AAClD;;;;AAIG;AACH,IAAA,WAAA,CAA+B,KAAiB,EAAA;AAC9C,QAAA,KAAK,EAAE,CAAC;QADqB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;KAE/C;AAED,IAAA,MAAM,QAAQ,GAAA;QACZ,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;AAClD,QAAA,MAAM,YAAY,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;AAChD,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAG;;AAEvD,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACvB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1B,aAAA;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,YAAY,CAAC;KACrB;AACF;;ACjDD;;;;;;;;;;;;;;;;AAgBG;AASH,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AACpC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AACpC,MAAM,uBAAuB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAC1D,MAAM,2BAA2B,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAE3D;;;;;;;;;;;;AAYG;AACG,MAAO,UAAW,SAAQ,OAAwB,CAAA;AAUtD;;;;;;;;;AASG;AACH,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC7B,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC;KAC1D;AAED;;;;;;;AAOG;AACK,IAAA,MAAM,cAAc,GAAA;AAC1B,QAAA,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,mBAAmB,EAAE;;AAEjD,YAAA,MAAM,IAAI,KAAK,CACX,2DAA2D,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,eAAe,IAAI,mBAAmB,EAAE;;AAEtD,YAAA,IAAI,CAAC,MAAM,CACP,mBAAmB,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAC1D,MAAM,sCAAsC;AACxC,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACtC,2DAA2D;gBAC3D,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;AAClE,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,YAAA,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;AAC5C,SAAA;;AAED,QAAA,MAAM,MAAM,GAA4B,IAAI,CAAC,eAAe,CAAC,MAAM,CAC/D,CAAC,QAAiC,EAAE,IAAI,KAAI;AAC1C,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3C,YAAA,OAAO,QAAQ,CAAC;SACjB,EACD,EAAE,CAAC,CAAC;QACR,MAAM,cAAc,GAChB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,MAAM,CACP,cAAc,CAAC,MAAM,KAAK,CAAC,EAC3B,MAAM,gCAAgC,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;;QAExE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACjD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChD,gBAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,oBAAA,MAAM,IAAI,KAAK,CACX,WAAW,GAAG,GAAG;wBACjB,+DAA+D;wBAC/D,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;AACzD,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;KAClC;AAEO,IAAA,MAAM,mBAAmB,GAAA;QAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACxC,YAAA,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACvC,IAAI,YAAY,CAAC,IAAI,EAAE;AACrB,gBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACvD,aAAA;AACD,YAAA,MAAM,SAAS,GAAW,YAAY,CAAC,KAAK,CAAC;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAChD,YAAA,OAAO,OAAO,CAAC;AAChB,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;KACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;IACH,WAA+B,CAAA,KAAiB,EAAE,SAAqB,EAAA;AACrE,QAAA,KAAK,EAAE,CAAC;QADqB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;QA9HxC,IAAS,CAAA,SAAA,GAAG,IAAI,CAAC;QACjB,IAAe,CAAA,eAAA,GAAa,IAAI,CAAC;QACjC,IAAoB,CAAA,oBAAA,GAAG,KAAK,CAAC;QAC7B,IAAa,CAAA,aAAA,GAAkC,IAAI,CAAC;QACpD,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;QAC9B,IAAS,CAAA,SAAA,GAAG,GAAG,CAAC;QAChB,IAAe,CAAA,eAAA,GAAG,KAAK,CAAC;QA0H9B,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,EAAE,CAAC;AAChB,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AAC9D,QAAA,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC;AAC7C,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC7C,QAAA,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC;QAC7D,IAAI,SAAS,CAAC,eAAe,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CACP,SAAS,CAAC,SAAS,IAAI,IAAI,EAC3B,MACI,gEAAgE,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACtB,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC;AAClE,SAAA;KACF;AAED,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC7B,SAAA;QACD,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,IAAI,IAAI,CAAC,SAAS,EAAE;;;AAGlB,YAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;AACD,QAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;KAChD;AAED,IAAA,eAAe,CAAC,IAAY,EAAA;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAqC,EAAE,CAAC;QACtD,MAAM,MAAM,GAAqC,EAAE,CAAC;AAEpD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACnE,YAAA,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,MAAM,EAAE;;gBAEzC,SAAS;AACV,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,WAAW,GAAG,IAAI,CAAC;gBACvB,IAAI,KAAK,KAAK,EAAE,EAAE;;;AAGhB,oBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;AAC1C,wBAAA,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,qBAAA;yBAAM,IAAI,MAAM,KAAK,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;wBACxD,MAAM,IAAI,KAAK,CACX,CAAA,gBAAA,EAAmB,GAAG,CAA2B,wBAAA,EAAA,IAAI,CAAE,CAAA,CAAC,CAAC;AAC9D,qBAAA;AAAM,yBAAA;wBACL,WAAW,GAAG,SAAS,CAAC;AACzB,qBAAA;AACF,iBAAA;AAAM,qBAAA;;AAEL,oBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,oBAAA,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE;;;AAGrB,wBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,EAAE;AACrC,4BAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACtC,yBAAA;AAAM,6BAAA;;4BAEL,WAAW,GAAG,KAAK,CAAC;AACrB,yBAAA;AACF,qBAAA;AAAM,yBAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;;wBAGnC,WAAW,GAAG,UAAU,CAAC;AAC1B,qBAAA;AAAM,yBAAA;;;wBAGL,QAAQ,MAAM,CAAC,KAAK;AAClB,4BAAA,KAAK,SAAS;gCACZ,WAAW,GAAG,UAAU,CAAC;gCACzB,MAAM;AACR,4BAAA,KAAK,OAAO;AACV,gCAAA,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gCACrC,MAAM;AACR,4BAAA,KAAK,MAAM;AACT,gCAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gCACrC,MAAM;AACR,4BAAA;gCACE,WAAW,GAAG,UAAU,CAAC;AAC5B,yBAAA;AACF,qBAAA;AACF,iBAAA;;AAED,gBAAA,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW;AACzB,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AAC1D,aAAA;AACF,SAAA;;;QAGD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,YAAA,OAAO,QAAQ,CAAC;AAEjB,SAAA;AAAM,aAAA;YACL,OAAO,EAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAC,CAAC;AACnC,SAAA;KACF;AAEO,IAAA,UAAU,CAAC,KAAa,EAAA;QAC9B,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACnD,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;KACF;;AAGO,IAAA,QAAQ,CAAC,IAAY,EAAE,oBAAoB,GAAG,IAAI,EAAA;QACxD,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;AACnB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,YAAY,GAAG,SAAS,CAAC;;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;AACnC,YAAA,QAAQ,YAAY;;AAElB,gBAAA,KAAK,SAAS;AACZ,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;AAEpB,wBAAA,KAAK,UAAU;AACb,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;4BACnB,YAAY,GAAG,WAAW,CAAC;4BAC3B,MAAM;;wBAER,KAAK,IAAI,CAAC,SAAS;AACjB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;;;4BAGnB,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;gCAClD,MAAM;AACP,6BAAA;AACD,4BAAA,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BAChB,YAAY,GAAG,SAAS,CAAC;4BACzB,MAAM;;AAER,wBAAA;4BACE,YAAY,GAAG,WAAW,CAAC;4BAC3B,UAAU,GAAG,CAAC,CAAC;4BACf,MAAM;AACT,qBAAA;oBACD,MAAM;;AAER,gBAAA,KAAK,WAAW;AACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;wBAEpB,KAAK,IAAI,CAAC,SAAS;AACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;4BAC3C,YAAY,GAAG,SAAS,CAAC;AACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;4BACnB,MAAM;AAET,qBAAA;oBACD,MAAM;;AAER,gBAAA,KAAK,WAAW;AACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;AAEpB,wBAAA,KAAK,UAAU;4BACb,YAAY,GAAG,uBAAuB,CAAC;4BACvC,MAAM;AAET,qBAAA;oBACD,MAAM;;AAER,gBAAA,KAAK,uBAAuB;AAC1B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;wBAEpB,KAAK,IAAI,CAAC,SAAS;AACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAC/C,YAAY,GAAG,SAAS,CAAC;AACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;4BACnB,MAAM;;AAER,wBAAA,KAAK,UAAU;4BACb,YAAY,GAAG,WAAW,CAAC;4BAC3B,MAAM;;AAER,wBAAA;4BACE,YAAY,GAAG,2BAA2B,CAAC;4BAC3C,MAAM;AACT,qBAAA;oBACD,MAAM;AACR,gBAAA,KAAK,2BAA2B;AAC9B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;AAEpB,wBAAA,KAAK,UAAU;4BACb,YAAY,GAAG,WAAW,CAAC;4BAC3B,MAAM;AAET,qBAAA;oBACD,MAAM;AAET,aAAA;AACF,SAAA;;QAED,IAAI,YAAY,KAAK,uBAAuB,EAAE;AAC5C,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;YACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACzC,SAAA;;QAED,IAAI,oBAAoB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACzE,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,qCAAA,EACZ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAA,4BAAA,EAA+B,MAAM,CAAA,CAAE,CAAC,CAAC;AACzE,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;AACA;AACA;;AC3YA;;;;;;;;;;;;;;;;AAgBG;AAMH;;;;;AAKG;AACG,MAAO,kBAAmB,SAAQ,YAA6B,CAAA;AAgBnE,IAAA,WAAA,CAAuC,gBAAkC,EAAA;AACvE,QAAA,KAAK,EAAE,CAAC;QAD6B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAfjE,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;QAiBvB,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,QAAA,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE;AACvD,YAAA,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;YAClC,MAAM,IAAI,KAAK,CACX,CAAmD,iDAAA,CAAA;AACnD,gBAAA,CAAA,4BAAA,EAA+B,IAAI,CAAC,OAAO,CAAA,CAAE,CAAC,CAAC;AACpD,SAAA;QAED,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,uBAAuB,IAAI,EAAE,CAAC;AAChE,QAAA,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;AAClD,QAAA,IAAI,CAAC,oBAAoB;AACrB,YAAA,gBAAgB,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC;AAC1D,QAAA,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;QACpE,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,IAAI,CAAC,CAAC;AAEzE,QAAA,IAAI,CAAC,kBAAkB;AACnB,YAAA,gBAAgB,CAAC,kBAAkB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACjE,QAAA,IAAI,CAAC,eAAe;AAChB,YAAA,gBAAgB,CAAC,eAAe,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACrD,MAAM,IAAI,KAAK,CACX,yDAAyD;AACzD,gBAAA,+CAA+C,CAAC,CAAC;AACtD,SAAA;KACF;IAED,OAAO,GAAA;AACL,QAAA,OAAO,YAAY,CAAC;KACrB;;AAGD,IAAA,aAAa,MAAM,CAAC,mBAAqC,EAAE,EAAA;QACzD,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;AACjE,SAAA;AAED,QAAA,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;;AAGpE,QAAA,MAAM,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAEjC,QAAA,OAAO,kBAAkB,CAAC;KAC3B;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;gBACtD,KAAK,EAAE,IAAI,CAAC,qBAAqB,IAAI,IAAI,GAAG,IAAI;AACJ,oBAAA,IAAI,CAAC,qBAAqB;AACtE,gBAAA,KAAK,EAAE,KAAK;AACb,aAAA,CAAC,CAAC;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CACX,CAAA,8CAAA,EAAiD,CAAC,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AACnE,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC5D,SAAA;AAED,QAAA,MAAM,cAAc;;AAEf,QAAA,MAAc,CAAC,YAAY,IAAK,MAAc,CAAC,kBAAkB,CAAC;AACvE,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,cAAc,EAAE,CAAC;AAEzC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;;YAGtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;AAClD,SAAA;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;YAC7D,MAAM,IAAI,KAAK,CACX,CAA6B,2BAAA,CAAA;gBAC7B,CAAa,UAAA,EAAA,IAAI,CAAC,YAAY,CAAI,EAAA,CAAA;AAClC,gBAAA,CAAA,QAAA,EAAW,IAAI,CAAC,YAAY,CAAC,UAAU,CAAA,CAAE,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACjE,QAAA,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,OAAO;KACR;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,iBAAyB,CAAC;AAC9B,QAAA,IAAI,cAAsB,CAAC;AAE3B,QAAA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QACjD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AACjE,YAAA,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAChD,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AACjE,YAAA,cAAc,GAAG,IAAI,CAAC,2BAA2B,CAC7C,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;AACnD,SAAA;QAED,OAAO;YACL,KAAK,EAAE,EAAC,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,cAAc,EAAC;AACrE,YAAA,IAAI,EAAE,KAAK;SACZ,CAAC;KACH;;;AAID,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KACoB,CAAC;KACjD;AAEO,IAAA,MAAM,YAAY,GAAA;QAExB,MAAM,aAAa,GAAmB,EAAE,CAAC;QACzC,MAAM,aAAa,GAAmB,EAAE,CAAC;QACzC,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB,QAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;AAC3B,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,MAAK;gBAClC,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC3B,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;oBAEnD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;AAClC,wBAAA,OAAO,CAAC,EAAC,aAAa,EAAE,aAAa,EAAC,CAAC,CAAC;AACzC,qBAAA;AACD,oBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;AACvE,iBAAA;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACxB,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACpD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3C,iBAAA;;AAGD,gBAAA,IAAI,EAAE,aAAa,KAAK,IAAI,CAAC,SAAS,EAAE;oBACtC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1B,oBAAA,OAAO,CAAC,EAAC,aAAa,EAAE,aAAa,EAAC,CAAC,CAAC;AACzC,iBAAA;aACF,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;AAC7C,SAAC,CAAC,CAAC;KACJ;;IAGD,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;AAC1B,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACnC,aAAA;AACF,SAAA;KACF;;IAGQ,OAAO,GAAA;AACd,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;;IAGD,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAEO,IAAA,YAAY,CAAC,KAAqB,EAAA;QACxC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;QAC5D,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AAC9D,QAAA,OAAO,QAAQ,CAAC;KACjB;IAEO,2BAA2B,CAAC,QAAsB,EAAE,KAAe,EAAA;AAEzE,QAAA,MAAM,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;AAEzD,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClD,QAAA,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC5B;AACF;;ACzOD;;;;;;;;;;;;;;;;AAgBG;AAMH;;;AAGG;AACG,MAAO,cAAe,SAAQ,YAAsB,CAAA;IAQxD,WACuB,CAAA,kBAAoC,EACpC,YAA0B,EAAA;AAC/C,QAAA,KAAK,EAAE,CAAC;QAFa,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAkB;QACpC,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;QATzC,IAAQ,CAAA,QAAA,GAAG,IAAI,CAAC;QAEhB,IAAM,CAAA,MAAA,GAAG,KAAK,CAAC;AASrB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,YAAA,IAAI,CAAC,QAAQ;AACT,gBAAA,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACzC,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;;AAEhC,gBAAA,MAAM,kBAAkB,GACpB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBACxE,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG;AAC5D,oBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACnC,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,kBAAkB,IAAI,CAAC,CAAC;gBACpD,MAAM,eAAe,GAAG,CAAC,CAAC,GAAG,mBAAmB,IAAI,CAAC,CAAC;AACtD,gBAAA,MAAM,YAAY,GAAG,cAAc,GAAG,kBAAkB,CAAC;AACzD,gBAAA,MAAM,aAAa,GAAG,mBAAmB,GAAG,eAAe,CAAC;gBAC5D,IAAI,CAAC,OAAO,GAAG,QAAQ,CACnB,CAAC,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,CAAC,EAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,aAAA;AACF,SAAA;KACF;IAED,OAAO,GAAA;AACL,QAAA,OAAO,QAAQ,CAAC;KACjB;;IAGD,aAAa,MAAM,CACf,kBAAqC,EAAE,eAA6B,EAAE,EAAA;QACxE,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;AACjE,SAAA;QAED,IAAI,CAAC,kBAAkB,EAAE;;;AAGvB,YAAA,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;gBAC3D,MAAM,IAAI,KAAK,CACX,0DAA0D;AAC1D,oBAAA,gDAAgD,CAAC,CAAC;AACvD,aAAA;AACD,YAAA,kBAAkB,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC;AACpD,YAAA,kBAAkB,CAAC,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;AACvD,SAAA;QACD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;;AAG5E,QAAA,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;AAE7B,QAAA,OAAO,cAAc,CAAC;KACvB;;AAGD,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,MAAM,CACP,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,MAAM;AACpC,iBAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,aAAa,CAAC,EACpD,MACI,CAA+B,4BAAA,EAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAI,EAAA,CAAA;AAC/D,gBAAA,CAAA,sCAAA,CAAwC,CAAC,CAAC;AACnD,SAAA;QAED,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;AACtD,gBAAA,KAAK,EAAE;AACL,oBAAA,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;AACpC,oBAAA,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;AACpC,wBAAA,IAAI,CAAC,YAAY,CAAC,UAAU;wBAC5B,MAAM;AACV,oBAAA,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK;AACpC,oBAAA,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;AACvC,iBAAA;AACF,aAAA,CAAC,CAAC;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;YAEV,CAAC,CAAC,OAAO,GAAG,CAAA,8CAAA,EAAiD,CAAC,CAAC,OAAO,EAAE,CAAC;AACzE,YAAA,MAAM,CAAC,CAAC;AACT,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACxD,SAAA;;QAGD,IAAI;YACF,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AACjD,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACd,YAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CACtD,IAAI,CAAC,MAAgC,CAAC,CAAC;AAC1C,SAAA;;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAEtB,QAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;;AAEjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,MAAK;AAC9C,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,GAAG,CAAC;QACR,IAAI;YACF,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACnD,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,IAAI,KAAK,CACX,CAAA,yCAAA,EAA4C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AACtE,SAAA;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI;AACF,gBAAA,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;AAC3D,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,CAAA,iCAAA,EAAoC,CAAC,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;AAClE,aAAA;AAAS,oBAAA;gBACR,GAAG,CAAC,OAAO,EAAE,CAAC;AACf,aAAA;AACF,SAAA;AAAM,aAAA;YACL,OAAO,EAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;AAClC,SAAA;KACF;IAEO,YAAY,GAAA;;;;QAIlB,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY;aAC9D,IAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,WAAW;gBAC/D,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;AACvE,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;;AAGD,IAAA,kBAAkB,CAAC,GAAa,EAAA;QAC9B,OAAO,IAAI,CAAC,MAAK;AACf,YAAA,MAAM,aAAa,GAAa,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;AACtE,YAAA,IAAI,YAAY,CAAC;YACjB,YAAY,GAAG,KAAK,CAAC,aAAa,CAC9B,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAC3D,UAAU,CAAC,CAAC;;AAEhB,YAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;YACjC,OAAO,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAA6B,CAAC,CAAC;AAC3E,SAAC,CAAC,CAAC;KACJ;;;AAID,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC;KAClC;;IAGD,IAAI,GAAA;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;AAEvC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAEtC,IAAI;AACF,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1C,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;AACd,YAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;AACpC,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACtB;;IAGQ,OAAO,GAAA;AACd,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;AACF;;AC/ND;;;;;;;;;;;;;;;;AAgBG;AAIH;;;;;;AAMG;MACmB,UAAU,CAAA;AAU/B,CAAA;AAED;AACA;AACA;;ACzCA;;;;;;;;;;;;;;;;AAgBG;AAIG,MAAgB,cAAe,SAAQ,YAAoB,CAAA;AAC/D;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,KAAK,CAAC,SAAiB,EAAA;AACrB,QAAA,OAAO,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC3C;AACF,CAAA;AAED;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA,MAAM,aAAc,SAAQ,cAAc,CAAA;IAGxC,WAAsB,CAAA,QAA8B,EAAE,SAAiB,EAAA;AACrE,QAAA,KAAK,EAAE,CAAC;QADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAElD,IAAI,CAAC,IAAI,GAAG,IAAI,iBAAiB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;KACxD;IAED,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;KAC5B;AAED,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KACzB;AACF,CAAA;AAED,MAAM,iBAAkB,SAAQ,iBAAyB,CAAA;IAIvD,WACc,CAAA,QAA8B,EAAY,SAAiB,EAAA;AACvE,QAAA,KAAK,EAAE,CAAC;QADI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAAY,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;;QAHzE,IAAS,CAAA,SAAA,GAAG,EAAE,CAAC;KAKd;IAED,OAAO,GAAA;AACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA,WAAA,EAAc,IAAI,CAAC,SAAS,CAAA,EAAA,CAAI,CAAC;KACnE;AAED,IAAA,MAAM,IAAI,GAAA;QACR,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/C,IAAI,WAAW,CAAC,IAAI,EAAE;AACpB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;AACzB,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;;;YAID,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAa,CAAC;;;;AAKlE,QAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,SAAA;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAEzC,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;AC/GD;;;;;;;;;;;;;;;;AAgBG;AAMG,MAAgB,iBAAkB,SAAQ,YAAwB,CAAA;AACtE;;;;;;;;AAQG;IACH,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;KAC/B;AACF,CAAA;AAED;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA,MAAM,YAAa,SAAQ,cAAc,CAAA;AAGvC,IAAA,WAAA,CAAsB,QAAkC,EAAA;AACtD,QAAA,KAAK,EAAE,CAAC;QADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;QAEtD,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KAC5C;IAED,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;KAC5B;AAED,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KACzB;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,MAAM,gBAAiB,SAAQ,iBAAyB,CAAA;AAMtD,IAAA,WAAA,CAA+B,QAAkC,EAAA;AAC/D,QAAA,KAAK,EAAE,CAAC;QADqB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;AAE/D,QAAA,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AACzC,SAAA;AAAM,aAAA;;YAEL,MAAM,EAAC,aAAa,EAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAC1C,SAAA;KACF;IACD,OAAO,GAAA;QACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;KAC7C;AAED,IAAA,MAAM,IAAI,GAAA;QACR,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC/C,QAAA,IAAI,KAAK,CAAC;QACV,IAAI,WAAW,CAAC,IAAI,EAAE;AACpB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;AACL,YAAA,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,IAAY,CAAC;AACjB,QAAA,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AAC3B,YAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;AACnD,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,SAAA;AACD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;AC7HD;;;;;;;;;;;;;;;;AAgBG;AAcH;;;;;;AAMG;AACG,MAAO,iBAAkB,SAAQ,iBAAiB,CAAA;IAItD,WACc,CAAA,IAAiB,EACjB,OAAA,GAAoC,EAAE,EAAA;AAClD,QAAA,KAAK,EAAE,CAAC;QAFI,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAa;QACjB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;AAElD,QAAA,IAAI,CAAC,MAAM,CACP,CAAC,IAAI,YAAY,UAAU;aACtB,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;iBAClB,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;AAC7C,gBAAA,KAAK,CAAC,EACf,MAAM,4DAA4D;AAC9D,YAAA,YAAY,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;;QAElC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC;KACnD;IAED,OAAO,GAAA;AACL,QAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;KAClC;AAED,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,UAAU;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACvC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;AAClC,SAAA;QACD,MAAM,KAAK,GAAG,IAAI,OAAO,CAAa,CAAC,OAAO,EAAE,MAAM,KAAI;YACxD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,YAAA,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;;;AAGnC,gBAAA,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5D,aAAA;AAAM,iBAAA;;;;AAKL,gBAAA,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;AACpC,gBAAA,UAAU,CAAC,MAAM,GAAG,CAAC,KAAK,KAAI;AAC5B,oBAAA,IAAI,IAAI,GAAkC,UAAU,CAAC,MAAM,CAAC;;;;oBAI5D,IAAI,IAAI,YAAY,WAAW,EAAE;AAC/B,wBAAA,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC7B,qBAAA;AACD,oBAAA,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC,EAAE;wBACjC,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;AACnE,qBAAA;oBACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,iBAAC,CAAC;AACF,gBAAA,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;oBAC7B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACtC,iBAAC,CAAC;AACF,gBAAA,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;oBAC7B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,iBAAC,CAAC;;;AAGF,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;;;AAGhD,gBAAA,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACrC,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACpB,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,EAAC,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;KAC5C;AACF;;AC7GD;;;;;;;;;;;;;;;;AAgBG;AAKH;;;;;;AAMG;AACI,eAAe,gBAAgB,CAClC,GAAgB,EAAE,OAAA,GAAoC,EAAE,EACxD,SAAoB,EAAA;AACtB,IAAA,IAAI,SAAS,CAAC;AACd,IAAA,IAAI,WAAW,CAAC;AAChB,IAAA,IAAI,CAAC,OAAO,GAAG,MAAM,QAAQ,EAAE;QAC7B,SAAS,GAAG,GAAa,CAAC;AAC3B,KAAA;AAAM,SAAA;AACL,QAAA,SAAS,GAAI,GAAe,CAAC,GAAG,CAAC;AACjC,QAAA,WAAW,GAAG,yBAAyB,CAAC,GAAc,CAAC,CAAC;AACzD,KAAA;AACD,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACzE,IAAI,QAAQ,CAAC,EAAE,EAAE;QACf,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACnD,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACtC,KAAA;AACH,CAAC;AAED;AACA,MAAM,yBAAyB,GAAG,CAAC,OAAgB,KAAI;AACrD,IAAA,MAAM,IAAI,GAAG;QACX,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC;AACF,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;;AC9DD;;;;;;;;;;;;;;;;AAgBG;AAEH;AACA;AACA;AACM,SAAU,WAAW,CAAC,MAAW,EAAA;AACrC,IAAA,OAAO,CAAC,OAAO,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC;AAC1E;;ACvBA;;;;;;;;;;;;;;;;AAgBG;AASH;;;AAGG;AACG,MAAO,cAAe,SAAQ,UAAU,CAAA;AAC5C;;;;;;;AAOG;IACH,WACc,CAAA,KAAyB,EAChB,OAAA,GAAoC,EAAE,EAAA;AAC3D,QAAA,KAAK,EAAE,CAAC;QAFI,IAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;QAChB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;KAE5D;AAED,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;;AAEnD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACzB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,YAAY,CAAE,IAAI,CAAC,KAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;;;QAGD,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACvE;AACF;;ACtDD;;;;;;;;;;;;;;;;AAgBG;AASH;;AAEG;AACG,MAAO,aAAc,SAAQ,UAAU,CAAA;AAC3C;;;;;;AAMG;IACH,WACuB,CAAA,GAAgB,EAChB,WAAA,GAAwC,EAAE,EAAA;AAC/D,QAAA,KAAK,EAAE,CAAC;QAFa,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA+B;KAEhE;;;;;AAMD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACzB,YAAA,OAAO,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,GAAa,EAAE,IAAI,CAAC,WAAW,CAAC;AAC3D,iBAAA,QAAQ,EAAE,CAAC;AACjB,SAAA;AAAM,aAAA;YACL,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACrD,SAAA;KACF;AACF;;ACtDD;;;;;;;;;;;;;;;;AAgBG;AAWH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EG;SACa,GAAG,CACf,MAAmB,EAAE,YAAuB,EAAE,EAAA;IAChD,OAAO,IAAI,UAAU,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAU,IAAI,CAChB,CAAsD,EAAA;AACxD,IAAA,MAAM,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACrC,OAAO,qBAAqB,CAAC,YAAY,IAAI,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDG;AACG,SAAU,SAAS,CACvB,SAAsE,EAAA;AAEtE,IAAA,OAAO,qBAAqB,CAAC,YAAW;AACtC,QAAA,MAAM,GAAG,GAAG,MAAM,SAAS,EAAE,CAAC;QAC9B,OAAO,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAChD,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,eAAe,MAAM,CACxB,kBAAqC,EACrC,YAA2B,EAAA;IAC7B,OAAO,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACjE,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;AACI,eAAe,UAAU,CAAC,gBAAmC,EAAA;AAElE,IAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACrD;;ACxRA;AAEA;AACM,MAAA,OAAO,GAAG;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}