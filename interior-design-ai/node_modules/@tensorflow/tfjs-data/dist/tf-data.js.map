{"version": 3, "file": "tf-data.js", "sources": ["../../../../node_modules/tslib/tslib.es6.js", "../../../../node_modules/seedrandom/lib/alea.js", "../../../../node_modules/seedrandom/lib/xor128.js", "../../../../node_modules/seedrandom/lib/xorwow.js", "../../../../node_modules/seedrandom/lib/xorshift7.js", "../../../../node_modules/seedrandom/lib/xor4096.js", "../../../../node_modules/seedrandom/lib/tychei.js", "../../../../node_modules/seedrandom/seedrandom.js", "../../../../node_modules/seedrandom/index.js", "../../../../tfjs-data/src/util/deep_map.ts", "../../../../tfjs-data/src/util/deep_clone.ts", "../../../../tfjs-data/src/util/ring_buffer.ts", "../../../../tfjs-data/src/util/growing_ring_buffer.ts", "../../../../tfjs-data/src/iterators/lazy_iterator.ts", "../../../../tfjs-data/src/dataset.ts", "../../../../tfjs-data/src/datasets/text_line_dataset.ts", "../../../../tfjs-data/src/datasets/csv_dataset.ts", "../../../../tfjs-data/src/iterators/microphone_iterator.ts", "../../../../tfjs-data/src/iterators/webcam_iterator.ts", "../../../../tfjs-data/src/datasource.ts", "../../../../tfjs-data/src/iterators/string_iterator.ts", "../../../../tfjs-data/src/iterators/byte_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/file_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/url_chunk_iterator.ts", "../../../../tfjs-data/src/util/source_util.ts", "../../../../tfjs-data/src/sources/file_data_source.ts", "../../../../tfjs-data/src/sources/url_data_source.ts", "../../../../tfjs-data/src/readers.ts", "../../../../tfjs-data/src/version.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\n\n// tslint:disable:no-any\n\n/**\n * A return value for a mapping function that can be applied via deepMap.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapResult = {\n  value: any,\n  recurse: boolean\n};\n\n/**\n * Apply a mapping function to a nested structure in a recursive manner.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapResult`.  The `DeepMapResult` either provides a\n *   replacement value for that node (i.e., replacing the subtree), or indicates\n *   that the node should be processed recursively.\n */\nexport function deepMap(input: any, mapFn: (x: any) => DeepMapResult): any|\n    any[] {\n  return deepMapInternal(input, mapFn);\n}\n\n/**\n * @param seen: A Map of known object mappings (i.e., memoized results of\n *   `mapFn()`)\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepMapInternal(\n    input: any, mapFn: (x: any) => DeepMapResult,\n    seen: Map<any, any> = new Map(), containedIn: Set<{}> = new Set()): any|\n    any[] {\n  if (input == null) {\n    return null;\n  }\n  if (typeof Blob === 'function' && input instanceof Blob) {\n    return input.slice();\n  }\n\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  if (seen.has(input)) {\n    return seen.get(input);\n  }\n  const result = mapFn(input);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep map function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    seen.set(input, result.value);\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const child = input[k];\n      const childResult = deepMapInternal(child, mapFn, seen, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    if (input.__proto__) {\n      mappedIterable.__proto__ = input.__proto__;\n    }\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// TODO(soergel, kangyizhang) Reconsider naming of deepZip() to avoid confusion\n// with zip()\n\n/**\n * Zip nested structures together in a recursive manner.\n *\n * This has the effect of transposing or pivoting data, e.g. converting it from\n * a row-major representation to a column-major representation.\n *\n * For example, `deepZip([{a: 1, b: 2}, {a: 3, b: 4}])` returns\n * `{a: [1, 3], b: [2, 4]}`.\n *\n * The inputs should all have the same nested structure (i.e., of arrays and\n * dicts).  The result is a single object with the same nested structure, where\n * the leaves are arrays collecting the values of the inputs at that location\n * (or, optionally, the result of a custom function applied to those arrays).\n *\n * @param inputs: An array of the objects to zip together.\n * @param zipFn: (optional) A function that expects an array of elements at a\n *   single node of the object tree, and returns a `DeepMapResult`.  The\n *   `DeepMapResult` either provides a result value for that node (i.e.,\n *   representing the subtree), or indicates that the node should be processed\n *   recursively.  The default zipFn recurses as far as possible and places\n *   arrays at the leaves.\n */\nexport function deepZip(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult = zipToList): any|any[] {\n  return deepZipInternal(inputs, zipFn);\n}\n\n/**\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepZipInternal(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult,\n    containedIn: Set<{}> = new Set()): any|any[] {\n  // The recursion follows the structure of input 0; it's assumed that all the\n  // other inputs have the same structure.\n  const input = inputs[0];\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  const result = zipFn(inputs);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep zip function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const children = inputs.map(x => x[k]);\n      const childResult = deepZipInternal(children, zipFn, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// tslint:disable-next-line:no-any\nexport function zipToList(x: any[]): DeepMapResult {\n  if (x === null) {\n    return null;\n  }\n  // TODO(soergel): validate array type?\n\n  if (isIterable(x[0])) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: x, recurse: false};\n  }\n}\n\n/**\n * A return value for an async map function for use with deepMapAndAwaitAll.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapAsyncResult = {\n  value: Promise<any>,\n  recurse: boolean\n};\n\n/**\n * Apply an async mapping function to a nested structure in a recursive manner.\n *\n * This first creates a nested structure of Promises, and then awaits all of\n * those, resulting in a single Promise for a resolved nested structure.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapAsyncResult`.  The `DeepMapAsyncResult` either provides\n *   a `Promise` for a replacement value for that node (i.e., replacing the\n *   subtree), or indicates that the node should be processed recursively.  Note\n *   that the decision whether or not to recurse must be made immediately; only\n *   the mapped value may be promised.\n */\nexport async function deepMapAndAwaitAll(\n    input: any, mapFn: (x: any) => DeepMapAsyncResult): Promise<any|any[]> {\n  const seen: Map<any, any> = new Map();\n\n  // First do a normal deepMap, collecting Promises in 'seen' as a side effect.\n  deepMapInternal(input, mapFn, seen);\n\n  // Replace the Promises in 'seen' in place.\n  // Note TypeScript provides no async map iteration, and regular map iteration\n  // is broken too, so sadly we have to do Array.from() to make it work.\n  // (There's no advantage to Promise.all(), and that would be tricky anyway.)\n  for (const key of Array.from(seen.keys())) {\n    const value = seen.get(key);\n    if (tf.util.isPromise(value)) {\n      const mappedValue = await value;\n      seen.set(key, mappedValue);\n    }\n  }\n\n  // Normal deepMap again, this time filling in the resolved values.\n  // It's unfortunate that we have to do two passes.\n  // TODO(soergel): test performance and think harder about a fast solution.\n  const result = deepMapInternal(input, mapFn, seen);\n  return result;\n}\n\n/**\n * Determine whether the argument is iterable.\n *\n * @returns true if the argument is an array or any non-Tensor object.\n */\n// tslint:disable-next-line:no-any\nexport function isIterable(obj: any): boolean {\n  let isTextDecoder = false;\n  if (tf.env().get('IS_BROWSER')) {\n    isTextDecoder = obj instanceof TextDecoder;\n  } else {\n    // tslint:disable-next-line:no-require-imports\n    const {StringDecoder} = require('string_decoder');\n    isTextDecoder = obj instanceof StringDecoder;\n  }\n  return obj != null && (!ArrayBuffer.isView(obj)) &&\n      (Array.isArray(obj) ||\n       (typeof obj === 'object' && !(obj instanceof tf.Tensor) &&\n        !(obj instanceof Promise) && !isTextDecoder));\n}\n\n/**\n * Determine whether the argument can be converted to Tensor.\n *\n * Tensors, primitives, arrays, and TypedArrays all qualify; anything else does\n * not.\n *\n * @returns true if the argument can be converted to Tensor.\n */\n// tslint:disable-next-line:no-any\nexport function canTensorify(obj: any): boolean {\n  return obj == null || isPrimitive(obj) || Array.isArray(obj) ||\n      (typeof obj === 'object' && (obj instanceof tf.Tensor)) ||\n      tf.util.isTypedArray(obj);\n}\n\n/**\n * Returns true if the given `value` is a primitive type. Otherwise returns\n * false. This is equivalant to node util.isPrimitive\n */\nfunction isPrimitive(value: any): boolean {\n  return (\n      value === null ||\n      (typeof value !== 'object' && typeof value !== 'function'));\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {RingBuffer} from './ring_buffer';\n\nexport class GrowingRingBuffer<T> extends RingBuffer<T> {\n  private static INITIAL_CAPACITY = 32;\n\n  /**\n   * Constructs a `GrowingRingBuffer`.\n   */\n  constructor() {\n    super(GrowingRingBuffer.INITIAL_CAPACITY);\n  }\n\n  override isFull() {\n    return false;\n  }\n\n  override push(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.push(value);\n  }\n\n  override unshift(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.unshift(value);\n  }\n\n  /**\n   * Doubles the capacity of the buffer.\n   */\n  private expand() {\n    const newCapacity = this.capacity * 2;\n    const newData = new Array<T>(newCapacity);\n    const len = this.length();\n\n    // Rotate the buffer to start at index 0 again, since we can't just\n    // allocate more space at the end.\n    for (let i = 0; i < len; i++) {\n      newData[i] = this.get(this.wrap(this.begin + i));\n    }\n\n    this.data = newData;\n    this.capacity = newCapacity;\n    this.doubledCapacity = 2 * this.capacity;\n    this.begin = 0;\n    this.end = len;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {Container} from '../types';\nimport {deepClone} from '../util/deep_clone';\nimport {deepMapAndAwaitAll, DeepMapAsyncResult, DeepMapResult, deepZip, zipToList} from '../util/deep_map';\nimport {GrowingRingBuffer} from '../util/growing_ring_buffer';\nimport {RingBuffer} from '../util/ring_buffer';\n\n/**\n * A nested structure of LazyIterators, used as the input to zip().\n */\nexport type IteratorContainer = Container<LazyIterator<tf.TensorContainer>>;\n\n// Here we implement a simple asynchronous iterator.\n// This lets us avoid using either third-party stream libraries or\n// recent TypeScript language support requiring polyfills.\n\n/**\n * Create a `LazyIterator` from an array of items.\n */\nexport function iteratorFromItems<T>(items: T[]): LazyIterator<T> {\n  return new ArrayIterator(items);\n}\n\n/**\n * Create a `LazyIterator` of incrementing integers.\n */\nexport function iteratorFromIncrementing(start: number): LazyIterator<number> {\n  let i = start;\n  return iteratorFromFunction(() => ({value: i++, done: false}));\n}\n\n/**\n * Create a `LazyIterator` from a function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * await iter.forEachAsync(e => console.log(e));\n * ```\n *\n * @param func A function that produces data on each call.\n */\nexport function iteratorFromFunction<T>(\n    func: () =>\n        IteratorResult<T>| Promise<IteratorResult<T>>): LazyIterator<T> {\n  return new FunctionCallIterator(func);\n}\n\n/**\n * Create a `LazyIterator` by concatenating underlying streams, which are\n * themselves provided as a stream.\n *\n * This can also be thought of as a \"stream flatten\" operation.\n *\n * @param baseIterators A stream of streams to be concatenated.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenated<T>(\n    baseIterators: LazyIterator<LazyIterator<T>>,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return new ChainedIterator(baseIterators, baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by concatenating streams produced by calling a\n * stream-generating function a given number of times.\n *\n * Since a `LazyIterator` is read-once, it cannot be repeated, but this\n * function can be used to achieve a similar effect:\n *\n *   LazyIterator.ofConcatenatedFunction(() => new MyIterator(), 6);\n *\n * @param iteratorFunc: A function that produces a new stream on each call.\n * @param count: The number of times to call the function.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenatedFunction<T>(\n    iteratorFunc: () => IteratorResult<LazyIterator<T>>, count: number,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return iteratorFromConcatenated(\n      iteratorFromFunction(iteratorFunc).take(count), baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by zipping together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nexport function iteratorFromZipped<O extends tf.TensorContainer>(\n    iterators: IteratorContainer,\n    mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL): LazyIterator<O> {\n  return new ZipIterator<O>(iterators, mismatchMode);\n}\n\n/**\n * An asynchronous iterator, providing lazy access to a potentially\n * unbounded stream of elements.\n *\n * Iterator can be obtained from a dataset:\n * `const iter = await dataset.iterator();`\n */\nexport abstract class LazyIterator<T> {\n  // This class implements AsyncIterator<T>, but we have not yet set the\n  // TypeScript --downlevelIteration flag to enable that.\n\n  abstract summary(): string;\n\n  /**\n   * Returns a `Promise` for the next element in the stream.\n   *\n   * When an item can be provided successfully, the return value is\n   * `{value:T, done:false}`.\n   *\n   * Calling next() on a closed stream returns `{value:null, done:true}`.\n   */\n  abstract next(): Promise<IteratorResult<T>>;\n\n  /**\n   * Collect all remaining elements of a bounded stream into an array.\n   * Obviously this will succeed only for small streams that fit in memory.\n   * Useful for testing.\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArray(): Promise<T[]> {\n    const result: T[] = [];\n    let x = await this.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await this.next();\n    }\n    return result;\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArrayForTest(): Promise<T[]> {\n    const stream = this.prefetch(100);\n    const result: T[] = [];\n    let x = await stream.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await stream.next();\n    }\n    return result;\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveFully(): Promise<void> {\n    let x = await this.next();\n    while (!x.done) {\n      x = await this.next();\n    }\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted, or a predicate fails.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveWhile(predicate: (r: T) => boolean): Promise<void> {\n    let x = await this.next();\n    let shouldContinue = predicate(x.value);\n    while ((!x.done) && shouldContinue) {\n      x = await this.next();\n      shouldContinue = predicate(x.value);\n    }\n  }\n\n  /**\n   * Handles errors thrown on this stream using a provided handler function.\n   *\n   * @param handler A function that handles any `Error` thrown during a `next()`\n   *   call and returns true if the stream should continue (dropping the failed\n   *   call) or false if the stream should quietly terminate.  If the handler\n   *   itself throws (or rethrows) an `Error`, that will be propagated.\n   *\n   * @returns A `LazyIterator` of elements passed through from upstream,\n   *   possibly filtering or terminating on upstream `next()` calls that\n   *   throw an `Error`.\n   */\n  handleErrors(handler: (error: Error) => boolean): LazyIterator<T> {\n    return new ErrorHandlingLazyIterator(this, handler);\n  }\n\n  // TODO(soergel): Implement reduce() etc.\n\n  /**\n   * Filters this stream according to `predicate`.\n   *\n   * @param predicate A function mapping a stream element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `LazyIterator` of elements for which the predicate was true.\n   */\n  filter(predicate: (value: T) => boolean): LazyIterator<T> {\n    return new FilterIterator(this, predicate);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  map<O>(transform: (value: T) => O): LazyIterator<O> {\n    return new MapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through an async 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a `Promise` for a\n   *   transformed stream element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  mapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform, forcing serial execution.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  serialMapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform).serial();\n  }\n\n  /**\n   * Maps this stream through a 1-to-many transform.\n   *\n   * @param transform A function mapping a stream element to an array of\n   *   transformed elements.\n   *\n   * @returns A `DataStream` of transformed elements.\n   */\n  flatmap<O>(transform: (value: T) => O[]): LazyIterator<O> {\n    return new FlatmapIterator(this, transform);\n  }\n\n  /**\n   * Apply a function to every element of the stream.\n   *\n   * @param f A function to apply to each stream element.\n   */\n  async forEachAsync(f: (value: T) => void): Promise<void> {\n    return this.map(f).resolveFully();\n  }\n\n  /**\n   * Apply a function to every element of the stream, forcing serial execution.\n   *\n   * @param f A function to apply to each stream element.  Should return 'true'\n   *   to indicate that the stream should continue, or 'false' to cause it to\n   *   terminate.\n   */\n  async serialForEach(f: (value: T) => Promise<boolean>): Promise<void> {\n    return this.serialMapAsync(f).resolveWhile(x => (x === true));\n  }\n\n  /**\n   * Groups elements into batches, represented as arrays of elements.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"Row-major\" means that the resulting batch is simply a collection of\n   * rows: `[row1, row2, row3, ...]`.  This is contrast to the column-major\n   * form, which is needed for vectorized computation.\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `LazyIterator` of batches of elements, represented as arrays\n   *   of the original element type.\n   */\n  rowMajorBatch(batchSize: number, smallLastBatch = true): LazyIterator<T[]> {\n    return new RowMajorBatchIterator(this, batchSize, smallLastBatch);\n  }\n\n  /**\n   * Groups elements into batches, represented in column-major form.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"column-major\" means that the resulting batch is a (potentially\n   * nested) structure representing the columns.  Each column entry, then,\n   * contains a collection of the values found in that column for a range of\n   * input elements.  This representation allows for vectorized computation, in\n   * contrast to the row-major form.\n   *\n   * The inputs should all have the same nested structure (i.e., of arrays and\n   * dicts).  The result is a single object with the same nested structure,\n   * where the leaves are arrays collecting the values of the inputs at that\n   * location (or, optionally, the result of a custom function applied to those\n   * arrays).\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @param zipFn: (optional) A function that expects an array of elements at a\n   *   single node of the object tree, and returns a `DeepMapResult`.  The\n   *   `DeepMapResult` either provides a result value for that node (i.e.,\n   *   representing the subtree), or indicates that the node should be processed\n   *   recursively.  The default zipFn recurses as far as possible and places\n   *   arrays at the leaves.\n   * @returns A `LazyIterator` of batches of elements, represented as an object\n   *   with collections at the leaves.\n   */\n  columnMajorBatch(\n      batchSize: number, smallLastBatch = true,\n      // tslint:disable-next-line:no-any\n      zipFn: (xs: any[]) => DeepMapResult = zipToList):\n      LazyIterator<tf.TensorContainer> {\n    // First collect the desired number of input elements as a row-major batch.\n    const rowBatches = this.rowMajorBatch(batchSize, smallLastBatch);\n    // Now 'rotate' or 'pivot' the data, collecting all values from each column\n    // in the batch (i.e., for each key within the elements) into an array.\n    return rowBatches.map(x => deepZip(x, zipFn));\n  }\n\n  /**\n   * Concatenate this `LazyIterator` with another.\n   *\n   * @param iterator A `LazyIterator` to be concatenated onto this one.\n   * @param baseErrorHandler An optional function that can intercept `Error`s\n   *   raised during a `next()` call on the base stream.  This function can\n   *   decide whether the error should be propagated, whether the error should\n   *   be ignored, or whether the base stream should be terminated.\n   * @returns A `LazyIterator`.\n   */\n  concatenate(\n      iterator: LazyIterator<T>,\n      baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n    return new ChainedIterator(\n        iteratorFromItems([this, iterator]), baseErrorHandler);\n  }\n\n  /**\n   * Limits this stream to return at most `count` items.\n   *\n   * @param count The maximum number of items to provide from the stream. If\n   * a negative or undefined value is given, the entire stream is returned\n   *   unaltered.\n   */\n  take(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new TakeIterator(this, count);\n  }\n\n  /**\n   * Skips the first `count` items in this stream.\n   *\n   * @param count The number of items to skip.  If a negative or undefined\n   * value is given, the entire stream is returned unaltered.\n   */\n  skip(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new SkipIterator(this, count);\n  }\n\n  /**\n   * Prefetch the first `bufferSize` items in this stream.\n   *\n   * Note this prefetches Promises, but makes no guarantees about when those\n   * Promises resolve.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   */\n  prefetch(bufferSize: number): LazyIterator<T> {\n    return new PrefetchIterator(this, bufferSize);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  /**\n   * Randomly shuffles the elements of this stream.\n   *\n   * @param bufferSize: An integer specifying the number of elements from\n   * this stream from which the new stream will sample.\n   * @param seed: (Optional.) An integer specifying the random seed that\n   * will be used to create the distribution.\n   */\n  shuffle(windowSize: number, seed?: string): LazyIterator<T> {\n    return new ShuffleIterator(this, windowSize, seed);\n  }\n\n  /**\n   * Force an iterator to execute serially: each next() call will await the\n   * prior one, so that they cannot execute concurrently.\n   */\n  serial(): LazyIterator<T> {\n    return new SerialIterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on LazyIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// Iterators that just extend LazyIterator directly\n// ============================================================================\n\nclass ArrayIterator<T> extends LazyIterator<T> {\n  private trav = 0;\n  constructor(protected items: T[]) {\n    super();\n  }\n\n  summary() {\n    return `Array of ${this.items.length} items`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.trav >= this.items.length) {\n      return {value: null, done: true};\n    }\n    const item = this.items[this.trav];\n    this.trav++;\n    return {value: deepClone(item), done: false};\n  }\n}\n\nclass FunctionCallIterator<T> extends LazyIterator<T> {\n  constructor(\n      protected nextFn: () => IteratorResult<T>| Promise<IteratorResult<T>>) {\n    super();\n  }\n\n  summary() {\n    return `Function call`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    try {\n      return this.nextFn();\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message =\n          `Error thrown while iterating through a dataset: ${e.message}`;\n      throw e;\n    }\n  }\n}\n\nclass SerialIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(protected upstream: LazyIterator<T>) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Serial`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    return this.upstream.next();\n  }\n}\n\nclass SkipIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  count = 0;\n\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Skip`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider tradeoffs of reading in parallel, eg.\n    // collecting next() promises in an Array and then waiting for\n    // Promise.all() of those. Benefit: pseudo-parallel execution.  Drawback:\n    // maybe delayed GC.\n    while (this.count++ < this.maxCount) {\n      const skipped = await this.upstream.next();\n      // short-circuit if upstream is already empty\n      if (skipped.done) {\n        return skipped;\n      }\n      tf.dispose(skipped.value as {});\n    }\n    return this.upstream.next();\n  }\n}\n\nclass TakeIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Take`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.count++ >= this.maxCount) {\n      return {value: null, done: true};\n    }\n    return this.upstream.next();\n  }\n}\n\n// Note this batch just groups items into row-wise element arrays.\n// Rotating these to a column-wise representation happens only at the dataset\n// level.\nclass RowMajorBatchIterator<T> extends LazyIterator<T[]> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T[]>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected batchSize: number,\n      protected enableSmallLastBatch = true) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> RowMajorBatch`;\n  }\n\n  async next(): Promise<IteratorResult<T[]>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T[]>> {\n    const batch: T[] = [];\n    while (batch.length < this.batchSize) {\n      const item = await this.upstream.next();\n      if (item.done) {\n        if (this.enableSmallLastBatch && batch.length > 0) {\n          return {value: batch, done: false};\n        }\n        return {value: null, done: true};\n      }\n      batch.push(item.value);\n    }\n    return {value: batch, done: false};\n  }\n}\n\nclass FilterIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected predicate: (value: T) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Filter`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      const item = await this.upstream.next();\n      if (item.done || this.predicate(item.value)) {\n        return item;\n      }\n      tf.dispose(item.value as {});\n    }\n  }\n}\n\nclass MapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Map`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\nclass ErrorHandlingLazyIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected handler: (error: Error) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> handleErrors`;\n  }\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      try {\n        return await this.upstream.next();\n      } catch (e) {\n        if (!this.handler(e)) {\n          return {value: null, done: true};\n        }\n        // If the handler returns true, loop and fetch the next upstream item.\n\n        // If the upstream iterator throws an endless stream of errors, and if\n        // the handler says to ignore them, then we loop forever here.  That is\n        // the correct behavior-- it's up to the handler to decide when to stop.\n      }\n    }\n  }\n}\n\nclass AsyncMapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => Promise<O>) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> AsyncMap`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = await this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\n// Iterators that maintain a queue of pending items\n// ============================================================================\n\n/**\n * A base class for transforming streams that operate by maintaining an\n * output queue of elements that are ready to return via next().  This is\n * commonly required when the transformation is 1-to-many:  A call to next()\n * may trigger a call to the underlying stream, which will produce many\n * mapped elements of this stream-- of which we need to return only one, so\n * we have to queue the rest.\n */\nexport abstract class OneToManyIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  protected outputQueue: RingBuffer<T>;\n\n  constructor() {\n    super();\n    this.outputQueue = new GrowingRingBuffer<T>();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  /**\n   * Read one or more chunks from upstream and process them, possibly\n   * reading or writing a carryover, and adding processed items to the\n   * output queue.  Note it's possible that no items are added to the queue\n   * on a given pump() call, even if the upstream stream is not closed\n   * (e.g., because items are filtered).\n   *\n   * @return `true` if any action was taken, i.e. fetching items from the\n   *   upstream source OR adding items to the output queue.  `false` if the\n   *   upstream source is exhausted AND nothing was added to the queue\n   * (i.e., any remaining carryover).\n   */\n  protected abstract pump(): Promise<boolean>;\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // Fetch so that the queue contains at least one item if possible.\n    // If the upstream source is exhausted, AND there are no items left in\n    // the output queue, then this stream is also exhausted.\n    while (this.outputQueue.length() === 0) {\n      // TODO(soergel): consider parallel reads.\n      if (!await this.pump()) {\n        return {value: null, done: true};\n      }\n    }\n    return {value: this.outputQueue.shift(), done: false};\n  }\n}\nclass FlatmapIterator<I, O> extends OneToManyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O[]) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Flatmap`;\n  }\n\n  async pump(): Promise<boolean> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return false;\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // that's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying any\n    // intermediate Tensors.  Here we are concerned only about the inputs.\n    const mappedArray = this.transform(item.value);\n    const outputTensors =\n        tf.tensor_util.getTensorsInContainer(mappedArray as {});\n    this.outputQueue.pushAll(mappedArray);\n\n    // TODO(soergel) faster intersection, and deduplicate outputTensors\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n\n    return true;\n  }\n}\n\n/**\n * Provides a `LazyIterator` that concatenates a stream of underlying\n * streams.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n */\nexport class ChainedIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>> = null;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private iterator: LazyIterator<T> = null;\n  private moreIterators: LazyIterator<LazyIterator<T>>;\n\n  constructor(\n      iterators: LazyIterator<LazyIterator<T>>,\n      private readonly baseErrorHandler?: (e: Error) => boolean) {\n    super();\n    this.moreIterators = iterators;\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of chained summaries';\n    return `${upstreamSummaries} -> Chained`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    this.lastRead = this.readFromChain(this.lastRead);\n    return this.lastRead;\n  }\n\n  private async readFromChain(lastRead: Promise<IteratorResult<T>>):\n      Promise<IteratorResult<T>> {\n    // Must await on the previous read since the previous read may have advanced\n    // the stream of streams, from which we need to read.\n    // This is unfortunate since we can't parallelize reads. Which means\n    // prefetching of chained streams is a no-op.\n    // One solution is to prefetch immediately upstream of this.\n    await lastRead;\n    if (this.iterator == null) {\n      const iteratorResult = await this.moreIterators.next();\n      if (iteratorResult.done) {\n        // No more streams to stream from.\n        return {value: null, done: true};\n      }\n      this.iterator = iteratorResult.value;\n      if (this.baseErrorHandler != null) {\n        this.iterator = this.iterator.handleErrors(this.baseErrorHandler);\n      }\n    }\n    const itemResult = await this.iterator.next();\n    if (itemResult.done) {\n      this.iterator = null;\n      return this.readFromChain(lastRead);\n    }\n    return itemResult;\n  }\n}\n\nexport enum ZipMismatchMode {\n  FAIL,      // require zipped streams to have the same length\n  SHORTEST,  // terminate zip when the first stream is exhausted\n  LONGEST    // use nulls for exhausted streams; use up the longest stream.\n}\n\n/**\n * Provides a `LazyIterator` that zips together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nclass ZipIterator<O extends tf.TensorContainer> extends LazyIterator<O> {\n  private count = 0;\n  private currentPromise: Promise<IteratorResult<O>> = null;\n\n  constructor(\n      protected readonly iterators: IteratorContainer,\n      protected readonly mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL) {\n    super();\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of zip summaries';\n    return `{${upstreamSummaries}} -> Zip`;\n  }\n\n  private async nextState(afterState: Promise<IteratorResult<O>>):\n      Promise<IteratorResult<O>> {\n    // This chaining ensures that the underlying next() are not even called\n    // before the previous ones have resolved.\n    await afterState;\n\n    // Collect underlying iterator \"done\" signals as a side effect in\n    // getNext()\n    let numIterators = 0;\n    let iteratorsDone = 0;\n\n    function getNext(container: IteratorContainer): DeepMapAsyncResult {\n      if (container instanceof LazyIterator) {\n        const result = container.next();\n        return {\n          value: result.then(x => {\n            numIterators++;\n            if (x.done) {\n              iteratorsDone++;\n            }\n            return x.value;\n          }),\n          recurse: false\n        };\n      } else {\n        return {value: null, recurse: true};\n      }\n    }\n\n    const mapped: O = await deepMapAndAwaitAll(this.iterators, getNext);\n\n    if (numIterators === iteratorsDone) {\n      // The streams have all ended.\n      return {value: null, done: true};\n    }\n    if (iteratorsDone > 0) {\n      switch (this.mismatchMode) {\n        case ZipMismatchMode.FAIL:\n          throw new Error(\n              'Zipped streams should have the same length. ' +\n              `Mismatched at element ${this.count}.`);\n        case ZipMismatchMode.SHORTEST:\n          return {value: null, done: true};\n        case ZipMismatchMode.LONGEST:\n        default:\n          // Continue.  The exhausted streams already produced value: null.\n      }\n    }\n\n    this.count++;\n    return {value: mapped, done: false};\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    this.currentPromise = this.nextState(this.currentPromise);\n    return this.currentPromise;\n  }\n}\n\n// Iterators that maintain a ring buffer of pending promises\n// ============================================================================\n\n/**\n * A stream that prefetches a given number of items from an upstream source,\n * returning them in FIFO order.\n *\n * Note this prefetches Promises, but makes no guarantees about when those\n * Promises resolve.\n */\nexport class PrefetchIterator<T> extends LazyIterator<T> {\n  protected buffer: RingBuffer<Promise<IteratorResult<T>>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected bufferSize: number) {\n    super();\n    this.buffer = new RingBuffer<Promise<IteratorResult<T>>>(bufferSize);\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Prefetch`;\n  }\n\n  /**\n   * Refill the prefetch buffer.  Returns only after the buffer is full, or\n   * the upstream source is exhausted.\n   */\n  protected refill() {\n    while (!this.buffer.isFull()) {\n      const v = this.upstream.next();\n      this.buffer.push(v);\n    }\n  }\n\n  next(): Promise<IteratorResult<T>> {\n    this.refill();\n    // This shift will never throw an error because the buffer is always\n    // full after a refill. If the stream is exhausted, the buffer will be\n    // full of Promises that will resolve to the end-of-stream signal.\n    return this.buffer.shift();\n  }\n}\n\n/**\n * A stream that performs a sliding-window random shuffle on an upstream\n * source. This is like a `PrefetchIterator` except that the items are\n * returned in randomized order.  Mixing naturally improves as the buffer\n * size increases.\n */\nexport class ShuffleIterator<T> extends PrefetchIterator<T> {\n  private readonly random: seedrandom.prng;\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private upstreamExhausted = false;\n\n  constructor(\n    protected override upstream: LazyIterator<T>, protected windowSize: number,\n      seed?: string) {\n    super(upstream, windowSize);\n    this.random = seedrandom.alea(seed || tf.util.now().toString());\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  override async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private randomInt(max: number) {\n    return Math.floor(this.random() * max);\n  }\n\n  protected chooseIndex(): number {\n    return this.randomInt(this.buffer.length());\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider performance\n    if (!this.upstreamExhausted) {\n      this.refill();\n    }\n    while (!this.buffer.isEmpty()) {\n      const chosenIndex = this.chooseIndex();\n      const result = await this.buffer.shuffleExcise(chosenIndex);\n      if (result.done) {\n        this.upstreamExhausted = true;\n      } else {\n        this.refill();\n        return result;\n      }\n    }\n    return {value: null, done: true};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\n\n/**\n * Represents a potentially large collection of text lines.\n *\n * The results are not batched.\n */\nexport class TextLineDataset extends Dataset<string> {\n  /**\n   * Create a `TextLineDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   */\n  constructor(protected readonly input: DataSource) {\n    super();\n  }\n\n  async iterator(): Promise<LazyIterator<string>> {\n    const inputIterator = await this.input.iterator();\n    const utf8Iterator = inputIterator.decodeUTF8();\n    const lineIterator = utf8Iterator.split('\\n').map(line => {\n      // Windows/DOS format text file has extra line breaker at the end of line.\n      if (line.endsWith('\\r')) {\n        line = line.slice(0, -1);\n      }\n      return line;\n    });\n    return lineIterator;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\nimport {ColumnConfig, CSVConfig} from '../types';\nimport {TextLineDataset} from './text_line_dataset';\n\nconst CODE_QUOTE = '\"';\nconst STATE_OUT = Symbol('out');\nconst STATE_FIELD = Symbol('field');\nconst STATE_QUOTE = Symbol('quote');\nconst STATE_QUOTE_AFTER_QUOTE = Symbol('quoteafterquote');\nconst STATE_WITHIN_QUOTE_IN_QUOTE = Symbol('quoteinquote');\n\n/**\n * Represents a potentially large collection of delimited text records.\n *\n * The produced `TensorContainer`s each contain one key-value pair for\n * every column of the table.  When a field is empty in the incoming data, the\n * resulting value is `undefined`, or throw error if it is required.  Values\n * that can be parsed as numbers are emitted as type `number`, other values\n * are parsed as `string`.\n *\n * The results are not batched.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport class CSVDataset extends Dataset<TensorContainer> {\n  base: TextLineDataset;\n  private hasHeader = true;\n  private fullColumnNames: string[] = null;\n  private columnNamesValidated = false;\n  private columnConfigs: {[key: string]: ColumnConfig} = null;\n  private configuredColumnsOnly = false;\n  private delimiter = ',';\n  private delimWhitespace = false;\n\n  /**\n   * Returns column names of the csv dataset. If `configuredColumnsOnly` is\n   * true, return column names in `columnConfigs`. If `configuredColumnsOnly` is\n   * false and `columnNames` is provided, `columnNames`. If\n   * `configuredColumnsOnly` is false and `columnNames` is not provided, return\n   * all column names parsed from the csv file. For example usage please go to\n   * `tf.data.csv`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async columnNames() {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    return this.configuredColumnsOnly ? Object.keys(this.columnConfigs) :\n                                        this.fullColumnNames;\n  }\n\n  /* 1) If `columnNames` is provided as string[], use this string[] as output\n   * keys in corresponding order. The length must match the number of inferred\n   * columns if `hasHeader` is true .\n   * 2) If `columnNames` is not provided, parse header line as `columnNames` if\n   * hasHeader is true. If `hasHeader` is false, throw an error.\n   * 3) If `columnConfigs` is provided, all the keys in `columnConfigs` must\n   * exist in parsed `columnNames`.\n   */\n  private async setColumnNames() {\n    const columnNamesFromFile = await this.maybeReadHeaderLine();\n    if (!this.fullColumnNames && !columnNamesFromFile) {\n      // Throw an error if columnNames is not provided and no header line.\n      throw new Error(\n          'Column names must be provided if there is no header line.');\n    } else if (this.fullColumnNames && columnNamesFromFile) {\n      // Check provided columnNames match header line.\n      util.assert(\n          columnNamesFromFile.length === this.fullColumnNames.length,\n          () => 'The length of provided columnNames (' +\n              this.fullColumnNames.length.toString() +\n              ') does not match the length of the header line read from ' +\n              'file (' + columnNamesFromFile.length.toString() + ').');\n    }\n    if (!this.fullColumnNames) {\n      this.fullColumnNames = columnNamesFromFile;\n    }\n    // Check if there are duplicate column names.\n    const counts: {[key: string]: number} = this.fullColumnNames.reduce(\n        (countAcc: {[key: string]: number}, name) => {\n          countAcc[name] = (countAcc[name] + 1) || 1;\n          return countAcc;\n        },\n        {});\n    const duplicateNames =\n        Object.keys(counts).filter((name) => (counts[name] > 1));\n    util.assert(\n        duplicateNames.length === 0,\n        () => 'Duplicate column names found: ' + duplicateNames.toString());\n    // Check if keys in columnConfigs match columnNames.\n    if (this.columnConfigs) {\n      for (const key of Object.keys(this.columnConfigs)) {\n        const index = this.fullColumnNames.indexOf(key);\n        if (index === -1) {\n          throw new Error(\n              'The key \"' + key +\n              '\" provided in columnConfigs does not match any of the column ' +\n              'names (' + this.fullColumnNames.toString() + ').');\n        }\n      }\n    }\n    this.columnNamesValidated = true;\n  }\n\n  private async maybeReadHeaderLine() {\n    if (this.hasHeader) {\n      const iter = await this.base.iterator();\n      const firstElement = await iter.next();\n      if (firstElement.done) {\n        throw new Error('No data was found for CSV parsing.');\n      }\n      const firstLine: string = firstElement.value;\n      const headers = this.parseRow(firstLine, false);\n      return headers;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Create a `CSVDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   * @param csvConfig (Optional) A CSVConfig object that contains configurations\n   *     of reading and decoding from CSV file(s).\n   *\n   *     hasHeader: (Optional) A boolean value that indicates whether the first\n   *     row of provided CSV file is a header line with column names, and should\n   *     not be included in the data. Defaults to `true`.\n   *\n   *     columnNames: (Optional) A list of strings that corresponds to\n   *     the CSV column names, in order. If provided, it ignores the column\n   *     names inferred from the header row. If not provided, infers the column\n   *     names from the first row of the records. If hasHeader is false and\n   *     columnNames is not provided, this method throws an error.\n   *\n   *     columnConfigs: (Optional) A dictionary whose key is column names, value\n   *     is an object stating if this column is required, column's data type,\n   *     default value, and if this column is label. If provided, keys must\n   *     correspond to names provided in columnNames or inferred from the file\n   *     header lines. If isLabel is true any column, returns an array of two\n   *     items: the first item is a dict of features key/value pairs, the second\n   *     item is a dict of labels key/value pairs. If no feature is marked as\n   *     label, returns a dict of features only.\n   *\n   *     configuredColumnsOnly (Optional) If true, only columns provided in\n   *     columnConfigs will be parsed and provided during iteration.\n   *\n   *     delimiter (Optional) The string used to parse each line of the input\n   *     file. Defaults to `,`.\n   */\n  constructor(protected readonly input: DataSource, csvConfig?: CSVConfig) {\n    super();\n    this.base = new TextLineDataset(input);\n    if (!csvConfig) {\n      csvConfig = {};\n    }\n    this.hasHeader = csvConfig.hasHeader === false ? false : true;\n    this.fullColumnNames = csvConfig.columnNames;\n    this.columnConfigs = csvConfig.columnConfigs;\n    this.configuredColumnsOnly = csvConfig.configuredColumnsOnly;\n    if (csvConfig.delimWhitespace) {\n      util.assert(\n          csvConfig.delimiter == null,\n          () =>\n              'Delimiter should not be provided when delimWhitespace is true.');\n      this.delimWhitespace = true;\n      this.delimiter = ' ';\n    } else {\n      this.delimiter = csvConfig.delimiter ? csvConfig.delimiter : ',';\n    }\n  }\n\n  async iterator(): Promise<LazyIterator<TensorContainer>> {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    let lines = await this.base.iterator();\n    if (this.hasHeader) {\n      // We previously read the first line to get the columnNames.\n      // Now that we're providing data, skip it.\n      lines = lines.skip(1);\n    }\n    return lines.map(x => this.makeDataElement(x));\n  }\n\n  makeDataElement(line: string): TensorContainer {\n    const values = this.parseRow(line);\n    const features: {[key: string]: TensorContainer} = {};\n    const labels: {[key: string]: TensorContainer} = {};\n\n    for (let i = 0; i < this.fullColumnNames.length; i++) {\n      const key = this.fullColumnNames[i];\n      const config = this.columnConfigs ? this.columnConfigs[key] : null;\n      if (this.configuredColumnsOnly && !config) {\n        // This column is not selected.\n        continue;\n      } else {\n        const value = values[i];\n        let parsedValue = null;\n        if (value === '') {\n          // If default value is provided, use it. If default value is not\n          // provided, set as undefined.\n          if (config && config.default !== undefined) {\n            parsedValue = config.default;\n          } else if (config && (config.required || config.isLabel)) {\n            throw new Error(\n                `Required column ${key} is empty in this line: ${line}`);\n          } else {\n            parsedValue = undefined;\n          }\n        } else {\n          // A value is present, so parse it based on type\n          const valueAsNum = Number(value);\n          if (isNaN(valueAsNum)) {\n            // The value is a string and this column is declared as boolean\n            // in config, parse it as boolean.\n            if (config && config.dtype === 'bool') {\n              parsedValue = this.getBoolean(value);\n            } else {\n              // Set value as string\n              parsedValue = value;\n            }\n          } else if (!config || !config.dtype) {\n            // If this value is a number and no type config is provided, return\n            // it as number.\n            parsedValue = valueAsNum;\n          } else {\n            // If this value is a number and data type is provided, parse it\n            // according to provided data type.\n            switch (config.dtype) {\n              case 'float32':\n                parsedValue = valueAsNum;\n                break;\n              case 'int32':\n                parsedValue = Math.floor(valueAsNum);\n                break;\n              case 'bool':\n                parsedValue = this.getBoolean(value);\n                break;\n              default:\n                parsedValue = valueAsNum;\n            }\n          }\n        }\n        // Check if this column is label.\n        (config && config.isLabel) ? labels[key] = parsedValue :\n                                     features[key] = parsedValue;\n      }\n    }\n    // If label exists, return an object of features and labels as {xs:features,\n    // ys:labels}, otherwise return features only.\n    if (Object.keys(labels).length === 0) {\n      return features;\n\n    } else {\n      return {xs: features, ys: labels};\n    }\n  }\n\n  private getBoolean(value: string): number {\n    if (value === '1' || value.toLowerCase() === 'true') {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n\n  // adapted from https://beta.observablehq.com/@mbostock/streaming-csv\n  private parseRow(line: string, validateElementCount = true): string[] {\n    const result: string[] = [];\n    let readOffset = 0;\n    const readLength = line.length;\n    let currentState = STATE_OUT;\n    // Goes through the line to parse quote.\n    for (let i = 0; i < readLength; i++) {\n      switch (currentState) {\n        // Before enter a new field\n        case STATE_OUT:\n          switch (line.charAt(i)) {\n            // Enter a quoted field\n            case CODE_QUOTE:\n              readOffset = i + 1;\n              currentState = STATE_QUOTE;\n              break;\n            // Read an empty field\n            case this.delimiter:\n              readOffset = i + 1;\n              // If delimiter is white space and configured to collapse\n              // multiple white spaces, ignore this white space.\n              if (this.delimiter === ' ' && this.delimWhitespace) {\n                break;\n              }\n              result.push('');\n              currentState = STATE_OUT;\n              break;\n            // Enter an unquoted field\n            default:\n              currentState = STATE_FIELD;\n              readOffset = i;\n              break;\n          }\n          break;\n        // In an unquoted field\n        case STATE_FIELD:\n          switch (line.charAt(i)) {\n            // Exit an unquoted field, add it to result\n            case this.delimiter:\n              result.push(line.substring(readOffset, i));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            default:\n          }\n          break;\n        // In a quoted field\n        case STATE_QUOTE:\n          switch (line.charAt(i)) {\n            // Read a quote after a quote\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE_AFTER_QUOTE;\n              break;\n            default:\n          }\n          break;\n        // This state means it's right after a second quote in a field\n        case STATE_QUOTE_AFTER_QUOTE:\n          switch (line.charAt(i)) {\n            // Finished a quoted field\n            case this.delimiter:\n              result.push(line.substring(readOffset, i - 1));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            // Finished a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            // In a quoted part in a quoted field\n            default:\n              currentState = STATE_WITHIN_QUOTE_IN_QUOTE;\n              break;\n          }\n          break;\n        case STATE_WITHIN_QUOTE_IN_QUOTE:\n          switch (line.charAt(i)) {\n            // Exit a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            default:\n          }\n          break;\n        default:\n      }\n    }\n    // Adds last item based on if it is quoted.\n    if (currentState === STATE_QUOTE_AFTER_QUOTE) {\n      result.push(line.substring(readOffset, readLength - 1));\n    } else {\n      result.push(line.substring(readOffset));\n    }\n    // Check if each row has the same number of elements as column names.\n    if (validateElementCount && result.length !== this.fullColumnNames.length) {\n      throw new Error(`Invalid row in csv file. Should have ${\n          this.fullColumnNames.length} elements in a row, but got ${result}`);\n    }\n    return result;\n  }\n}\n\n// TODO(soergel): add more basic datasets for parity with tf.data\n// tf.data.FixedLengthRecordDataset()\n// tf.data.TFRecordDataset()\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env, Tensor, tensor, Tensor2D, Tensor3D, TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {MicrophoneConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of tensors from microphone audio stream. The tensors are\n * representing audio data as frequency-domain spectrogram generated with\n * browser's native FFT. Tensors representing time-domain waveform is available\n * based on configuration. Only works in browser environment.\n */\nexport class MicrophoneIterator extends LazyIterator<TensorContainer> {\n  private isClosed = false;\n  private stream: MediaStream;\n  private readonly fftSize: number;\n  private readonly columnTruncateLength: number;\n  private freqData: Float32Array;\n  private timeData: Float32Array;\n  private readonly numFrames: number;\n  private analyser: AnalyserNode;\n  private audioContext: AudioContext;\n  private sampleRateHz: number;\n  private readonly audioTrackConstraints: MediaTrackConstraints;\n  private readonly smoothingTimeConstant: number;\n  private readonly includeSpectrogram: boolean;\n  private readonly includeWaveform: boolean;\n\n  private constructor(protected readonly microphoneConfig: MicrophoneConfig) {\n    super();\n    this.fftSize = microphoneConfig.fftSize || 1024;\n    const fftSizeLog2 = Math.log2(this.fftSize);\n    if (this.fftSize < 0 || fftSizeLog2 < 4 || fftSizeLog2 > 14 ||\n        !Number.isInteger(fftSizeLog2)) {\n      throw new Error(\n          `Invalid fftSize: it must be a power of 2 between ` +\n          `2 to 4 and 2 to 14, but got ${this.fftSize}`);\n    }\n\n    this.numFrames = microphoneConfig.numFramesPerSpectrogram || 43;\n    this.sampleRateHz = microphoneConfig.sampleRateHz;\n    this.columnTruncateLength =\n        microphoneConfig.columnTruncateLength || this.fftSize;\n    this.audioTrackConstraints = microphoneConfig.audioTrackConstraints;\n    this.smoothingTimeConstant = microphoneConfig.smoothingTimeConstant || 0;\n\n    this.includeSpectrogram =\n        microphoneConfig.includeSpectrogram === false ? false : true;\n    this.includeWaveform =\n        microphoneConfig.includeWaveform === true ? true : false;\n    if (!this.includeSpectrogram && !this.includeWaveform) {\n      throw new Error(\n          'Both includeSpectrogram and includeWaveform are false. ' +\n          'At least one type of data should be returned.');\n    }\n  }\n\n  summary() {\n    return `microphone`;\n  }\n\n  // Construct a MicrophoneIterator and start the audio stream.\n  static async create(microphoneConfig: MicrophoneConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'microphone API is only supported in browser environment.');\n    }\n\n    const microphoneIterator = new MicrophoneIterator(microphoneConfig);\n\n    // Call async function start() to initialize the audio stream.\n    await microphoneIterator.start();\n\n    return microphoneIterator;\n  }\n\n  // Start the audio stream and FFT.\n  async start(): Promise<void> {\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        audio: this.audioTrackConstraints == null ? true :\n                                                    this.audioTrackConstraints,\n        video: false\n      });\n    } catch (e) {\n      throw new Error(\n          `Error thrown while initializing video stream: ${e.message}`);\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain audio from microphone.');\n    }\n\n    const ctxConstructor =\n        // tslint:disable-next-line:no-any\n        (window as any).AudioContext || (window as any).webkitAudioContext;\n    this.audioContext = new ctxConstructor();\n\n    if (!this.sampleRateHz) {\n      // If sample rate is not provided, use the available sample rate on\n      // device.\n      this.sampleRateHz = this.audioContext.sampleRate;\n    } else if (this.audioContext.sampleRate !== this.sampleRateHz) {\n      throw new Error(\n          `Mismatch in sampling rate: ` +\n          `Expected: ${this.sampleRateHz}; ` +\n          `Actual: ${this.audioContext.sampleRate}`);\n    }\n\n    const streamSource = this.audioContext.createMediaStreamSource(this.stream);\n    this.analyser = this.audioContext.createAnalyser();\n    this.analyser.fftSize = this.fftSize * 2;\n    this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;\n    streamSource.connect(this.analyser);\n    this.freqData = new Float32Array(this.fftSize);\n    this.timeData = new Float32Array(this.fftSize);\n    return;\n  }\n\n  async next(): Promise<IteratorResult<TensorContainer>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let spectrogramTensor: Tensor;\n    let waveformTensor: Tensor;\n\n    const audioDataQueue = await this.getAudioData();\n    if (this.includeSpectrogram) {\n      const freqData = this.flattenQueue(audioDataQueue.freqDataQueue);\n      spectrogramTensor = this.getTensorFromAudioDataArray(\n          freqData, [this.numFrames, this.columnTruncateLength, 1]);\n    }\n    if (this.includeWaveform) {\n      const timeData = this.flattenQueue(audioDataQueue.timeDataQueue);\n      waveformTensor = this.getTensorFromAudioDataArray(\n          timeData, [this.numFrames * this.fftSize, 1]);\n    }\n\n    return {\n      value: {'spectrogram': spectrogramTensor, 'waveform': waveformTensor},\n      done: false\n    };\n  }\n\n  // Capture one result from the audio stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<{spectrogram: Tensor3D, waveform: Tensor2D}> {\n    return (await this.next()).value as\n        {spectrogram: Tensor3D, waveform: Tensor2D};\n  }\n\n  private async getAudioData():\n      Promise<{freqDataQueue: Float32Array[], timeDataQueue: Float32Array[]}> {\n    const freqDataQueue: Float32Array[] = [];\n    const timeDataQueue: Float32Array[] = [];\n    let currentFrames = 0;\n    return new Promise(resolve => {\n      const intervalID = setInterval(() => {\n        if (this.includeSpectrogram) {\n          this.analyser.getFloatFrequencyData(this.freqData);\n          // If the audio stream is initializing, return empty queue.\n          if (this.freqData[0] === -Infinity) {\n            resolve({freqDataQueue, timeDataQueue});\n          }\n          freqDataQueue.push(this.freqData.slice(0, this.columnTruncateLength));\n        }\n        if (this.includeWaveform) {\n          this.analyser.getFloatTimeDomainData(this.timeData);\n          timeDataQueue.push(this.timeData.slice());\n        }\n\n        // Clean interval and return when all frames have been collected\n        if (++currentFrames === this.numFrames) {\n          clearInterval(intervalID);\n          resolve({freqDataQueue, timeDataQueue});\n        }\n      }, this.fftSize / this.sampleRateHz * 1e3);\n    });\n  }\n\n  // Stop the audio stream and pause the iterator.\n  stop(): void {\n    if (!this.isClosed) {\n      this.isClosed = true;\n      this.analyser.disconnect();\n      this.audioContext.close();\n      if (this.stream != null && this.stream.getTracks().length > 0) {\n        this.stream.getTracks()[0].stop();\n      }\n    }\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor[]> {\n    throw new Error('Can not convert infinite audio stream to array.');\n  }\n\n  // Return audio sampling rate in Hz\n  getSampleRate(): number {\n    return this.sampleRateHz;\n  }\n\n  private flattenQueue(queue: Float32Array[]): Float32Array {\n    const frameSize = queue[0].length;\n    const freqData = new Float32Array(queue.length * frameSize);\n    queue.forEach((data, i) => freqData.set(data, i * frameSize));\n    return freqData;\n  }\n\n  private getTensorFromAudioDataArray(freqData: Float32Array, shape: number[]):\n      Tensor {\n    const vals = new Float32Array(util.sizeFromShape(shape));\n    // If the data is less than the output shape, the rest is padded with zeros.\n    vals.set(freqData, vals.length - freqData.length);\n    return tensor(vals, shape);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {browser, cast, env, expandDims, image, reshape, tensor1d, Tensor1D, tensor2d, Tensor2D, Tensor3D, Tensor4D, tidy, util} from '@tensorflow/tfjs-core';\nimport {WebcamConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of image tensors from webcam video stream. Only works in\n * browser environment.\n */\nexport class WebcamIterator extends LazyIterator<Tensor3D> {\n  private isClosed = true;\n  private stream: MediaStream;\n  private resize = false;\n  private cropSize: [number, number];\n  private cropBox: Tensor2D;\n  private cropBoxInd: Tensor1D;\n\n  private constructor(\n      protected readonly webcamVideoElement: HTMLVideoElement,\n      protected readonly webcamConfig: WebcamConfig) {\n    super();\n    if (this.needToResize()) {\n      this.resize = true;\n      this.cropSize =\n          [this.webcamConfig.resizeHeight, this.webcamConfig.resizeWidth];\n      this.cropBoxInd = tensor1d([0], 'int32');\n      if (this.webcamConfig.centerCrop) {\n        // Calculate the box based on resizing shape.\n        const widthCroppingRatio =\n            this.webcamConfig.resizeWidth * 1.0 / this.webcamVideoElement.width;\n        const heightCroppingRatio = this.webcamConfig.resizeHeight * 1.0 /\n            this.webcamVideoElement.height;\n        const widthCropStart = (1 - widthCroppingRatio) / 2;\n        const heightCropStart = (1 - heightCroppingRatio) / 2;\n        const widthCropEnd = widthCropStart + widthCroppingRatio;\n        const heightCropEnd = heightCroppingRatio + heightCropStart;\n        this.cropBox = tensor2d(\n            [heightCropStart, widthCropStart, heightCropEnd, widthCropEnd],\n            [1, 4]);\n      } else {\n        this.cropBox = tensor2d([0, 0, 1, 1], [1, 4]);\n      }\n    }\n  }\n\n  summary() {\n    return `webcam`;\n  }\n\n  // Construct a WebcamIterator and start it's video stream.\n  static async create(\n      webcamVideoElement?: HTMLVideoElement, webcamConfig: WebcamConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'tf.data.webcam is only supported in browser environment.');\n    }\n\n    if (!webcamVideoElement) {\n      // If webcam video element is not provided, create a hidden video element\n      // with provided width and height.\n      webcamVideoElement = document.createElement('video');\n      if (!webcamConfig.resizeWidth || !webcamConfig.resizeHeight) {\n        throw new Error(\n            'Please provide webcam video element, or resizeWidth and ' +\n            'resizeHeight to create a hidden video element.');\n      }\n      webcamVideoElement.width = webcamConfig.resizeWidth;\n      webcamVideoElement.height = webcamConfig.resizeHeight;\n    }\n    const webcamIterator = new WebcamIterator(webcamVideoElement, webcamConfig);\n\n    // Call async function to initialize the video stream.\n    await webcamIterator.start();\n\n    return webcamIterator;\n  }\n\n  // Async function to start video stream.\n  async start(): Promise<void> {\n    if (this.webcamConfig.facingMode) {\n      util.assert(\n          (this.webcamConfig.facingMode === 'user') ||\n              (this.webcamConfig.facingMode === 'environment'),\n          () =>\n              `Invalid webcam facing mode: ${this.webcamConfig.facingMode}. ` +\n              `Please provide 'user' or 'environment'`);\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          deviceId: this.webcamConfig.deviceId,\n          facingMode: this.webcamConfig.facingMode ?\n              this.webcamConfig.facingMode :\n              'user',\n          width: this.webcamVideoElement.width,\n          height: this.webcamVideoElement.height\n        }\n      });\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message = `Error thrown while initializing video stream: ${e.message}`;\n      throw e;\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain video from webcam.');\n    }\n\n    // Older browsers may not have srcObject\n    try {\n      this.webcamVideoElement.srcObject = this.stream;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = window.URL.createObjectURL(\n        this.stream as unknown as MediaSource);\n    }\n    // Start the webcam video stream\n    this.webcamVideoElement.play();\n\n    this.isClosed = false;\n\n    return new Promise<void>(resolve => {\n      // Add event listener to make sure the webcam has been fully initialized.\n      this.webcamVideoElement.onloadedmetadata = () => {\n        resolve();\n      };\n    });\n  }\n\n  async next(): Promise<IteratorResult<Tensor3D>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let img;\n    try {\n      img = browser.fromPixels(this.webcamVideoElement);\n    } catch (e) {\n      throw new Error(\n          `Error thrown converting video to pixels: ${JSON.stringify(e)}`);\n    }\n    if (this.resize) {\n      try {\n        return {value: this.cropAndResizeFrame(img), done: false};\n      } catch (e) {\n        throw new Error(`Error thrown cropping the video: ${e.message}`);\n      } finally {\n        img.dispose();\n      }\n    } else {\n      return {value: img, done: false};\n    }\n  }\n\n  private needToResize() {\n    // If resizeWidth and resizeHeight are provided, and different from the\n    // width and height of original HTMLVideoElement, then resizing and cropping\n    // is required.\n    if (this.webcamConfig.resizeWidth && this.webcamConfig.resizeHeight &&\n        (this.webcamVideoElement.width !== this.webcamConfig.resizeWidth ||\n         this.webcamVideoElement.height !== this.webcamConfig.resizeHeight)) {\n      return true;\n    }\n    return false;\n  }\n\n  // Cropping and resizing each frame based on config\n  cropAndResizeFrame(img: Tensor3D): Tensor3D {\n    return tidy(() => {\n      const expandedImage: Tensor4D = expandDims(cast(img, 'float32'), (0));\n      let resizedImage;\n      resizedImage = image.cropAndResize(\n          expandedImage, this.cropBox, this.cropBoxInd, this.cropSize,\n          'bilinear');\n      // Extract image from batch cropping.\n      const shape = resizedImage.shape;\n      return reshape(resizedImage, shape.slice(1) as [number, number, number]);\n    });\n  }\n\n  // Capture one frame from the video stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<Tensor3D> {\n    return (await this.next()).value;\n  }\n\n  // Stop the video stream and pause webcam iterator.\n  stop(): void {\n    const tracks = this.stream.getTracks();\n\n    tracks.forEach(track => track.stop());\n\n    try {\n      this.webcamVideoElement.srcObject = null;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = null;\n    }\n    this.isClosed = true;\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor3D[]> {\n    throw new Error('Can not convert infinite video stream to array.');\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {ByteChunkIterator} from './iterators/byte_chunk_iterator';\n\n/**\n * Represents a data source readable as a stream of binary data chunks.\n *\n * Because `Dataset`s can be read repeatedly (via `Dataset.iterator()`), this\n * provides a means to repeatedly create streams from the underlying data\n * sources.\n */\nexport abstract class DataSource {\n  /**\n   * Obtain a new stream of binary data chunks.\n   *\n   * Starts the new stream from the beginning of the data source, even if other\n   * streams have been obtained previously.\n   */\n  abstract iterator(): Promise<ByteChunkIterator>;\n\n  // TODO(soergel): consider chainable Dataset construction here\n}\n\n// TODO(soergel): consider convenience factory functions here\n// in combination with chainable source->dataset above, e.g.:\n// tf.data.url(...).asCsvDataset().shuffle().batch()\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\n\nexport abstract class StringIterator extends LazyIterator<string> {\n  /**\n   * Splits a string stream on a given separator.\n   *\n   * It is assumed that the incoming chunk boundaries have no semantic meaning,\n   * so conceptually the incoming stream is treated simply as the concatenation\n   * of its elements.\n   *\n   * The outgoing stream provides chunks corresponding to the results of the\n   * standard string split() operation (even if such a chunk spanned incoming\n   * chunks).  The separators are not included.\n   *\n   * A typical usage is to split a text file (represented as a stream with\n   * arbitrary chunk boundaries) into lines.\n   *\n   * @param upstream A readable stream of strings that can be treated as\n   *   concatenated.\n   * @param separator A character to split on.\n   */\n  split(separator: string): StringIterator {\n    return new SplitIterator(this, separator);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on StringIterator.  Unfortunately they can't be placed in separate files, due\n// to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class SplitIterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass SplitIterator extends StringIterator {\n  private impl: SplitIteratorImpl;\n\n  constructor(protected upstream: LazyIterator<string>, separator: string) {\n    super();\n    this.impl = new SplitIteratorImpl(upstream, separator);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\nclass SplitIteratorImpl extends OneToManyIterator<string> {\n  // A partial string at the end of an upstream chunk\n  carryover = '';\n\n  constructor(\n      protected upstream: LazyIterator<string>, protected separator: string) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Split('${this.separator}')`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    if (chunkResult.done) {\n      if (this.carryover === '') {\n        return false;\n      }\n\n      // Pretend that the pump succeeded in order to emit the small last batch.\n      // The next pump() call will actually fail.\n      this.outputQueue.push(this.carryover);\n      this.carryover = '';\n      return true;\n    }\n    const lines = chunkResult.value.split(this.separator) as string[];\n    // Note the behavior: \" ab \".split(' ') === ['', 'ab', '']\n    // Thus the carryover may be '' if the separator falls on a chunk\n    // boundary; this produces the correct result.\n\n    lines[0] = this.carryover + lines[0];\n    for (const line of lines.slice(0, -1)) {\n      this.outputQueue.push(line);\n    }\n    this.carryover = lines[lines.length - 1];\n\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\nimport {StringIterator} from './string_iterator';\n\nexport abstract class ByteChunkIterator extends LazyIterator<Uint8Array> {\n  /**\n   * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n   *\n   * The byte arrays producetd from the ByteChunkIterator on which this is\n   * called will be interpreted as concatenated.  No assumptions are made about\n   * the boundaries of the incoming chunks, so a multi-byte UTF8 encoding of a\n   * character may span the boundary between chunks.  This naturally happens,\n   * for instance, when reading fixed-size byte arrays from a file.\n   */\n  decodeUTF8(): StringIterator {\n    return new Utf8Iterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on ByteChunkIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class Utf8Iterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass Utf8Iterator extends StringIterator {\n  private impl: Utf8IteratorImpl;\n\n  constructor(protected upstream: LazyIterator<Uint8Array>) {\n    super();\n    this.impl = new Utf8IteratorImpl(upstream);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\n/**\n * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n *\n * This is tricky because the incoming byte array boundaries may disrupt a\n * multi-byte UTF8 character. Thus any incomplete character data at the end of\n * a chunk must be carried over and prepended to the next chunk before\n * decoding. Luckily with native decoder, TextDecoder in browser and\n * string_decoder in node, byte array boundaries are handled automatically.\n *\n * In the context of an input pipeline for machine learning, UTF8 decoding is\n * needed to parse text files containing training examples or prediction\n * requests (e.g., formatted as CSV or JSON). We cannot use the built-in\n * decoding provided by FileReader.readAsText() because here we are in a\n * streaming context, which FileReader does not support.\n *\n * @param upstream A `LazyIterator` of `Uint8Arrays` containing UTF8-encoded\n *   text, which should be interpreted as concatenated.  No assumptions are\n *   made about the boundaries of the incoming chunks, so a multi-byte UTF8\n *   encoding of a character may span the boundary between chunks.  This\n *   naturally happens, for instance, when reading fixed-size byte arrays from a\n *   file.\n */\nclass Utf8IteratorImpl extends OneToManyIterator<string> {\n  // `decoder` as `any` here to dynamically assign value based on the\n  // environment.\n  // tslint:disable-next-line:no-any\n  decoder: any;\n\n  constructor(protected readonly upstream: LazyIterator<Uint8Array>) {\n    super();\n    if (env().get('IS_BROWSER')) {\n      this.decoder = new TextDecoder('utf-8');\n    } else {\n      // tslint:disable-next-line:no-require-imports\n      const {StringDecoder} = require('string_decoder');\n      this.decoder = new StringDecoder('utf8');\n    }\n  }\n  summary() {\n    return `${this.upstream.summary()} -> Utf8`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    let chunk;\n    if (chunkResult.done) {\n      return false;\n    } else {\n      chunk = chunkResult.value;\n    }\n\n    let text: string;\n    if (env().get('IS_BROWSER')) {\n      text = this.decoder.decode(chunk, {stream: true});\n    } else {\n      text = this.decoder.write(Buffer.from(chunk.buffer));\n    }\n    this.outputQueue.push(text);\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// Skip tslint any type check cause this method is aiming to check type of\n// input.\n// tslint:disable-next-line:no-any\nexport function isLocalPath(source: any): boolean {\n  return (typeof source === 'string') && source.slice(0, 7) === 'file://';\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {urlChunkIterator} from '../iterators/url_chunk_iterator';\nimport {isLocalPath} from '../util/source_util';\nimport {FileDataSource} from './file_data_source';\n\n/*\n * Represents a URL readable as a stream of binary data chunks.\n */\nexport class URLDataSource extends DataSource {\n  /**\n   * Create a `URLDataSource`.\n   *\n   * @param url A source URL string, or a `Request` object.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected readonly url: RequestInfo,\n      protected readonly fileOptions: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  // TODO(soergel): provide appropriate caching options.  Currently this\n  // will download the URL anew for each call to iterator().  Since we have\n  // to treat the downloaded file as a blob/buffer anyway, we may as well retain\n  // it-- but that raises GC issues.  Also we may want a persistent disk cache.\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.url)) {\n      return (new FileDataSource(this.url as string, this.fileOptions))\n          .iterator();\n    } else {\n      return urlChunkIterator(this.url, this.fileOptions);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer} from '@tensorflow/tfjs-core';\nimport {Dataset, datasetFromIteratorFn} from './dataset';\nimport {CSVDataset} from './datasets/csv_dataset';\nimport {iteratorFromFunction} from './iterators/lazy_iterator';\nimport {MicrophoneIterator} from './iterators/microphone_iterator';\nimport {WebcamIterator} from './iterators/webcam_iterator';\nimport {URLDataSource} from './sources/url_data_source';\nimport {CSVConfig, MicrophoneConfig, WebcamConfig} from './types';\n\n/**\n * Create a `CSVDataset` by reading and decoding CSV file(s) from provided URL\n * or local path if it's in Node environment.\n *\n * Note: If isLabel in columnConfigs is `true` for at least one column, the\n * element in returned `CSVDataset` will be an object of\n * `{xs:features, ys:labels}`: xs is a dict of features key/value pairs, ys\n * is a dict of labels key/value pairs. If no column is marked as label,\n * returns a dict of features only.\n *\n * ```js\n * const csvUrl =\n * 'https://storage.googleapis.com/tfjs-examples/multivariate-linear-regression/data/boston-housing-train.csv';\n *\n * async function run() {\n *   // We want to predict the column \"medv\", which represents a median value of\n *   // a home (in $1000s), so we mark it as a label.\n *   const csvDataset = tf.data.csv(\n *     csvUrl, {\n *       columnConfigs: {\n *         medv: {\n *           isLabel: true\n *         }\n *       }\n *     });\n *\n *   // Number of features is the number of column names minus one for the label\n *   // column.\n *   const numOfFeatures = (await csvDataset.columnNames()).length - 1;\n *\n *   // Prepare the Dataset for training.\n *   const flattenedDataset =\n *     csvDataset\n *     .map(({xs, ys}) =>\n *       {\n *         // Convert xs(features) and ys(labels) from object form (keyed by\n *         // column name) to array form.\n *         return {xs:Object.values(xs), ys:Object.values(ys)};\n *       })\n *     .batch(10);\n *\n *   // Define the model.\n *   const model = tf.sequential();\n *   model.add(tf.layers.dense({\n *     inputShape: [numOfFeatures],\n *     units: 1\n *   }));\n *   model.compile({\n *     optimizer: tf.train.sgd(0.000001),\n *     loss: 'meanSquaredError'\n *   });\n *\n *   // Fit the model using the prepared Dataset\n *   return model.fitDataset(flattenedDataset, {\n *     epochs: 10,\n *     callbacks: {\n *       onEpochEnd: async (epoch, logs) => {\n *         console.log(epoch + ':' + logs.loss);\n *       }\n *     }\n *   });\n * }\n *\n * await run();\n * ```\n *\n * @param source URL or local path to get CSV file. If it's a local path, it\n * must have prefix `file://` and it only works in node environment.\n * @param csvConfig (Optional) A CSVConfig object that contains configurations\n *     of reading and decoding from CSV file(s).\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function csv(\n    source: RequestInfo, csvConfig: CSVConfig = {}): CSVDataset {\n  return new CSVDataset(new URLDataSource(source), csvConfig);\n}\n\n/**\n * Create a `Dataset` that produces each element by calling a provided function.\n *\n * Note that repeated iterations over this `Dataset` may produce different\n * results, because the function will be called anew for each element of each\n * iteration.\n *\n * Also, beware that the sequence of calls to this function may be out of order\n * in time with respect to the logical order of the Dataset. This is due to the\n * asynchronous lazy nature of stream processing, and depends on downstream\n * transformations (e.g. .shuffle()). If the provided function is pure, this is\n * no problem, but if it is a closure over a mutable state (e.g., a traversal\n * pointer), then the order of the produced elements may be scrambled.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const ds = tf.data.func(func);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param f A function that produces one data element on each call.\n */\nexport function func<T extends TensorContainer>(\n    f: () => IteratorResult<T>| Promise<IteratorResult<T>>): Dataset<T> {\n  const iter = iteratorFromFunction(f);\n  return datasetFromIteratorFn(async () => iter);\n}\n\n/**\n * Create a `Dataset` that produces each element from provided JavaScript\n * generator, which is a function that returns a (potentially async) iterator.\n *\n * For more information on iterators and generators, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Iterators_and_Generators .\n * For the iterator protocol, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols .\n *\n * Example of creating a dataset from an iterator factory:\n * ```js\n * function makeIterator() {\n *   const numElements = 10;\n *   let index = 0;\n *\n *   const iterator = {\n *     next: () => {\n *       let result;\n *       if (index < numElements) {\n *         result = {value: index, done: false};\n *         index++;\n *         return result;\n *       }\n *       return {value: index, done: true};\n *     }\n *   };\n *   return iterator;\n * }\n * const ds = tf.data.generator(makeIterator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * Example of creating a dataset from a generator:\n * ```js\n * function* dataGenerator() {\n *   const numElements = 10;\n *   let index = 0;\n *   while (index < numElements) {\n *     const x = index;\n *     index++;\n *     yield x;\n *   }\n * }\n *\n * const ds = tf.data.generator(dataGenerator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param generator A JavaScript function that returns\n *     a (potentially async) JavaScript iterator.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function generator<T extends TensorContainer>(\n  generator: () => Iterator<T> | Promise<Iterator<T>> | AsyncIterator<T>,\n): Dataset<T> {\n  return datasetFromIteratorFn(async () => {\n    const gen = await generator();\n    return iteratorFromFunction(() => gen.next());\n  });\n}\n\n/**\n * Create an iterator that generates `Tensor`s from webcam video stream. This\n * API only works in Browser environment when the device has webcam.\n *\n * Note: this code snippet only works when the device has a webcam. It will\n * request permission to open the webcam when running.\n * ```js\n * const videoElement = document.createElement('video');\n * videoElement.width = 100;\n * videoElement.height = 100;\n * const cam = await tf.data.webcam(videoElement);\n * const img = await cam.capture();\n * img.print();\n * cam.stop();\n * ```\n *\n * @param webcamVideoElement A `HTMLVideoElement` used to play video from\n *     webcam. If this element is not provided, a hidden `HTMLVideoElement` will\n *     be created. In that case, `resizeWidth` and `resizeHeight` must be\n *     provided to set the generated tensor shape.\n * @param webcamConfig A `WebcamConfig` object that contains configurations of\n *     reading and manipulating data from webcam video stream.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function webcam(\n    webcamVideoElement?: HTMLVideoElement,\n    webcamConfig?: WebcamConfig): Promise<WebcamIterator> {\n  return WebcamIterator.create(webcamVideoElement, webcamConfig);\n}\n\n/**\n * Create an iterator that generates frequency-domain spectrogram `Tensor`s from\n * microphone audio stream with browser's native FFT. This API only works in\n * browser environment when the device has microphone.\n *\n * Note: this code snippet only works when the device has a microphone. It will\n * request permission to open the microphone when running.\n * ```js\n * const mic = await tf.data.microphone({\n *   fftSize: 1024,\n *   columnTruncateLength: 232,\n *   numFramesPerSpectrogram: 43,\n *   sampleRateHz:44100,\n *   includeSpectrogram: true,\n *   includeWaveform: true\n * });\n * const audioData = await mic.capture();\n * const spectrogramTensor = audioData.spectrogram;\n * spectrogramTensor.print();\n * const waveformTensor = audioData.waveform;\n * waveformTensor.print();\n * mic.stop();\n * ```\n *\n * @param microphoneConfig A `MicrophoneConfig` object that contains\n *     configurations of reading audio data from microphone.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function microphone(microphoneConfig?: MicrophoneConfig):\n    Promise<MicrophoneIterator> {\n  return MicrophoneIterator.create(microphoneConfig);\n}\n", "/** @license See the LICENSE file. */\n\n// This code is auto-generated, do not modify this file!\nconst version = '4.22.0';\nexport {version};\n"], "names": ["this", "define", "require$$0", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "require$$6", "tf", "seedrandom.alea", "util", "env", "tensor", "tensor1d", "tensor2d", "browser", "tidy", "expandDims", "cast", "image", "reshape"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;;;;;;;;;;;;;IAagF;IAChF;IAEA,IAAI,aAAa,GAAG,UAAS,CAAC,EAAE,CAAC,EAAA;QAC7B,aAAa,GAAG,MAAM,CAAC,cAAc;aAChC,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAA,EAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;YAC5E,UAAU,CAAC,EAAE,CAAC,EAAA,EAAI,KAAK,IAAI,CAAC,IAAI,CAAC;gBAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;oBAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACtG,IAAA,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;IAEc,SAAA,SAAS,CAAC,CAAC,EAAE,CAAC,EAAA;IAC1B,IAAA,IAAI,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,IAAI;IACrC,QAAA,MAAM,IAAI,SAAS,CAAC,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,+BAA+B,CAAC,CAAC;IAC9F,IAAA,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,SAAS,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IACvC,IAAA,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF,CAAC;IAwCK,SAAU,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAA;IACvD,IAAA,SAAS,KAAK,CAAC,KAAK,EAAA,EAAI,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,OAAO,EAAA,EAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5G,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,OAAO,EAAE,MAAM,EAAA;IACrD,QAAA,SAAS,SAAS,CAAC,KAAK,EAAA,EAAI,IAAI;gBAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAAE,SAAA;IAAC,QAAA,OAAO,CAAC,EAAE;gBAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAAE,SAAA,EAAE;IAC3F,QAAA,SAAS,QAAQ,CAAC,KAAK,EAAA,EAAI,IAAI;gBAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAAE,SAAA;IAAC,QAAA,OAAO,CAAC,EAAE;gBAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAAE,SAAA,EAAE;IAC9F,QAAA,SAAS,IAAI,CAAC,MAAM,EAAA,EAAI,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE;IAC9G,QAAA,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1E,KAAC,CAAC,CAAC;IACP,CAAC;IAEe,SAAA,WAAW,CAAC,OAAO,EAAE,IAAI,EAAA;IACrC,IAAA,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,YAAA,EAAa,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAAE,YAAA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjH,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzJ,SAAS,IAAI,CAAC,CAAC,EAAA,EAAI,OAAO,UAAU,CAAC,EAAI,EAAA,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClE,SAAS,IAAI,CAAC,EAAE,EAAA;IACZ,QAAA,IAAI,CAAC;IAAE,YAAA,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;IAC9D,QAAA,OAAO,CAAC;gBAAE,IAAI;IACV,gBAAA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI;IAAE,oBAAA,OAAO,CAAC,CAAC;IAC7J,gBAAA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAAE,oBAAA,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,gBAAA,QAAQ,EAAE,CAAC,CAAC,CAAC;IACT,oBAAA,KAAK,CAAC,CAAC;IAAC,oBAAA,KAAK,CAAC;4BAAE,CAAC,GAAG,EAAE,CAAC;4BAAC,MAAM;IAC9B,oBAAA,KAAK,CAAC;4BAAE,CAAC,CAAC,KAAK,EAAE,CAAC;IAAC,wBAAA,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxD,oBAAA,KAAK,CAAC;4BAAE,CAAC,CAAC,KAAK,EAAE,CAAC;IAAC,wBAAA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,wBAAA,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;4BAAC,SAAS;IACjD,oBAAA,KAAK,CAAC;IAAE,wBAAA,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAAC,wBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;4BAAC,SAAS;IACjD,oBAAA;IACI,wBAAA,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;gCAAE,CAAC,GAAG,CAAC,CAAC;gCAAC,SAAS;IAAE,yBAAA;IAC5G,wBAAA,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAAE,4BAAA,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gCAAC,MAAM;IAAE,yBAAA;IACtF,wBAAA,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IAAE,4BAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,GAAG,EAAE,CAAC;gCAAC,MAAM;IAAE,yBAAA;4BACrE,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IAAE,4BAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,4BAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gCAAC,MAAM;IAAE,yBAAA;4BACnE,IAAI,CAAC,CAAC,CAAC,CAAC;IAAE,4BAAA,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACtB,wBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;4BAAC,SAAS;IAC9B,iBAAA;oBACD,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC9B,aAAA;IAAC,YAAA,OAAO,CAAC,EAAE;IAAE,gBAAA,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAAC,CAAC,GAAG,CAAC,CAAC;IAAE,aAAA;IAAS,oBAAA;IAAE,gBAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAAE,aAAA;IAC1D,QAAA,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAAE,YAAA,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;YAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;SACpF;IACL,CAAC;IAkBK,SAAU,QAAQ,CAAC,CAAC,EAAA;QACtB,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC9E,IAAA,IAAI,CAAC;IAAE,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACxB,IAAA,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;YAAE,OAAO;IAC1C,YAAA,IAAI,EAAE,YAAA;IACF,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM;wBAAE,CAAC,GAAG,KAAK,CAAC,CAAC;IACnC,gBAAA,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;iBAC3C;aACJ,CAAC;IACF,IAAA,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;IAC3F,CAAC;IAEe,SAAA,MAAM,CAAC,CAAC,EAAE,CAAC,EAAA;IACvB,IAAA,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC3D,IAAA,IAAI,CAAC,CAAC;IAAE,QAAA,OAAO,CAAC,CAAC;IACjB,IAAA,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QACjC,IAAI;YACA,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI;IAAE,YAAA,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC9E,KAAA;IACD,IAAA,OAAO,KAAK,EAAE;IAAE,QAAA,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAAE,KAAA;IAC/B,YAAA;YACJ,IAAI;IACA,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAAE,gBAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,SAAA;IACO,gBAAA;IAAE,YAAA,IAAI,CAAC;oBAAE,MAAM,CAAC,CAAC,KAAK,CAAC;IAAE,SAAA;IACpC,KAAA;IACD,IAAA,OAAO,EAAE,CAAC;IACd,CAAC;aAkBe,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAA;IACxC,IAAA,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;IAAE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACjF,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE;IACpB,gBAAA,IAAI,CAAC,EAAE;IAAE,oBAAA,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrD,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACnB,aAAA;IACJ,SAAA;IACD,IAAA,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICrJA,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,IAAI,CAAC,IAAI,EAAA;gBAChB,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;gBAE7B,EAAE,CAAC,IAAI,GAAG,YAAA;IACR,gBAAA,IAAI,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,sBAAsB,CAAC;IACxD,gBAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACd,gBAAA,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IACd,gBAAA,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC,aAAG,CAAC;;IAGF,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,YAAA,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,YAAA,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,YAAA,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE;IAAE,gBAAA,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAAE,aAAA;IAC9B,YAAA,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,YAAA,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE;IAAE,gBAAA,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAAE,aAAA;IAC9B,YAAA,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,YAAA,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE;IAAE,gBAAA,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAAE,aAAA;gBAC9B,IAAI,GAAG,IAAI,CAAC;aACb;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACZ,YAAA,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACZ,YAAA,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACZ,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;gBACtB,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,EACnB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;IACnB,YAAA,IAAI,CAAC,KAAK,GAAG,cAAa,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,CAAA;gBACjE,IAAI,CAAC,MAAM,GAAG,YAAA;IACZ,gBAAA,OAAO,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAsB,CAAC;IACrE,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;IACT,gBAAA,IAAI,QAAO,KAAK,CAAC,IAAI,QAAQ;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC/C,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,SAAS,IAAI,GAAA;gBACX,IAAI,CAAC,GAAG,UAAU,CAAC;gBAEnB,IAAI,IAAI,GAAG,UAAS,IAAI,EAAA;IACtB,gBAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,oBAAA,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACxB,oBAAA,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC;IAChC,oBAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACZ,CAAC,IAAI,CAAC,CAAC;wBACP,CAAC,IAAI,CAAC,CAAC;IACP,oBAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACZ,CAAC,IAAI,CAAC,CAAC;IACP,oBAAA,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;IACtB,iBAAA;oBACD,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;IAC9C,aAAG,CAAC;IAEF,YAAA,OAAO,IAAI,CAAC;aACb;IAGD,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,SAAA;IAED,KAAC,EACCA,cAAI,EAC2B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;IC5GD,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,MAAM,CAAC,IAAI,EAAA;IAClB,YAAA,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;IAE5B,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;;gBAGT,EAAE,CAAC,IAAI,GAAG,YAAA;IACR,gBAAA,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5B,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACZ,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACZ,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACZ,gBAAA,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,aAAG,CAAC;IAEF,YAAA,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;;IAEvB,gBAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IACb,aAAA;IAAM,iBAAA;;oBAEL,OAAO,IAAI,IAAI,CAAC;IACjB,aAAA;;IAGD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5C,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClC,EAAE,CAAC,IAAI,EAAE,CAAC;IACX,aAAA;aACF;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;IACtB,YAAA,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,EACrB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,YAAa,EAAA,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,GAAG,YAAA;oBACZ,GAAG;IACD,oBAAA,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACtC,QAAQ,MAAM,KAAK,CAAC,EAAE;IACvB,gBAAA,OAAO,MAAM,CAAC;IAClB,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;IACT,gBAAA,IAAI,QAAO,KAAK,CAAC,IAAI,QAAQ;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC/C,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,SAAA;IAED,KAAC,EACCD,cAAI,EAC2B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;IC3ED,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,MAAM,CAAC,IAAI,EAAA;IAClB,YAAA,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;;gBAG5B,EAAE,CAAC,IAAI,GAAG,YAAA;IACR,gBAAA,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAAC,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAAC,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAAC,gBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACnD,gBAAA,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;IAC9B,qBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1D,aAAG,CAAC;IAEF,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IAET,YAAA,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;;IAEvB,gBAAA,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IACb,aAAA;IAAM,iBAAA;;oBAEL,OAAO,IAAI,IAAI,CAAC;IACjB,aAAA;;IAGD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5C,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClC,gBAAA,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;IACvB,oBAAA,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IAChC,iBAAA;oBACD,EAAE,CAAC,IAAI,EAAE,CAAC;IACX,aAAA;aACF;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;IACtB,YAAA,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,EACrB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,YAAa,EAAA,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,GAAG,YAAA;oBACZ,GAAG;IACD,oBAAA,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACtC,QAAQ,MAAM,KAAK,CAAC,EAAE;IACvB,gBAAA,OAAO,MAAM,CAAC;IAClB,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;IACT,gBAAA,IAAI,QAAO,KAAK,CAAC,IAAI,QAAQ;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC/C,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,SAAA;IAED,KAAC,EACCD,cAAI,EAC2B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;;;IC9ED,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,MAAM,CAAC,IAAI,EAAA;gBAClB,IAAI,EAAE,GAAG,IAAI,CAAC;;gBAGd,EAAE,CAAC,IAAI,GAAG,YAAA;;IAER,gBAAA,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAE,CAAA,CAAC,EAAE,CAAC,CAAI;IAChC,gBAAA,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,gBAAA,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;oBAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC5C,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBACxC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACvC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACtC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;oBAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,gBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACT,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnB,gBAAA,OAAO,CAAC,CAAC;IACb,aAAG,CAAC;IAEF,YAAA,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAA;IACpB,gBAAA,IAAI,CAAC,CAAE,CAAG,CAAC,GAAG,GAAG;IAEjB,gBAAA,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;;IAEvB,oBAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjB,iBAAA;IAAM,qBAAA;;IAEL,oBAAA,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;IACjB,oBAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;IAChC,wBAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;iCACrB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IACjD,qBAAA;IACF,iBAAA;;IAED,gBAAA,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC;IAAE,oBAAA,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/B,gBAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;wBAAC,CAAC;oBACtC,IAAI,CAAC,IAAI,CAAC;wBAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAAM,oBAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEzC,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;;oBAGT,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;wBACxB,EAAE,CAAC,IAAI,EAAE,CAAC;IACX,iBAAA;iBACF;IAED,YAAA,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aAChB;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;gBAChB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAClB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;gBACtB,IAAI,IAAI,IAAI,IAAI;IAAE,gBAAA,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;IACrC,YAAA,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,EACrB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,YAAa,EAAA,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,GAAG,YAAA;oBACZ,GAAG;IACD,oBAAA,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACtC,QAAQ,MAAM,KAAK,CAAC,EAAE;IACvB,gBAAA,OAAO,MAAM,CAAC;IAClB,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;oBACT,IAAI,KAAK,CAAC,CAAC;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7B,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACvB,SAAA;IAED,KAAC,EACCD,cAAI,EAC2B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtED,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,MAAM,CAAC,IAAI,EAAA;gBAClB,IAAI,EAAE,GAAG,IAAI,CAAC;;gBAGd,EAAE,CAAC,IAAI,GAAG,YAAA;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAE7B,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;;oBAEhC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;IACtB,gBAAA,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAC3B,gBAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,gBAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,gBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACd,gBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;;oBAEd,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;;IAET,gBAAA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IACtC,aAAG,CAAC;IAEF,YAAA,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAA;IACpB,gBAAA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;IACvC,gBAAA,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;;wBAEvB,CAAC,GAAG,IAAI,CAAC;wBACT,IAAI,GAAG,IAAI,CAAC;IACb,iBAAA;IAAM,qBAAA;;IAEL,oBAAA,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;wBACnB,CAAC,GAAG,CAAC,CAAC;wBACN,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,iBAAA;;IAED,gBAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;;IAEnC,oBAAA,IAAI,IAAI;IAAE,wBAAA,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;;wBAEvD,IAAI,CAAC,KAAK,CAAC;4BAAE,CAAC,GAAG,CAAC,CAAC;IACnB,oBAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,oBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACd,oBAAA,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACZ,oBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;wBACd,IAAI,CAAC,IAAI,CAAC,EAAE;4BACV,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;IACzB,wBAAA,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,wBAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,qBAAA;IACF,iBAAA;;oBAED,IAAI,CAAC,IAAI,GAAG,EAAE;IACZ,oBAAA,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,iBAAA;;;;oBAID,CAAC,GAAG,GAAG,CAAC;IACR,gBAAA,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;wBAC5B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;IACtB,oBAAA,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAC3B,oBAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,oBAAA,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACb,oBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACd,oBAAA,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACd,oBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACd,iBAAA;;IAED,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;iBACV;IAED,YAAA,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aAChB;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAClB,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;gBACtB,IAAI,IAAI,IAAI,IAAI;IAAE,gBAAA,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;IACrC,YAAA,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,EACrB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,YAAa,EAAA,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,GAAG,YAAA;oBACZ,GAAG;IACD,oBAAA,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACtC,QAAQ,MAAM,KAAK,CAAC,EAAE;IACvB,gBAAA,OAAO,MAAM,CAAC;IAClB,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;oBACT,IAAI,KAAK,CAAC,CAAC;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7B,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACrB,SAAA;IAED,KAAC,EACCD,cAAI;IACJ,IAA+B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;;IC7ID,IAAA,CAAC,UAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAA;YAEhC,SAAS,MAAM,CAAC,IAAI,EAAA;IAClB,YAAA,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;;gBAG5B,EAAE,CAAC,IAAI,GAAG,YAAA;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3C,gBAAA,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChB,gBAAA,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChB,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtC,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvB,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;oBAClC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,aAAG,CAAC;IAEJ;;;;;;;;;;;;;;IAcA;IAEE,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,YAAA,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;IACtB,YAAA,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;gBAElB,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;oBAE7B,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,CAAC;IAChC,gBAAA,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IACjB,aAAA;IAAM,iBAAA;;oBAEL,OAAO,IAAI,IAAI,CAAC;IACjB,aAAA;;IAGD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5C,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClC,EAAE,CAAC,IAAI,EAAE,CAAC;IACX,aAAA;aACF;IAED,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,OAAO,CAAC,CAAC;aACV;IAED,QAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAA;IACtB,YAAA,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,EACrB,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAC1B,IAAI,GAAG,YAAa,EAAA,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,GAAG,YAAA;oBACZ,GAAG;IACD,oBAAA,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;qBACtC,QAAQ,MAAM,KAAK,CAAC,EAAE;IACvB,gBAAA,OAAO,MAAM,CAAC;IAClB,aAAG,CAAC;IACF,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,YAAA,IAAI,KAAK,EAAE;IACT,gBAAA,IAAI,QAAO,KAAK,CAAC,IAAI,QAAQ;IAAE,oBAAA,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC/C,gBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACjD,aAAA;IACD,YAAA,OAAO,IAAI,CAAC;aACb;IAED,QAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;IAC5B,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,SAAA;IAAM,aAAA,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;gBAC/B,MAAM,CAAC,cAAa,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,SAAA;IAAM,aAAA;IACL,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,SAAA;IAED,KAAC,EACCD,cAAI,EAC2B,MAAM;QACrC,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;SACxC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC5ED,IAAA,CAAC,UAAU,MAAM,EAAE,IAAI,EAAE,IAAI,EAAA;;;;IAK7B,QAAA,IAAI,KAAK,GAAG,GAAG;YACX,MAAM,GAAG,CAAC;YACV,MAAM,GAAG,EAAE;YACX,OAAO,GAAG,QAAQ;IAClB,QAAA,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,EACpC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAClC,QAAQ,GAAG,YAAY,GAAG,CAAC,EAC3B,IAAI,GAAG,KAAK,GAAG,CAAC,EAChB,UAAU,CAAC;;;;;IAMf,QAAA,SAAS,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAA;gBACzC,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE,CAAC,CAAC;;gBAGlE,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IACxC,gBAAA,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;;IAG/C,YAAA,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAIzB,YAAA,IAAI,IAAI,GAAG,YAAA;oBACT,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;oBAClB,CAAC,GAAG,UAAU;IACd,gBAAA,CAAC,GAAG,CAAC,CAAC;IACV,gBAAA,OAAO,CAAC,GAAG,YAAY,EAAE;wBACvB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;IACpB,oBAAA,CAAC,IAAI,KAAK,CAAC;wBACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,iBAAA;IACD,gBAAA,OAAO,CAAC,IAAI,QAAQ,EAAE;IACpB,oBAAA,CAAC,IAAI,CAAC,CAAC;IACP,oBAAA,CAAC,IAAI,CAAC,CAAC;IACP,oBAAA,CAAC,MAAM,CAAC,CAAC;IACV,iBAAA;oBACD,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvB,aAAG,CAAC;IAEF,YAAA,IAAI,CAAC,KAAK,GAAG,YAAa,EAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAA;IACjD,YAAA,IAAI,CAAC,KAAK,GAAG,YAAa,EAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAA;IAC3D,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;;gBAGnB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;IAG/B,YAAA,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ;IAC5B,gBAAA,UAAS,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAA;IACtC,oBAAA,IAAI,KAAK,EAAE;;4BAET,IAAI,KAAK,CAAC,CAAC,EAAE;IAAE,4BAAA,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAAE,yBAAA;;IAEnC,wBAAA,IAAI,CAAC,KAAK,GAAG,YAAA,EAAa,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;IACnD,qBAAA;;;IAID,oBAAA,IAAI,YAAY,EAAE;IAAE,wBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAAC,wBAAA,OAAO,IAAI,CAAC;IAAE,qBAAA;;;;IAInD,wBAAA,OAAO,IAAI,CAAC;IAClB,iBAAA,EACL,IAAI,EACJ,SAAS,EACT,QAAQ,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,EACrD,OAAO,CAAC,KAAK,CAAC,CAAC;aAChB;;;;;;;;;;;YAYD,SAAS,IAAI,CAAC,GAAG,EAAA;IACf,YAAA,IAAI,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,EACtB,EAAE,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;;gBAGzD,IAAI,CAAC,MAAM,EAAE;IAAE,gBAAA,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;IAAE,aAAA;;gBAGlC,OAAO,CAAC,GAAG,KAAK,EAAE;IAChB,gBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IACZ,aAAA;gBACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;IAC1B,gBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,gBAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACV,aAAA;;IAGD,YAAA,CAAC,EAAE,CAAC,CAAC,GAAG,UAAS,KAAK,EAAA;;oBAEpB,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EACR,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBACjC,OAAO,KAAK,EAAE,EAAE;IACd,oBAAA,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,oBAAA,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,iBAAA;IACD,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IAAC,gBAAA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACnB,gBAAA,OAAO,CAAC,CAAC;;;;IAIV,aAAA,EAAE,KAAK,CAAC,CAAC;aACX;;;;;IAMD,QAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAA;IAChB,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAClB,YAAA,OAAO,CAAC,CAAC;aACV;;;;;IAMD,QAAA,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,EAAA;IACzB,YAAA,IAAI,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;IAC1C,YAAA,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;oBAC5B,KAAK,IAAI,IAAI,GAAG,EAAE;wBAChB,IAAI;IAAE,wBAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE,qBAAA;wBAAC,OAAO,CAAC,EAAE,GAAE;IACjE,iBAAA;IACF,aAAA;gBACD,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;aACtE;;;;;;IAOD,QAAA,SAAS,MAAM,CAAC,IAAI,EAAE,GAAG,EAAA;gBACvB,IAAI,UAAU,GAAG,IAAI,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACzC,YAAA,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE;IAC5B,gBAAA,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;wBACX,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,aAAA;IACD,YAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;;;;;;IAOD,QAAA,SAAS,QAAQ,GAAA;gBACf,IAAI;IACF,gBAAA,IAAI,GAAG,CAAC;oBACR,IAAI,UAAU,KAAK,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE;;IAEhD,oBAAA,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAClB,iBAAA;IAAM,qBAAA;IACL,oBAAA,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC5B,oBAAA,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;IACzD,iBAAA;IACD,gBAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtB,aAAA;IAAC,YAAA,OAAO,CAAC,EAAE;IACV,gBAAA,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS,EAC1B,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IACzC,gBAAA,OAAO,CAAC,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,aAAA;aACF;;;;;YAMD,SAAS,QAAQ,CAAC,CAAC,EAAA;gBACjB,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACxC;;;;;;;;YASD,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;;;;;YAM5B,IAAmC,MAAM,CAAC,OAAO,EAAE;IACjD,YAAA,MAAA,CAAA,OAAA,GAAiB,UAAU,CAAC;;gBAE5B,IAAI;oBACF,UAAU,GAAG,UAAiB,CAAC;IAChC,aAAA;gBAAC,OAAO,EAAE,EAAE,GAAE;IAChB,SAAA;iBAEM;;IAEL,YAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC;IACrC,SAAA;;IAID,KAAC;;;IAGC,IAAA,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAGD,cAAI,EAC3C,EAAE;IACF,IAAA,IAAI;SACL,CAAA;;;;IC5PD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA,IAAI,IAAI,GAAGE,WAAqB,CAAC;IAEjC;IACA;IACA;IACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;IAErC;IACA;IACA;IACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;IAErC;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,SAAS,GAAGC,gBAA0B,CAAC;IAE3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,OAAO,GAAGC,cAAwB,CAAC;IAEvC;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;IAErC;IACA;IACA,IAAI,EAAE,GAAGC,iBAAuB,CAAC;IAEjC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;IACf,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;IACzB,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;IACrB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IAEnB,IAAA,UAAc,GAAG,EAAE;;IC1BnB;;;;;;;;;;;;;;;;IAgBG;IACa,SAAA,OAAO,CAAC,KAAU,EAAE,KAAgC,EAAA;IAElE,IAAA,OAAO,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;;;IAKG;IACH,SAAS,eAAe,CACpB,KAAU,EAAE,KAAgC,EAC5C,IAA+B,EAAE,WAAgC,EAAA;QAAjE,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAA0B,GAAA,IAAA,GAAG,EAAE,CAAA,EAAA;QAAE,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAA2B,GAAA,IAAA,GAAG,EAAE,CAAA,EAAA;QAEnE,IAAI,KAAK,IAAI,IAAI,EAAE;IACjB,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;QACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,EAAE;IACvD,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACtB,KAAA;IAED,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC3D,KAAA;IACD,IAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACnB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxB,KAAA;IACD,IAAA,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAE5B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;IAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;IAC1E,KAAA;IAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO,MAAM,CAAC,KAAK,CAAC;IACrB,KAAA;IAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;IAE5B,QAAA,IAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvB,QAAA,KAAK,IAAM,CAAC,IAAI,KAAK,EAAE;IACrB,YAAA,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,YAAA,IAAM,WAAW,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACrE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;IACjC,SAAA;IACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,SAAS,EAAE;IACnB,YAAA,cAAc,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAC5C,SAAA;IACD,QAAA,OAAO,cAAc,CAAC;IACvB,KAAA;IAAM,SAAA;IACL,QAAA,MAAM,IAAI,KAAK,CAAC,gDAAyC,KAAK,CAAE,CAAC,CAAC;IACnE,KAAA;IACH,CAAC;IAED;IACA;IAEA;;;;;;;;;;;;;;;;;;;;;IAqBG;IACa,SAAA,OAAO,CACnB,MAAa,EAAE,KAA+C,EAAA;IAA/C,IAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAA+C,GAAA,SAAA,CAAA,EAAA;IAChE,IAAA,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;IAGG;IACH,SAAS,eAAe,CACpB,MAAa,EAAE,KAAmC,EAClD,WAAgC,EAAA;QAAhC,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAA2B,GAAA,IAAA,GAAG,EAAE,CAAA,EAAA;;;IAGlC,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC3D,KAAA;IACD,IAAA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;IAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;IAC1E,KAAA;IAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACnB,OAAO,MAAM,CAAC,KAAK,CAAC;IACrB,KAAA;IAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;IAE5B,QAAA,IAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oCACZ,CAAC,EAAA;IACV,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,EAAI,EAAA,OAAA,CAAC,CAAC,CAAC,CAAC,CAAJ,EAAI,CAAC,CAAC;gBACvC,IAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAClE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;;YAHlC,KAAK,IAAM,CAAC,IAAI,KAAK,EAAA;wBAAV,CAAC,CAAA,CAAA;IAIX,SAAA;IACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1B,QAAA,OAAO,cAAc,CAAC;IACvB,KAAA;IAAM,SAAA;IACL,QAAA,MAAM,IAAI,KAAK,CAAC,gDAAyC,KAAK,CAAE,CAAC,CAAC;IACnE,KAAA;IACH,CAAC;IAED;IACM,SAAU,SAAS,CAAC,CAAQ,EAAA;QAChC,IAAI,CAAC,KAAK,IAAI,EAAE;IACd,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;;IAGD,IAAA,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACrC,KAAA;IAAM,SAAA;YACL,OAAO,EAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;IACnC,KAAA;IACH,CAAC;IAaD;;;;;;;;;;;;;;;;;;;;;IAqBG;IACmB,SAAA,kBAAkB,CACpC,KAAU,EAAE,KAAqC,EAAA;;;;;;;IAC7C,oBAAA,IAAI,GAAkB,IAAI,GAAG,EAAE,CAAC;;IAGtC,oBAAA,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;;;wBAMlB,EAAA,GAAA,QAAA,CAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;;;;wBAA9B,GAAG,GAAA,EAAA,CAAA,KAAA,CAAA;IACN,oBAAA,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;6BACxBC,aAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAxB,OAAwB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACN,oBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAA,CAAA;;IAAzB,oBAAA,WAAW,GAAG,EAAW,CAAA,IAAA,EAAA,CAAA;IAC/B,oBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;;;;;;;;;;;;;;;;;wBAOzB,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACnD,oBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;IACf,CAAA;IAED;;;;IAIG;IACH;IACM,SAAU,UAAU,CAAC,GAAQ,EAAA;QACjC,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAIA,aAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;IAC9B,QAAA,aAAa,GAAG,GAAG,YAAY,WAAW,CAAC;IAC5C,KAAA;IAAM,SAAA;;IAEE,QAAA,IAAA,aAAa,GAAI,OAAO,CAAC,gBAAgB,CAAC,cAA7B,CAA8B;IAClD,QAAA,aAAa,GAAG,GAAG,YAAY,aAAa,CAAC;IAC9C,KAAA;IACD,IAAA,OAAO,GAAG,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC5C,SAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IAClB,aAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,EAAE,GAAG,YAAYA,aAAE,CAAC,MAAM,CAAC;oBACtD,EAAE,GAAG,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;IAOG;IACH;IACM,SAAU,YAAY,CAAC,GAAQ,EAAA;IACnC,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;IACxD,SAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,GAAG,YAAYA,aAAE,CAAC,MAAM,CAAC,CAAC;IACvD,QAAAA,aAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;;IAGG;IACH,SAAS,WAAW,CAAC,KAAU,EAAA;QAC7B,QACI,KAAK,KAAK,IAAI;aACb,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE;IAClE;;ICpSA;;;;;;;;;;;;;;;;IAgBG;IAKG,SAAU,SAAS,CAAI,SAAY,EAAA;IACvC,IAAA,OAAO,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED;IACA,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,IAAA,IAAI,IAAI,YAAYA,aAAE,CAAC,MAAM,EAAE;IAC7B,QAAA,QAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,EAAE;IAChD,KAAA;IAAM,SAAA,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;YAC3B,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACrC,KAAA;IAAM,SAAA;YACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;IACtC,KAAA;IACH;;IClCA;;;;;;;;;;;;;;;;IAgBG;IAEH;;IAEG;IACH,IAAA,UAAA,kBAAA,YAAA;IAUE;;;IAGG;IACH,IAAA,SAAA,UAAA,CAAmB,QAAgB,EAAA;IAAhB,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;;;IAVzB,QAAA,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;IACV,QAAA,IAAA,CAAA,GAAG,GAAG,CAAC,CAAC;YAUhB,IAAI,QAAQ,IAAI,IAAI,EAAE;IACpB,YAAA,MAAM,IAAI,UAAU,CAAC,kDAAkD,CAAC,CAAC;IAC1E,SAAA;YACD,IAAI,QAAQ,GAAG,CAAC,EAAE;IAChB,YAAA,MAAM,IAAI,UAAU,CAAC,4CAA4C,CAAC,CAAC;IACpE,SAAA;YACD,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAI,QAAQ,CAAC,CAAC;IACnC,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,QAAQ,CAAC;SACrC;IAED;;IAEG;QACO,UAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAa,EAAA;;YAE1B,OAAO,KAAK,GAAG,CAAC,EAAE;IAChB,YAAA,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC;IAC/B,SAAA;IACD,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;SACrC,CAAA;QAES,UAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,KAAa,EAAA;YACzB,IAAI,KAAK,GAAG,CAAC,EAAE;IACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;IAC9D,SAAA;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzC,CAAA;IAES,IAAA,UAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,KAAa,EAAE,KAAQ,EAAA;YACnC,IAAI,KAAK,GAAG,CAAC,EAAE;IACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;IAC9D,SAAA;YACD,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;SAC1C,CAAA;IAED;;IAEG;IACH,IAAA,UAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;YACE,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;YACnC,IAAI,MAAM,GAAG,CAAC,EAAE;IACd,YAAA,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IACxC,SAAA;IACD,QAAA,OAAO,MAAM,CAAC;SACf,CAAA;IAED;;;;IAIG;IACH,IAAA,UAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;YACE,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;SACxC,CAAA;IAED;;;;IAIG;IACH,IAAA,UAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC5B,CAAA;IAED;;IAEG;QACH,UAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAQ,EAAA;IACX,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;IACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;IAC9C,SAAA;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC1B,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACpC,CAAA;IAED;;IAEG;QACH,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,MAAW,EAAA;;;IACjB,YAAA,KAAoB,IAAA,QAAA,GAAA,QAAA,CAAA,MAAM,CAAA,8BAAA,EAAE,CAAA,UAAA,CAAA,IAAA,EAAA,UAAA,GAAA,QAAA,CAAA,IAAA,EAAA,EAAA;IAAvB,gBAAA,IAAM,KAAK,GAAA,UAAA,CAAA,KAAA,CAAA;IACd,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,aAAA;;;;;;;;;SACF,CAAA;IAED;;IAEG;IACH,IAAA,UAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA;IACE,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;IAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/C,SAAA;IACD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACnC,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAC9B,QAAA,OAAO,MAAM,CAAC;SACf,CAAA;IAED;;IAEG;QACH,UAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAQ,EAAA;IACd,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;IACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;IAC9C,SAAA;IACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAC7B,CAAA;IAED;;IAEG;IACH,IAAA,UAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;IACE,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;IAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/C,SAAA;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAChC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACvC,QAAA,OAAO,MAAM,CAAC;SACf,CAAA;IAED;;;;;;;;IAQG;QACH,UAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,aAAqB,EAAA;IACjC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;IAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAC/C,SAAA;IACD,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC;YACpD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC5B,QAAA,OAAO,MAAM,CAAC;SACf,CAAA;QACF,OAAA,UAAA,CAAA;IAAA,CAAA,EAAA,CAAA;;IC3JD,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAA0C,SAAa,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;IAGrD;;IAEG;IACH,IAAA,SAAA,iBAAA,GAAA;mBACE,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,iBAAiB,CAAC,gBAAgB,CAAC,IAAA,IAAA,CAAA;SAC1C;IAEQ,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IACP,QAAA,OAAO,KAAK,CAAC;SACd,CAAA;QAEQ,iBAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAQ,EAAA;YACpB,IAAI,MAAA,CAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,CAAE,EAAE;gBAClB,IAAI,CAAC,MAAM,EAAE,CAAC;IACf,SAAA;IACD,QAAA,MAAA,CAAA,SAAA,CAAM,IAAI,CAAA,IAAA,CAAA,IAAA,EAAC,KAAK,CAAC,CAAC;SACnB,CAAA;QAEQ,iBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAQ,EAAA;YACvB,IAAI,MAAA,CAAA,SAAA,CAAM,MAAM,CAAA,IAAA,CAAA,IAAA,CAAE,EAAE;gBAClB,IAAI,CAAC,MAAM,EAAE,CAAC;IACf,SAAA;IACD,QAAA,MAAA,CAAA,SAAA,CAAM,OAAO,CAAA,IAAA,CAAA,IAAA,EAAC,KAAK,CAAC,CAAC;SACtB,CAAA;IAED;;IAEG;IACK,IAAA,iBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IACN,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACtC,QAAA,IAAM,OAAO,GAAG,IAAI,KAAK,CAAI,WAAW,CAAC,CAAC;IAC1C,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;;;YAI1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,YAAA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IAClD,SAAA;IAED,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;IACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;IACzC,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB,CAAA;;KA/CH,CAA0C,UAAa;IACtC,iBAAgB,CAAA,gBAAA,GAAG,EAAE;;ICWtC;IACA;IACA;IAEA;;IAEG;IACG,SAAU,iBAAiB,CAAI,KAAU,EAAA;IAC7C,IAAA,OAAO,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAUD;;;;;;;;;;;;IAYG;IACG,SAAU,oBAAoB,CAChC,IACiD,EAAA;IACnD,IAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;IAWG;IACa,SAAA,wBAAwB,CACpC,aAA4C,EAC5C,gBAAwC,EAAA;IAC1C,IAAA,OAAO,IAAI,eAAe,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC9D,CAAC;IAyBD;;;;;;;;;;;;;;;;;;;;;;;IAuBG;IACa,SAAA,kBAAkB,CAC9B,SAA4B,EAC5B,YAAoD,EAAA;IAApD,IAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAA,GAAgC,eAAe,CAAC,IAAI,CAAA,EAAA;IACtD,IAAA,OAAO,IAAI,WAAW,CAAI,SAAS,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;IAMG;IACH,IAAA,YAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,YAAA,GAAA;SAwUC;IAxTC;;;;;;;IAOG;IACG,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;;4BACQ,MAAM,GAAQ,EAAE,CAAC;IACf,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;IAArB,wBAAA,CAAC,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;;;iCAClB,CAAC,CAAC,CAAC,IAAI,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACZ,wBAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;4BAArB,CAAC,GAAG,SAAiB,CAAC;;IAExB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;IACf,KAAA,CAAA;IAED;;;;;;;;;;IAUG;IACG,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;;;IACQ,wBAAA,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;4BAC5B,MAAM,GAAQ,EAAE,CAAC;IACf,wBAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAvB,wBAAA,CAAC,GAAG,EAAmB,CAAA,IAAA,EAAA,CAAA;;;iCACpB,CAAC,CAAC,CAAC,IAAI,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACZ,wBAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjB,wBAAA,OAAA,CAAA,CAAA,YAAM,MAAM,CAAC,IAAI,EAAE,CAAA,CAAA;;4BAAvB,CAAC,GAAG,SAAmB,CAAC;;IAE1B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;;IACf,KAAA,CAAA;IAED;;;;;;IAMG;IACG,IAAA,YAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,YAAA;;;;;IACU,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;IAArB,wBAAA,CAAC,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;;;iCAClB,CAAC,CAAC,CAAC,IAAI,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACR,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;4BAArB,CAAC,GAAG,SAAiB,CAAC;;;;;;IAEzB,KAAA,CAAA;IAED;;;;;;IAMG;QACG,YAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,SAA4B,EAAA;;;;;IACrC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;IAArB,wBAAA,CAAC,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;IACrB,wBAAA,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;;;kCACjC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IAC5B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;4BAArB,CAAC,GAAG,SAAiB,CAAC;IACtB,wBAAA,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;;;;;;IAEvC,KAAA,CAAA;IAED;;;;;;;;;;;IAWG;QACH,YAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,OAAkC,EAAA;IAC7C,QAAA,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACrD,CAAA;;IAID;;;;;;;IAOG;QACH,YAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,SAAgC,EAAA;IACrC,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC5C,CAAA;IAED;;;;;;;IAOG;QACH,YAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAO,SAA0B,EAAA;IAC/B,QAAA,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACzC,CAAA;IAED;;;;;;;IAOG;QACH,YAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAY,SAAmC,EAAA;IAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC9C,CAAA;IAED;;;;;;;IAOG;QACH,YAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAkB,SAAmC,EAAA;YACnD,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;SACvD,CAAA;IAED;;;;;;;IAOG;QACH,YAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAW,SAA4B,EAAA;IACrC,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC7C,CAAA;IAED;;;;IAIG;QACG,YAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,CAAqB,EAAA;;;oBACtC,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAA;;;IACnC,KAAA,CAAA;IAED;;;;;;IAMG;QACG,YAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UAAoB,CAAiC,EAAA;;;oBACnD,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAA,CAAC,EAAI,EAAA,QAAC,CAAC,KAAK,IAAI,EAAC,EAAA,CAAC,CAAC,CAAA;;;IAC/D,KAAA,CAAA;IAED;;;;;;;;;;;;;;;;;IAiBG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,SAAiB,EAAE,cAAqB,EAAA;IAArB,QAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAAqB,GAAA,IAAA,CAAA,EAAA;YACpD,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;SACnE,CAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UACI,SAAiB,EAAE,cAAqB;;QAExC,KAA+C,EAAA;IAF5B,QAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAAqB,GAAA,IAAA,CAAA,EAAA;IAExC,QAAA,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,KAA+C,GAAA,SAAA,CAAA,EAAA;;YAGjD,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;;IAGjE,QAAA,OAAO,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,EAAI,EAAA,OAAA,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAjB,EAAiB,CAAC,CAAC;SAC/C,CAAA;IAED;;;;;;;;;IASG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UACI,QAAyB,EACzB,gBAAwC,EAAA;IAC1C,QAAA,OAAO,IAAI,eAAe,CACtB,iBAAiB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;SAC5D,CAAA;IAED;;;;;;IAMG;QACH,YAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAa,EAAA;IAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;IAC9B,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACtC,CAAA;IAED;;;;;IAKG;QACH,YAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAa,EAAA;IAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;IAC9B,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACtC,CAAA;IAED;;;;;;;;IAQG;QACH,YAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,UAAkB,EAAA;IACzB,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SAC/C,CAAA;;IAID;;;;;;;IAOG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,UAAkB,EAAE,IAAa,EAAA;YACvC,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SACpD,CAAA;IAED;;;IAGG;IACH,IAAA,YAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IACE,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;SACjC,CAAA;QACF,OAAA,YAAA,CAAA;IAAA,CAAA,EAAA,CAAA,CAAA;IAED;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;QAA+B,SAAe,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;IAE5C,IAAA,SAAA,aAAA,CAAsB,KAAU,EAAA;IAAhC,QAAA,IAAA,KAAA,GACE,iBAAO,IACR,IAAA,CAAA;IAFqB,QAAA,KAAK,CAAA,KAAA,GAAL,KAAK,CAAK;IADxB,QAAA,KAAI,CAAA,IAAA,GAAG,CAAC,CAAC;;SAGhB;IAED,IAAA,aAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,mBAAY,IAAI,CAAC,KAAK,CAAC,MAAM,WAAQ,CAAC;SAC9C,CAAA;IAEK,IAAA,aAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;oBACE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;wBAClC,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,iBAAA;oBACK,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,IAAI,EAAE,CAAC;IACZ,gBAAA,OAAA,CAAA,CAAA,aAAO,EAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;IAC9C,KAAA,CAAA;QACF,OAAA,aAAA,CAAA;IAAA,CAlBD,CAA+B,YAAe,CAkB7C,CAAA,CAAA;IAED,IAAA,oBAAA,kBAAA,UAAA,MAAA,EAAA;QAAsC,SAAe,CAAA,oBAAA,EAAA,MAAA,CAAA,CAAA;IACnD,IAAA,SAAA,oBAAA,CACc,MAA2D,EAAA;IADzE,QAAA,IAAA,KAAA,GAEE,iBAAO,IACR,IAAA,CAAA;IAFa,QAAA,KAAM,CAAA,MAAA,GAAN,MAAM,CAAqD;;SAExE;IAED,IAAA,oBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,eAAe,CAAC;SACxB,CAAA;IAEK,IAAA,oBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;oBACE,IAAI;IACF,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;IACtB,iBAAA;IAAC,gBAAA,OAAO,CAAC,EAAE;;IAEV,oBAAA,CAAC,CAAC,OAAO;IACL,wBAAA,kDAAA,CAAA,MAAA,CAAmD,CAAC,CAAC,OAAO,CAAE,CAAC;IACnE,oBAAA,MAAM,CAAC,CAAC;IACT,iBAAA;;;;IACF,KAAA,CAAA;QACF,OAAA,oBAAA,CAAA;IAAA,CApBD,CAAsC,YAAe,CAoBpD,CAAA,CAAA;IAED,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAe,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IAK7C,IAAA,SAAA,cAAA,CAAsB,QAAyB,EAAA;IAA/C,QAAA,IAAA,KAAA,GACE,iBAAO,IAER,IAAA,CAAA;IAHqB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAE7C,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,eAAY,CAAC;SAC/C,CAAA;IAEK,IAAA,cAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAEa,IAAA,cAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;IACN,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;;;IAC7B,KAAA,CAAA;QACF,OAAA,cAAA,CAAA;IAAA,CA1BD,CAAgC,YAAe,CA0B9C,CAAA,CAAA;IAED,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAA8B,SAAe,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;QAQ3C,SAAsB,YAAA,CAAA,QAAyB,EAAY,QAAgB,EAAA;IAA3E,QAAA,IAAA,KAAA,GACE,iBAAO,IAER,IAAA,CAAA;IAHqB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAAY,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;IAF3E,QAAA,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;IAIR,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAED,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAU,CAAC;SAC7C,CAAA;IAEK,IAAA,YAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAEa,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;kCAKC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACjB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAApC,wBAAA,OAAO,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;;4BAE1C,IAAI,OAAO,CAAC,IAAI,EAAE;IAChB,4BAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;IAChB,yBAAA;IACD,wBAAAA,aAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAW,CAAC,CAAC;;IAElC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;;;;IAC7B,KAAA,CAAA;QACF,OAAA,YAAA,CAAA;IAAA,CAzCD,CAA8B,YAAe,CAyC5C,CAAA,CAAA;IAED,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAA8B,SAAe,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;QAE3C,SAAsB,YAAA,CAAA,QAAyB,EAAY,QAAgB,EAAA;IAA3E,QAAA,IAAA,KAAA,GACE,iBAAO,IACR,IAAA,CAAA;IAFqB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAAY,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;IAD3E,QAAA,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;SAGT;IAED,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAU,CAAC;SAC7C,CAAA;IAEK,IAAA,YAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;oBACE,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjC,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,iBAAA;IACD,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;;;IAC7B,KAAA,CAAA;QACF,OAAA,YAAA,CAAA;IAAA,CAhBD,CAA8B,YAAe,CAgB5C,CAAA,CAAA;IAED;IACA;IACA;IACA,IAAA,qBAAA,kBAAA,UAAA,MAAA,EAAA;QAAuC,SAAiB,CAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;IAKtD,IAAA,SAAA,qBAAA,CACc,QAAyB,EAAY,SAAiB,EACtD,oBAA2B,EAAA;IAA3B,QAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,oBAA2B,GAAA,IAAA,CAAA,EAAA;IAFzC,QAAA,IAAA,KAAA,GAGE,iBAAO,IAER,IAAA,CAAA;IAJa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAAY,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;IACtD,QAAA,KAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAO;IAEvC,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAED,IAAA,qBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,sBAAmB,CAAC;SACtD,CAAA;IAEK,IAAA,qBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAEa,IAAA,qBAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;4BACA,KAAK,GAAQ,EAAE,CAAC;;;IACf,wBAAA,IAAA,EAAA,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACrB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BACvC,IAAI,IAAI,CAAC,IAAI,EAAE;gCACb,IAAI,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oCACjD,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;IACpC,6BAAA;gCACD,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;IACD,wBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;gCAEzB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;;IACpC,KAAA,CAAA;QACF,OAAA,qBAAA,CAAA;IAAA,CAvCD,CAAuC,YAAiB,CAuCvD,CAAA,CAAA;IAED,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAe,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;QAK7C,SACc,cAAA,CAAA,QAAyB,EACzB,SAAgC,EAAA;IAF9C,QAAA,IAAA,KAAA,GAGE,iBAAO,IAER,IAAA,CAAA;IAJa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IACzB,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAuB;IAE5C,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,eAAY,CAAC;SAC/C,CAAA;IAEK,IAAA,cAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAEa,IAAA,cAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;IAES,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;IACvC,wBAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;IAC3C,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;IACb,yBAAA;IACD,wBAAAA,aAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;IAEhC,KAAA,CAAA;QACF,OAAA,cAAA,CAAA;IAAA,CAlCD,CAAgC,YAAe,CAkC9C,CAAA,CAAA;IAED,IAAA,WAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAe,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;QAC7C,SACc,WAAA,CAAA,QAAyB,EACzB,SAA0B,EAAA;IAFxC,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IACzB,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;;SAEvC;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAS,CAAC;SAC5C,CAAA;IAEK,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;IACe,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BACvC,IAAI,IAAI,CAAC,IAAI,EAAE;gCACb,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;4BACK,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;4BAOtE,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACpC,aAAa,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;;IAIzE,4BAAA,KAAgB,cAAA,GAAA,QAAA,CAAA,YAAY,CAAA,EAAE,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,CAAA,IAAA,EAAA,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA;oCAAnB,CAAC,GAAA,gBAAA,CAAA,KAAA,CAAA;oCACV,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;wCACpD,CAAC,CAAC,OAAO,EAAE,CAAC;IACb,iCAAA;IACF,6BAAA;;;;;;;;;4BACD,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;;IACrC,KAAA,CAAA;QACF,OAAA,WAAA,CAAA;IAAA,CAnCD,CAAgC,YAAe,CAmC9C,CAAA,CAAA;IAED,IAAA,yBAAA,kBAAA,UAAA,MAAA,EAAA;QAA2C,SAAe,CAAA,yBAAA,EAAA,MAAA,CAAA,CAAA;QAExD,SACc,yBAAA,CAAA,QAAyB,EACzB,OAAkC,EAAA;IAFhD,QAAA,IAAA,KAAA,GAGE,iBAAO,IAER,IAAA,CAAA;IAJa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IACzB,QAAA,KAAO,CAAA,OAAA,GAAP,OAAO,CAA2B;IAHhD,QAAA,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;IAKR,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAED,IAAA,yBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,qBAAkB,CAAC;SACrD,CAAA;IAMK,IAAA,yBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAEK,IAAA,yBAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;;;;IAGa,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;IAAjC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA0B,CAAC,CAAA;;;IAElC,wBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAC,CAAC,EAAE;gCACpB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;;;;;;;IAQN,KAAA,CAAA;QACF,OAAA,yBAAA,CAAA;IAAA,CA1CD,CAA2C,YAAe,CA0CzD,CAAA,CAAA;IAED,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;QAAqC,SAAe,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;QAClD,SACc,gBAAA,CAAA,QAAyB,EACzB,SAAmC,EAAA;IAFjD,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IACzB,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAA0B;;SAEhD;IAED,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAc,CAAC;SACjD,CAAA;IAEK,IAAA,gBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;IACe,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BACvC,IAAI,IAAI,CAAC,IAAI,EAAE;gCACb,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;4BACK,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;4BAO7D,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,CAAA;;IAAzC,wBAAA,MAAM,GAAG,EAAgC,CAAA,IAAA,EAAA,CAAA;4BACzC,aAAa,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;;IAIzE,4BAAA,KAAgB,cAAA,GAAA,QAAA,CAAA,YAAY,CAAA,EAAE,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,CAAA,IAAA,EAAA,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA;oCAAnB,CAAC,GAAA,gBAAA,CAAA,KAAA,CAAA;oCACV,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;wCACpD,CAAC,CAAC,OAAO,EAAE,CAAC;IACb,iCAAA;IACF,6BAAA;;;;;;;;;4BACD,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;;IACrC,KAAA,CAAA;QACF,OAAA,gBAAA,CAAA;IAAA,CAnCD,CAAqC,YAAe,CAmCnD,CAAA,CAAA;IAED;IACA;IAEA;;;;;;;IAOG;IACH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAmD,SAAe,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;IAQhE,IAAA,SAAA,iBAAA,GAAA;IAAA,QAAA,IAAA,KAAA,GACE,iBAAO,IAGR,IAAA,CAAA;IAFC,QAAA,KAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,EAAK,CAAC;IAC9C,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAEK,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;IAgBK,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;kCAIS,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IAE/B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;;IAAtB,wBAAA,IAAI,EAAC,EAAiB,CAAA,IAAA,EAAA,CAAA,EAAE;gCACtB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;;IAEH,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,EAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;;IACvD,KAAA,CAAA;QACF,OAAA,iBAAA,CAAA;IAAA,CAjDD,CAAmD,YAAe,CAiDjE,CAAA,CAAA;IACD,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;QAAoC,SAAoB,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QACtD,SACc,eAAA,CAAA,QAAyB,EACzB,SAA4B,EAAA;IAF1C,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IACzB,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;;SAEzC;IAED,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,gBAAa,CAAC;SAChD,CAAA;IAEK,IAAA,eAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;IACe,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BACvC,IAAI,IAAI,CAAC,IAAI,EAAE;IACb,4BAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;IACd,yBAAA;4BACK,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;4BAMtE,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACzC,aAAa,GACfA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,WAAiB,CAAC,CAAC;IAC5D,wBAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;;;IAItC,4BAAA,KAAgB,cAAA,GAAA,QAAA,CAAA,YAAY,CAAA,EAAE,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,CAAA,IAAA,EAAA,gBAAA,GAAA,cAAA,CAAA,IAAA,EAAA,EAAA;oCAAnB,CAAC,GAAA,gBAAA,CAAA,KAAA,CAAA;oCACV,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;wCACpD,CAAC,CAAC,OAAO,EAAE,CAAC;IACb,iCAAA;IACF,6BAAA;;;;;;;;;IAED,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;IACb,KAAA,CAAA;QACF,OAAA,eAAA,CAAA;IAAA,CArCD,CAAoC,iBAAoB,CAqCvD,CAAA,CAAA;IAED;;;;;;;;IAQG;IACH,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;QAAwC,SAAe,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;QASrD,SACI,eAAA,CAAA,SAAwC,EACvB,gBAAwC,EAAA;IAF7D,QAAA,IAAA,KAAA,GAGE,iBAAO,IAER,IAAA,CAAA;IAHoB,QAAA,KAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAwB;;;IARrD,QAAA,KAAQ,CAAA,QAAA,GAA+B,IAAI,CAAC;;IAG5C,QAAA,KAAQ,CAAA,QAAA,GAAoB,IAAI,CAAC;IAOvC,QAAA,KAAI,CAAC,aAAa,GAAG,SAAS,CAAC;;SAChC;IAED,IAAA,eAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,IAAM,iBAAiB,GAAG,6CAA6C,CAAC;YACxE,OAAO,EAAA,CAAA,MAAA,CAAG,iBAAiB,EAAA,aAAA,CAAa,CAAC;SAC1C,CAAA;IAEK,IAAA,eAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;oBACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAClD,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;QAEa,eAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UAAoB,QAAoC,EAAA;;;;;;;;;;;IAO9D,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAA,CAAA;;;;;;;IAAd,wBAAA,EAAA,CAAA,IAAA,EAAc,CAAC;IACX,wBAAA,IAAA,EAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAA,EAArB,OAAqB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACA,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAhD,wBAAA,cAAc,GAAG,EAA+B,CAAA,IAAA,EAAA,CAAA;4BACtD,IAAI,cAAc,CAAC,IAAI,EAAE;;gCAEvB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;IACD,wBAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;IACrC,wBAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;IACjC,4BAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACnE,yBAAA;;IAEgB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAvC,wBAAA,UAAU,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BAC7C,IAAI,UAAU,CAAC,IAAI,EAAE;IACnB,4BAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAA;IACrC,yBAAA;IACD,wBAAA,OAAA,CAAA,CAAA,aAAO,UAAU,CAAC,CAAA;;;;IACnB,KAAA,CAAA;QACF,OAAA,eAAA,CAAA;IAAA,CApDD,CAAwC,YAAe,CAoDtD,CAAA,CAAA;IAED,IAAY,eAIX,CAAA;IAJD,CAAA,UAAY,eAAe,EAAA;QACzB,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;QACJ,eAAA,CAAA,eAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;IACR,IAAA,eAAO,CAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;IACT,CAAC,EAJW,eAAe,KAAf,eAAe,GAI1B,EAAA,CAAA,CAAA,CAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BG;IACH,IAAA,WAAA,kBAAA,UAAA,MAAA,EAAA;QAAwD,SAAe,CAAA,WAAA,EAAA,MAAA,CAAA,CAAA;QAIrE,SACuB,WAAA,CAAA,SAA4B,EAC5B,YAAoD,EAAA;IAApD,QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAAA,GAAgC,eAAe,CAAC,IAAI,CAAA,EAAA;IAF3E,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHsB,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;IAC5B,QAAA,KAAY,CAAA,YAAA,GAAZ,YAAY,CAAwC;IALnE,QAAA,KAAK,CAAA,KAAA,GAAG,CAAC,CAAC;IACV,QAAA,KAAc,CAAA,cAAA,GAA+B,IAAI,CAAC;;SAMzD;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,IAAM,iBAAiB,GAAG,yCAAyC,CAAC;YACpE,OAAO,GAAA,CAAA,MAAA,CAAI,iBAAiB,EAAA,UAAA,CAAU,CAAC;SACxC,CAAA;QAEa,WAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,UAAsC,EAAA;;gBAW5D,SAAS,OAAO,CAAC,SAA4B,EAAA;oBAC3C,IAAI,SAAS,YAAY,YAAY,EAAE;IACrC,oBAAA,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;wBAChC,OAAO;IACL,wBAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,EAAA;IAClB,4BAAA,YAAY,EAAE,CAAC;gCACf,IAAI,CAAC,CAAC,IAAI,EAAE;IACV,gCAAA,aAAa,EAAE,CAAC;IACjB,6BAAA;gCACD,OAAO,CAAC,CAAC,KAAK,CAAC;IACjB,yBAAC,CAAC;IACF,wBAAA,OAAO,EAAE,KAAK;yBACf,CAAC;IACH,iBAAA;IAAM,qBAAA;wBACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACrC,iBAAA;iBACF;;;;;;;IAvBD,oBAAA,OAAA,CAAA,CAAA,YAAM,UAAU,CAAA,CAAA;;;;IAAhB,wBAAA,EAAA,CAAA,IAAA,EAAgB,CAAC;4BAIb,YAAY,GAAG,CAAC,CAAC;4BACjB,aAAa,GAAG,CAAC,CAAC;4BAoBJ,OAAM,CAAA,CAAA,YAAA,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA,CAAA;;IAA7D,wBAAA,MAAM,GAAM,EAAiD,CAAA,IAAA,EAAA,CAAA;4BAEnE,IAAI,YAAY,KAAK,aAAa,EAAE;;gCAElC,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;4BACD,IAAI,aAAa,GAAG,CAAC,EAAE;gCACrB,QAAQ,IAAI,CAAC,YAAY;oCACvB,KAAK,eAAe,CAAC,IAAI;wCACvB,MAAM,IAAI,KAAK,CACX,8CAA8C;IAC9C,wCAAA,wBAAA,CAAA,MAAA,CAAyB,IAAI,CAAC,KAAK,EAAA,GAAA,CAAG,CAAC,CAAC;oCAC9C,KAAK,eAAe,CAAC,QAAQ;wCAC3B,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;oCACnC,KAAK,eAAe,CAAC,OAAO,CAAC;;IAG9B,6BAAA;IACF,yBAAA;4BAED,IAAI,CAAC,KAAK,EAAE,CAAC;4BACb,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;;;;IACrC,KAAA,CAAA;IAEK,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;oBACE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC1D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,cAAc,CAAC,CAAA;;;IAC5B,KAAA,CAAA;QACF,OAAA,WAAA,CAAA;IAAA,CAxED,CAAwD,YAAe,CAwEtE,CAAA,CAAA;IAED;IACA;IAEA;;;;;;IAMG;IACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;QAAyC,SAAe,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;QAGtD,SACc,gBAAA,CAAA,QAAyB,EAAY,UAAkB,EAAA;IADrE,QAAA,IAAA,KAAA,GAEE,iBAAO,IAER,IAAA,CAAA;IAHa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAAY,QAAA,KAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;YAEnE,KAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAA6B,UAAU,CAAC,CAAC;;SACtE;IAED,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAc,CAAC;SACjD,CAAA;IAED;;;IAGG;IACO,IAAA,gBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;IACR,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;gBAC5B,IAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,SAAA;SACF,CAAA;IAED,IAAA,gBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;YACE,IAAI,CAAC,MAAM,EAAE,CAAC;;;;IAId,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SAC5B,CAAA;QACF,OAAA,gBAAA,CAAA;IAAA,CA/BD,CAAyC,YAAe,CA+BvD,CAAA,CAAA;IAED;;;;;IAKG;IACH,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;QAAwC,SAAmB,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;IAUzD,IAAA,SAAA,eAAA,CACqB,QAAyB,EAAY,UAAkB,EACxE,IAAa,EAAA;IAFjB,QAAA,IAAA,KAAA,GAGE,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,QAAQ,EAAE,UAAU,CAAC,IAG5B,IAAA,CAAA;IALoB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;IAAY,QAAA,KAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;;IAHpE,QAAA,KAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;IAMhC,QAAA,KAAI,CAAC,MAAM,GAAGC,eAAe,CAAC,IAAI,IAAID,aAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChE,QAAA,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;;SAC7D;IAEc,IAAA,eAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IAKP,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC,CAAC;oBAC5D,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,CAAA;;;IACtB,KAAA,CAAA;QAEO,eAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,GAAW,EAAA;YAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;SACxC,CAAA;IAES,IAAA,eAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;YACR,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SAC7C,CAAA;IAEK,IAAA,eAAA,CAAA,SAAA,CAAA,UAAU,GAAhB,YAAA;;;;;;;IAEE,wBAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gCAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;IACf,yBAAA;;;IACM,wBAAA,IAAA,CAAA,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACrB,wBAAA,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;4BACxB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAA,CAAA;;IAArD,wBAAA,MAAM,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;4BAC3D,IAAI,MAAM,CAAC,IAAI,EAAE;IACf,4BAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC/B,yBAAA;IAAM,6BAAA;gCACL,IAAI,CAAC,MAAM,EAAE,CAAC;IACd,4BAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;IACf,yBAAA;;gCAEH,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;;;;IAClC,KAAA,CAAA;QACF,OAAA,eAAA,CAAA;IAAA,CApDD,CAAwC,gBAAmB,CAoD1D,CAAA;;ICtoCD;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BG;AACH,QAAA,OAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,OAAA,GAAA;IAWW,QAAA,IAAI,CAAA,IAAA,GAAW,IAAI,CAAC;SA2c9B;;;;;;IAncC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,SAAiB,EAAE,cAAqB,EAAA;YAA9C,IAuBC,KAAA,GAAA,IAAA,CAAA;IAvBwB,QAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAAqB,GAAA,IAAA,CAAA,EAAA;YAC5C,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAAA,aAAE,CAAC,IAAI,CAAC,MAAM,CACV,SAAS,GAAG,CAAC,EAAE,YAAA,EAAM,OAAA,mDACrB,CAAA,MAAA,CAAA,SAAS,CAAE,CADU,EACV,CAAC,CAAC;IACjB,QAAA,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;;;IAG/C,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAClB,SAAA;IAAM,aAAA,IAAI,cAAc,EAAE;;;gBAGzB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;IACzC,SAAA;IAAM,aAAA;;;gBAGL,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;IAC1C,SAAA;IACD,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;IACnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;gCAA7B,OAAO,CAAA,CAAA,aAAA,CAAC,SAAqB;IACxB,6BAAA,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC,CAAA;;;iBACnE,EAAE,IAAI,CAAC,CAAC;SACV,CAAA;IAED;;;;;;;;;;;;;;IAcG;QACH,OAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,OAAmB,EAAA;YAA/B,IAoBC,KAAA,GAAA,IAAA,CAAA;YAnBC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;;;gBAGvD,IAAI,GAAG,QAAQ,CAAC;IACjB,SAAA;iBAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;;;gBAGpD,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACjC,SAAA;IAAM,aAAA;;;gBAGL,IAAI,GAAG,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,qBAAqB,CACxB,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;;IACK,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;;wBAAtB,EAAA,GAAA,CAAA,MAAC,EAAA,CAAA,IAAA,EAAqB,CAAC,EAAC,WAAW,CAAA;IAAC,oBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,QAAQ,EAAE,CAAA,CAAA;4BAA5D,OAAA,CAAA,CAAA,aAAA,EAAA,CAAA,KAAA,CAAA,EAAA,EAAA,CAAoC,EAAwB,CAAA,IAAA,EAAA,CAAA,CAAC,CAAA,CAAA;;qBAAA,EACjE,IAAI,CAAC,CAAC;SACX,CAAA;IAED;;;;;;;;;;;;;;;IAeG;QACH,OAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,SAAgC,EAAA;YAAvC,IAcC,KAAA,GAAA,IAAA,CAAA;YAbC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,IAAI,CAAC;IACT,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;;gBAE1B,IAAI,GAAG,QAAQ,CAAC;IACjB,SAAA;IAAM,aAAA;;;gBAGL,IAAI,GAAG,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;IACnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;gCAA7B,OAAO,CAAA,CAAA,aAAA,CAAC,EAAqB,CAAA,IAAA,EAAA,EAAE,MAAM,CAAC,UAAA,CAAC,EAAA,EAAI,OAAAA,aAAE,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,SAAS,CAAC,CAAC,CAAC,CAAZ,EAAY,CAAC,CAAA,EAAA,CAAC,CAAC,CAAA;;;iBACzE,EAAE,IAAI,CAAC,CAAC;SACV,CAAA;IAED;;;;;;;;;;;;;;;IAeG;QACG,OAAY,CAAA,SAAA,CAAA,YAAA,GAAlB,UAAmB,CAAqB,EAAA;;;;IAC9B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;gCAA7B,OAAO,CAAA,CAAA,aAAA,CAAC,SAAqB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;;;;IAChD,KAAA,CAAA;IAED;;;;;;;;;;;;;;IAcG;QACH,OAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAkC,SAA0B,EAAA;YAA5D,IAKC,KAAA,GAAA,IAAA,CAAA;YAJC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;IACnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;gCAA7B,OAAO,CAAA,CAAA,aAAA,CAAC,EAAqB,CAAA,IAAA,EAAA,EAAE,GAAG,CAAC,UAAA,CAAC,EAAA,EAAI,OAAAA,aAAE,CAAC,IAAI,CAAC,YAAM,EAAA,OAAA,SAAS,CAAC,CAAC,CAAC,CAAZ,EAAY,CAAC,CAAA,EAAA,CAAC,CAAC,CAAA;;;IACtE,SAAA,CAAA,CAAA,EAAA,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SACf,CAAA;IAED;;;;;;;;;;;;;;;;;;;;;;IAsBG;QACH,OAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAuC,SAAmC,EAAA;YAA1E,IAMC,KAAA,GAAA,IAAA,CAAA;YAJC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;IACnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;gCAA7B,OAAO,CAAA,CAAA,aAAA,CAAC,SAAqB,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA;;;IACpD,SAAA,CAAA,CAAA,EAAA,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SACf,CAAA;IAED;;;;;;;;IAQG;QACH,OAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,UAAkB,EAAA;YAA3B,IASC,KAAA,GAAA,IAAA,CAAA;YARC,IAAI,UAAU,IAAI,IAAI,EAAE;IACtB,YAAA,MAAM,IAAI,UAAU,CAChB,2DAA2D,CAAC,CAAC;IAClE,SAAA;YAED,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,OAAO,qBAAqB,CACxB,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;;IAAa,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;4BAAtB,OAAA,CAAA,CAAA,aAAA,CAAC,SAAqB,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAA,CAAA;;IAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1E,CAAA;IAED;;;;;;;;;;;;;;;;;IAiBG;QACH,OAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAc,EAAA;YAArB,IAwBC,KAAA,GAAA,IAAA,CAAA;YAvBC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;;;;IAIlC,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IAC1B,SAAA;iBAAM,IAAI,KAAK,KAAK,CAAC,EAAE;;gBAEtB,IAAI,GAAG,CAAC,CAAC;IACV,SAAA;IAAM,aAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;gBAGlE,IAAI,GAAG,QAAQ,CAAC;IACjB,SAAA;IAAM,aAAA;;gBAEL,IAAI,GAAG,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;oBACrB,gBAAgB,GAAG,oBAAoB,CACzC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;;IAAqB,gCAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;wCAA9B,OAAA,CAAA,CAAA,cAAE,QAAK,GAAE,EAAA,CAAA,IAAA,EAAqB,EAAE,EAAI,CAAA,IAAA,GAAE,KAAK,EAAA,EAAA,EAAE,CAAA;;;IAAA,iBAAA,CAAA,CAAA,EAAA,CAAC,CAAC;oBAC/D,OAAO,CAAA,CAAA,aAAA,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;;iBAC/D,EAAE,IAAI,CAAC,CAAC;SACV,CAAA;IAED;;;;;;;;;;;;;;;;IAgBG;QACH,OAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAa,EAAA;YAAlB,IAoBC,KAAA,GAAA,IAAA,CAAA;YAnBC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,IAAI,CAAC;IACT,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;;IAIzD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IAC1B,SAAA;IAAM,aAAA,IACH,IAAI,CAAC,IAAI,IAAI,IAAI;IACjB,aAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;gBAG3D,IAAI,GAAG,CAAC,CAAC;IACV,SAAA;IAAM,aAAA;;gBAEL,IAAI,GAAG,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,qBAAqB,CACxB,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;;IAAa,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;4BAAtB,OAAA,CAAA,CAAA,aAAA,CAAC,SAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA,CAAA;;qBAAA,EAAE,IAAI,CAAC,CAAC;SAC5D,CAAA;IAMD;;;;;;;;;;;;;;;;;;;;IAoBG;IACH,IAAA,OAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,UAAkB,EAAE,IAAa,EAAE,sBAA6B,EAAA;YAAxE,IAuBC,KAAA,GAAA,IAAA,CAAA;IAvB0C,QAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,sBAA6B,GAAA,IAAA,CAAA,EAAA;IAEtE,QAAA,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE;IACxC,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;IACrB,gBAAA,MAAM,IAAI,UAAU,CAChB,0DAA0D,CAAC,CAAC;IACjE,aAAA;IAAM,iBAAA;oBACL,MAAM,IAAI,UAAU,CAChB,4DAA4D;wBAC5D,6DAA6D;wBAC7D,yDAAyD;IACzD,oBAAA,kCAAA,CAAA,MAAA,CAAmC,IAAI,CAAC,IAAI,EAAA,YAAA,CAAY,CAAC,CAAC;IAC/D,aAAA;IACF,SAAA;YACD,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAM,MAAM,GAAGC,eAAe,CAAC,IAAI,IAAID,aAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjE,QAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;;IACvB,wBAAA,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAC3B,wBAAA,IAAI,sBAAsB,EAAE;IAC1B,4BAAA,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;IACzB,yBAAA;IACO,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;IAA7B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,CAAC,EAAA,CAAA,IAAA,EAAqB,EAAE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;;;IACtE,SAAA,CAAA,CAAA,EAAA,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SACf,CAAA;IAED;;;;;;;;;;;;;;;;IAgBG;QACH,OAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAa,EAAA;YAAlB,IAiBC,KAAA,GAAA,IAAA,CAAA;YAhBC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,QAAA,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE;;;gBAG1C,IAAI,GAAG,KAAK,CAAC;IACd,SAAA;iBAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;IAGlD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAClB,SAAA;IAAM,aAAA;;gBAEL,IAAI,GAAG,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,qBAAqB,CACxB,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;;IAAa,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;4BAAtB,OAAA,CAAA,CAAA,aAAA,CAAC,SAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA,CAAA;;qBAAA,EAAE,IAAI,CAAC,CAAC;SAC5D,CAAA;IAED;;;;;;;;;;;;;;;IAeG;IACG,IAAA,OAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;;IACE,wBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;IAC1B,4BAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACnE,yBAAA;IACO,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;IAA7B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,CAAC,EAAqB,CAAA,IAAA,EAAA,EAAE,OAAO,EAAE,CAAC,CAAA;;;;IAC1C,KAAA,CAAA;IAED;;;;;;;;;;IAUG;IACG,IAAA,OAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;;IACE,wBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;IAC1B,4BAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACnE,yBAAA;IACO,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;IAA7B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,CAAC,EAAqB,CAAA,IAAA,EAAA,EAAE,cAAc,EAAE,CAAC,CAAA;;;;IACjD,KAAA,CAAA;;;IA7HD;IAEgB,OAAe,CAAA,eAAA,GAAG,KAAH,CAAS;IA8H1C;;;;;;;;;;;IAWG;IACa,SAAA,qBAAqB,CACjC,UAA0C,EAC1C,IAAmB,EAAA;IAAnB,IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAmB,GAAA,IAAA,CAAA,EAAA;QACrB,OAAO,mBAAA,UAAA,MAAA,EAAA;YAAkB,SAAU,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;IAAxB,QAAA,SAAA,OAAA,GAAA;IAAA,YAAA,IAAA,KAAA,GAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,aAAA,CAAA,EAAA,EAAA,MAAA,WAUV,EAAA,KAAA,CAAA,CAAA,IAAA,IAAA,CAAA;IATU,YAAA,KAAI,CAAA,IAAA,GAAG,IAAI,CAAC;;aAStB;IAPC;;;IAGG;IACG,QAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;wBACE,OAAO,CAAA,CAAA,aAAA,UAAU,EAAE,CAAC,CAAA;;;IACrB,SAAA,CAAA;YACF,OAAA,OAAA,CAAA;IAAA,KAAA,CAVwB,OAAU,CAAA,GAWjC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;IAiBG;IACG,SAAU,KAAK,CAA+B,KAAU,EAAA;QAA9D,IAGC,KAAA,GAAA,IAAA,CAAA;IAFC,IAAA,OAAO,qBAAqB,CACxB,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;IAAY,QAAA,OAAA,CAAA,CAAA,aAAA,iBAAiB,CAAC,KAAK,CAAC,CAAA,CAAA;IAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwCG;IACG,SAAU,GAAG,CAA+B,QAA0B,EAAA;QAA5E,IAgCC,KAAA,GAAA,IAAA,CAAA;;IA7BC,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;IACzB,QAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACtE,KAAA;IACD,IAAA,IAAI,IAAI,CAAC;IACT,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;IAC3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACxC,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI;IAChC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI,CAAC,CAAC;IACxE,SAAA;IACF,KAAA;aAAM,IAAI,QAAQ,YAAY,MAAM,EAAE;IACrC,QAAA,KAAK,IAAM,EAAE,IAAI,QAAQ,EAAE;IACzB,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI;IACjC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI,CAAC,CAAC;IACzE,SAAA;IACF,KAAA;IACD,IAAA,OAAO,qBAAqB,CAAI,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;IACd,gBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,QAAQ,EAAE,UAAA,CAAC,EAAA;4BAClD,IAAI,CAAC,YAAY,OAAO,EAAE;IACxB,4BAAA,OAAO,EAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;IAC9C,yBAAA;IAAM,6BAAA,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;gCACxB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACrC,yBAAA;IAAM,6BAAA;gCACL,MAAM,IAAI,KAAK,CACX,4DAA4D;IAC5D,gCAAA,iBAAiB,CAAC,CAAC;IACxB,yBAAA;IACH,qBAAC,CAAC,CAAA,CAAA;;IAVI,oBAAA,OAAO,GAAG,EAUd,CAAA,IAAA,EAAA,CAAA;wBACF,OAAO,CAAA,CAAA,aAAA,kBAAkB,CAAI,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAA;;;aACjE,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED;;;;;;IAMG;IACH;IACA,SAAS,eAAe,CAAC,IAAW,EAAA;QAClC,IAAI,IAAI,KAAK,IAAI,EAAE;IACjB,QAAA,OAAO,IAAI,CAAC;IACb,KAAA;;IAGD,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAE3B,IAAA,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;;IAE5B,QAAA,IAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,EAAC,KAAK,EAAA,KAAA,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;IAChC,KAAA;;QAGD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;IACtC,CAAC;IAED;;;IAGG;IACH,SAAS,WAAW,CAAoC,MAAW,EAAA;IAEjE,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;;IAEvB,QAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC1D,KAAA;QAED,IAAI,MAAM,CAAC,CAAC,CAAC,YAAYA,aAAE,CAAC,MAAM,EAAE;;IAElC,QAAA,OAAOA,aAAE,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;IACxC,KAAA;IAAM,SAAA;;IAEL,QAAA,OAAOA,aAAE,CAAC,MAAM,CAAC,MAAoB,CAAC,CAAC;IACxC,KAAA;IACH;;IC1qBA;;;;IAIG;AACH,QAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;QAAqC,SAAe,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;IAClD;;;;IAIG;IACH,IAAA,SAAA,eAAA,CAA+B,KAAiB,EAAA;IAAhD,QAAA,IAAA,KAAA,GACE,iBAAO,IACR,IAAA,CAAA;IAF8B,QAAA,KAAK,CAAA,KAAA,GAAL,KAAK,CAAY;;SAE/C;IAEK,IAAA,eAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;;IACwB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA,CAAA;;IAA3C,wBAAA,aAAa,GAAG,EAA2B,CAAA,IAAA,EAAA,CAAA;IAC3C,wBAAA,YAAY,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;4BAC1C,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,EAAA;;IAEpD,4BAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oCACvB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,6BAAA;IACD,4BAAA,OAAO,IAAI,CAAC;IACd,yBAAC,CAAC,CAAC;IACH,wBAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;;IACrB,KAAA,CAAA;QACF,OAAA,eAAA,CAAA;IAAA,CAtBD,CAAqC,OAAe,CAsBnD;;ICxBD,IAAM,UAAU,GAAG,GAAG,CAAC;IACvB,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAChC,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IACpC,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IACpC,IAAM,uBAAuB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC1D,IAAM,2BAA2B,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;IAE3D;;;;;;;;;;;;IAYG;AACH,QAAA,UAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAwB,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA;IAgGtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BG;QACH,SAA+B,UAAA,CAAA,KAAiB,EAAE,SAAqB,EAAA;IAAvE,QAAA,IAAA,KAAA,GACE,iBAAO,IAmBR,IAAA,CAAA;IApB8B,QAAA,KAAK,CAAA,KAAA,GAAL,KAAK,CAAY;IA9HxC,QAAA,KAAS,CAAA,SAAA,GAAG,IAAI,CAAC;IACjB,QAAA,KAAe,CAAA,eAAA,GAAa,IAAI,CAAC;IACjC,QAAA,KAAoB,CAAA,oBAAA,GAAG,KAAK,CAAC;IAC7B,QAAA,KAAa,CAAA,aAAA,GAAkC,IAAI,CAAC;IACpD,QAAA,KAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;IAC9B,QAAA,KAAS,CAAA,SAAA,GAAG,GAAG,CAAC;IAChB,QAAA,KAAe,CAAA,eAAA,GAAG,KAAK,CAAC;YA0H9B,KAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,SAAS,EAAE;gBACd,SAAS,GAAG,EAAE,CAAC;IAChB,SAAA;IACD,QAAA,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;IAC9D,QAAA,KAAI,CAAC,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC;IAC7C,QAAA,KAAI,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;IAC7C,QAAA,KAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC;YAC7D,IAAI,SAAS,CAAC,eAAe,EAAE;IAC7B,YAAAE,OAAI,CAAC,MAAM,CACP,SAAS,CAAC,SAAS,IAAI,IAAI,EAC3B,cACI,OAAA,gEAAgE,CAAhE,EAAgE,CAAC,CAAC;IAC1E,YAAA,KAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5B,YAAA,KAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACtB,SAAA;IAAM,aAAA;IACL,YAAA,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC;IAClE,SAAA;;SACF;IA1ID;;;;;;;;;IASG;IACG,IAAA,UAAA,CAAA,SAAA,CAAA,WAAW,GAAjB,YAAA;;;;;IACM,wBAAA,IAAA,CAAA,CAAC,IAAI,CAAC,oBAAoB,EAA1B,OAA0B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IAC5B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,EAAE,CAAA,CAAA;;IAA3B,wBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;;IAE9B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;gCAC/B,IAAI,CAAC,eAAe,CAAC,CAAA;;;;IAC1D,KAAA,CAAA;IAED;;;;;;;IAOG;IACW,IAAA,UAAA,CAAA,SAAA,CAAA,cAAc,GAApB,YAAA;;;;;;;IACsB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA,CAAA;;IAAtD,wBAAA,mBAAmB,GAAG,EAAgC,CAAA,IAAA,EAAA,CAAA;IAC5D,wBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,mBAAmB,EAAE;;IAEjD,4BAAA,MAAM,IAAI,KAAK,CACX,2DAA2D,CAAC,CAAC;IAClE,yBAAA;IAAM,6BAAA,IAAI,IAAI,CAAC,eAAe,IAAI,mBAAmB,EAAE;;IAEtD,4BAAAA,OAAI,CAAC,MAAM,CACP,mBAAmB,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAC1D,YAAA,EAAM,OAAA,sCAAsC;IACxC,gCAAA,KAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE;oCACtC,2DAA2D;IAC3D,gCAAA,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAA,EAAA,CAAC,CAAC;IAClE,yBAAA;IACD,wBAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;IACzB,4BAAA,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;IAC5C,yBAAA;4BAEK,MAAM,GAA4B,IAAI,CAAC,eAAe,CAAC,MAAM,CAC/D,UAAC,QAAiC,EAAE,IAAI,EAAA;IACtC,4BAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3C,4BAAA,OAAO,QAAQ,CAAC;6BACjB,EACD,EAAE,CAAC,CAAC;4BACF,cAAc,GAChB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAC,IAAI,IAAK,QAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC,EAAA,CAAC,CAAC;4BAC7DA,OAAI,CAAC,MAAM,CACP,cAAc,CAAC,MAAM,KAAK,CAAC,EAC3B,YAAA,EAAM,OAAA,gCAAgC,GAAG,cAAc,CAAC,QAAQ,EAAE,CAA5D,EAA4D,CAAC,CAAC;;4BAExE,IAAI,IAAI,CAAC,aAAa,EAAE;;oCACtB,KAAkB,EAAA,GAAA,QAAA,CAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA,EAAE,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;wCAAxC,GAAG,GAAA,EAAA,CAAA,KAAA,CAAA;wCACN,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAChD,oCAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,wCAAA,MAAM,IAAI,KAAK,CACX,WAAW,GAAG,GAAG;gDACjB,+DAA+D;gDAC/D,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;IACzD,qCAAA;IACF,iCAAA;;;;;;;;;IACF,yBAAA;IACD,wBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;;;;;IAClC,KAAA,CAAA;IAEa,IAAA,UAAA,CAAA,SAAA,CAAA,mBAAmB,GAAzB,YAAA;;;;;;iCACF,IAAI,CAAC,SAAS,EAAd,OAAc,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IACH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;;IAAjC,wBAAA,IAAI,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;IAClB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAhC,wBAAA,YAAY,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;4BACtC,IAAI,YAAY,CAAC,IAAI,EAAE;IACrB,4BAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACvD,yBAAA;IACK,wBAAA,SAAS,GAAW,YAAY,CAAC,KAAK,CAAC;4BACvC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAChD,wBAAA,OAAA,CAAA,CAAA,aAAO,OAAO,CAAC,CAAA;IAEf,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;IAEf,KAAA,CAAA;IAwDK,IAAA,UAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;;;;IACM,wBAAA,IAAA,CAAA,CAAC,IAAI,CAAC,oBAAoB,EAA1B,OAA0B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;IAC5B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,cAAc,EAAE,CAAA,CAAA;;IAA3B,wBAAA,EAAA,CAAA,IAAA,EAA2B,CAAC;;IAElB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA;;IAAlC,wBAAA,KAAK,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BACtC,IAAI,IAAI,CAAC,SAAS,EAAE;;;IAGlB,4BAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvB,yBAAA;IACD,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,EAAI,EAAA,OAAA,KAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAvB,EAAuB,CAAC,CAAC,CAAA;;;;IAChD,KAAA,CAAA;QAED,UAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,IAAY,EAAA;YAC1B,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACnC,IAAM,QAAQ,GAAqC,EAAE,CAAC;YACtD,IAAM,MAAM,GAAqC,EAAE,CAAC;IAEpD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IACpC,YAAA,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACnE,YAAA,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,MAAM,EAAE;;oBAEzC,SAAS;IACV,aAAA;IAAM,iBAAA;IACL,gBAAA,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxB,IAAI,WAAW,GAAG,IAAI,CAAC;oBACvB,IAAI,KAAK,KAAK,EAAE,EAAE;;;IAGhB,oBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;IAC1C,wBAAA,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,qBAAA;6BAAM,IAAI,MAAM,KAAK,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;4BACxD,MAAM,IAAI,KAAK,CACX,kBAAA,CAAA,MAAA,CAAmB,GAAG,EAA2B,0BAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAE,CAAC,CAAC;IAC9D,qBAAA;IAAM,yBAAA;4BACL,WAAW,GAAG,SAAS,CAAC;IACzB,qBAAA;IACF,iBAAA;IAAM,qBAAA;;IAEL,oBAAA,IAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,oBAAA,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE;;;IAGrB,wBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,EAAE;IACrC,4BAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACtC,yBAAA;IAAM,6BAAA;;gCAEL,WAAW,GAAG,KAAK,CAAC;IACrB,yBAAA;IACF,qBAAA;IAAM,yBAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;;4BAGnC,WAAW,GAAG,UAAU,CAAC;IAC1B,qBAAA;IAAM,yBAAA;;;4BAGL,QAAQ,MAAM,CAAC,KAAK;IAClB,4BAAA,KAAK,SAAS;oCACZ,WAAW,GAAG,UAAU,CAAC;oCACzB,MAAM;IACR,4BAAA,KAAK,OAAO;IACV,gCAAA,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;oCACrC,MAAM;IACR,4BAAA,KAAK,MAAM;IACT,gCAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oCACrC,MAAM;IACR,4BAAA;oCACE,WAAW,GAAG,UAAU,CAAC;IAC5B,yBAAA;IACF,qBAAA;IACF,iBAAA;;IAED,gBAAA,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW;IACzB,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;IAC1D,aAAA;IACF,SAAA;;;YAGD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;IACpC,YAAA,OAAO,QAAQ,CAAC;IAEjB,SAAA;IAAM,aAAA;gBACL,OAAO,EAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAC,CAAC;IACnC,SAAA;SACF,CAAA;QAEO,UAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,KAAa,EAAA;YAC9B,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;IACnD,YAAA,OAAO,CAAC,CAAC;IACV,SAAA;IAAM,aAAA;IACL,YAAA,OAAO,CAAC,CAAC;IACV,SAAA;SACF,CAAA;;IAGO,IAAA,UAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,IAAY,EAAE,oBAA2B,EAAA;IAA3B,QAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,oBAA2B,GAAA,IAAA,CAAA,EAAA;YACxD,IAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;YAC/B,IAAI,YAAY,GAAG,SAAS,CAAC;;YAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;IACnC,YAAA,QAAQ,YAAY;;IAElB,gBAAA,KAAK,SAAS;IACZ,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;IAEpB,wBAAA,KAAK,UAAU;IACb,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;gCACnB,YAAY,GAAG,WAAW,CAAC;gCAC3B,MAAM;;4BAER,KAAK,IAAI,CAAC,SAAS;IACjB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;;;gCAGnB,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;oCAClD,MAAM;IACP,6BAAA;IACD,4BAAA,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gCAChB,YAAY,GAAG,SAAS,CAAC;gCACzB,MAAM;;IAER,wBAAA;gCACE,YAAY,GAAG,WAAW,CAAC;gCAC3B,UAAU,GAAG,CAAC,CAAC;gCACf,MAAM;IACT,qBAAA;wBACD,MAAM;;IAER,gBAAA,KAAK,WAAW;IACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;4BAEpB,KAAK,IAAI,CAAC,SAAS;IACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;gCAC3C,YAAY,GAAG,SAAS,CAAC;IACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;gCACnB,MAAM;IAET,qBAAA;wBACD,MAAM;;IAER,gBAAA,KAAK,WAAW;IACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;IAEpB,wBAAA,KAAK,UAAU;gCACb,YAAY,GAAG,uBAAuB,CAAC;gCACvC,MAAM;IAET,qBAAA;wBACD,MAAM;;IAER,gBAAA,KAAK,uBAAuB;IAC1B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;4BAEpB,KAAK,IAAI,CAAC,SAAS;IACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC/C,YAAY,GAAG,SAAS,CAAC;IACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;gCACnB,MAAM;;IAER,wBAAA,KAAK,UAAU;gCACb,YAAY,GAAG,WAAW,CAAC;gCAC3B,MAAM;;IAER,wBAAA;gCACE,YAAY,GAAG,2BAA2B,CAAC;gCAC3C,MAAM;IACT,qBAAA;wBACD,MAAM;IACR,gBAAA,KAAK,2BAA2B;IAC9B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;IAEpB,wBAAA,KAAK,UAAU;gCACb,YAAY,GAAG,WAAW,CAAC;gCAC3B,MAAM;IAET,qBAAA;wBACD,MAAM;IAET,aAAA;IACF,SAAA;;YAED,IAAI,YAAY,KAAK,uBAAuB,EAAE;IAC5C,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,SAAA;IAAM,aAAA;gBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;IACzC,SAAA;;YAED,IAAI,oBAAoB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;IACzE,YAAA,MAAM,IAAI,KAAK,CAAC,uCAAA,CAAA,MAAA,CACZ,IAAI,CAAC,eAAe,CAAC,MAAM,EAAA,8BAAA,CAAA,CAAA,MAAA,CAA+B,MAAM,CAAE,CAAC,CAAC;IACzE,SAAA;IACD,QAAA,OAAO,MAAM,CAAC;SACf,CAAA;QACF,OAAA,UAAA,CAAA;IAAA,CA1VD,CAAgC,OAAwB,CA0VvD,EAAA;IAED;IACA;IACA;;ICrXA;;;;;IAKG;IACH,IAAA,kBAAA,kBAAA,UAAA,MAAA,EAAA;QAAwC,SAA6B,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;IAgBnE,IAAA,SAAA,kBAAA,CAAuC,gBAAkC,EAAA;IAAzE,QAAA,IAAA,KAAA,GACE,iBAAO,IA0BR,IAAA,CAAA;IA3BsC,QAAA,KAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;IAfjE,QAAA,KAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;YAiBvB,KAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC;YAChD,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,QAAA,IAAI,KAAI,CAAC,OAAO,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE;IACvD,YAAA,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;gBAClC,MAAM,IAAI,KAAK,CACX,mDAAmD;IACnD,gBAAA,8BAAA,CAAA,MAAA,CAA+B,KAAI,CAAC,OAAO,CAAE,CAAC,CAAC;IACpD,SAAA;YAED,KAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,uBAAuB,IAAI,EAAE,CAAC;IAChE,QAAA,KAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;IAClD,QAAA,KAAI,CAAC,oBAAoB;IACrB,YAAA,gBAAgB,CAAC,oBAAoB,IAAI,KAAI,CAAC,OAAO,CAAC;IAC1D,QAAA,KAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;YACpE,KAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,IAAI,CAAC,CAAC;IAEzE,QAAA,KAAI,CAAC,kBAAkB;IACnB,YAAA,gBAAgB,CAAC,kBAAkB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;IACjE,QAAA,KAAI,CAAC,eAAe;IAChB,YAAA,gBAAgB,CAAC,eAAe,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;YAC7D,IAAI,CAAC,KAAI,CAAC,kBAAkB,IAAI,CAAC,KAAI,CAAC,eAAe,EAAE;gBACrD,MAAM,IAAI,KAAK,CACX,yDAAyD;IACzD,gBAAA,+CAA+C,CAAC,CAAC;IACtD,SAAA;;SACF;IAED,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,YAAY,CAAC;SACrB,CAAA;;QAGY,kBAAM,CAAA,MAAA,GAAnB,UAAoB,gBAAuC,EAAA;IAAvC,QAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,gBAAuC,GAAA,EAAA,CAAA,EAAA;;;;;;4BACzD,IAAI,CAACC,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;IAC5B,4BAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;IACjE,yBAAA;IAEK,wBAAA,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;;IAGpE,wBAAA,OAAA,CAAA,CAAA,YAAM,kBAAkB,CAAC,KAAK,EAAE,CAAA,CAAA;;;IAAhC,wBAAA,EAAA,CAAA,IAAA,EAAgC,CAAC;IAEjC,wBAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,CAAA;;;;IAC3B,KAAA,CAAA;;IAGK,IAAA,kBAAA,CAAA,SAAA,CAAA,KAAK,GAAX,YAAA;;;;;;;IAEI,wBAAA,EAAA,GAAA,IAAI,CAAA;IAAU,wBAAA,OAAA,CAAA,CAAA,YAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;oCACtD,KAAK,EAAE,IAAI,CAAC,qBAAqB,IAAI,IAAI,GAAG,IAAI;IACJ,oCAAA,IAAI,CAAC,qBAAqB;IACtE,gCAAA,KAAK,EAAE,KAAK;IACb,6BAAA,CAAC,CAAA,CAAA;;4BAJF,EAAK,CAAA,MAAM,GAAG,EAAA,CAAA,IAAA,EAIZ,CAAC;;;;4BAEH,MAAM,IAAI,KAAK,CACX,gDAAA,CAAA,MAAA,CAAiD,GAAC,CAAC,OAAO,CAAE,CAAC,CAAC;;IAGpE,wBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IAChB,4BAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC5D,yBAAA;4BAEK,cAAc;;IAEf,wBAAA,MAAc,CAAC,YAAY,IAAK,MAAc,CAAC,kBAAkB,CAAC;IACvE,wBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,cAAc,EAAE,CAAC;IAEzC,wBAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;;gCAGtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;IAClD,yBAAA;iCAAM,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;gCAC7D,MAAM,IAAI,KAAK,CACX,6BAA6B;oCAC7B,YAAa,CAAA,MAAA,CAAA,IAAI,CAAC,YAAY,EAAI,IAAA,CAAA;IAClC,gCAAA,UAAA,CAAA,MAAA,CAAW,IAAI,CAAC,YAAY,CAAC,UAAU,CAAE,CAAC,CAAC;IAChD,yBAAA;4BAEK,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;4BACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;4BACzC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;IACjE,wBAAA,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;4BACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC/C,OAAO,CAAA,CAAA,YAAA,CAAA;;;;IACR,KAAA,CAAA;IAEK,IAAA,kBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;4BACE,IAAI,IAAI,CAAC,QAAQ,EAAE;gCACjB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;IAKsB,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,YAAY,EAAE,CAAA,CAAA;;IAA1C,wBAAA,cAAc,GAAG,EAAyB,CAAA,IAAA,EAAA,CAAA;4BAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gCACrB,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACjE,4BAAA,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAChD,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/D,yBAAA;4BACD,IAAI,IAAI,CAAC,eAAe,EAAE;gCAClB,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACjE,4BAAA,cAAc,GAAG,IAAI,CAAC,2BAA2B,CAC7C,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,yBAAA;4BAED,OAAO,CAAA,CAAA,aAAA;oCACL,KAAK,EAAE,EAAC,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,cAAc,EAAC;IACrE,gCAAA,IAAI,EAAE,KAAK;iCACZ,CAAC,CAAA;;;;IACH,KAAA,CAAA;;;IAIK,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;IACU,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;IAAzB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,CAAC,EAAA,CAAA,IAAA,EAAiB,EAAE,KACoB,CAAC,CAAA;;;;IACjD,KAAA,CAAA;IAEa,IAAA,kBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,YAAA;;;;;oBAEA,aAAa,GAAmB,EAAE,CAAC;oBACnC,aAAa,GAAmB,EAAE,CAAC;oBACrC,aAAa,GAAG,CAAC,CAAC;IACtB,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAC,UAAA,OAAO,EAAA;4BACxB,IAAM,UAAU,GAAG,WAAW,CAAC,YAAA;gCAC7B,IAAI,KAAI,CAAC,kBAAkB,EAAE;oCAC3B,KAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;;oCAEnD,IAAI,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;wCAClC,OAAO,CAAC,EAAC,aAAa,EAAA,aAAA,EAAE,aAAa,EAAA,aAAA,EAAC,CAAC,CAAC;IACzC,iCAAA;IACD,gCAAA,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACvE,6BAAA;gCACD,IAAI,KAAI,CAAC,eAAe,EAAE;oCACxB,KAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;oCACpD,aAAa,CAAC,IAAI,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;IAC3C,6BAAA;;IAGD,4BAAA,IAAI,EAAE,aAAa,KAAK,KAAI,CAAC,SAAS,EAAE;oCACtC,aAAa,CAAC,UAAU,CAAC,CAAC;oCAC1B,OAAO,CAAC,EAAC,aAAa,EAAA,aAAA,EAAE,aAAa,EAAA,aAAA,EAAC,CAAC,CAAC;IACzC,6BAAA;6BACF,EAAE,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;IAC7C,qBAAC,CAAC,CAAC,CAAA;;;IACJ,KAAA,CAAA;;IAGD,IAAA,kBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;IACE,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;IAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC1B,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC7D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACnC,aAAA;IACF,SAAA;SACF,CAAA;;IAGQ,IAAA,kBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACP,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE,CAAA;;IAGD,IAAA,kBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;YACE,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B,CAAA;QAEO,kBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,KAAqB,EAAA;YACxC,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAClC,IAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;YAC5D,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,CAAC,EAAK,EAAA,OAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAA,EAAA,CAAC,CAAC;IAC9D,QAAA,OAAO,QAAQ,CAAC;SACjB,CAAA;IAEO,IAAA,kBAAA,CAAA,SAAA,CAAA,2BAA2B,GAA3B,UAA4B,QAAsB,EAAE,KAAe,EAAA;IAEzE,QAAA,IAAM,IAAI,GAAG,IAAI,YAAY,CAACD,OAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEzD,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClD,QAAA,OAAOE,SAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5B,CAAA;QACF,OAAA,kBAAA,CAAA;IAAA,CA7MD,CAAwC,YAA6B,CA6MpE,CAAA;;ICnND;;;IAGG;IACH,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAAoC,SAAsB,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;QAQxD,SACuB,cAAA,CAAA,kBAAoC,EACpC,YAA0B,EAAA;IAFjD,QAAA,IAAA,KAAA,GAGE,iBAAO,IAuBR,IAAA,CAAA;IAzBsB,QAAA,KAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAkB;IACpC,QAAA,KAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;IATzC,QAAA,KAAQ,CAAA,QAAA,GAAG,IAAI,CAAC;IAEhB,QAAA,KAAM,CAAA,MAAA,GAAG,KAAK,CAAC;IASrB,QAAA,IAAI,KAAI,CAAC,YAAY,EAAE,EAAE;IACvB,YAAA,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACnB,YAAA,KAAI,CAAC,QAAQ;IACT,gBAAA,CAAC,KAAI,CAAC,YAAY,CAAC,YAAY,EAAE,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACpE,KAAI,CAAC,UAAU,GAAGC,WAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACzC,YAAA,IAAI,KAAI,CAAC,YAAY,CAAC,UAAU,EAAE;;IAEhC,gBAAA,IAAM,kBAAkB,GACpB,KAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,GAAG,KAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;oBACxE,IAAM,mBAAmB,GAAG,KAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG;IAC5D,oBAAA,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBACnC,IAAM,cAAc,GAAG,CAAC,CAAC,GAAG,kBAAkB,IAAI,CAAC,CAAC;oBACpD,IAAM,eAAe,GAAG,CAAC,CAAC,GAAG,mBAAmB,IAAI,CAAC,CAAC;IACtD,gBAAA,IAAM,YAAY,GAAG,cAAc,GAAG,kBAAkB,CAAC;IACzD,gBAAA,IAAM,aAAa,GAAG,mBAAmB,GAAG,eAAe,CAAC;oBAC5D,KAAI,CAAC,OAAO,GAAGC,WAAQ,CACnB,CAAC,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,CAAC,EAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,aAAA;IAAM,iBAAA;oBACL,KAAI,CAAC,OAAO,GAAGA,WAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/C,aAAA;IACF,SAAA;;SACF;IAED,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,QAAQ,CAAC;SACjB,CAAA;;IAGY,IAAA,cAAA,CAAA,MAAM,GAAnB,UACI,kBAAqC,EAAE,YAA+B,EAAA;IAA/B,QAAA,IAAA,YAAA,KAAA,KAAA,CAAA,EAAA,EAAA,YAA+B,GAAA,EAAA,CAAA,EAAA;;;;;;4BACxE,IAAI,CAACH,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;IAC5B,4BAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;IACjE,yBAAA;4BAED,IAAI,CAAC,kBAAkB,EAAE;;;IAGvB,4BAAA,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gCACrD,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;oCAC3D,MAAM,IAAI,KAAK,CACX,0DAA0D;IAC1D,oCAAA,gDAAgD,CAAC,CAAC;IACvD,6BAAA;IACD,4BAAA,kBAAkB,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC;IACpD,4BAAA,kBAAkB,CAAC,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;IACvD,yBAAA;4BACK,cAAc,GAAG,IAAI,cAAc,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;;IAG5E,wBAAA,OAAA,CAAA,CAAA,YAAM,cAAc,CAAC,KAAK,EAAE,CAAA,CAAA;;;IAA5B,wBAAA,EAAA,CAAA,IAAA,EAA4B,CAAC;IAE7B,wBAAA,OAAA,CAAA,CAAA,aAAO,cAAc,CAAC,CAAA;;;;IACvB,KAAA,CAAA;;IAGK,IAAA,cAAA,CAAA,SAAA,CAAA,KAAK,GAAX,YAAA;;;;;;;IACE,wBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;gCAChCD,OAAI,CAAC,MAAM,CACP,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,MAAM;IACpC,iCAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,aAAa,CAAC,EACpD,YAAA,EACI,OAAA,8BAA+B,CAAA,MAAA,CAAA,KAAI,CAAC,YAAY,CAAC,UAAU,EAAI,IAAA,CAAA;oCAC/D,wCAAwC,CAAA,EAAA,CAAC,CAAC;IACnD,yBAAA;;;;IAGC,wBAAA,EAAA,GAAA,IAAI,CAAA;IAAU,wBAAA,OAAA,CAAA,CAAA,YAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;IACtD,gCAAA,KAAK,EAAE;IACL,oCAAA,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;IACpC,oCAAA,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;IACpC,wCAAA,IAAI,CAAC,YAAY,CAAC,UAAU;4CAC5B,MAAM;IACV,oCAAA,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK;IACpC,oCAAA,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;IACvC,iCAAA;IACF,6BAAA,CAAC,CAAA,CAAA;;4BATF,EAAK,CAAA,MAAM,GAAG,EAAA,CAAA,IAAA,EASZ,CAAC;;;;;4BAGH,GAAC,CAAC,OAAO,GAAG,gDAAA,CAAA,MAAA,CAAiD,GAAC,CAAC,OAAO,CAAE,CAAC;IACzE,wBAAA,MAAM,GAAC,CAAC;;IAGV,wBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IAChB,4BAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACxD,yBAAA;;4BAGD,IAAI;gCACF,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;IACjD,yBAAA;IAAC,wBAAA,OAAO,KAAK,EAAE;IACd,4BAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACnB,4BAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CACtD,IAAI,CAAC,MAAgC,CAAC,CAAC;IAC1C,yBAAA;;IAED,wBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;IAE/B,wBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAEtB,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;;IAE9B,gCAAA,KAAI,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,YAAA;IACzC,oCAAA,OAAO,EAAE,CAAC;IACZ,iCAAC,CAAC;IACJ,6BAAC,CAAC,CAAC,CAAA;;;;IACJ,KAAA,CAAA;IAEK,IAAA,cAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;oBACE,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,iBAAA;oBAGD,IAAI;wBACF,GAAG,GAAGK,UAAO,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACnD,iBAAA;IAAC,gBAAA,OAAO,CAAC,EAAE;IACV,oBAAA,MAAM,IAAI,KAAK,CACX,2CAAA,CAAA,MAAA,CAA4C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;IACtE,iBAAA;oBACD,IAAI,IAAI,CAAC,MAAM,EAAE;wBACf,IAAI;IACF,wBAAA,OAAA,CAAA,CAAA,aAAO,EAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;IAC3D,qBAAA;IAAC,oBAAA,OAAO,CAAC,EAAE;4BACV,MAAM,IAAI,KAAK,CAAC,mCAAA,CAAA,MAAA,CAAoC,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;IAClE,qBAAA;IAAS,4BAAA;4BACR,GAAG,CAAC,OAAO,EAAE,CAAC;IACf,qBAAA;IACF,iBAAA;IAAM,qBAAA;wBACL,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAA;IAClC,iBAAA;;;;IACF,KAAA,CAAA;IAEO,IAAA,cAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;;;;YAIN,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY;iBAC9D,IAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,WAAW;oBAC/D,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;IACvE,YAAA,OAAO,IAAI,CAAC;IACb,SAAA;IACD,QAAA,OAAO,KAAK,CAAC;SACd,CAAA;;QAGD,cAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,GAAa,EAAA;YAAhC,IAWC,KAAA,GAAA,IAAA,CAAA;IAVC,QAAA,OAAOC,OAAI,CAAC,YAAA;IACV,YAAA,IAAM,aAAa,GAAaC,aAAU,CAACC,OAAI,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;IACtE,YAAA,IAAI,YAAY,CAAC;gBACjB,YAAY,GAAGC,QAAK,CAAC,aAAa,CAC9B,aAAa,EAAE,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,UAAU,EAAE,KAAI,CAAC,QAAQ,EAC3D,UAAU,CAAC,CAAC;;IAEhB,YAAA,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;gBACjC,OAAOC,UAAO,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAA6B,CAAC,CAAC;IAC3E,SAAC,CAAC,CAAC;SACJ,CAAA;;;IAIK,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAb,YAAA;;;;IACU,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;IAAzB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,CAAC,EAAA,CAAA,IAAA,EAAiB,EAAE,KAAK,CAAC,CAAA;;;;IAClC,KAAA,CAAA;;IAGD,IAAA,cAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;YACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IAEvC,QAAA,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,EAAI,EAAA,OAAA,KAAK,CAAC,IAAI,EAAE,CAAZ,EAAY,CAAC,CAAC;YAEtC,IAAI;IACF,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1C,SAAA;IAAC,QAAA,OAAO,KAAK,EAAE;IACd,YAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;IACpC,SAAA;IACD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB,CAAA;;IAGQ,IAAA,cAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACP,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE,CAAA;QACF,OAAA,cAAA,CAAA;IAAA,CArMD,CAAoC,YAAsB,CAqMzD,CAAA;;IC/ND;;;;;;;;;;;;;;;;IAgBG;IAIH;;;;;;IAMG;IACH,IAAA,UAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,UAAA,GAAA;SAUC;QAAA,OAAA,UAAA,CAAA;IAAA,CAAA,EAAA,CAAA,CAAA;IAED;IACA;IACA;;ICrBA,IAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAA6C,SAAoB,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IAAjE,IAAA,SAAA,cAAA,GAAA;;SAsBC;IArBC;;;;;;;;;;;;;;;;;IAiBG;QACH,cAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,SAAiB,EAAA;IACrB,QAAA,OAAO,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC3C,CAAA;QACF,OAAA,cAAA,CAAA;IAAA,CAtBD,CAA6C,YAAoB,CAsBhE,CAAA,CAAA;IAED;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;QAA4B,SAAc,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;QAGxC,SAAsB,aAAA,CAAA,QAA8B,EAAE,SAAiB,EAAA;IAAvE,QAAA,IAAA,KAAA,GACE,iBAAO,IAER,IAAA,CAAA;IAHqB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;YAElD,KAAI,CAAC,IAAI,GAAG,IAAI,iBAAiB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;;SACxD;IAED,IAAA,aAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;SAC5B,CAAA;IAEK,IAAA,aAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;IACE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;;;IACzB,KAAA,CAAA;QACF,OAAA,aAAA,CAAA;IAAA,CAfD,CAA4B,cAAc,CAezC,CAAA,CAAA;IAED,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAgC,SAAyB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;QAIvD,SACc,iBAAA,CAAA,QAA8B,EAAY,SAAiB,EAAA;IADzE,QAAA,IAAA,KAAA,GAEE,iBAAO,IACR,IAAA,CAAA;IAFa,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;IAAY,QAAA,KAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;;IAHzE,QAAA,KAAS,CAAA,SAAA,GAAG,EAAE,CAAC;;SAKd;IAED,IAAA,iBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,EAAG,CAAA,MAAA,CAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAA,aAAA,CAAA,CAAA,MAAA,CAAc,IAAI,CAAC,SAAS,EAAA,IAAA,CAAI,CAAC;SACnE,CAAA;IAEK,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;IACsB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAxC,wBAAA,WAAW,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BAC9C,IAAI,WAAW,CAAC,IAAI,EAAE;IACpB,4BAAA,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;IACzB,gCAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;IACd,6BAAA;;;gCAID,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtC,4BAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACpB,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;IACb,yBAAA;4BACK,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAa,CAAC;;;;IAKlE,wBAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;IACrC,4BAAA,KAAmB,EAAA,GAAA,QAAA,CAAA,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,EAAE,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;oCAA5B,IAAI,GAAA,EAAA,CAAA,KAAA,CAAA;IACb,gCAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,6BAAA;;;;;;;;;4BACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEzC,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;IACb,KAAA,CAAA;QACF,OAAA,iBAAA,CAAA;IAAA,CAvCD,CAAgC,iBAAyB,CAuCxD,CAAA;;ICzFD,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAgD,SAAwB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;IAAxE,IAAA,SAAA,iBAAA,GAAA;;SAaC;IAZC;;;;;;;;IAQG;IACH,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;IACE,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;SAC/B,CAAA;QACF,OAAA,iBAAA,CAAA;IAAA,CAbD,CAAgD,YAAwB,CAavE,CAAA,CAAA;IAED;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA,IAAA,YAAA,kBAAA,UAAA,MAAA,EAAA;QAA2B,SAAc,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA;IAGvC,IAAA,SAAA,YAAA,CAAsB,QAAkC,EAAA;IAAxD,QAAA,IAAA,KAAA,GACE,iBAAO,IAER,IAAA,CAAA;IAHqB,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;YAEtD,KAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;SAC5C;IAED,IAAA,YAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;SAC5B,CAAA;IAEK,IAAA,YAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;IACE,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;;;IACzB,KAAA,CAAA;QACF,OAAA,YAAA,CAAA;IAAA,CAfD,CAA2B,cAAc,CAexC,CAAA,CAAA;IAED;;;;;;;;;;;;;;;;;;;;;IAqBG;IACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;QAA+B,SAAyB,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;IAMtD,IAAA,SAAA,gBAAA,CAA+B,QAAkC,EAAA;IAAjE,QAAA,IAAA,KAAA,GACE,iBAAO,IAQR,IAAA,CAAA;IAT8B,QAAA,KAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;IAE/D,QAAA,IAAIT,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAC3B,KAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACzC,SAAA;IAAM,aAAA;;IAEE,YAAA,IAAA,aAAa,GAAI,OAAO,CAAC,gBAAgB,CAAC,cAA7B,CAA8B;gBAClD,KAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;IAC1C,SAAA;;SACF;IACD,IAAA,gBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;YACE,OAAO,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAU,CAAC;SAC7C,CAAA;IAEK,IAAA,gBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;IACsB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA,CAAA;;IAAxC,wBAAA,WAAW,GAAG,EAA0B,CAAA,IAAA,EAAA,CAAA;4BAE9C,IAAI,WAAW,CAAC,IAAI,EAAE;IACpB,4BAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;IACd,yBAAA;IAAM,6BAAA;IACL,4BAAA,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAC3B,yBAAA;IAGD,wBAAA,IAAIA,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;IAC3B,4BAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;IACnD,yBAAA;IAAM,6BAAA;IACL,4BAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACtD,yBAAA;IACD,wBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;IACb,KAAA,CAAA;QACF,OAAA,gBAAA,CAAA;IAAA,CAtCD,CAA+B,iBAAyB,CAsCvD,CAAA;;IC/FD;;;;;;IAMG;IACH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;QAAuC,SAAiB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;QAItD,SACc,iBAAA,CAAA,IAAiB,EACjB,OAAsC,EAAA;IAAtC,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAsC,GAAA,EAAA,CAAA,EAAA;IAFpD,QAAA,IAAA,KAAA,GAGE,iBAAO,IAWR,IAAA,CAAA;IAba,QAAA,KAAI,CAAA,IAAA,GAAJ,IAAI,CAAa;IACjB,QAAA,KAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;IAElD,QAAAD,OAAI,CAAC,MAAM,CACP,CAAC,IAAI,YAAY,UAAU;iBACtBC,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;qBAClB,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;IAC7C,gBAAA,KAAK,CAAC,EACf,YAAM,EAAA,OAAA,4DAA4D;gBAC9D,YAAY,CAAA,EAAA,CAAC,CAAC;YACtB,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;;YAElC,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC;;SACnD;IAED,IAAA,iBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;IACE,QAAA,OAAO,aAAc,CAAA,MAAA,CAAA,IAAI,CAAC,IAAI,CAAE,CAAC;SAClC,CAAA;IAEK,IAAA,iBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;;;;IACE,wBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,UAAU;IAC5B,4BAAA,IAAI,CAAC,IAAI,CAAC,UAAU;IACpB,4BAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gCACvC,OAAO,CAAA,CAAA,aAAA,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC,CAAA;IAClC,yBAAA;IACK,wBAAA,KAAK,GAAG,IAAI,OAAO,CAAa,UAAC,OAAO,EAAE,MAAM,EAAA;gCACpD,IAAM,GAAG,GAAG,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,SAAS,CAAC;IACzC,4BAAA,IAAI,KAAI,CAAC,IAAI,YAAY,UAAU,EAAE;;;IAGnC,gCAAA,OAAO,CAAC,IAAI,UAAU,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5D,6BAAA;IAAM,iCAAA;;;;IAKL,gCAAA,IAAM,YAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,gCAAA,YAAU,CAAC,MAAM,GAAG,UAAC,KAAK,EAAA;IACxB,oCAAA,IAAI,IAAI,GAAkC,YAAU,CAAC,MAAM,CAAC;;;;wCAI5D,IAAI,IAAI,YAAY,WAAW,EAAE;IAC/B,wCAAA,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IAC7B,qCAAA;IACD,oCAAA,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC,EAAE;4CACjC,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;IACnE,qCAAA;wCACD,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,iCAAC,CAAC;IACF,gCAAA,YAAU,CAAC,OAAO,GAAG,UAAC,KAAK,EAAA;wCACzB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IACtC,iCAAC,CAAC;IACF,gCAAA,YAAU,CAAC,OAAO,GAAG,UAAC,KAAK,EAAA;wCACzB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACvC,iCAAC,CAAC;;;IAGF,gCAAA,IAAM,KAAK,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;;;IAGhD,gCAAA,YAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACrC,6BAAA;IACD,4BAAA,KAAI,CAAC,MAAM,GAAG,GAAG,CAAC;IACpB,yBAAC,CAAC,CAAC;;IACa,wBAAA,OAAA,CAAA,CAAA,YAAM,KAAK,CAAA,CAAA;gCAA3B,OAAQ,CAAA,CAAA,cAAA,EAAA,CAAA,KAAK,IAAG,EAAA,CAAA,IAAA,EAAW,CAAC,EAAE,EAAA,CAAA,IAAI,GAAE,KAAK,EAAE,EAAA,EAAA,CAAA;;;;IAC5C,KAAA,CAAA;QACF,OAAA,iBAAA,CAAA;IAAA,CAxED,CAAuC,iBAAiB,CAwEvD,CAAA;;ICxFD;;;;;;IAMG;aACmB,gBAAgB,CAClC,GAAgB,EAAE,OAAsC,EACxD,SAAoB,EAAA;IADF,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAsC,GAAA,EAAA,CAAA,EAAA;;;;;;IAI1D,oBAAA,IAAI,CAAC,OAAO,GAAG,MAAM,QAAQ,EAAE;4BAC7B,SAAS,GAAG,GAAa,CAAC;IAC3B,qBAAA;IAAM,yBAAA;IACL,wBAAA,SAAS,GAAI,GAAe,CAAC,GAAG,CAAC;IACjC,wBAAA,WAAW,GAAG,yBAAyB,CAAC,GAAc,CAAC,CAAC;IACzD,qBAAA;IACgB,oBAAA,OAAA,CAAA,CAAA,YAAM,CAAC,SAAS,IAAID,OAAI,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CAAA,CAAA;;IAAlE,oBAAA,QAAQ,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;6BACpE,QAAQ,CAAC,EAAE,EAAX,OAAW,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;6BACU,UAAU,CAAA,IAAA,CAAA;IAAC,oBAAA,OAAA,CAAA,CAAA,YAAM,QAAQ,CAAC,WAAW,EAAE,CAAA,CAAA;;IAAxD,oBAAA,UAAU,GAAG,KAAA,EAAA,CAAA,KAAA,CAAI,UAAU,EAAA,CAAA,KAAA,CAAA,EAAC,SAA4B,CAAC,CAAA,GAAA,CAAA;IAC/D,oBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;IAElD,gBAAA,KAAA,CAAA,EAAA,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;;;;IAExC,CAAA;IAED;IACA,IAAM,yBAAyB,GAAG,UAAC,OAAgB,EAAA;IACjD,IAAA,IAAM,IAAI,GAAG;YACX,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC;IACF,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;;IC9DD;;;;;;;;;;;;;;;;IAgBG;IAEH;IACA;IACA;IACM,SAAU,WAAW,CAAC,MAAW,EAAA;IACrC,IAAA,OAAO,CAAC,OAAO,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC;IAC1E;;ICEA;;;IAGG;AACH,QAAA,cAAA,kBAAA,UAAA,MAAA,EAAA;QAAoC,SAAU,CAAA,cAAA,EAAA,MAAA,CAAA,CAAA;IAC5C;;;;;;;IAOG;QACH,SACc,cAAA,CAAA,KAAyB,EAChB,OAAsC,EAAA;IAAtC,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAAsC,GAAA,EAAA,CAAA,EAAA;IAF7D,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHa,QAAA,KAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;IAChB,QAAA,KAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;;SAE5D;IAEK,IAAA,cAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;;IACE,gBAAA,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIC,MAAG,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;IAE7C,oBAAA,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzB,oBAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,YAAY,CAAE,IAAI,CAAC,KAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,iBAAA;;;oBAGD,OAAO,CAAA,CAAA,aAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;;;IACvE,KAAA,CAAA;QACF,OAAA,cAAA,CAAA;IAAA,CAzBD,CAAoC,UAAU,CAyB7C;;IC7BD;;IAEG;AACH,QAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;QAAmC,SAAU,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;IAC3C;;;;;;IAMG;QACH,SACuB,aAAA,CAAA,GAAgB,EAChB,WAA0C,EAAA;IAA1C,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAA0C,GAAA,EAAA,CAAA,EAAA;IAFjE,QAAA,IAAA,KAAA,GAGE,iBAAO,IACR,IAAA,CAAA;IAHsB,QAAA,KAAG,CAAA,GAAA,GAAH,GAAG,CAAa;IAChB,QAAA,KAAW,CAAA,WAAA,GAAX,WAAW,CAA+B;;SAEhE;;;;;IAMK,IAAA,aAAA,CAAA,SAAA,CAAA,QAAQ,GAAd,YAAA;;;IACE,gBAAA,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IACzB,oBAAA,OAAA,CAAA,CAAA,aAAO,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,GAAa,EAAE,IAAI,CAAC,WAAW,CAAC;IAC3D,6BAAA,QAAQ,EAAE,CAAC,CAAA;IACjB,iBAAA;IAAM,qBAAA;wBACL,OAAO,CAAA,CAAA,aAAA,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;IACrD,iBAAA;;;IACF,KAAA,CAAA;QACF,OAAA,aAAA,CAAA;IAAA,CA1BD,CAAmC,UAAU,CA0B5C;;IC3BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6EG;IACa,SAAA,GAAG,CACf,MAAmB,EAAE,SAAyB,EAAA;IAAzB,IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAyB,GAAA,EAAA,CAAA,EAAA;QAChD,OAAO,IAAI,UAAU,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;IAuBG;IACG,SAAU,IAAI,CAChB,CAAsD,EAAA;QAD1D,IAIC,KAAA,GAAA,IAAA,CAAA;IAFC,IAAA,IAAM,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACrC,IAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA,EAAA,OAAA,WAAA,CAAA,IAAA,EAAA,UAAA,EAAA,EAAA;IAAY,QAAA,OAAA,CAAA,CAAA,aAAA,IAAI,CAAA,CAAA;IAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyDG;IACG,SAAU,SAAS,CACvB,SAAsE,EAAA;QADxE,IAOC,KAAA,GAAA,IAAA,CAAA;IAJC,IAAA,OAAO,qBAAqB,CAAC,YAAA,EAAA,OAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,KAAA,CAAA,EAAA,YAAA;;;;4BACf,OAAM,CAAA,CAAA,YAAA,SAAS,EAAE,CAAA,CAAA;;IAAvB,oBAAA,GAAG,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;wBAC7B,OAAO,CAAA,CAAA,aAAA,oBAAoB,CAAC,YAAA,EAAM,OAAA,GAAG,CAAC,IAAI,EAAE,CAAA,EAAA,CAAC,CAAC,CAAA;;;IAC/C,KAAA,CAAA,CAAA,EAAA,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BG;IACmB,SAAA,MAAM,CACxB,kBAAqC,EACrC,YAA2B,EAAA;;;gBAC7B,OAAO,CAAA,CAAA,aAAA,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC,CAAA;;;IAChE,CAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiCG;IACG,SAAgB,UAAU,CAAC,gBAAmC,EAAA;;;IAElE,YAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAA;;;IACpD;;ICxRD;IAEA;AACM,QAAA,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}