/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-data/dist/util/growing_ring_buffer" />
import { RingBuffer } from './ring_buffer';
export declare class GrowingRingBuffer<T> extends RingBuffer<T> {
    private static INITIAL_CAPACITY;
    /**
     * Constructs a `GrowingRingBuffer`.
     */
    constructor();
    isFull(): boolean;
    push(value: T): void;
    unshift(value: T): void;
    /**
     * Doubles the capacity of the buffer.
     */
    private expand;
}
