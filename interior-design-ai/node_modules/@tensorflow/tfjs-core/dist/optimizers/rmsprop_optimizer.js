/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ENGINE } from '../engine';
import { dispose, tidy } from '../globals';
import { add } from '../ops/add';
import { div } from '../ops/div';
import { mul } from '../ops/mul';
import { sqrt } from '../ops/sqrt';
import { square } from '../ops/square';
import { sub } from '../ops/sub';
import { zerosLike } from '../ops/zeros_like';
import { Optimizer } from './optimizer';
/** @doclink Optimizer */
export class RMSPropOptimizer extends Optimizer {
    /** @nocollapse */
    static get className() {
        // Name matters for Python compatibility.
        // This is a getter instead of a property because when it's a property, it
        // prevents the entire class from being tree-shaken.
        return 'RMSProp';
    }
    constructor(learningRate, decay = 0.9, momentum = 0.0, epsilon = null, centered = false) {
        super();
        this.learningRate = learningRate;
        this.decay = decay;
        this.momentum = momentum;
        this.epsilon = epsilon;
        this.accumulatedMeanSquares = [];
        this.accumulatedMoments = [];
        this.accumulatedMeanGrads = [];
        this.centered = centered;
        if (epsilon == null) {
            this.epsilon = ENGINE.backend.epsilon();
        }
        if (learningRate == null) {
            throw new Error(`learningRate for RMSPropOptimizer must be defined.`);
        }
    }
    applyGradients(variableGradients) {
        const variableNames = Array.isArray(variableGradients) ?
            variableGradients.map(item => item.name) :
            Object.keys(variableGradients);
        variableNames.forEach((name, i) => {
            const value = ENGINE.registeredVariables[name];
            const trainable = false;
            if (this.accumulatedMeanSquares[i] == null) {
                this.accumulatedMeanSquares[i] = {
                    originalName: `${name}/rms`,
                    variable: tidy(() => zerosLike(value).variable(trainable))
                };
            }
            if (this.accumulatedMoments[i] == null) {
                this.accumulatedMoments[i] = {
                    originalName: `${name}/momentum`,
                    variable: tidy(() => zerosLike(value).variable(trainable))
                };
            }
            if (this.accumulatedMeanGrads[i] == null && this.centered) {
                this.accumulatedMeanGrads[i] = {
                    originalName: `${name}/mg`,
                    variable: tidy(() => zerosLike(value).variable(trainable))
                };
            }
            const gradient = Array.isArray(variableGradients) ?
                variableGradients[i].tensor :
                variableGradients[name];
            if (gradient == null) {
                return;
            }
            const accumulatedMeanSquare = this.accumulatedMeanSquares[i].variable;
            const accumulatedMoments = this.accumulatedMoments[i].variable;
            tidy(() => {
                const newAccumulatedMeanSquare = add(mul(accumulatedMeanSquare, this.decay), mul(square(gradient), 1 - this.decay));
                if (this.centered) {
                    const accumulatedMeanGrad = this.accumulatedMeanGrads[i].variable;
                    // Centered gradient
                    const newAccumulatedMeanGrad = add(mul(accumulatedMeanGrad, this.decay), mul(gradient, 1 - this.decay));
                    const gradContribution = div(mul(gradient, this.learningRate), sqrt(sub(newAccumulatedMeanSquare, add(square(newAccumulatedMeanGrad), this.epsilon))));
                    const newAccumulatedMoments = add(mul(accumulatedMoments, this.momentum), gradContribution);
                    accumulatedMeanSquare.assign(newAccumulatedMeanSquare);
                    accumulatedMeanGrad.assign(newAccumulatedMeanGrad);
                    accumulatedMoments.assign(newAccumulatedMoments);
                    const newValue = sub(value, newAccumulatedMoments);
                    value.assign(newValue);
                }
                else {
                    // Plain gradient
                    const newAccumulatedMeanSquare = add(mul(accumulatedMeanSquare, this.decay), mul(square(gradient), 1 - this.decay));
                    const newAccumulatedMoments = add(mul(accumulatedMoments, this.momentum), div(mul(gradient, this.learningRate), sqrt(add(newAccumulatedMeanSquare, this.epsilon))));
                    accumulatedMeanSquare.assign(newAccumulatedMeanSquare);
                    accumulatedMoments.assign(newAccumulatedMoments);
                    const newValue = sub(value, newAccumulatedMoments);
                    value.assign(newValue);
                }
            });
        });
        this.incrementIterations();
    }
    dispose() {
        if (this.accumulatedMeanSquares != null) {
            dispose(this.accumulatedMeanSquares.map(v => v.variable));
        }
        if (this.accumulatedMeanGrads != null && this.centered) {
            dispose(this.accumulatedMeanGrads.map(v => v.variable));
        }
        if (this.accumulatedMoments != null) {
            dispose(this.accumulatedMoments.map(v => v.variable));
        }
    }
    async getWeights() {
        // Order matters for Python compatibility.
        const variables = [...this.accumulatedMeanSquares, ...this.accumulatedMoments];
        if (this.centered) {
            variables.push(...this.accumulatedMeanGrads);
        }
        return [await this.saveIterations()].concat(variables.map(v => ({ name: v.originalName, tensor: v.variable })));
    }
    async setWeights(weightValues) {
        weightValues = await this.extractIterations(weightValues);
        const variableCount = this.centered ? weightValues.length / 3 : weightValues.length / 2;
        const trainable = false;
        this.accumulatedMeanSquares =
            weightValues.slice(0, variableCount).map(v => ({
                originalName: v.name,
                variable: v.tensor.variable(trainable)
            }));
        this.accumulatedMoments =
            weightValues.slice(variableCount, variableCount * 2)
                .map(v => ({
                originalName: v.name,
                variable: v.tensor.variable(trainable)
            }));
        if (this.centered) {
            this.accumulatedMeanGrads =
                weightValues.slice(variableCount * 2, variableCount * 3)
                    .map(v => ({
                    originalName: v.name,
                    variable: v.tensor.variable(trainable)
                }));
        }
    }
    getConfig() {
        return {
            'learningRate': this.learningRate,
            'decay': this.decay,
            'momentum': this.momentum,
            'epsilon': this.epsilon,
            'centered': this.centered
        };
    }
    /** @nocollapse */
    static fromConfig(cls, config) {
        return new cls(config['learningRate'], config['decay'], config['momentum'], config['epsilon'], config['centered']);
    }
}
//# sourceMappingURL=data:application/json;base64,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