/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ENGINE } from './engine';
import { env } from './environment';
import { getGlobalTensorClass } from './tensor';
import { isWebGLData, isWebGPUData } from './types';
import { assert, flatten, inferDtype, isTypedArray, toTypedArray } from './util';
import { bytesPerElement } from './util_base';
export function inferShape(val, dtype) {
    let firstElem = val;
    if (isTypedArray(val)) {
        return dtype === 'string' ? [] : [val.length];
    }
    if (isWebGLData(val)) {
        const usedChannels = val.channels || 'RGBA';
        return [val.height, val.width * usedChannels.length];
    }
    else if (isWebGPUData(val)) {
        return [val.buffer.size / (dtype == null ? 4 : bytesPerElement(dtype))];
    }
    if (!Array.isArray(val)) {
        return []; // Scalar.
    }
    const shape = [];
    while (Array.isArray(firstElem) ||
        isTypedArray(firstElem) && dtype !== 'string') {
        shape.push(firstElem.length);
        firstElem = firstElem[0];
    }
    if (Array.isArray(val) &&
        env().getBool('TENSORLIKE_CHECK_SHAPE_CONSISTENCY')) {
        deepAssertShapeConsistency(val, shape, []);
    }
    return shape;
}
function deepAssertShapeConsistency(val, shape, indices) {
    indices = indices || [];
    if (!(Array.isArray(val)) && !isTypedArray(val)) {
        assert(shape.length === 0, () => `Element arr[${indices.join('][')}] is a primitive, ` +
            `but should be an array/TypedArray of ${shape[0]} elements`);
        return;
    }
    assert(shape.length > 0, () => `Element arr[${indices.join('][')}] should be a primitive, ` +
        `but is an array of ${val.length} elements`);
    assert(val.length === shape[0], () => `Element arr[${indices.join('][')}] should have ${shape[0]} ` +
        `elements, but has ${val.length} elements`);
    const subShape = shape.slice(1);
    for (let i = 0; i < val.length; ++i) {
        deepAssertShapeConsistency(val[i], subShape, indices.concat(i));
    }
}
function assertDtype(expectedDtype, actualDType, argName, functionName) {
    if (expectedDtype === 'string_or_numeric') {
        return;
    }
    if (expectedDtype == null) {
        throw new Error(`Expected dtype cannot be null.`);
    }
    if (expectedDtype !== 'numeric' && expectedDtype !== actualDType ||
        expectedDtype === 'numeric' && actualDType === 'string') {
        throw new Error(`Argument '${argName}' passed to '${functionName}' must ` +
            `be ${expectedDtype} tensor, but got ${actualDType} tensor`);
    }
}
export function convertToTensor(x, argName, functionName, parseAsDtype = 'numeric') {
    if (x instanceof getGlobalTensorClass()) {
        assertDtype(parseAsDtype, x.dtype, argName, functionName);
        return x;
    }
    let inferredDtype = inferDtype(x);
    // If the user expects a bool/int/float, use that info to update the
    // inferredDtype when it is not a string.
    if (inferredDtype !== 'string' &&
        ['bool', 'int32', 'float32'].indexOf(parseAsDtype) >= 0) {
        inferredDtype = parseAsDtype;
    }
    assertDtype(parseAsDtype, inferredDtype, argName, functionName);
    if ((x == null) ||
        (!isTypedArray(x) && !Array.isArray(x) && typeof x !== 'number' &&
            typeof x !== 'boolean' && typeof x !== 'string')) {
        const type = x == null ? 'null' : x.constructor.name;
        throw new Error(`Argument '${argName}' passed to '${functionName}' must be a ` +
            `Tensor or TensorLike, but got '${type}'`);
    }
    const inferredShape = inferShape(x, inferredDtype);
    if (!isTypedArray(x) && !Array.isArray(x)) {
        x = [x];
    }
    const skipTypedArray = true;
    const values = inferredDtype !== 'string' ?
        toTypedArray(x, inferredDtype) :
        flatten(x, [], skipTypedArray);
    return ENGINE.makeTensor(values, inferredShape, inferredDtype);
}
export function convertToTensorArray(arg, argName, functionName, parseAsDtype = 'numeric') {
    if (!Array.isArray(arg)) {
        throw new Error(`Argument ${argName} passed to ${functionName} must be a ` +
            '`Tensor[]` or `TensorLike[]`');
    }
    const tensors = arg;
    return tensors.map((t, i) => convertToTensor(t, `${argName}[${i}]`, functionName, parseAsDtype));
}
//# sourceMappingURL=data:application/json;base64,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