import cjsModule from './index.js';

export const constants = cjsModule.constants;
export const defaults = cjsModule.defaults;
export const deprecationEntries = cjsModule.deprecationEntries;
export const descriptions = cjsModule.descriptions;
export const isJSONString = cjsModule.isJSONString;
export const normalize = cjsModule.normalize;
export const readConfig = cjsModule.readConfig;
export const readConfigs = cjsModule.readConfigs;
export const readInitialOptions = cjsModule.readInitialOptions;
export const replaceRootDirInPath = cjsModule.replaceRootDirInPath;
