{"version": 3, "file": "is-disabled.mjs", "names": ["getLocalName", "elementsSupportingDisabledAttribute", "Set", "isDisabled", "element", "localName", "has", "hasAttribute", "getAttribute"], "sources": ["../sources/is-disabled.ts"], "sourcesContent": ["import { getLocalName } from \"./getRole\";\n\nconst elementsSupportingDisabledAttribute = new Set([\n\t\"button\",\n\t\"fieldset\",\n\t\"input\",\n\t\"optgroup\",\n\t\"option\",\n\t\"select\",\n\t\"textarea\",\n]);\n\n/**\n * Check if an element is disabled\n * https://www.w3.org/TR/html-aam-1.0/#html-attribute-state-and-property-mappings\n * https://www.w3.org/TR/wai-aria-1.1/#aria-disabled\n *\n * @param element\n * @returns {boolean} true if disabled, otherwise false\n */\nexport function isDisabled(element: Element): boolean {\n\tconst localName = getLocalName(element);\n\treturn elementsSupportingDisabledAttribute.has(localName) &&\n\t\telement.hasAttribute(\"disabled\")\n\t\t? true\n\t\t: element.getAttribute(\"aria-disabled\") === \"true\";\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAW;AAExC,IAAMC,mCAAmC,GAAG,IAAIC,GAAG,CAAC,CACnD,QAAQ,EACR,UAAU,EACV,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,UAAU,CACV,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,OAAgB,EAAW;EACrD,IAAMC,SAAS,GAAGL,YAAY,CAACI,OAAO,CAAC;EACvC,OAAOH,mCAAmC,CAACK,GAAG,CAACD,SAAS,CAAC,IACxDD,OAAO,CAACG,YAAY,CAAC,UAAU,CAAC,GAC9B,IAAI,GACJH,OAAO,CAACI,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;AACpD"}