{"name": "strip-indent", "version": "3.0.0", "description": "Strip leading whitespace from each line in a string", "license": "MIT", "repository": "sindresorhus/strip-indent", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string"], "dependencies": {"min-indent": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}