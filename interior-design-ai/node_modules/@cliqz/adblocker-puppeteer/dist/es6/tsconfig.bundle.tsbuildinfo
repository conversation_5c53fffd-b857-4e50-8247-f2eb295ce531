{"program": {"fileNames": ["../../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../../node_modules/typescript/lib/lib.es2019.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.d.ts", "../../../../node_modules/typescript/lib/lib.dom.d.ts", "../../../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../../../../node_modules/typescript/lib/lib.scripthost.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../../node_modules/typescript/lib/lib.es2017.full.d.ts", "../../../../node_modules/@types/node/assert.d.ts", "../../../../node_modules/@types/node/assert/strict.d.ts", "../../../../node_modules/@types/node/globals.d.ts", "../../../../node_modules/@types/node/async_hooks.d.ts", "../../../../node_modules/@types/node/buffer.d.ts", "../../../../node_modules/@types/node/child_process.d.ts", "../../../../node_modules/@types/node/cluster.d.ts", "../../../../node_modules/@types/node/console.d.ts", "../../../../node_modules/@types/node/constants.d.ts", "../../../../node_modules/@types/node/crypto.d.ts", "../../../../node_modules/@types/node/dgram.d.ts", "../../../../node_modules/@types/node/diagnostics_channel.d.ts", "../../../../node_modules/@types/node/dns.d.ts", "../../../../node_modules/@types/node/dns/promises.d.ts", "../../../../node_modules/@types/node/domain.d.ts", "../../../../node_modules/@types/node/events.d.ts", "../../../../node_modules/@types/node/fs.d.ts", "../../../../node_modules/@types/node/fs/promises.d.ts", "../../../../node_modules/@types/node/http.d.ts", "../../../../node_modules/@types/node/http2.d.ts", "../../../../node_modules/@types/node/https.d.ts", "../../../../node_modules/@types/node/inspector.d.ts", "../../../../node_modules/@types/node/module.d.ts", "../../../../node_modules/@types/node/net.d.ts", "../../../../node_modules/@types/node/os.d.ts", "../../../../node_modules/@types/node/path.d.ts", "../../../../node_modules/@types/node/perf_hooks.d.ts", "../../../../node_modules/@types/node/process.d.ts", "../../../../node_modules/@types/node/punycode.d.ts", "../../../../node_modules/@types/node/querystring.d.ts", "../../../../node_modules/@types/node/readline.d.ts", "../../../../node_modules/@types/node/repl.d.ts", "../../../../node_modules/@types/node/stream.d.ts", "../../../../node_modules/@types/node/stream/promises.d.ts", "../../../../node_modules/@types/node/stream/consumers.d.ts", "../../../../node_modules/@types/node/stream/web.d.ts", "../../../../node_modules/@types/node/string_decoder.d.ts", "../../../../node_modules/@types/node/timers.d.ts", "../../../../node_modules/@types/node/timers/promises.d.ts", "../../../../node_modules/@types/node/tls.d.ts", "../../../../node_modules/@types/node/trace_events.d.ts", "../../../../node_modules/@types/node/tty.d.ts", "../../../../node_modules/@types/node/url.d.ts", "../../../../node_modules/@types/node/util.d.ts", "../../../../node_modules/@types/node/v8.d.ts", "../../../../node_modules/@types/node/vm.d.ts", "../../../../node_modules/@types/node/wasi.d.ts", "../../../../node_modules/@types/node/worker_threads.d.ts", "../../../../node_modules/@types/node/zlib.d.ts", "../../../../node_modules/@types/node/globals.global.d.ts", "../../../../node_modules/@types/node/index.d.ts", "../../../../node_modules/devtools-protocol/types/protocol.d.ts", "../../../../node_modules/devtools-protocol/types/protocol-mapping.d.ts", "../../../../node_modules/puppeteer/lib/types.d.ts", "../../../../node_modules/tldts-core/dist/types/src/lookup/interface.d.ts", "../../../../node_modules/tldts-core/dist/types/src/options.d.ts", "../../../../node_modules/tldts-core/dist/types/src/factory.d.ts", "../../../../node_modules/tldts-core/dist/types/src/lookup/fast-path.d.ts", "../../../../node_modules/tldts-core/dist/types/index.d.ts", "../../../../node_modules/tldts-experimental/dist/types/index.d.ts", "../../../adblocker-extended-selectors/dist/types/src/types.d.ts", "../../../adblocker-extended-selectors/dist/types/src/parse.d.ts", "../../../adblocker-extended-selectors/dist/types/src/eval.d.ts", "../../../adblocker-extended-selectors/dist/types/src/extended.d.ts", "../../../adblocker-extended-selectors/dist/types/adblocker.d.ts", "../../../adblocker-content/dist/types/adblocker.d.ts", "../../../../node_modules/@remusao/smaz/dist/types/index.d.ts", "../../../adblocker/dist/types/src/compression.d.ts", "../../../adblocker/dist/types/src/data-view.d.ts", "../../../adblocker/dist/types/src/config.d.ts", "../../../adblocker/dist/types/src/events.d.ts", "../../../adblocker/dist/types/src/fetch.d.ts", "../../../adblocker/dist/types/src/html-filtering.d.ts", "../../../adblocker/dist/types/src/engine/domains.d.ts", "../../../adblocker/dist/types/src/filters/interface.d.ts", "../../../adblocker/dist/types/src/filters/cosmetic.d.ts", "../../../../node_modules/@types/har-format/index.d.ts", "../../../../node_modules/@types/chrome/har-format/index.d.ts", "../../../../node_modules/@types/chrome/chrome-cast/index.d.ts", "../../../../node_modules/@types/filewriter/index.d.ts", "../../../../node_modules/@types/filesystem/index.d.ts", "../../../../node_modules/@types/chrome/index.d.ts", "../../../../node_modules/@types/firefox-webext-browser/index.d.ts", "../../../adblocker/dist/types/src/request.d.ts", "../../../adblocker/dist/types/src/filters/network.d.ts", "../../../adblocker/dist/types/src/lists.d.ts", "../../../adblocker/dist/types/src/resources.d.ts", "../../../adblocker/dist/types/src/engine/reverse-index.d.ts", "../../../adblocker/dist/types/src/engine/bucket/filters.d.ts", "../../../adblocker/dist/types/src/engine/bucket/cosmetic.d.ts", "../../../adblocker/dist/types/src/engine/bucket/network.d.ts", "../../../adblocker/dist/types/src/engine/engine.d.ts", "../../../adblocker/dist/types/src/tokens-buffer.d.ts", "../../../adblocker/dist/types/src/utils.d.ts", "../../../adblocker/dist/types/src/encoding.d.ts", "../../../adblocker/dist/types/adblocker.d.ts", "../../adblocker.ts", "../../../../node_modules/@types/benchmark/index.d.ts", "../../../../node_modules/@types/keyv/index.d.ts", "../../../../node_modules/@types/http-cache-semantics/index.d.ts", "../../../../node_modules/@types/responselike/index.d.ts", "../../../../node_modules/@types/cacheable-request/index.d.ts", "../../../../node_modules/@types/chai/index.d.ts", "../../../../node_modules/@types/chai-as-promised/index.d.ts", "../../../../node_modules/@types/command-line-args/index.d.ts", "../../../../node_modules/@types/command-line-usage/index.d.ts", "../../../../node_modules/@types/estree/index.d.ts", "../../../../node_modules/@types/parse5/lib/tree-adapters/default.d.ts", "../../../../node_modules/@types/parse5/index.d.ts", "../../../../node_modules/@types/tough-cookie/index.d.ts", "../../../../node_modules/@types/jsdom/base.d.ts", "../../../../node_modules/@types/jsdom/ts4.0/index.d.ts", "../../../../node_modules/@types/jsdom/index.d.ts", "../../../../node_modules/@types/minimatch/index.d.ts", "../../../../node_modules/@types/minimist/index.d.ts", "../../../../node_modules/@types/mocha/index.d.ts", "../../../../node_modules/@types/normalize-package-data/index.d.ts", "../../../../node_modules/@types/parse-json/index.d.ts", "../../../../node_modules/@types/resolve/index.d.ts", "../../../../node_modules/@types/sinonjs__fake-timers/index.d.ts", "../../../../node_modules/@types/sinon/index.d.ts", "../../../../node_modules/@types/sinon-chai/index.d.ts", "../../../../node_modules/@types/yauzl/index.d.ts"], "fileInfos": [{"version": "3ac1b83264055b28c0165688fda6dfcc39001e9e7828f649299101c23ad0a0c3", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "e6b724280c694a9f588847f754198fb96c43d805f065c3a5b28bbc9594541c84", "e21c071ca3e1b4a815d5f04a7475adcaeea5d64367e840dd0154096d705c3940", {"version": "72704b10d97777e15f1a581b73f88273037ef752d2e50b72287bd0a90af64fe6", "affectsGlobalScope": true}, {"version": "dbb73d4d99be496175cb432c74c2615f78c76f4272f1d83cba11ee0ed6dbddf0", "affectsGlobalScope": true}, {"version": "7fac8cb5fc820bc2a59ae11ef1c5b38d3832c6d0dfaec5acdb5569137d09a481", "affectsGlobalScope": true}, {"version": "097a57355ded99c68e6df1b738990448e0bf170e606707df5a7c0481ff2427cd", "affectsGlobalScope": true}, {"version": "d8996609230d17e90484a2dd58f22668f9a05a3bfe00bfb1d6271171e54a31fb", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "0d5f52b3174bee6edb81260ebcd792692c32c81fd55499d69531496f3f2b25e7", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "62d80405c46c3f4c527ee657ae9d43fda65a0bf582292429aea1e69144a522a6", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "1b3fe904465430e030c93239a348f05e1be80640d91f2f004c3512c2c2c89f34", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "5075b36ab861c8c0c45377cb8c96270d7c65f0eeaf105d53fac6850da61f1027", "affectsGlobalScope": true}, {"version": "10bbdc1981b8d9310ee75bfac28ee0477bb2353e8529da8cff7cb26c409cb5e8", "affectsGlobalScope": true}, "d2f31f19e1ba6ed59be9259d660a239d9a3fcbbc8e038c6b2009bde34b175fed", "0cba3a5d7b81356222594442753cf90dd2892e5ccfe1d262aaca6896ba6c1380", "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", {"version": "77f0b5c6a193a699c9f7d7fb0578e64e562d271afa740783665d2a827104a873", "affectsGlobalScope": true}, "e5979905796fe2740d85fbaf4f11f42b7ee1851421afe750823220813421b1af", {"version": "fcdcb42da18dd98dc286b1876dd425791772036012ae61263c011a76b13a190f", "affectsGlobalScope": true}, "1dab5ab6bcf11de47ab9db295df8c4f1d92ffa750e8f095e88c71ce4c3299628", "f71f46ccd5a90566f0a37b25b23bc4684381ab2180bdf6733f4e6624474e1894", {"version": "54e65985a3ee3cec182e6a555e20974ea936fc8b8d1738c14e8ed8a42bd921d4", "affectsGlobalScope": true}, "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "5b30f550565fd0a7524282c81c27fe8534099e2cd26170ca80852308f07ae68d", "34e5de87d983bc6aefef8b17658556e3157003e8d9555d3cb098c6bef0b5fbc8", "787e96225b2a4c37625e3bdaa7b3e3b1fa0a24f067a494ac0e6aaa23be4752e4", "f27371653aded82b2b160f7a7033fb4a5b1534b6f6081ef7be1468f0f15327d3", "c762cd6754b13a461c54b59d0ae0ab7aeef3c292c6cf889873f786ee4d8e75c9", "f4ea7d5df644785bd9fbf419930cbaec118f0d8b4160037d2339b8e23c059e79", {"version": "bfea28e6162ed21a0aeed181b623dcf250aa79abf49e24a6b7e012655af36d81", "affectsGlobalScope": true}, "b8aca9d0c81abb02bec9b7621983ae65bde71da6727580070602bd2500a9ce2a", "ae97e20f2e10dbeec193d6a2f9cd9a367a1e293e7d6b33b68bacea166afd7792", "10d4796a130577d57003a77b95d8723530bbec84718e364aa2129fa8ffba0378", "063f53ff674228c190efa19dd9448bcbd540acdbb48a928f4cf3a1b9f9478e43", "bf73c576885408d4a176f44a9035d798827cc5020d58284cb18d7573430d9022", "7ae078ca42a670445ae0c6a97c029cb83d143d62abd1730efb33f68f0b2c0e82", {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true}, "287b21dc1d1b9701c92e15e7dd673dfe6044b15812956377adffb6f08825b1bc", "12eea70b5e11e924bb0543aea5eadc16ced318aa26001b453b0d561c2fd0bd1e", "08777cd9318d294646b121838574e1dd7acbb22c21a03df84e1f2c87b1ad47f2", "08a90bcdc717df3d50a2ce178d966a8c353fd23e5c392fd3594a6e39d9bb6304", {"version": "4cd4cff679c9b3d9239fd7bf70293ca4594583767526916af8e5d5a47d0219c7", "affectsGlobalScope": true}, "2a12d2da5ac4c4979401a3f6eaafa874747a37c365e4bc18aa2b171ae134d21b", "002b837927b53f3714308ecd96f72ee8a053b8aeb28213d8ec6de23ed1608b66", "1dc9c847473bb47279e398b22c740c83ea37a5c88bf66629666e3cf4c5b9f99c", "a9e4a5a24bf2c44de4c98274975a1a705a0abbaad04df3557c2d3cd8b1727949", "00fa7ce8bc8acc560dc341bbfdf37840a8c59e6a67c9bfa3fa5f36254df35db2", "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", {"version": "806ef4cac3b3d9fa4a48d849c8e084d7c72fcd7b16d76e06049a9ed742ff79c0", "affectsGlobalScope": true}, "44b8b584a338b190a59f4f6929d072431950c7bd92ec2694821c11bce180c8a5", "5f0ed51db151c2cdc4fa3bb0f44ce6066912ad001b607a34e65a96c52eb76248", {"version": "3345c276cab0e76dda86c0fb79104ff915a4580ba0f3e440870e183b1baec476", "affectsGlobalScope": true}, "664d8f2d59164f2e08c543981453893bc7e003e4dfd29651ce09db13e9457980", "103d70bfbeb3cd3a3f26d1705bf986322d8738c2c143f38ebb743b1e228d7444", "f52fbf64c7e480271a9096763c4882d356b05cab05bf56a64e68a95313cd2ce2", "59bdb65f28d7ce52ccfc906e9aaf422f8b8534b2d21c32a27d7819be5ad81df7", {"version": "3a2da34079a2567161c1359316a32e712404b56566c45332ac9dcee015ecce9f", "affectsGlobalScope": true}, "28a2e7383fd898c386ffdcacedf0ec0845e5d1a86b5a43f25b86bc315f556b79", "3aff9c8c36192e46a84afe7b926136d520487155154ab9ba982a8b544ea8fc95", "a880cf8d85af2e4189c709b0fea613741649c0e40fffb4360ec70762563d5de0", "85bbf436a15bbeda4db888be3062d47f99c66fd05d7c50f0f6473a9151b6a070", "9f9c49c95ecd25e0cb2587751925976cf64fd184714cb11e213749c80cf0f927", "f0c75c08a71f9212c93a719a25fb0320d53f2e50ca89a812640e08f8ad8c408c", {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true}, "9cafe917bf667f1027b2bb62e2de454ecd2119c80873ad76fc41d941089753b8", "e1718076b9790914c5ee2f44f90161a533ac35ef54feaddc1017b6bd052f4e06", "13a7b0847883cadf7bd6d8df067bd6b6f8e55daa53b7df480048c90bb603e2a1", "e576067e49c77196b341714bb33b03909c3d2f84f48a7a33107c2e749fcf5633", "863cbb90fdbdd1d4d46722580a9648a44732bbbca2ca36655f0951a872154ccc", "4ed6832518a6e057aca6c6861a7d86f432064a49b1cb6c960e472bcc2404e82a", "45c1b68819be5f90018e54b257c0fff392fa02224db1622d9eecd31649ffade7", "899c62c52e9f287a86c1c4dd1281495fd80c652ccc578d93b976fa6c1efa1941", "ef35ce8b7e5f2a3b1fea423bec4d4549ca26303572686bde88bd93f820f582f9", "5de310f85a2c8f027298730a918cdd1806092870d86e082720cbc8c23fb5bad3", "5fe81e388cf6260f480360b654ddf498898f4e977f1dfeb79c802151e98afcbf", "cff1fb9b7ab37e13ef21f6c1ca5c34dc1ba2ee18ea3bba66a3e8b7f0540199fb", "02bc1b02dd7f6bc0549cd9b8a5daffe06cdfc91c89c70bfd55e92707cf7a4ce9", "1d4812918af588deab4fd16cc854010f40ff759dec3b100eb3e420e2534e6f09", "f8c61688b7fd59d00fd3338a9b30ae8b8a0a138b5a72b924018c532be67786cf", "e06254cfb877079862b3a9baf61201e0582f38a64d33dfd1af799b43b61320a5", "46cb2fd15647a989b19403a74a3729c223be2480417baf0901f0fe02aeec1cfa", "13dad93793443055ab4d5f12feedf9053c6c948346821cd09c8f80b47340880a", "48ef5e0999da19656490f41810e83f27bd56fab07cdcb229501028aabb233a12", "70ab51558622d6855d91abd0f4c069782cb441a5995e6e21b4bdc3f0a0c19991", "db23f4b1c76552e245d49edde3f6460c659b751ae86ea2cae3dff4a117481b13", "c728cdb667b1d17d31bf3a97e17962aca3d6d1ee185954564ee3e2537e8ea96b", "f0fcd3798b8ee9cbefb491ebc5e2672ed59208fc3024f31f2b41eed7173db3c9", "da23f423f1450cd00bcee05601391bef7c7e52b5f9711c369a61244623359ca7", "59d1659c06f1c337a4ddc6dc25de0eaa58c9cbe73054dfbda8605a7b4b47d5b2", "5dd2fc36e6c3b7c5861584791b58f49b5e33c7e80d9d785bdbe8c4f943fd16d3", "b2acff88950d7d874f31dd760e1332b8028a7ffba08866c02c1cf5ac093842d1", {"version": "be7abf1df570aea13a80f9e26c48e4ec51ee5b5c807326fc730eadba8a118905", "affectsGlobalScope": true}, {"version": "3e0f52d6edf0329a6a82e93d45ef4fa616154280fd4d3f17ac380080f918a6bb", "affectsGlobalScope": true}, {"version": "b63a86ef33f79196f0af1ddfbefbf2ec6860daa4bd34bb8f6cdf0adc69c2fb1c", "affectsGlobalScope": true}, {"version": "b61b844b8d784ccf5131fe9780ce8ada9a5ae2f89919e4ac241dbca817dfd980", "affectsGlobalScope": true}, {"version": "7c5ede7b2331f1fad0e8d567308a550c6b5aca9bd3ef38f350f330be362d3cb0", "affectsGlobalScope": true}, {"version": "3bad983c1fd88629129725167d42669c27fe54babd399a800d7172104e48b3ca", "affectsGlobalScope": true}, "b170763d89ab7c7ab78762e243fc6f52fb956709765fcd8e60e1eedc8b83852f", "7e4d1cfdfc467a5e102cddb6f2405652634a724fed8a9a0e9490ffdac049ad4c", "ac8931aed03509a6c56b63671caf1f3930c1adbadec938ac8b4b88d665912b42", "bd4dc7f70eb575d54b20ef4d421ab215608b9f6654af9caf4cc72633f31045e7", "0e193deac3a15fbdb0ca372217ff4409c724828b506b9f4b57fb6603880e57af", "43a3a51fc94f56bf10fbe6845f05b39f656870bfe0a919a8fead8bd81540e2b3", "5974a50710f11c42fe561f820c0701bac0e409017bd7723c0ec77bfddfed600c", "81d5ff6b76890a63b168e3d011f092260bb7e87ce9cfd89e93c4019285b5ba27", "2480e06a03085cba1df5264d304520d286e67fb9a3b62c4b99f43f1d4e3f6947", "975461c8ac2df6b413c84104b2cf248949923d590013357b92454606fb09e4f2", "2a7eaa9da6f621825109518d02c1e3c0b7e02d2e66acb1d4fd33b31ca64cd049", "91de30867691dc876d3646c32ad3b762a379fb677cdec65bc6c2d9619ce6e590", "64917be14f753840a7a2739907ef072458168752ac0278e957e8422c7f1a732a", "7754aedaaab62bc51d2e42a3efe2e7294e4ac072e59f0b504a84971f5ca6e2be", "ff46d390cc6f5dc2963ff2c0c6352defb1046f6a44293d58e314ef7febca724e", "82169f198ffdfc787fba368ccfad2b2d8ef3712f3c696df94ac13f6884bbbe2d", "cab425b5559edac18327eb2c3c0f47e7e9f71b667290b7689faafd28aac69eae", "3cfb0cb51cc2c2e1b313d7c4df04dbf7e5bda0a133c6b309bf6af77cf614b971", "f992cd6cc0bcbaa4e6c810468c90f2d8595f8c6c3cf050c806397d3de8585562", {"version": "3a15910b7f45dfc393f010ee8f913580b08d65752800fc48147ea13445acd5f7", "affectsGlobalScope": true}, {"version": "63e2182615c513e89bb8a3e749d08f7c379e86490fcdbf6d35f2c14b3507a6e8", "affectsGlobalScope": true}, "629766229f541d92210f30a92b6038568ec165fab14b7ee53bdf13667da37ca3", "29193c018378ca9c8033eaa974c02c1f503e8fcd8a2bf406057c53f7d3fa17a8", "7d7c8ef7d48a035142c07dae60545ecc0e4af4c337742760cb09726f2f8e31db", "fc37aca06f6b8b296c42412a2e75ab53d30cd1fa8a340a3bb328a723fd678377", "201f92d97704e7e17e1c6e208fea976d75ff7b04235b79057891a33c7330bd77", "660fa40695c7ca19a59fd09d31d495d952eee946e445a2c455ec63f255ec3050", "fd20dfa2434a61a87e3fa9450f9de2ed2c365ea43b17b34ac6616d90d9681381", "389303117a81e90897689e7adb4b53a062e68a6fe4067088fae9552907aa28c3", {"version": "d4c4fe14b23180acf25e4a68dc3bb9e5c38233dd3de12a4ab9569e636090ac9b", "affectsGlobalScope": true}, "8841e2aa774b89bd23302dede20663306dc1b9902431ac64b24be8b8d0e3f649", "209e814e8e71aec74f69686a9506dd7610b97ab59dcee9446266446f72a76d05", {"version": "3f6d6465811321abc30a1e5f667feed63e5b3917b3d6c8d6645daf96c75f97ba", "affectsGlobalScope": true}, "6fa0008bf91a4cc9c8963bace4bba0bd6865cbfa29c3e3ccc461155660fb113a", "2b8264b2fefd7367e0f20e2c04eed5d3038831fe00f5efbc110ff0131aab899b", "8a19491eba2108d5c333c249699f40aff05ad312c04a17504573b27d91f0aede", "550650516d34048712520ffb1fce4a02f2d837761ee45c7d9868a7a35e7b0343", "24a09dc3e07d69b0a224dbd14b2994c40d1efd0954e759004b61b9e683dabe5f", {"version": "0fd3b5704bf037608646df5aa053fd06819ff69302ff6ada9736c300f79df852", "affectsGlobalScope": true}, "b2d70a269840a9528db473ac7565442434333a05c1f66801a7a672e82beb903e"], "options": {"allowUnreachableCode": false, "allowUnusedLabels": false, "composite": false, "declaration": false, "declarationDir": "../..", "declarationMap": false, "exactOptionalPropertyTypes": true, "module": 5, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noUncheckedIndexedAccess": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./", "sourceMap": true, "strict": true, "strictNullChecks": true, "strictPropertyInitialization": true, "target": 4, "useUnknownInCatchVariables": true}, "fileIdsList": [[87, 154, 155, 156], [59, 62, 86, 87, 94, 142, 143, 144, 154, 155, 156], [87, 146, 154, 155, 156], [87, 120, 154, 155, 156], [87, 121, 122, 124, 154, 155, 156], [87, 123, 154, 155, 156], [59, 87, 89, 94, 152, 153, 155, 156], [87, 154, 155], [87, 154, 156], [59, 87, 94, 154, 155, 156], [44, 87, 154, 155, 156], [47, 87, 154, 155, 156], [48, 53, 87, 154, 155, 156], [49, 59, 60, 67, 76, 86, 87, 154, 155, 156], [49, 50, 59, 67, 87, 154, 155, 156], [51, 87, 154, 155, 156], [52, 53, 60, 68, 87, 154, 155, 156], [53, 76, 83, 87, 154, 155, 156], [54, 56, 59, 67, 87, 154, 155, 156], [55, 87, 154, 155, 156], [56, 57, 87, 154, 155, 156], [58, 59, 87, 154, 155, 156], [59, 87, 154, 155, 156], [59, 60, 61, 76, 86, 87, 154, 155, 156], [59, 60, 61, 76, 87, 154, 155, 156], [87, 91, 154, 155, 156], [62, 67, 76, 86, 87, 154, 155, 156], [59, 60, 62, 63, 67, 76, 83, 86, 87, 154, 155, 156], [62, 64, 76, 83, 86, 87, 154, 155, 156], [44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 154, 155, 156], [59, 65, 87, 154, 155, 156], [66, 86, 87, 154, 155, 156], [56, 59, 67, 76, 87, 154, 155, 156], [68, 87, 154, 155, 156], [69, 87, 154, 155, 156], [47, 70, 87, 154, 155, 156], [71, 85, 87, 91, 154, 155, 156], [72, 87, 154, 155, 156], [73, 87, 154, 155, 156], [59, 74, 87, 154, 155, 156], [74, 75, 87, 89, 154, 155, 156], [59, 76, 77, 78, 87, 154, 155, 156], [76, 78, 87, 154, 155, 156], [76, 77, 87, 154, 155, 156], [79, 87, 154, 155, 156], [80, 87, 154, 155, 156], [59, 81, 82, 87, 154, 155, 156], [81, 82, 87, 154, 155, 156], [53, 67, 76, 83, 87, 154, 155, 156], [84, 87, 154, 155, 156], [67, 85, 87, 154, 155, 156], [48, 62, 73, 86, 87, 154, 155, 156], [53, 87, 154, 155, 156], [76, 87, 88, 154, 155, 156], [87, 89, 154, 155, 156], [87, 90, 154, 155, 156], [48, 53, 59, 61, 70, 76, 86, 87, 89, 91, 154, 155, 156], [76, 87, 92, 154, 155, 156], [87, 151, 154, 155, 156], [87, 152, 154, 155, 156], [87, 94, 154, 155, 156], [62, 76, 87, 94, 154, 155, 156], [87, 146, 154, 155, 156, 164], [87, 154, 155, 156, 163], [59, 76, 87, 94, 154, 155, 156], [87, 95, 154, 155, 156], [49, 76, 87, 94, 95, 96, 154, 155, 156], [87, 98, 99, 100, 101, 154, 155, 156], [87, 98, 99, 154, 155, 156], [87, 98, 154, 155, 156], [87, 102, 154, 155, 156], [87, 108, 154, 155, 156], [87, 104, 105, 106, 107, 154, 155, 156], [87, 104, 154, 155, 156], [87, 97, 103, 109, 139, 154, 155, 156], [87, 113, 115, 116, 119, 127, 128, 129, 130, 131, 135, 137, 138, 154, 155, 156], [87, 110, 154, 155, 156], [87, 112, 154, 155, 156], [87, 111, 154, 155, 156], [87, 109, 112, 113, 119, 131, 132, 154, 155, 156], [87, 112, 113, 118, 154, 155, 156], [87, 112, 113, 127, 128, 131, 132, 154, 155, 156], [87, 109, 113, 114, 115, 116, 119, 127, 128, 129, 130, 133, 134, 154, 155, 156], [87, 108, 112, 116, 117, 118, 154, 155, 156], [87, 112, 117, 118, 127, 154, 155, 156], [87, 113, 119, 128, 154, 155, 156], [87, 125, 126, 154, 155, 156], [87, 136, 154, 155, 156]], "referencedMap": [[110, 1], [141, 1], [145, 2], [147, 3], [146, 1], [122, 1], [121, 4], [125, 5], [148, 1], [149, 1], [150, 1], [124, 6], [123, 1], [126, 1], [120, 1], [143, 1], [154, 7], [156, 8], [155, 9], [142, 10], [157, 1], [158, 1], [159, 1], [44, 11], [45, 11], [47, 12], [48, 13], [49, 14], [50, 15], [51, 16], [52, 17], [53, 18], [54, 19], [55, 20], [56, 21], [57, 21], [58, 22], [59, 23], [60, 24], [61, 25], [46, 26], [93, 1], [62, 27], [63, 28], [64, 29], [94, 30], [65, 31], [66, 32], [67, 33], [68, 34], [69, 35], [70, 36], [71, 37], [72, 38], [73, 39], [74, 40], [75, 41], [76, 42], [78, 43], [77, 44], [79, 45], [80, 46], [81, 47], [82, 48], [83, 49], [84, 50], [85, 51], [86, 52], [87, 53], [88, 54], [89, 55], [90, 56], [91, 57], [92, 58], [160, 1], [161, 1], [152, 59], [151, 60], [162, 61], [144, 62], [165, 63], [164, 64], [163, 1], [153, 1], [166, 65], [96, 66], [95, 1], [97, 67], [102, 68], [100, 69], [101, 70], [98, 1], [99, 1], [103, 71], [8, 1], [9, 1], [13, 1], [12, 1], [2, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [21, 1], [3, 1], [4, 1], [43, 1], [25, 1], [22, 1], [23, 1], [24, 1], [26, 1], [27, 1], [28, 1], [5, 1], [29, 1], [30, 1], [31, 1], [32, 1], [6, 1], [33, 1], [34, 1], [35, 1], [36, 1], [7, 1], [41, 1], [37, 1], [38, 1], [39, 1], [40, 1], [1, 1], [42, 1], [11, 1], [10, 1], [109, 72], [108, 73], [106, 74], [107, 1], [105, 74], [104, 1], [140, 75], [139, 76], [111, 77], [113, 78], [112, 79], [138, 1], [133, 80], [132, 81], [134, 82], [117, 78], [135, 83], [131, 81], [114, 1], [115, 1], [119, 84], [118, 78], [128, 85], [116, 1], [129, 86], [127, 87], [130, 78], [136, 1], [137, 88]], "exportedModulesMap": [[110, 1], [141, 1], [145, 2], [147, 3], [146, 1], [122, 1], [121, 4], [125, 5], [148, 1], [149, 1], [150, 1], [124, 6], [123, 1], [126, 1], [120, 1], [143, 1], [154, 7], [156, 8], [155, 9], [142, 10], [157, 1], [158, 1], [159, 1], [44, 11], [45, 11], [47, 12], [48, 13], [49, 14], [50, 15], [51, 16], [52, 17], [53, 18], [54, 19], [55, 20], [56, 21], [57, 21], [58, 22], [59, 23], [60, 24], [61, 25], [46, 26], [93, 1], [62, 27], [63, 28], [64, 29], [94, 30], [65, 31], [66, 32], [67, 33], [68, 34], [69, 35], [70, 36], [71, 37], [72, 38], [73, 39], [74, 40], [75, 41], [76, 42], [78, 43], [77, 44], [79, 45], [80, 46], [81, 47], [82, 48], [83, 49], [84, 50], [85, 51], [86, 52], [87, 53], [88, 54], [89, 55], [90, 56], [91, 57], [92, 58], [160, 1], [161, 1], [152, 59], [151, 60], [162, 61], [144, 62], [165, 63], [164, 64], [163, 1], [153, 1], [166, 65], [96, 66], [95, 1], [97, 67], [102, 68], [100, 69], [101, 70], [98, 1], [99, 1], [103, 71], [8, 1], [9, 1], [13, 1], [12, 1], [2, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [21, 1], [3, 1], [4, 1], [43, 1], [25, 1], [22, 1], [23, 1], [24, 1], [26, 1], [27, 1], [28, 1], [5, 1], [29, 1], [30, 1], [31, 1], [32, 1], [6, 1], [33, 1], [34, 1], [35, 1], [36, 1], [7, 1], [41, 1], [37, 1], [38, 1], [39, 1], [40, 1], [1, 1], [42, 1], [11, 1], [10, 1], [109, 72], [108, 73], [106, 74], [107, 1], [105, 74], [104, 1], [140, 75], [139, 76], [111, 77], [113, 78], [112, 79], [138, 1], [133, 80], [132, 81], [134, 82], [117, 78], [135, 83], [131, 81], [114, 1], [115, 1], [119, 84], [118, 78], [128, 85], [116, 1], [129, 86], [127, 87], [130, 78], [136, 1], [137, 88]], "semanticDiagnosticsPerFile": [110, 141, 145, 147, 146, 122, 121, 125, 148, 149, 150, 124, 123, 126, 120, 143, 154, 156, 155, 142, 157, 158, 159, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 46, 93, 62, 63, 64, 94, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78, 77, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 160, 161, 152, 151, 162, 144, 165, 164, 163, 153, 166, 96, 95, 97, 102, 100, 101, 98, 99, 103, 8, 9, 13, 12, 2, 14, 15, 16, 17, 18, 19, 20, 21, 3, 4, 43, 25, 22, 23, 24, 26, 27, 28, 5, 29, 30, 31, 32, 6, 33, 34, 35, 36, 7, 41, 37, 38, 39, 40, 1, 42, 11, 10, 109, 108, 106, 107, 105, 104, 140, 139, 111, 113, 112, 138, 133, 132, 134, 117, 135, 131, 114, 115, 119, 118, 128, 116, 129, 127, 130, 136, 137]}, "version": "4.6.4"}