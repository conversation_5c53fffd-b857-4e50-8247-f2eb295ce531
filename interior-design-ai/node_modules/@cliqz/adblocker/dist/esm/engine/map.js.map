{"version": 3, "file": "map.js", "sourceRoot": "", "sources": ["../../../src/engine/map.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAClF,OAAO,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AAE9C,MAAM,YAAY,GAAW,MAAM,CAAC,gBAAgB,KAAK,CAAC,CAAC;AAE3D;;;;;GAKG;AACH,MAAM,OAAO,UAAU;IACd,MAAM,CAAC,WAAW,CACvB,MAAsB,EACtB,WAAwC;QAExC,MAAM,qBAAqB,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QACjD,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAE1C,wEAAwE;QACxE,oEAAoE;QACpE,mEAAmE;QACnE,sEAAsE;QACtE,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAC5E,iBAAiB,EAAE,KAAK;SACzB,CAAC,CAAC;QACH,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QACzE,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC;QAClC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,kFAAkF;QAEnG,OAAO,IAAI,UAAU,CAAC;YACpB,WAAW;YACX,sEAAsE;YACtE,+CAA+C;YAC/C,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE;YACjB,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1B,SAAS,EAAE,GAAG,EAAE;gBACd,WAAW;YACb,CAAC;SACF,CAAC,CAAC,eAAe,CAAC;YACjB,YAAY;YACZ,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAgBD,YAAY,EACV,SAAS,EACT,WAAW,EACX,OAAO,EACP,iBAAiB,EACjB,MAAM,GAOP;QA1BD,wEAAwE;QACxE,sEAAsE;QACtE,oEAAoE;QACpE,sDAAsD;QACrC,UAAK,GAAqB,IAAI,GAAG,EAAE,CAAC;QAE7C,iBAAY,GAAgB,kBAAkB,CAAC;QAC/C,sBAAiB,GAAgB,kBAAkB,CAAC;QACpD,qBAAgB,GAAW,CAAC,CAAC;QAC7B,mBAAc,GAAW,CAAC,CAAC;QAkBjC,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;QAEpC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,YAAY,GAAe,EAAE,CAAC;YAEpC,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAEzB,wEAAwE;YACxE,yEAAyE;YACzE,IAAI,mBAAmB,GAAG,CAAC,CAAC;YAC5B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,mBAAmB,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC;YAED,mDAAmD;YACnD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,CAAC;oBACnB,YAAY,EAAE,kBAAkB;oBAChC,gBAAgB,EAAE,CAAC;oBACnB,cAAc,EAAE,CAAC;oBACjB,iBAAiB,EAAE,kBAAkB;oBACrC,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;iBACzD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,qEAAqE;gBACrE,uEAAuE;gBACvE,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxB,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,oBAAoB;YAC3D,CAAC;YAED,gFAAgF;YAChF,mBAAmB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YAE5C,qFAAqF;YACrF,MAAM,qBAAqB,GAAW,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC3E,MAAM,IAAI,GAAW,qBAAqB,GAAG,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAyB,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpB,CAAC;YAED,qFAAqF;YACrF,mBAAmB,IAAI,qBAAqB,GAAG,CAAC,CAAC;YAEjD,mEAAmE;YACnE,iEAAiE;YACjE,0EAA0E;YAC1E,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,mBAAmB,EAAE,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1F,MAAM,iBAAiB,GAAG,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;YAC3E,MAAM,YAAY,GAAG,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;YACjE,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAEzC,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,4BAA4B;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,MAAM,KAAK,GAAM,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,IAAI,GAAa,YAAY,CAAC,CAAC,CAAC,CAAC;gBAEvC,sEAAsE;gBACtE,6DAA6D;gBAC7D,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC;gBAC9B,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAEzB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,iEAAiE;oBACjE,8DAA8D;oBAC9D,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAED,4EAA4E;YAC5E,IAAI,mBAAmB,GAAG,CAAC,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClD,MAAM,aAAa,GAAuB,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtD,iBAAiB,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC;gBAC3C,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,aAAa,EAAE,CAAC;oBAChD,YAAY,CAAC,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC;oBAC5C,YAAY,CAAC,mBAAmB,EAAE,CAAC,GAAG,UAAU,CAAC;gBACnD,CAAC;YACH,CAAC;YAED,mBAAmB;YACnB,IAAI,CAAC,eAAe,CAAC;gBACnB,YAAY;gBACZ,gBAAgB;gBAChB,cAAc,EAAE,YAAY,CAAC,MAAM;gBACnC,iBAAiB;gBACjB,IAAI,EAAE,MAAM;aACb,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,EACtB,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,IAAI,GAOL;QACC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,SAAS;QACd,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAErB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,qGAAqG;QACrG,OAAO,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAsB;QACrC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvC,0EAA0E;QAC1E,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACvD,CAAC;IAEM,GAAG,CAAC,GAAW;QACpB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAErD,2CAA2C;QAC3C,IAAI,aAAa,KAAK,YAAY,EAAE,CAAC;YACnC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,gEAAgE;QAChE,uEAAuE;QACvE,sEAAsE;QACtE,kBAAkB;QAClB,MAAM,WAAW,GACf,MAAM,KAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC;YAC1C,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM;YAC1B,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEzC,sDAAsD;QACtD,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACpD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,YAAY,KAAK,GAAG,EAAE,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,EAAE,CAAC,CAAC,+BAA+B;QAC5C,CAAC;QAED,sEAAsE;QACtE,6DAA6D;QAC7D,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC;CACF"}