{"version": 3, "file": "organizations.js", "sourceRoot": "", "sources": ["../../../../src/engine/metadata/organizations.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACvC,OAAO,EAAkB,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChE,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAa1C;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,YAAiB;IACvC,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,WAAW,EACX,OAAO,EACP,WAAW,EAAE,UAAU,EACvB,kBAAkB,EAAE,gBAAgB,EACpC,eAAe,EAAE,cAAc,EAC/B,WAAW,EAAE,UAAU,GACxB,GAAG,YAAY,CAAC;IAEjB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,WAAW,KAAK,IAAI,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QACpD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,UAAU,KAAK,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,gBAAgB,KAAK,IAAI,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;QACtE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,cAAc,KAAK,IAAI,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,UAAU,KAAK,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,YAA6B;IAClD,OAAO,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,YAA2B;IAC3D,OAAO,CACL,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC;QAC5B,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC;QAC7B,UAAU,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC;QAC1C,UAAU,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC;QAC1C,UAAU,CAAC,YAAY,CAAC,OAAO,IAAI,EAAE,CAAC;QACtC,UAAU,CAAC,YAAY,CAAC,kBAAkB,IAAI,EAAE,CAAC;QACjD,UAAU,CAAC,YAAY,CAAC,eAAe,IAAI,EAAE,CAAC;QAC9C,UAAU,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC,CAC3C,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,YAA2B,EAAE,IAAoB;IACzE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IAC1C,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;IACrD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;AAChD,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,IAAoB;IAC9C,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;QACnB,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;QACpB,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;QACnC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;QACnC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;QAC/B,kBAAkB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;QAC1C,eAAe,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;QACvC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;KACpC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,aAA8B;IACtD,OAAO,IAAI,UAAU,CAAC;QACpB,iBAAiB;QACjB,OAAO,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACjD,SAAS;QACT,WAAW;QACX,MAAM,EAAE,aAAa;KACtB,CAAC,CAAC;AACL,CAAC"}