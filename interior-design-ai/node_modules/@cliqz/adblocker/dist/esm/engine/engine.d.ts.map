{"version": 3, "file": "engine.d.ts", "sourceRoot": "", "sources": ["../../../src/engine/engine.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAEvE,OAAO,MAAM,MAAM,cAAc,CAAC;AAElC,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,EAGL,KAAK,EAIN,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,cAAc,MAAM,wBAAwB,CAAC;AACpD,OAAO,aAAa,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAgB,MAAM,aAAa,CAAC;AACnF,OAAO,OAAO,MAAM,eAAe,CAAC;AACpC,OAAO,SAAS,MAAM,iBAAiB,CAAC;AACxC,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AACxD,OAAO,mBAAmB,MAAM,qBAAqB,CAAC;AACtD,OAAO,UAAU,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,MAAM,eAAe,CAAC;AAC/D,OAAO,YAAY,EAAE,EAAE,GAAG,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,kBAAkB,MAAM,0BAA0B,CAAC;AAM1D,eAAO,MAAM,cAAc,MAAM,CAAC;AAiClC,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,OAAO,CAAC;IACf,QAAQ,EACJ,SAAS,GACT;QACE,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACN,SAAS,EAAE,aAAa,GAAG,SAAS,CAAC;IACrC,MAAM,EAAE,aAAa,GAAG,SAAS,CAAC;IAClC,QAAQ,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC;CAC9C;AAED,MAAM,WAAW,OAAO;IACtB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC;IAC5C,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;CAC5D;AAED,KAAK,4BAA4B,GAAG;IAClC,OAAO,EAAE,OAAO,CAAC;IACjB,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC;CAChC,CAAC;AAEF,KAAK,6BAA6B,GAC9B;IACE,GAAG,EAAE,MAAM,CAAC;IACZ,aAAa,EAAE,GAAG,CAAC;IACnB,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC;CACjC,GACD;IACE,OAAO,EAAE,OAAO,CAAC;IACjB,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC;CACjC,CAAC;AAEN,KAAK,uBAAuB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC;AAEpF,MAAM,MAAM,mBAAmB,GAAG;IAChC,iBAAiB,EAAE,uBAAuB,CAAC;IAC3C,iBAAiB,EAAE,uBAAuB,CAAC;IAC3C,oBAAoB,EAAE,uBAAuB,CAAC;IAC9C,qBAAqB,EAAE,uBAAuB,CAAC;IAC/C,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;IACzD,eAAe,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IACtE,iBAAiB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IACzD,gBAAgB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IACvD,gBAAgB,EAAE,CAChB,KAAK,EAAE;QACL,MAAM,CAAC,EAAE,cAAc,GAAG,aAAa,GAAG,SAAS,CAAC;QACpD,SAAS,CAAC,EAAE,cAAc,GAAG,aAAa,GAAG,SAAS,CAAC;KACxD,EACD,OAAO,EAAE,6BAA6B,GAAG,4BAA4B,KAClE,GAAG,CAAC;CACV,CAAC;AAEF,MAAM,CAAC,OAAO,OAAO,YAAa,SAAQ,YAAY,CAAC,mBAAmB,CAAC;IACzE,OAAO,CAAC,MAAM,CAAC,UAAU;WAeX,KAAK,CAAC,CAAC,SAAS,YAAY,EACxC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EAC/B,MAAM,GAAE,OAAO,CAAC,MAAM,CAAM,GAC3B,CAAC;IAIJ;;;;;;;OAOG;WACW,SAAS,CAAC,CAAC,SAAS,OAAO,YAAY,EACnD,IAAI,EAAE,CAAC,EACP,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,MAAM,EAAE,EACd,MAAM,GAAE,OAAO,CAAC,MAAM,CAAM,EAC5B,OAAO,CAAC,EAAE,OAAO,GAChB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAgB3B;;;;;;;OAOG;WACW,mBAAmB,CAAC,CAAC,SAAS,OAAO,YAAY,EAC7D,IAAI,EAAE,CAAC,EACP,SAAS,GAAE,KAAa,EACxB,OAAO,CAAC,EAAE,OAAO,GAChB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAI3B;;;OAGG;WACW,0BAA0B,CAAC,CAAC,SAAS,OAAO,YAAY,EACpE,IAAI,EAAE,CAAC,EACP,SAAS,GAAE,KAAa,EACxB,OAAO,CAAC,EAAE,OAAO,GAChB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAI3B;;;OAGG;WACW,gBAAgB,CAAC,CAAC,SAAS,OAAO,YAAY,EAC1D,IAAI,EAAE,CAAC,EACP,SAAS,GAAE,KAAa,EACxB,OAAO,CAAC,EAAE,OAAO,GAChB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;WAIb,aAAa,CAAC,CAAC,SAAS,OAAO,YAAY,EACvD,IAAI,EAAE,CAAC,EACP,WAAW,EAAE,GAAG,EAChB,OAAO,GAAE,OAAO,CAAC,MAAM,CAAM,GAC5B,YAAY,CAAC,CAAC,CAAC;IAelB;;;;;;;;;;;OAWG;WACW,KAAK,CAAC,CAAC,SAAS,OAAO,YAAY,EAC/C,IAAI,EAAE,CAAC,EACP,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,EAC1B,EACE,aAAqB,GACtB,GAAE;QACD,aAAa,CAAC,EAAE,OAAO,CAAC;KACpB,GACL,YAAY,CAAC,CAAC,CAAC;WAoHJ,KAAK,CAAC,CAAC,SAAS,YAAY,EACxC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EAC/B,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,OAAO,CAAC,MAAM,CAAM,GAC5B,CAAC;WAQU,WAAW,CAAC,CAAC,SAAS,YAAY,EAC9C,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EAC/B,UAAU,EAAE,UAAU,GACrB,CAAC;IA8EG,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAE3B,aAAa,EAAE,kBAAkB,CAAC;IAElC,GAAG,EAAE,mBAAmB,CAAC;IACzB,cAAc,EAAE,mBAAmB,CAAC;IACpC,UAAU,EAAE,mBAAmB,CAAC;IAChC,UAAU,EAAE,mBAAmB,CAAC;IAChC,SAAS,EAAE,mBAAmB,CAAC;IAC/B,OAAO,EAAE,mBAAmB,CAAC;IAC7B,SAAS,EAAE,oBAAoB,CAAC;IAChC,WAAW,EAAE,UAAU,CAAC;IAExB,QAAQ,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC/B,SAAS,EAAE,SAAS,CAAC;IAC5B,SAAgB,MAAM,EAAE,MAAM,CAAC;gBAEnB,EAEV,eAAoB,EACpB,cAAmB,EACnB,aAAkB,EAElB,MAAqB,EACrB,KAAiB,GAClB,GAAE;QACD,eAAe,CAAC,EAAE,cAAc,EAAE,CAAC;QACnC,cAAc,CAAC,EAAE,aAAa,EAAE,CAAC;QACjC,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;QAC/B,KAAK,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC5B,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;KACrB;IA0CN,OAAO,CAAC,gBAAgB;IAIjB,SAAS,CAAC,GAAG,EAAE,GAAG;IAIzB;;;;;;;;;;;OAWG;IACI,iBAAiB,IAAI,MAAM;IA6BlC;;;;OAIG;IACI,SAAS,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,UAAU;IAiDhD;;OAEG;IACI,WAAW,IAAI,MAAM,EAAE;IAIvB,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO;IAIvD;;OAEG;IACI,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO;IASxD,UAAU,IAAI;QAAE,cAAc,EAAE,aAAa,EAAE,CAAC;QAAC,eAAe,EAAE,cAAc,EAAE,CAAA;KAAE;IAyB3F;;OAEG;IACI,MAAM,CACX,EACE,iBAAsB,EACtB,kBAAuB,EACvB,gBAAqB,EACrB,sBAA2B,EAC3B,qBAA0B,EAC1B,oBAAyB,GAC1B,EAAE,OAAO,CAAC,SAAS,CAAC,EACrB,GAAG,GAAE,GAAe,GACnB,OAAO;IA8GH,cAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE,eAAe,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO;IA2E7F;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,YAAY,EAAE;IA4EvD;;;OAGG;IACI,mBAAmB,CAAC,EAEzB,GAAG,EACH,QAAQ,EACR,MAAM,EAGN,OAAO,EACP,KAAK,EACL,GAAG,EAGH,YAAmB,EACnB,iBAAwB,EACxB,gBAAuB,EACvB,eAAsB,EACtB,oBAA2B,EAE3B,WAAW,EACX,aAAa,GACd,EAAE;QACD,GAAG,EAAE,MAAM,CAAC;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;QAElC,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;QAC/B,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;QAC7B,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;QAE3B,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B,gBAAgB,CAAC,EAAE,OAAO,CAAC;QAC3B,eAAe,CAAC,EAAE,OAAO,CAAC;QAC1B,oBAAoB,CAAC,EAAE,OAAO,CAAC;QAE/B,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QACjC,aAAa,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC;KACjC,GAAG,sBAAsB;IAqK1B;;OAEG;IACI,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,GAAG,CAAC,aAAa,CAAC;IAiCrD;;;OAGG;IACI,gBAAgB,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,GAAG,SAAS;IAiE7D;;;OAGG;IACI,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,GAAE,OAAO,GAAG,SAAiB,GAAG,gBAAgB;IAsHpF,kBAAkB,CACvB,OAAO,EAAE,OAAO,EAChB,EAAE,iBAAyB,EAAE;;KAAK,GACjC,oBAAoB,EAAE;IA4BlB,YAAY;IAOZ,WAAW;IAOX,WAAW;IAOX,WAAW;IAOX,UAAU;IAOV,WAAW;CAMnB"}