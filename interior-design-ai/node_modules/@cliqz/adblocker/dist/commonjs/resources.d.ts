/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
import { StaticDataView } from './data-view.js';
export interface Resource {
    name: string;
    aliases: string[];
    body: string;
    contentType: string;
}
interface Scriptlet {
    name: string;
    aliases: string[];
    body: string;
    dependencies: string[];
    executionWorld?: 'MAIN' | 'ISOLATED';
    requiresTrust?: boolean;
}
/**
 * Abstraction on top of resources.txt used for redirections as well as script
 * injections. It contains logic to parse, serialize and get resources by name
 * for use in the engine.
 */
export default class Resources {
    static deserialize(buffer: StaticDataView): Resources;
    static parse(data: string, { checksum }: {
        checksum: string;
    }): Resources;
    static copy(sourceResources: Resources): Resources;
    readonly checksum: string;
    readonly scriptlets: Scriptlet[];
    readonly resources: Resource[];
    private readonly scriptletsByName;
    private readonly resourcesByName;
    private readonly scriptletsCache;
    constructor({ checksum, resources, scriptlets }?: Partial<Resources>);
    /**
     * In case of scriptlet or resource update, you need to clear the populated caches and mappings by calling this method.
     */
    updateAliases(): void;
    getResource(name: string): {
        body: string;
        contentType: string;
        dataUrl: string;
    };
    getScriptlet(name: string): string | undefined;
    private getScriptletDependencies;
    getSerializedSize(): number;
    serialize(buffer: StaticDataView): void;
}
export {};
//# sourceMappingURL=resources.d.ts.map