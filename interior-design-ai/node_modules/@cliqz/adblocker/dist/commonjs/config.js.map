{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAEH,iDAA4D;AAE5D,MAAqB,MAAM;IAClB,MAAM,CAAC,WAAW,CAAC,MAAsB;QAC9C,OAAO,IAAI,MAAM,CAAC;YAChB,KAAK,EAAE,MAAM,CAAC,OAAO,EAAE;YACvB,iBAAiB,EAAE,MAAM,CAAC,OAAO,EAAE;YACnC,mBAAmB,EAAE,MAAM,CAAC,OAAO,EAAE;YACrC,mBAAmB,EAAE,MAAM,CAAC,OAAO,EAAE;YACrC,sBAAsB,EAAE,MAAM,CAAC,OAAO,EAAE;YACxC,mBAAmB,EAAE,MAAM,CAAC,OAAO,EAAE;YACrC,sCAAsC,EAAE,MAAM,CAAC,OAAO,EAAE;YACxD,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE;YACzC,cAAc,EAAE,MAAM,CAAC,OAAO,EAAE;YAChC,cAAc,EAAE,MAAM,CAAC,OAAO,EAAE;YAChC,mBAAmB,EAAE,MAAM,CAAC,OAAO,EAAE;YACrC,oBAAoB,EAAE,MAAM,CAAC,OAAO,EAAE;YACtC,qBAAqB,EAAE,MAAM,CAAC,OAAO,EAAE;YACvC,2BAA2B,EAAE,MAAM,CAAC,OAAO,EAAE;YAC7C,kBAAkB,EAAE,MAAM,CAAC,OAAO,EAAE;YACpC,iBAAiB,EAAE,MAAM,CAAC,OAAO,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAmBD,YAAY,EACV,KAAK,GAAG,KAAK,EACb,iBAAiB,GAAG,KAAK,EACzB,mBAAmB,GAAG,KAAK,EAC3B,mBAAmB,GAAG,IAAI,EAC1B,sBAAsB,GAAG,IAAI,EAC7B,mBAAmB,GAAG,IAAI,EAC1B,sCAAsC,GAAG,IAAI,EAC7C,uBAAuB,GAAG,KAAK,EAC/B,cAAc,GAAG,IAAI,EACrB,cAAc,GAAG,IAAI,EACrB,mBAAmB,GAAG,IAAI,EAC1B,oBAAoB,GAAG,IAAI,EAC3B,qBAAqB,GAAG,KAAK,EAC7B,2BAA2B,GAAG,IAAI,EAClC,kBAAkB,GAAG,IAAI,EACzB,iBAAiB,GAAG,KAAK,MACN,EAAE;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QACrD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,sCAAsC,GAAG,sCAAsC,CAAC;QACrF,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QACvD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACnD,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;QAC/D,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAEM,iBAAiB;QACtB,uEAAuE;QACvE,iCAAiC;QACjC,OAAO,EAAE,GAAG,IAAA,yBAAU,GAAE,CAAC;IAC3B,CAAC;IAEM,SAAS,CAAC,MAAsB;QACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC7D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC1C,CAAC;CACF;AAnGD,yBAmGC"}