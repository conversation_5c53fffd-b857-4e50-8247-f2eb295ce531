{"version": 3, "file": "preprocessor.js", "sourceRoot": "", "sources": ["../../src/preprocessor.ts"], "names": [], "mappings": ";;;AAyCA,gDAuBC;AAhED,iDAA4D;AA8B5D,MAAa,GAAI,SAAQ,GAAqB;CAAG;AAAjD,kBAAiD;AAEjD,IAAkB,kBAKjB;AALD,WAAkB,kBAAkB;IAClC,iEAAW,CAAA;IACX,6DAAS,CAAA;IACT,2DAAQ,CAAA;IACR,6DAAS,CAAA;AACX,CAAC,EALiB,kBAAkB,kCAAlB,kBAAkB,QAKnC;AAID,SAAgB,kBAAkB,CAAC,IAAY;IAC7C,yFAAyF;IACzF,IACE,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS;QACnC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EACnC,CAAC;QACD,OAAO,kBAAkB,CAAC,OAAO,CAAC;IACpC,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC;IAClC,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9B,OAAO,kBAAkB,CAAC,IAAI,CAAC;IACjC,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC/B,OAAO,kBAAkB,CAAC,KAAK,CAAC;IAClC,CAAC;IAED,OAAO,kBAAkB,CAAC,OAAO,CAAC;AACpC,CAAC;AAED,MAAM,gBAAgB,GAAG,kCAAkC,CAAC;AAC5D,MAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAE5C,MAAM,QAAQ,GAAG,CAAC,UAAkB,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC5E,MAAM,YAAY,GAAG,CAAC,UAAkB,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAEhF,MAAM,UAAU,GAA2B;IACzC,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;CACR,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAE9F,MAAM,cAAc,GAAG,CAAC,UAAkB,EAAE,GAAQ,EAAW,EAAE;IAC/D,IAAI,UAAU,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,UAAU,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,mHAAmH;AACnH,4EAA4E;AAC5E,wEAAwE;AACxE,oDAAoD;AACpD,GAAG;AACH,kFAAkF;AAClF,GAAG;AACH,4EAA4E;AAC5E,uEAAuE;AACvE,6BAA6B;AACtB,MAAM,QAAQ,GAAG,CAAC,UAAkB,EAAE,GAAQ,EAAW,EAAE;IAChE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;QAC7B,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC1B,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,cAAc,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;IAEpC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,wCAAwC;IACxC,8DAA8D;IAC9D,+DAA+D;IAC/D,sCAAsC;IACtC,oEAAoE;IACpE,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;QAC7F,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,MAAM,GAAyB,EAAE,CAAC;IACxC,MAAM,KAAK,GAAyB,EAAE,CAAC;IAEvC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC7D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,CAAC;YAC5B,CAAC;YAED,2CAA2C;YAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,KAAK,CAAC,GAAG,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,OACE,KAAK,CAAC,MAAM;gBACZ,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;gBAC7C,UAAU,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC,EAClE,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,CAAC;YAC5B,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,qCAAqC;IACrC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,CAAC;IAC5B,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;YAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;YAE1B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACnB,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AAC3B,CAAC,CAAC;AAvFW,QAAA,QAAQ,YAuFnB;AAEF,MAAqB,YAAY;IACxB,MAAM,CAAC,YAAY,CAAC,IAAY;QACrC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,IAAY,EAAE,SAAyB;QACzD,OAAO,IAAI,IAAI,CAAC;YACd,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC;YAC1C,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,IAAoB;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAY,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,IAAI,CAAC;YACd,SAAS;YACT,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAKD,YAAY,EACV,SAAS,EACT,SAAS,GAAG,IAAI,GAAG,EAAE,GAItB;QACC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEM,QAAQ,CAAC,GAAQ;QACtB,OAAO,IAAA,gBAAQ,EAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IAEM,SAAS,CAAC,IAAoB;QACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAEM,iBAAiB;QACtB,IAAI,aAAa,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/C,aAAa,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE/C,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AA5DD,+BA4DC"}