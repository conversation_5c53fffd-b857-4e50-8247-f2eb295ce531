{"version": 3, "file": "html.js", "sourceRoot": "", "sources": ["../../../../src/engine/bucket/html.ts"], "names": [], "mappings": ";;;;;AASA,0EAAqD;AACrD,4EAAuD;AAEvD,kDAA6F;AAC7F,2EAA+C;AAC/C,+CAAmD;AAEnD,MAAqB,UAAU;IACtB,MAAM,CAAC,WAAW,CAAC,MAAsB,EAAE,MAAc;QAC9D,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAE1C,MAAM,CAAC,YAAY,GAAG,0BAAY,CAAC,WAAW,CAC5C,MAAM,EACN,oBAAa,CAAC,WAAW,EACzB,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,8BAAe,CAAC,CAAC,CAAC,kCAAmB,EAClE,MAAM,CACP,CAAC;QAEF,MAAM,CAAC,eAAe,GAAG,0BAAY,CAAC,WAAW,CAC/C,MAAM,EACN,oBAAa,CAAC,WAAW,EACzB,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,8BAAe,CAAC,CAAC,CAAC,kCAAmB,EAClE,MAAM,CACP,CAAC;QAEF,MAAM,CAAC,aAAa,GAAG,0BAAY,CAAC,WAAW,CAC7C,MAAM,EACN,qBAAc,CAAC,WAAW,EAC1B,mCAAoB,EACpB,MAAM,CACP,CAAC;QAEF,MAAM,CAAC,WAAW,GAAG,0BAAY,CAAC,WAAW,CAC3C,MAAM,EACN,qBAAc,CAAC,WAAW,EAC1B,mCAAoB,EACpB,MAAM,CACP,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAQD,YAAY,EACV,OAAO,GAAG,EAAE,EACZ,MAAM,GAIP;QACC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,YAAY,GAAG,IAAI,0BAAY,CAAC;YACnC,MAAM;YACN,WAAW,EAAE,oBAAa,CAAC,WAAW;YACtC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,8BAAe,CAAC,CAAC,CAAC,kCAAmB;SAC7E,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,0BAAY,CAAC;YACtC,MAAM;YACN,WAAW,EAAE,oBAAa,CAAC,WAAW;YACtC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,8BAAe,CAAC,CAAC,CAAC,kCAAmB;SAC7E,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,GAAG,IAAI,0BAAY,CAAC;YACpC,MAAM;YACN,WAAW,EAAE,qBAAc,CAAC,WAAW;YACvC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,mCAAoB;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,0BAAY,CAAC;YAClC,MAAM;YACN,WAAW,EAAE,qBAAc,CAAC,WAAW;YACvC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,mCAAoB;SAC/B,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,MAAM,CACX,UAA8C,EAC9C,cAAuC;QAEvC,MAAM,cAAc,GAAoB,EAAE,CAAC;QAC3C,MAAM,gBAAgB,GAAoB,EAAE,CAAC;QAC7C,MAAM,eAAe,GAAqB,EAAE,CAAC;QAC7C,MAAM,aAAa,GAAqB,EAAE,CAAC;QAE3C,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;YAChC,IAAI,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC;gBAC7B,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;oBACzB,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBACrC,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;oBACtB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACN,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IACzD,CAAC;IAEM,SAAS,CAAC,MAAsB;QACrC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,iBAAiB;QACtB,OAAO,CACL,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CACrC,CAAC;IACJ,CAAC;IAEM,cAAc,CACnB,OAAgB,EAChB,gBAAsE;QAOtE,MAAM,cAAc,GAAoB,EAAE,CAAC;QAC3C,MAAM,eAAe,GAAqB,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAoB,EAAE,CAAC;QACvC,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,MAAqB,EAAE,EAAE;gBACnF,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,MAAM,CAAC,CAAA,EAAE,CAAC;oBACzD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC9B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,mEAAmE;QACnE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,MAAqB,EAAE,EAAE;gBACtF,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,MAAM,CAAC,CAAA,EAAE,CAAC;oBACzD,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YACtE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;YAC1C,MAAM,cAAc,GAAG,IAAA,gCAAkB,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,MAAsB,EAAE,EAAE;gBAChF,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,MAAM,CAAC,CAAA,EAAE,CAAC;oBAClE,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,uEAAuE;YACvE,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,IAAoB,EAAE,EAAE;oBAC5E,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,IAAI,CAAC,CAAA,EAAE,CAAC;wBAC9D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;oBAED,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,cAAc;YACd,eAAe;YACf,OAAO;YACP,UAAU;SACX,CAAC;IACJ,CAAC;IAEM,UAAU;QACf,MAAM,OAAO,GAAuC,EAAE,CAAC;QACvD,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,EACjC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,EAC/B,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAC9B,CAAC;IACJ,CAAC;CACF;AA1MD,6BA0MC"}