/*!
 * Copyright (c) 2017-present Ghostery GmbH. All rights reserved.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */
export declare function compactTokens(tokens: Uint32Array): Uint32Array;
export declare function hasEmptyIntersection(s1: Uint32Array, s2: Uint32Array): boolean;
export declare function concatTypedArrays(arrays: Uint32Array[]): Uint32Array;
export declare function mergeCompactSets(arrays: Uint32Array[]): Uint32Array;
//# sourceMappingURL=compact-set.d.ts.map