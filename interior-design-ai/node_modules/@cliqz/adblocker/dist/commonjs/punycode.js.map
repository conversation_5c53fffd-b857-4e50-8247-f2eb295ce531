{"version": 3, "file": "punycode.js", "sourceRoot": "", "sources": ["../../src/punycode.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;;AA+IH,wBA8EC;AASD,wBAqFC;AAaD,8BASC;AAaD,0BAQC;AApWD,iDAAiD;AACjD,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,4BAA4B;AAEvD,4BAA4B;AAC5B,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO;AAC7B,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,SAAS;AAEhC,0BAA0B;AAC1B,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,kBAAkB;AACtD,MAAM,eAAe,GAAG,2BAA2B,CAAC,CAAC,sBAAsB;AAI3E,MAAM,MAAM,GAER;IACF,eAAe,EAAE,eAAe;IAChC,WAAW,EAAE,gDAAgD;IAC7D,UAAU,EAAE,iDAAiD;CAC9D,CAAC;AAEF,4BAA4B;AAC5B,MAAM,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;AAElC,8EAA8E;AAE9E;;;;;GAKG;AACH,SAAS,KAAK,CAAC,IAAgB;IAC7B,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,SAAS,UAAU,CAAC,GAAW;IAC7B,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,OAAO,OAAO,GAAG,MAAM,EAAE,CAAC;QACxB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QACxC,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC;YAC3D,wDAAwD;YACxD,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;gBAChC,iBAAiB;gBACjB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACN,uEAAuE;gBACvE,4DAA4D;gBAC5D,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,YAAY,CAAC,SAAiB;IACrC,IAAI,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;QAC5B,OAAO,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IACD,IAAI,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;QAC5B,OAAO,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IACD,IAAI,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;QAC5B,OAAO,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,YAAY,CAAC,KAAa,EAAE,IAAY;IAC/C,mCAAmC;IACnC,2BAA2B;IAC3B,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED;;;;GAIG;AACH,SAAS,KAAK,CAAC,KAAa,EAAE,SAAiB,EAAE,SAAkB;IACjE,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;IAC1D,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;IACvC,OAAO,uBAAuB,CAAC,KAAK,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;QAC9E,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,MAAM,CAAC,KAAa;IAClC,mBAAmB;IACnB,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,QAAQ,CAAC;IACjB,IAAI,IAAI,GAAG,WAAW,CAAC;IAEvB,wEAAwE;IACxE,uEAAuE;IACvE,6CAA6C;IAE7C,IAAI,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACzC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACd,KAAK,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;QAC/B,iCAAiC;QACjC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YAChC,KAAK,CAAC,WAAW,CAAC,CAAC;QACrB,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,4EAA4E;IAC5E,wDAAwD;IAExD,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,yBAAyB,GAAI,CAAC;QAC5F,6DAA6D;QAC7D,6DAA6D;QAC7D,2DAA2D;QAC3D,8DAA8D;QAC9D,sCAAsC;QACtC,MAAM,IAAI,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,GAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACzD,IAAI,KAAK,IAAI,WAAW,EAAE,CAAC;gBACzB,KAAK,CAAC,eAAe,CAAC,CAAC;YACzB,CAAC;YAED,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAEtD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC1D,KAAK,CAAC,UAAU,CAAC,CAAC;YACpB,CAAC;YAED,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;YACf,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAEhE,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,MAAM;YACR,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC;gBACxC,KAAK,CAAC,UAAU,CAAC,CAAC;YACpB,CAAC;YAED,CAAC,IAAI,UAAU,CAAC;QAClB,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;QAExC,qDAAqD;QACrD,qDAAqD;QACrD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,KAAK,CAAC,UAAU,CAAC,CAAC;QACpB,CAAC;QAED,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACzB,CAAC,IAAI,GAAG,CAAC;QAET,4CAA4C;QAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,MAAM,CAAC,GAAW;IAChC,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,iEAAiE;IACjE,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;IAE9B,oBAAoB;IACpB,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IAEjC,wBAAwB;IACxB,IAAI,CAAC,GAAG,QAAQ,CAAC;IACjB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,IAAI,GAAG,WAAW,CAAC;IAEvB,gCAAgC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAClC,IAAI,cAAc,GAAG,WAAW,CAAC;IAEjC,wEAAwE;IACxE,oDAAoD;IAEpD,8DAA8D;IAC9D,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzB,CAAC;IAED,sBAAsB;IACtB,OAAO,cAAc,GAAG,WAAW,EAAE,CAAC;QACpC,yEAAyE;QACzE,cAAc;QACd,IAAI,CAAC,GAAG,MAAM,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,YAAY,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBAC1C,CAAC,GAAG,YAAY,CAAC;YACnB,CAAC;QACH,CAAC;QAED,yEAAyE;QACzE,8BAA8B;QAC9B,MAAM,qBAAqB,GAAG,cAAc,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,EAAE,CAAC;YACjE,KAAK,CAAC,UAAU,CAAC,CAAC;QACpB,CAAC;QAED,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB,CAAC;QACzC,CAAC,GAAG,CAAC,CAAC;QAEN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,YAAY,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,CAAC;gBACzC,KAAK,CAAC,UAAU,CAAC,CAAC;YACpB,CAAC;YACD,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBACvB,4DAA4D;gBAC5D,IAAI,CAAC,GAAG,KAAK,CAAC;gBACd,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,GAAI,CAAC,IAAI,IAAI,EAAE,CAAC;oBAClD,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAChE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACV,MAAM;oBACR,CAAC;oBACD,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;oBACtB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,CAAC;gBACvC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,qBAAqB,EAAE,cAAc,KAAK,WAAW,CAAC,CAAC;gBAC3E,KAAK,GAAG,CAAC,CAAC;gBACV,EAAE,cAAc,CAAC;YACnB,CAAC;QACH,CAAC;QAED,EAAE,KAAK,CAAC;QACR,EAAE,CAAC,CAAC;IACN,CAAC;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,SAAS,CAAC,KAAa;IACrC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjE,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,OAAO,CAAC,IAAI,CACV,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CACpF,CAAC;IACJ,CAAC;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,OAAO,CAAC,KAAa;IACnC,uDAAuD;IACvD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjE,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED;;;;GAIG;AACU,QAAA,OAAO,GAAG,OAAO,CAAC"}