"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Smaz = void 0;
exports.decompress = decompress;
exports.compress = compress;
exports.getCompressedSize = getCompressedSize;
const smaz_compress_1 = require("@remusao/smaz-compress");
const smaz_decompress_1 = require("@remusao/smaz-decompress");
class Smaz {
    constructor(codebook, maxSize = 30000) {
        this.codebook = codebook;
        this.compressor = new smaz_compress_1.SmazCompress(codebook, maxSize);
        this.decompressor = new smaz_decompress_1.SmazDecompress(codebook);
    }
    compress(str) {
        return this.compressor.compress(str);
    }
    getCompressedSize(str) {
        return this.compressor.getCompressedSize(str);
    }
    decompress(buffer) {
        return this.decompressor.decompress(buffer);
    }
}
exports.Smaz = Smaz;
const dictionary = ' ;the;e;t;a;of;o;and;i;n;s;e ;r; th; t;in;he;th;h;he ;to;\r\n;l;s ;d; a;an;er;c; o;d ;on; of;re;of ;t ;, ;is;u;at;   ;n ;or;which;f;m;as;it;that;\n;was;en;  ; w;es; an; i;f ;g;p;nd; s;nd ;ed ;w;ed;http://;https://;for;te;ing;y ;The; c;ti;r ;his;st; in;ar;nt;,; to;y;ng; h;with;le;al;to ;b;ou;be;were; b;se;o ;ent;ha;ng ;their;";hi;from; f;in ;de;ion;me;v;.;ve;all;re ;ri;ro;is ;co;f t;are;ea;. ;her; m;er ; p;es ;by;they;di;ra;ic;not;s, ;d t;at ;ce;la;h ;ne;as ;tio;on ;n t;io;we; a ;om;, a;s o;ur;li;ll;ch;had;this;e t;g ;e\r\n; wh;ere; co;e o;a ;us; d;ss;\n\r\n;\r\n\r;="; be; e;s a;ma;one;t t;or ;but;el;so;l ;e s;s,;no;ter; wa;iv;ho;e a; r;hat;s t;ns;ch ;wh;tr;ut;/;have;ly ;ta; ha; on;tha;-; l;ati;en ;pe; re;there;ass;si; fo;wa;ec;our;who;its;z;fo;rs;>;ot;un;<;im;th ;nc;ate;><;ver;ad; we;ly;ee; n;id; cl;ac;il;</;rt; wi;div;e, ; it;whi; ma;ge;x;e c;men;.com'.split(";");
let SMAZ;
function getDefaultSmaz() {
    if (SMAZ === undefined) {
        SMAZ = new Smaz(dictionary);
    }
    return SMAZ;
}
function decompress(array) {
    return getDefaultSmaz().decompress(array);
}
function compress(str) {
    return getDefaultSmaz().compress(str);
}
function getCompressedSize(str) {
    return getDefaultSmaz().getCompressedSize(str);
}
//# sourceMappingURL=index.js.map