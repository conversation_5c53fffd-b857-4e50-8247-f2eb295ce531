{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAQ,MAAM,EAAE,MAAM,eAAe,CAAC;AAE7C,MAAM,iBAAiB,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAE5C,MAAM,OAAO,YAAY;IAKvB,YAAY,QAA2B,EAAE,OAAO,GAAG,KAAK;QACtD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAEM,iBAAiB,CAAC,GAAW;QAClC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,OAAO,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;YACd,IAAI,IAAI,GAAqB,IAAI,CAAC,IAAI,CAAC;YAEvC,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvB,MAAM;gBACR,CAAC;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBACjB,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YACD,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC;gBAChB,aAAa,EAAE,CAAC;gBAChB,UAAU,EAAE,CAAC;gBAEb,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC;oBAC1B,WAAW,IAAI,CAAC,GAAG,aAAa,CAAC;oBACjC,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;oBACxB,WAAW,IAAI,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAC7D,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;gBAED,WAAW,EAAE,CAAC;gBACd,UAAU,GAAG,eAAe,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,WAAW,IAAI,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAED,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;QAEvB,OAAO,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;YACd,IAAI,IAAI,GAAqB,IAAI,CAAC,IAAI,CAAC;YAEvC,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvB,MAAM;gBACR,CAAC;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBACjB,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YAED,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC;gBAChB,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC9D,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC;oBAC1B,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;oBAC7D,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;oBACxB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;oBAC7D,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC;gBAClC,UAAU,GAAG,eAAe,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAC3C,CAAC;IAEO,aAAa,CAAC,aAAqB,EAAE,WAAmB;QAC9D,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAG,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,aAAa,CAAC;YAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;CACF"}