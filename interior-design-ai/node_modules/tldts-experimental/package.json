{"name": "tldts-experimental", "version": "5.7.112", "description": "Library to work against complex domain names, subdomains and URIs.", "author": {"name": "<PERSON><PERSON><PERSON>"}, "contributors": ["<PERSON> <alexe<PERSON><PERSON>ahoodot<PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://github.com/remusao/tldts#readme", "bugs": {"url": "https://github.com/remusao/tldts/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/remusao/tldts.git"}, "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src", "index.ts"], "scripts": {"clean": "rimraf dist coverage", "lint": "tslint --config ../../tslint.json --project ./tsconfig.json", "build": "tsc --build ./tsconfig.json", "bundle": "tsc --build ./tsconfig.bundle.json && rollup --config ./rollup.config.ts --configPlugin typescript", "prepack": "yarn run bundle", "test": "mocha --config ../../.mocharc.js"}, "devDependencies": {"@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@types/chai": "^4.2.18", "@types/mocha": "^10.0.0", "@types/node": "^18.0.0", "chai": "^4.2.0", "mocha": "^10.1.0", "nyc": "^15.0.1", "rimraf": "^4.1.2", "rollup": "^3.17.3", "rollup-plugin-sourcemaps": "^0.6.1", "tldts-tests": "^5.7.112", "tslint": "^6.0.0", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.2"}, "dependencies": {"tldts-core": "^5.7.112"}, "keywords": ["tld", "sld", "domain", "subdomain", "subdomain", "hostname", "browser", "uri", "url", "domain name", "public suffix", "url parsing", "typescript"], "gitHead": "ccf61e39d15095f079c6dc495029d9262397d378"}