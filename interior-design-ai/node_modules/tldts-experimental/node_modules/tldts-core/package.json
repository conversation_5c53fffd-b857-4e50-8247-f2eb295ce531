{"name": "tldts-core", "version": "5.7.112", "description": "tldts core primitives (internal module)", "author": {"name": "<PERSON><PERSON><PERSON>"}, "contributors": ["<PERSON> <alexe<PERSON><PERSON>ahoodot<PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://github.com/remusao/tldts#readme", "bugs": {"url": "https://github.com/remusao/tldts/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/remusao/tldts.git"}, "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src", "index.ts"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "lint": "tslint --config ../../tslint.json --project ./tsconfig.json", "build": "tsc --build ./tsconfig.json", "bundle": "tsc --build ./tsconfig.bundle.json && rollup --config ./rollup.config.ts --configPlugin typescript", "prepack": "yarn run bundle", "test": "nyc mocha --config ../../.mocharc.js"}, "devDependencies": {"@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^11.0.0", "@types/chai": "^4.2.18", "@types/mocha": "^10.0.0", "@types/node": "^18.0.0", "chai": "^4.2.0", "mocha": "^10.1.0", "nyc": "^15.0.1", "rimraf": "^4.1.2", "rollup": "^3.17.3", "rollup-plugin-sourcemaps": "^0.6.1", "tslint": "^6.0.0", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.2"}, "gitHead": "ccf61e39d15095f079c6dc495029d9262397d378"}