{"version": 3, "file": "is-valid.js", "sourceRoot": "", "sources": ["../../../src/is-valid.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAEH,SAAS,YAAY,CAAC,IAAY;IAChC,OAAO,CACL,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,GAAG,CACxE,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,mBAAyB,QAAgB;IACvC,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IAED,KAAI,eAAgB,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;QAClE,OAAO,KAAK,CAAC;KACd;IAED,qCAAqC;IACrC,IAAI,YAAY,GAAW,CAAC,CAAC,CAAC;IAC9B,IAAI,YAAY,GAAW,CAAC,CAAC,CAAC;IAC9B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE;YACzB;YACE,+DAA+D;YAC/D,CAAC,GAAG,YAAY,GAAG,EAAE;gBACrB,sDAAsD;gBACtD,YAAY,KAAK,EAAE;gBACnB,+DAA+D;gBAC/D,YAAY,KAAK,EAAE;gBACnB,qEAAqE;gBACrE,YAAY,KAAK,EAAE,EACnB;gBACA,OAAO,KAAK,CAAC;aACd;YAED,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM,IACL,EAAC,eAAgB,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC;YAClE,KAAK,EACL;YACA,uDAAuD;YACvD,OAAO,KAAK,CAAC;SACd;QAED,YAAY,GAAG,IAAI,CAAC;KACrB;IAED,OAAO;IACL,iDAAiD;IACjD,GAAG,GAAG,YAAY,GAAG,CAAC,IAAI,EAAE;QAC5B,wEAAwE;QACxE,wEAAwE;QACxE,sDAAsD;QACtD,YAAY,KAAK,EAAE,CACpB,CAAC;AACJ,CAAC;AAtDD,4BAsDC"}