{"version": 3, "file": "extract-hostname.js", "sourceRoot": "", "sources": ["../../../src/extract-hostname.ts"], "names": [], "mappings": ";;AAAA;;;GAGG;AACH,SAAwB,eAAe,CACrC,GAAW,EACX,kBAA2B;IAE3B,IAAI,KAAK,GAAW,CAAC,CAAC;IACtB,IAAI,GAAG,GAAW,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,QAAQ,GAAY,KAAK,CAAC;IAE9B,wEAAwE;IACxE,IAAI,kBAAkB,KAAK,KAAK,EAAE;QAChC,gCAAgC;QAChC,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;YACpC,OAAO,IAAI,CAAC;SACb;QAED,sBAAsB;QACtB,OAAO,KAAK,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACxD,KAAK,IAAI,CAAC,CAAC;SACZ;QAED,uBAAuB;QACvB,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACvD,GAAG,IAAI,CAAC,CAAC;SACV;QAED,eAAe;QACf,IACE,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS;YACtC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAC1C;YACA,KAAK,IAAI,CAAC,CAAC;SACZ;aAAM;YACL,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;gBAC1B,qEAAqE;gBACrE,oEAAoE;gBACpE,kDAAkD;gBAClD,MAAM,YAAY,GAAG,eAAe,GAAG,KAAK,CAAC;gBAC7C,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACjC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAErC,IACE,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB;oBACA,QAAQ;iBACT;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB;oBACA,OAAO;iBACR;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB;oBACA,MAAM;iBACP;qBAAM,IACL,YAAY,KAAK,CAAC;oBAClB,EAAE,KAAK,GAAG,CAAC,SAAS;oBACpB,EAAE,KAAK,GAAG,CAAC,SAAS,EACpB;oBACA,KAAK;iBACN;qBAAM;oBACL,6BAA6B;oBAC7B,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE;wBAC/C,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;wBAC7C,IACE,CAAC,CAAC,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,GAAG,CAAC,IAAI,SAAS;4BAC3D,CAAC,aAAa,IAAI,EAAE,IAAI,aAAa,IAAI,EAAE,CAAC,IAAI,SAAS;4BACzD,aAAa,KAAK,EAAE,IAAI,MAAM;4BAC9B,aAAa,KAAK,EAAE,IAAI,MAAM;4BAC5B,aAAa,KAAK,EAAE,CAAC,KAAK,KAAK,CAAC,MAAM;0BACxC;4BACA,OAAO,IAAI,CAAC;yBACb;qBACF;iBACF;gBAED,mCAAmC;gBACnC,KAAK,GAAG,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE;oBAC7C,KAAK,IAAI,CAAC,CAAC;iBACZ;aACF;SACF;QAED,wEAAwE;QACxE,uEAAuE;QACvE,4CAA4C;QAC5C,IAAI,iBAAiB,GAAW,CAAC,CAAC,CAAC;QACnC,IAAI,qBAAqB,GAAW,CAAC,CAAC,CAAC;QACvC,IAAI,WAAW,GAAW,CAAC,CAAC,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;YACnC,MAAM,IAAI,GAAW,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvC,IACE,IAAI,KAAK,EAAE,IAAI,MAAM;gBACrB,IAAI,KAAK,EAAE,IAAI,MAAM;gBACrB,IAAI,KAAK,EAAE,CAAC,MAAM;cAClB;gBACA,GAAG,GAAG,CAAC,CAAC;gBACR,MAAM;aACP;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE;gBACtB,MAAM;gBACN,iBAAiB,GAAG,CAAC,CAAC;aACvB;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE;gBACtB,MAAM;gBACN,qBAAqB,GAAG,CAAC,CAAC;aAC3B;iBAAM,IAAI,IAAI,KAAK,EAAE,EAAE;gBACtB,MAAM;gBACN,WAAW,GAAG,CAAC,CAAC;aACjB;iBAAM,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;gBACnC,QAAQ,GAAG,IAAI,CAAC;aACjB;SACF;QAED,yBAAyB;QACzB,IACE,iBAAiB,KAAK,CAAC,CAAC;YACxB,iBAAiB,GAAG,KAAK;YACzB,iBAAiB,GAAG,GAAG,EACvB;YACA,KAAK,GAAG,iBAAiB,GAAG,CAAC,CAAC;SAC/B;QAED,wBAAwB;QACxB,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE;YAC1C,IAAI,qBAAqB,KAAK,CAAC,CAAC,EAAE;gBAChC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;aAClE;YACD,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,WAAW,GAAG,KAAK,IAAI,WAAW,GAAG,GAAG,EAAE;YACzE,mBAAmB;YACnB,GAAG,GAAG,WAAW,CAAC;SACnB;KACF;IAED,qBAAqB;IACrB,OAAO,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE;QAClE,GAAG,IAAI,CAAC,CAAC;KACV;IAED,MAAM,QAAQ,GACZ,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAElE,IAAI,QAAQ,EAAE;QACZ,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;KAC/B;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAjKD,kCAiKC"}