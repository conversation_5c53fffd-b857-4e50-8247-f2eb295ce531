import { IOptions, IResult } from 'tldts-core';
export declare function parse(url: string, options?: Partial<IOptions>): IResult;
export declare function getHostname(url: string, options?: Partial<IOptions>): string | null;
export declare function getPublicSuffix(url: string, options?: Partial<IOptions>): string | null;
export declare function getDomain(url: string, options?: Partial<IOptions>): string | null;
export declare function getSubdomain(url: string, options?: Partial<IOptions>): string | null;
export declare function getDomainWithoutSuffix(url: string, options?: Partial<IOptions>): string | null;
